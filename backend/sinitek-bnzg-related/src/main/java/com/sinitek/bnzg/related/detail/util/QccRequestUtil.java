package com.sinitek.bnzg.related.detail.util;

import static com.sinitek.bnzg.related.common.constant.RelatedMessageCodeConstant.REQUEST_EXCEPTION;
import static com.sinitek.bnzg.related.common.constant.RelatedMessageCodeConstant.THROUGH_QUERY_FAIL;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.sinitek.bnzg.related.detail.dto.QccTokenDTO;
import com.sinitek.bnzg.related.detail.support.QccResponse;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-07-25 10:32
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class QccRequestUtil {

    private static final String KEY = "key";

    private static final String TOKEN_KEY = "Token";

    private static final String TIMESPAN = "Timespan";

    private static final int DEFAULT_TIME_OUT = 30000;

    /**
     * 构建HTTP请求对象 此方法用于创建一个HttpRequest对象，并根据提供的参数设置请求头 主要用于添加认证所需的Token和Timespan到请求头中
     *
     * @param tokenDTO 包含Token和Timespan信息的数据传输对象
     * @param url 请求的目标URL地址
     * @return 返回配置好的HttpRequest对象
     */
    public static HttpRequest buildBaseRequest(QccTokenDTO tokenDTO, String url,
        Integer timeoutMillis) {
        // 创建一个GET类型的HttpRequest对象
        HttpRequest request = HttpUtil.createGet(url);
        request.form(KEY, tokenDTO.getKey());
        // 在请求头中添加Token信息
        request.header(TOKEN_KEY, tokenDTO.getToken());
        // 在请求头中添加Timespan信息
        request.header(TIMESPAN, tokenDTO.getTimespan().toString());
        // 返回配置好的HttpRequest对象

        if (Objects.isNull(timeoutMillis)) {
            request.timeout(DEFAULT_TIME_OUT);
        } else {
            request.timeout(timeoutMillis);
        }

        return request;
    }

    public static QccResponse sendRequest(HttpRequest request) {
        System.setProperty("https.protocols", "TLSv1.2");

        // 将HTTP请求执行结果的主体部分转换为Map对象
        String body;

        try {
            String url = request.getUrl();
            log.info("QCC请求地址: {}", url);
            log.info("QCC请求参数: {}", JsonUtil.toJsonString(request.form()));
            body = request.execute().body();
        } catch (Exception e) {
            log.error("企查查请求异常,{}", e.getMessage(), e);
            throw new BussinessException(REQUEST_EXCEPTION);
        }

        log.debug("QCC请求响应结果：{}", body);
        Map<String, Object> response = JsonUtil.toMap(body);
        QccResponse qccResponse = new QccResponse(response);

        // 检查响应的状态码是否为"200"
        if (Objects.equals(Boolean.TRUE, qccResponse.getSuccessFlag())) {
            return qccResponse;
        }

        String message = qccResponse.getMessage();
        if (StringUtils.isBlank(message)) {
            message = "返回值异常";
        }

        // 如果状态码不是"200"，抛出业务异常，表示获取数据失败
        throw new BussinessException(THROUGH_QUERY_FAIL, message);
    }
}
