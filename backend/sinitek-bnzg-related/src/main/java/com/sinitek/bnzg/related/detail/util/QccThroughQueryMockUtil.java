package com.sinitek.bnzg.related.detail.util;

import com.sinitek.bnzg.related.common.util.RelatedStringUtil;
import com.sinitek.bnzg.related.detail.dto.ThroughInfoDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.lowcode.llm.util.LlmConfigUtil;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-07 16:13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class QccThroughQueryMockUtil {

    public static final String MOCK_THROUGH_JSON = "/mock/through.json";

    public static List<ThroughInfoDTO> generateThroughInfoList(String queryKey) {
        String json = LlmConfigUtil.loadConfigByFile(MOCK_THROUGH_JSON);
        boolean isName = RelatedStringUtil.isName(queryKey);
        if (isName) {
            json = json.replaceAll("@name@", queryKey);
        }

        try {
            // 模拟请求耗时
            // 如果查询关键词zui'hou
            TimeUnit.SECONDS.sleep(3 * 60);
        } catch (Exception e) {
            // 忽略报错
        }

        return JsonUtil.toJavaObjectList(json, ThroughInfoDTO.class);
    }

}
