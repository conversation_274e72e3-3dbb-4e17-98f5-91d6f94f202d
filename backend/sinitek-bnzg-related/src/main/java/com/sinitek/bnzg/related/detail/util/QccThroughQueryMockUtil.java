package com.sinitek.bnzg.related.detail.util;

import com.sinitek.bnzg.related.common.util.RelatedStringUtil;
import com.sinitek.bnzg.related.detail.dto.ThroughInfoDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.lowcode.llm.util.LlmConfigUtil;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-08-07 16:13
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class QccThroughQueryMockUtil {

    public static final String MOCK_THROUGH_JSON = "/mock/through.json";

    private static final int DEFAULT_SLEEP_MINUTES = 3;

    public static List<ThroughInfoDTO> generateThroughInfoList(String queryKey) {
        String json = LlmConfigUtil.loadConfigByFile(MOCK_THROUGH_JSON);
        boolean isName = RelatedStringUtil.isName(queryKey);
        if (isName) {
            json = json.replaceAll("@name@", queryKey);
        }

        try {
            // 模拟请求耗时
            // 如果查询关键词最后一位是数字,且大于0，则以数字作为分钟数
            int sleepMinutes = extractMinutesFromQueryKey(queryKey);
            TimeUnit.SECONDS.sleep(sleepMinutes * 60L);
        } catch (Exception e) {
            // 忽略报错
            log.error("模拟查询耗时时休眠失败, {}", e.getMessage(), e);
        }

        return JsonUtil.toJavaObjectList(json, ThroughInfoDTO.class);
    }

    /**
     * 从查询关键词中提取分钟数
     * 如果查询关键词最后一位是数字且大于0，则以数字作为分钟数，否则默认为3分钟
     *
     * @param queryKey 查询关键词
     * @return 分钟数
     */
    private static int extractMinutesFromQueryKey(String queryKey) {
        if (queryKey == null || queryKey.isEmpty()) {
            return DEFAULT_SLEEP_MINUTES; // 默认3分钟
        }

        // 获取最后一个字符
        char lastChar = queryKey.charAt(queryKey.length() - 1);

        // 检查是否为数字
        if (Character.isDigit(lastChar)) {
            int digit = Character.getNumericValue(lastChar);
            // 如果数字大于0，则使用该数字作为分钟数
            if (digit > 0) {
                return digit;
            }
        }

        // 默认返回3分钟
        return DEFAULT_SLEEP_MINUTES;
    }

}
