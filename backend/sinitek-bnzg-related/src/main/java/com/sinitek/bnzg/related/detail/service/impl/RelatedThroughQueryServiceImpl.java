package com.sinitek.bnzg.related.detail.service.impl;

import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.bnzg.related.common.util.RelatedStringUtil;
import com.sinitek.bnzg.related.common.util.StopWatchUtil;
import com.sinitek.bnzg.related.detail.constant.RelatedDetailConstant;
import com.sinitek.bnzg.related.detail.dto.RelatedDetailQueryParamDTO;
import com.sinitek.bnzg.related.detail.dto.RelatedDetailQueryResultDTO;
import com.sinitek.bnzg.related.detail.dto.RelatedQueryParamDTO;
import com.sinitek.bnzg.related.detail.dto.RelatedThroughQueryParamDTO;
import com.sinitek.bnzg.related.detail.dto.ThroughInfoDTO;
import com.sinitek.bnzg.related.detail.service.IQccService;
import com.sinitek.bnzg.related.detail.service.IRelatedDetailQueryService;
import com.sinitek.bnzg.related.detail.service.IRelatedDetailService;
import com.sinitek.bnzg.related.detail.service.IRelatedThroughQueryService;
import com.sinitek.bnzg.related.detail.util.QccThroughQueryMockUtil;
import com.sinitek.bnzg.related.status.service.IRelatedSysStatusService;
import com.sinitek.bnzg.related.usage.util.RelatedUseEventUtil;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-18 14:47
 */
@Slf4j
@Service
public class RelatedThroughQueryServiceImpl implements IRelatedThroughQueryService {

    @Autowired
    private IQccService qccService;

    @Autowired
    private IRelatedDetailQueryService relatedDetailQueryService;

    @Autowired
    private IRelatedDetailService relatedDetailService;

    @Autowired
    private IRelatedSysStatusService relatedSysStatusService;

    private int SINGLE_QUERY_MAX_SIZE = 1000;

    @Override
    @SuppressWarnings("squid:S1075")
    public List<ThroughInfoDTO> throughQuery(RelatedThroughQueryParamDTO param) {
        String queryKey = param.getQueryKey();
        String operatorId = param.getOperatorId();
        String orgName = param.getOperatorName();

        queryKey = RelatedStringUtil.toHalfWidth(queryKey);

        RelatedUseEventUtil.publishThroughQueryCreateEvent(operatorId, new Date(), queryKey);

        this.relatedSysStatusService.checkRelatedSysStatusWithExThrow();

        List<ThroughInfoDTO> throughInfoList;
        Integer boolIntValue = SettingUtils.getIntegerValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.QCC_MOCK_FLAG);
        Boolean mockEnableFlag = CommonBooleanEnum.getBoolValueByValue(boolIntValue);
        if (mockEnableFlag) {
            log.info("操作人[{},{}] mock throughQuery 穿透查询，queryName: {},mockFilePath: {}",
                operatorId, orgName,
                queryKey, QccThroughQueryMockUtil.MOCK_THROUGH_JSON);

            throughInfoList = QccThroughQueryMockUtil.generateThroughInfoList(queryKey);
        } else {
            log.info("操作人[{},{}] throughQuery 穿透查询，queryName:{}", operatorId, orgName,
                queryKey);
            throughInfoList = fetchThroughInfo(queryKey, operatorId);
        }
        StopWatchUtil.start("获取并处理关联详情");
        // 获取并处理关联详情
        throughInfoList = processThroughInfoWithRelatedDetails(throughInfoList);

        StopWatchUtil.start("数据处理并返回");
        //若“是否为关联方” 不存在“是”，则不展示
        boolean hasRelated = throughInfoList.stream()
            .anyMatch(item -> Objects.equals(Boolean.TRUE, item.getIsRelated()));
        if (!hasRelated) {
            // 返回空数组前台会提示不存在关联关系
            return Collections.emptyList();
        }

        return throughInfoList;
    }

    /**
     * 从QCC服务获取所有穿透信息
     *
     * @param queryName 查询名称
     * @return 穿透信息列表
     */
    private List<ThroughInfoDTO> fetchThroughInfo(String queryName,
        String operatorId) {
        List<ThroughInfoDTO> result = new LinkedList<>();

        // singleQuery 接口,list中总是有数据的
        StopWatchUtil.start("单次查询");
        List<RelatedDetailQueryResultDTO> list = this.relatedDetailQueryService.singleQuery(
            RelatedQueryParamDTO.builder()
                .queryKey(queryName)
                .operatorId(operatorId)
                .opTime(new Date())
                .build());

        if (CollectionUtils.isNotEmpty(list)) {
            RelatedDetailQueryResultDTO self = list.get(0);
            ThroughInfoDTO selfInfo = new ThroughInfoDTO();
            selfInfo.setType("本级");
            selfInfo.setName(self.getName());
            selfInfo.setCode(self.getCode());
            selfInfo.setIsRelated(self.getIsRelated());
            selfInfo.setIsRelatedName(self.getIsRelatedName());
            selfInfo.setLevel("-");
            selfInfo.setStockPercent("-");
            selfInfo.setPartyName(self.getPartyName());
            result.add(selfInfo);
        }

        StopWatchUtil.start("穿透股东查询");
        result.addAll(this.qccService.listStockList(queryName));
        StopWatchUtil.start("穿透实际控制人查询");
        result.addAll(this.qccService.listActualControlList(queryName));
        StopWatchUtil.start("穿透投资查询");
        result.addAll(this.qccService.listInvestList(queryName));
        return result;
    }

    /**
     * 从关联详情服务获取并过滤关联详情
     *
     * @param throughInfoList 穿透信息列表
     * @return 过滤后的关联详情列表
     */
    private List<RelatedDetailQueryResultDTO> fetchRelatedDetails(
        List<ThroughInfoDTO> throughInfoList) {
        int size = throughInfoList.size();
        if (size > SINGLE_QUERY_MAX_SIZE) {
            // 每 1000 条数据查询一次
            List<RelatedDetailQueryResultDTO> result = new LinkedList<>();
            for (int i = 0; i < size; i += SINGLE_QUERY_MAX_SIZE) {
                int end = Math.min(i + SINGLE_QUERY_MAX_SIZE, size);
                log.info("匹配序号为[{}]-[{}]的数据", i, end);
                List<ThroughInfoDTO> subList = throughInfoList.subList(i, end);
                List<String> nameList = subList.stream()
                    .map(ThroughInfoDTO::getName)
                    .distinct() // 使用distinct去除重复名称
                    .collect(Collectors.toList());
                List<RelatedDetailQueryResultDTO> relatedList = this.relatedDetailService.findByParam(
                    RelatedDetailQueryParamDTO.builder()
                        .nameList(nameList)
                        .build());
                result.addAll(relatedList);
            }
            return result;
        } else {
            List<String> nameList = throughInfoList.stream()
                .map(ThroughInfoDTO::getName)
                .distinct() // 使用distinct去除重复名称
                .collect(Collectors.toList());

            List<RelatedDetailQueryResultDTO> allRelatedList = this.relatedDetailService.findByParam(
                RelatedDetailQueryParamDTO.builder()
                    .nameList(nameList)
                    .build());

            return allRelatedList.stream()
                .filter(item -> Objects.nonNull(item.getIsRelated()) && item.getIsRelated())
                .collect(Collectors.toList());
        }
    }

    /**
     * 根据关联详情处理穿透信息
     *
     * @param throughInfoList 穿透信息列表
     */
    private List<ThroughInfoDTO> processThroughInfoWithRelatedDetails(
        List<ThroughInfoDTO> throughInfoList) {
        List<RelatedDetailQueryResultDTO> relatedDetails = fetchRelatedDetails(throughInfoList);
        // 构建关联详情映射
        Map<String, List<RelatedDetailQueryResultDTO>> relatedNameMap = relatedDetails.stream()
            .collect(
                Collectors.groupingBy(RelatedDetailQueryResultDTO::getName));

        //throughInfoList根据type排序
        List<ThroughInfoDTO> newList = new ArrayList<>(throughInfoList.size());
        ThroughInfoDTO querySelf = throughInfoList.stream()
            .filter(item -> item.getType().equals("本级"))
            .findFirst().orElse(null);
        if (Objects.nonNull(querySelf)) {
            newList.add(querySelf);
        }
        newList.addAll(throughInfoList.stream()
            .filter(item -> item.getType().equals("实际控制人"))
            .collect(Collectors.toList()));
        newList.addAll(throughInfoList.stream()
            .filter(item -> item.getType().equals("穿透股东"))
            .collect(Collectors.toList()));
        newList.addAll(throughInfoList.stream()
            .filter(item -> item.getType().equals("穿透投资"))
            .collect(Collectors.toList()));

        // 处理每个穿透信息对象
        List<ThroughInfoDTO> resultList = new ArrayList<>(throughInfoList.size());
        int currentIndex = 1;
        for (int i = 0; i < newList.size(); i++) {
            // 穿透对象
            ThroughInfoDTO throughInfoDTO = newList.get(i);
            String name = throughInfoDTO.getName();

            List<RelatedDetailQueryResultDTO> relatedList = MapUtils.getObject(relatedNameMap,
                RelatedStringUtil.getRelatedName(name));

            if (CollectionUtils.isNotEmpty(relatedList)) {
                throughInfoDTO.setRank(String.valueOf(currentIndex));
                throughInfoDTO.setIsRelated(true);
                throughInfoDTO.setIsRelatedName(RelatedDetailConstant.YES_STR);
                String partyName = relatedList.stream()
                    .map(item -> String.format("%s(%s)", item.getName(), item.getPartyName()))
                    .collect(Collectors.joining("\n")).trim();
                throughInfoDTO.setPartyName(partyName);
                resultList.add(throughInfoDTO);
                currentIndex++;
            }
        }
        return resultList;
    }
}
