package com.sinitek.bnzg.related.detail.util;

import org.junit.jupiter.api.Test;
import java.lang.reflect.Method;
import static org.junit.jupiter.api.Assertions.*;

/**
 * QccThroughQueryMockUtil 测试类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public class QccThroughQueryMockUtilTest {

    @Test
    public void testExtractMinutesFromQueryKey() throws Exception {
        // 使用反射访问私有方法
        Method method = QccThroughQueryMockUtil.class.getDeclaredMethod("extractMinutesFromQueryKey", String.class);
        method.setAccessible(true);

        // 测试用例1: 查询关键词以数字结尾且大于0
        assertEquals(5, method.invoke(null, "公司名称5"));
        assertEquals(9, method.invoke(null, "企业查询9"));
        assertEquals(1, method.invoke(null, "测试1"));
        assertEquals(7, method.invoke(null, "keyword7"));

        // 测试用例2: 查询关键词以0结尾（应该返回默认值3）
        assertEquals(3, method.invoke(null, "公司名称0"));
        assertEquals(3, method.invoke(null, "test0"));

        // 测试用例3: 查询关键词不以数字结尾
        assertEquals(3, method.invoke(null, "公司名称"));
        assertEquals(3, method.invoke(null, "企业查询a"));
        assertEquals(3, method.invoke(null, "test_keyword"));

        // 测试用例4: 空字符串或null
        assertEquals(3, method.invoke(null, ""));
        assertEquals(3, method.invoke(null, (String) null));

        // 测试用例5: 单个数字字符
        assertEquals(8, method.invoke(null, "8"));
        assertEquals(3, method.invoke(null, "0"));

        System.out.println("所有测试用例通过！");
    }

    @Test
    public void testRealWorldExamples() throws Exception {
        Method method = QccThroughQueryMockUtil.class.getDeclaredMethod("extractMinutesFromQueryKey", String.class);
        method.setAccessible(true);

        // 实际使用场景示例
        System.out.println("=== 实际使用场景示例 ===");
        
        String[] testCases = {
            "阿里巴巴2",     // 应该返回2分钟
            "腾讯科技5",     // 应该返回5分钟
            "百度公司",      // 应该返回3分钟（默认）
            "字节跳动0",     // 应该返回3分钟（0不算有效）
            "华为技术9",     // 应该返回9分钟
            "小米集团a",     // 应该返回3分钟（非数字结尾）
            ""              // 应该返回3分钟（空字符串）
        };

        for (String testCase : testCases) {
            int result = (int) method.invoke(null, testCase);
            System.out.printf("查询关键词: '%s' -> 等待时间: %d分钟%n", testCase, result);
        }
    }
}
