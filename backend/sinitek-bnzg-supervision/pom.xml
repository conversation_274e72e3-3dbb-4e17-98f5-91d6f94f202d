<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <artifactId>sinitek-bnzg-supervision</artifactId>
  <dependencies>
    <dependency>
      <artifactId>sinitek-bnzg-common</artifactId>
      <groupId>com.sinitek.bnzg</groupId>
    </dependency>

    <dependency>
      <artifactId>poi-tl</artifactId>
      <groupId>com.deepoove</groupId>
      <version>${poi-tl.version}</version>
    </dependency>

    <dependency>
      <artifactId>aspose-words</artifactId>
      <classifier>jdk17</classifier>
      <groupId>com.aspose</groupId>
      <version>${aspose-words.version}</version>
    </dependency>

    <dependency>
      <artifactId>sinitek-sirmapp</artifactId>
      <groupId>com.sinitek.sinicube</groupId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <artifactId>sinitek-lowcode-test-tools</artifactId>
      <groupId>com.sinitek.sinicube</groupId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <artifactId>h2</artifactId>
      <groupId>com.h2database</groupId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <artifactId>spring-boot-starter-test</artifactId>
      <groupId>org.springframework.boot</groupId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-freemarker</artifactId>
    </dependency>
  </dependencies>

  <modelVersion>4.0.0</modelVersion>
  <packaging>jar</packaging>

  <parent>
    <artifactId>sinitek-bnzgsjhg</artifactId>
    <groupId>com.sinitek.bnzg</groupId>
    <version>1.2.2</version>
  </parent>

  <properties>
    <aspose-words.version>21.6</aspose-words.version>
    <poi-tl.version>1.12.2</poi-tl.version>
  </properties>

</project>
