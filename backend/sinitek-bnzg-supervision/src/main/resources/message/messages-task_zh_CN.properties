com.sinitek.bnzg.supervision.task.name_can_not_exceed=任务名称不能超过100个字符
com.sinitek.bnzg.supervision.task.sourcedescription_can_not_exceed=制度发文/监管要求/其他任务背景不能超过500个字符
com.sinitek.bnzg.supervision.task.brief_can_not_exceed=任务描述不能超过500个字符
com.sinitek.bnzg.supervision.task.taskrequirements_can_not_exceed=任务处理要求不能超过500个字符
com.sinitek.bnzg.supervision.task.attachmentrequirements_can_not_exceed=上传文档要求不能超过500个字符
com.sinitek.bnzg.supervision.task.namepath_can_not_exceed=拼接后的任务名。;分隔不能超过500个字符
com.sinitek.bnzg.supervision.task.remarks_can_not_exceed=操作说明不能超过500个字符
com.sinitek.bnzg.supervision.task.main_participant_can_not_be_null = 任务主办不能为空
com.sinitek.bnzg.supervision.task.main_participant_can_not_be_found = 无法获取承办任务【{0}】
com.sinitek.bnzg.supervision.task.main_participant_has_been_processed = 已经处理过该任务，请勿重复处理
com.sinitek.bnzg.supervision.task.require_approve_can_not_be_found = 请输入是否需要审批
com.sinitek.bnzg.supervision.task.overdue_attachment_can_not_be_empty = 请上传未逾期证明
com.sinitek.bnzg.supervision.task.failed_of_department_chief_not_be_found = 未找到部门长，提交失败
com.sinitek.bnzg.supervision.task.failed_of_assisting_task_not_be_done = 存在未处理的协办任务，提交失败
com.sinitek.bnzg.supervision.task.failed_of_main_task_not_be_done = 当前任务已存在主办审批,请前往审批列表处理 
com.sinitek.bnzg.supervision.task.failed_of_supervise_task_not_be_done = 当前任务已存在督办审批,请前往审批列表处理
com.sinitek.bnzg.supervision.task.approver_can_not_be_empty = 审批人为空，请选择审批人


#任务流转信息

# 操作
SUP_TASK_LOG_OPERATION_OF_CREATE_TASK=任务派发
SUP_TASK_LOG_OPERATION_OF_DEAL_TASK_MAIN = 主办处理
SUP_TASK_LOG_OPERATION_OF_DEAL_TASK_ASSISTING = 协办处理
SUP_TASK_LOG_OPERATION_OF_RETURN=主办退回协办处理结果


# 描述
SUP_TASK_LOG_BRIEF_OF_CREATE_TASK=任务派发
SUP_TASK_LOG_BRIEF_OF_DEAL_TASK_MAIN = 主办人【{0}】提交了任务【{1}】的处理结果
SUP_TASK_LOG_BRIEF_OF_DEAL_TASK_ASSISTING = 协办人【{0}】提交了任务【{1}】的处理结果
SUP_TASK_LOG_BRIEF_OF_RETURN=协办人【{0}】处理结果被退回，退回原因：{1}
TASK_DEALER_ERROR=任务处理人与当前登录用户不一致，处理失败

# document
9904007004=下载附件时,获取到文档数据为空


