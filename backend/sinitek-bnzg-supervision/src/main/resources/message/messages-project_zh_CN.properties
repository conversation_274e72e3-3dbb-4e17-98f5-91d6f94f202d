com.sinitek.sup.project.title_already_exists=项目标题已存在
com.sinitek.sup.project.project_id_can_not_be_null=项目id不能为空
com.sinitek.sup.project.type_can_not_be_null=项目类型不能为空
com.sinitek.sup.project.title_can_not_be_null=项目标题不能为空
com.sinitek.sup.project.report_subjects_can_not_be_null=上报主体不能为空
com.sinitek.sup.project.title_can_not_exceed=项目标题[{0}]不能超过50个字符
com.sinitek.sup.project.task_brief_can_not_exceed=任务描述不能超过500个字符
com.sinitek.sup.project.task_requirements_can_not_exceed=任务处理要求不能超过500个字符
com.sinitek.sup.project.attachment_requirements_can_not_exceed=上传文档要求不能超过500个字符
com.sinitek.sup.project.start_date_can_not_be_null=生效开始日期不能为空
com.sinitek.sup.project.repeat_type_can_not_be_null=重复频率不能为空
com.sinitek.sup.project.day_of_week_can_not_be_null=重复频率为[每周]时，一周内某天不能为空
com.sinitek.sup.project.day_of_month_can_not_be_null=重复频率为[每月、每季度、每年、每半年]时，一月内某天不能为空
com.sinitek.sup.project.month_number_can_not_be_null=重复频率为[每季度]时，季度内某月不能为空
com.sinitek.sup.project.month_number_can_not_exceed=重复频率为[每季度]时，季度内第几月不能大于3
com.sinitek.sup.project.month_of_year_can_not_be_null=重复频率为[每季度、每年、每半年]，月份不能为空
com.sinitek.sup.project.month_of_year_can_not_exceed=重复频率为[每半年]时，月份不能超过6
com.sinitek.sup.project.task_duration_days_can_not_be_null=任务持续天数不能为空
com.sinitek.sup.project.end_day_type_can_not_be_null=结束日类型不能为空
com.sinitek.sup.project.task_name_can_not_be_null=任务名称不能为空
com.sinitek.sup.project.task_name_can_not_exceed=任务名称不能超过50个字符
com.sinitek.sup.project.lead_dept_id_can_not_be_null=主办部门不能为空
com.sinitek.sup.project.lead_emp_id_can_not_be_null=主办人不能为空
com.sinitek.sup.project.approver_id_can_not_be_null=审批人不能为空
com.sinitek.sup.project.source_description_can_not_exceed=制度发文/监管要求/其他任务背景不能超过500个字符
com.sinitek.sup.project.project_edit_permission_denied=状态仅为[草稿]的项目允许编辑操作
com.sinitek.sup.project.project_start_remind_config_error=开始日期提醒配置不能超过[{0}]条
com.sinitek.sup.project.project_end_remind_config_error=存在重复的结束日期提醒配置
com.sinitek.sup.project.repeat_type_not_found = 重复频率:[{0}]，未找到对应重复频率枚举类
com.sinitek.sup.project.dep_id_can_not_be_null=所属部门不能为空
com.sinitek.sup.project.holiday_strategy_can_not_be_null=节假日策略不能为空
com.sinitek.sup.project.task_name_param_can_not_be_null=任务名称参数不能为空
com.sinitek.sup.project.project_not_exist=项目[{0}]数据不存在
com.sinitek.sup.project.project_submit_status_error= 仅状态为[草稿]的项目可以提交
com.sinitek.sup.project.remind_type_can_not_be_null= 提醒方式不能为空
com.sinitek.sup.project.end_date_can_not_early_than_start_date=项目生效结束日期不能早于项目生效开始日期
com.sinitek.sup.project.project_type_change_permission_denied=项目类型不允许修改
com.sinitek.sup.project.task_start_date_can_not_be_null=重复频率为[从不]时，任务开始日期不能为空
com.sinitek.sup.project.task_end_date_can_not_be_null=重复频率为[从不]时，任务结束日期不能为空
com.sinitek.sup.project.repeat_type_not_match=当前任务开始结束日期与项目重复频率不一致，请重新打开新增或编辑任务页面后操作
com.sinitek.sup.project.project_task_not_configured=项目[{0}]，存在未配置开始结束日期任务，请配置完整后提交
com.sinitek.sup.project.project_task_parent_remind_rule_not_exist=父任务id:[{0}]，未配置任务开始结束日期规则，无法预览任务
com.sinitek.sup.project.task_end_date_limit=重复频率为[从不]时，任务结束日期不能小于开始日期
com.sinitek.sup.project.task_name_already_exists=任务名称[{0}]已存在

com.sinitek.bnzg.supervision.project.approval.approval_param_error=审批参数有误，保存审批参数失败
com.sinitek.bnzg.supervision.project.approval.project_have_been_approved=项目已经被审批
com.sinitek.bnzg.supervision.project.approval.project_approval_record_not_exist=项目审批记录不存在
com.sinitek.bnzg.supervision.project.approval.project_approving=该项目在{0}审批中
com.sinitek.bnzg.supervision.project.approval.project_approver_error=项目审批人和当前用户不一致，提交审批失败



com.sinitek.bnzg.supervision.project.auth.message.input_project_warn=项目权限校验: 请确认关联的项目是否正确
com.sinitek.bnzg.supervision.project.auth.message.no_project_auth=当前用户【{0}】没有项目【{1}】的{2}权限


com.sinitek.bnzg.supervision.project.remind.message.message_link_error=邮件跳转链接有误，请检查跳转链接
com.sinitek.bnzg.supervision.project.remind.message.message_link_approver_error=邮件链接任务处理人和当前用户不一致