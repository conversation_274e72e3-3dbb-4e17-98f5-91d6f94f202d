package com.sinitek.bnzg.supervision.project.remind.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
@Data
@ApiModel(value = "消息提醒-结果DTO")
public class MessageRemindResultDTO {

    @ApiModelProperty("是否已经处理完成")
    private Boolean dealFlag;

    @ApiModelProperty("处理/查看url")
    private String url;
}
