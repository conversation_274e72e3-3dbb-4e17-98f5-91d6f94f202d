package com.sinitek.bnzg.supervision.project.remind.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Data
public class MessageRemindSendDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    @ApiModelProperty(value = "来源记录ID")
    private Long sourceId;

    @ApiModelProperty(value = "发送方式")
    private Integer sendMode;

    @ApiModelProperty(value = "提醒类型")
    private Integer type;

    @ApiModelProperty(value = "提醒日期")
    private Date remindDate;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "提交人ID")
    private String submitId;

    @ApiModelProperty(value = "提交人名称")
    private String submitName;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "收件人id")
    private String receiverId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "跳转链接")
    private String url;
}
