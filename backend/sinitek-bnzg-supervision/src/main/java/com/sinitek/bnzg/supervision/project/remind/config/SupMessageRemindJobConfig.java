package com.sinitek.bnzg.supervision.project.remind.config;

import com.sinitek.bnzg.supervision.project.remind.job.SupMessageRemindJob;
import com.sinitek.sirm.common.quartz.constant.QuartzConstant;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@Configuration
public class SupMessageRemindJobConfig {

    public static final String JOB_NAME = "任务督办邮件提醒";

    @Bean
    public JobDetail supMessageRemindJobDetail() {
        return JobBuilder.newJob(SupMessageRemindJob.class)
                .withIdentity(JOB_NAME, QuartzConstant.SYSTEM_TYPE)
                .storeDurably()
                .withDescription("任务督办邮件提醒")
                .build();
    }

    /**
     * 每天8:30执行
     * @param supMessageRemindJobDetail 任务督办邮件提醒
     * @return
     */
    @Bean
    public Trigger supMessageRemindJobTrigger(JobDetail supMessageRemindJobDetail) {
        return TriggerBuilder.newTrigger()
                .forJob(supMessageRemindJobDetail)
                .withIdentity(JOB_NAME, QuartzConstant.SYSTEM_TYPE)
                .startNow()
                .withSchedule(CronScheduleBuilder.cronSchedule("0 30 8 ? * *"))
                .usingJobData("name", JOB_NAME)
                .withDescription(supMessageRemindJobDetail.getDescription())
                .build();
    }
}
