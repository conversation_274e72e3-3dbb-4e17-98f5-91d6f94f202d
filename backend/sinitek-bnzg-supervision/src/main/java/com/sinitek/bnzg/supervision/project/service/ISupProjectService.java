package com.sinitek.bnzg.supervision.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.supervision.project.dto.SupProjectDepIdsResultDTO;
import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.entity.*;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
public interface ISupProjectService{

    /**
     *
     * 项目新增/编辑
     *
     **/
    Long saveOrUpdate(SupProjectSaveDTO dto);

    /**
     *
     * 检查项目标题是否已存在
     *
     **/
    void checkTitleExist(String title, Long ignoreId);

    /**
     *
     * 查询项目详情
     *
     **/
    SupProjectDetailResultDTO getById(Long id);

    /**
     *
     * 分页查询项目
     *
     **/
    IPage<SupProjectSearchResultDTO> search(SupProjectSearchParamDTO dto);


    /**
     *
     * 根据id集合查询项目
     *
     **/
    List<SupProject> findProjectsByIds(List<Long> ids);

    /**
     *
     * 项目提交
     *
     **/
    void submit(SupProjectSubmitDTO dto);

    /**
     *
     * 创建任务实例
     *
     **/
    void createTask(Long projectId);

    /**
     *
     * 创建非重复任务实例
     *
     **/
    void createNotRepeatTask(List<SupProjectTask> allNoRepeatTasks, SupProject project, List<Long> startedTaskIds, List<SupProjectTaskCreateDTO> failDispatchTaskList, List<SupProjectTaskCreateDTO> successDispatchTaskList);

    /**
     *
     * 创建非重复子任务实例
     *
     **/
    void createSubNotRepeatTasks(SupProjectTask rootTask, SupProject project, Long parentTaskId, List<SupProjectTask> allTasks,
                                 Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap,
                                 Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap,
                                 Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap,
                                 Map<Long, List<Attachment>> allAttachmentsMap, List<Long> startedTaskIds,
                                 List<SupProjectTaskCreateDTO> failDispatchTaskList, List<SupProjectTaskCreateDTO> successDispatchTaskList);

    /**
     *
     * 创建重复任务实例
     *
     **/
    void createRepeatTask(List<SupProjectTask> repeatTasks, SupProject project, List<Long> startedTaskIds,
                          List<SupProjectTaskCreateDTO> failDispatchTaskList, List<SupProjectTaskCreateDTO> successDispatchTaskList);

    /**
     * 查询项目详情
     */
    SupProjectDetailResultDTO detail(Long id);

    /**
    * 项目提交前校验
    */
    List<SupProjectCheckResultDTO> checkSubmit(SupProjectSubmitDTO dto);

    /**
    * 查询当前登录人可选所属部门
    */
    SupProjectDepIdsResultDTO loadSupDepIds();

    /**
    * 项目复制
    */
    void copy(Long id);
}
