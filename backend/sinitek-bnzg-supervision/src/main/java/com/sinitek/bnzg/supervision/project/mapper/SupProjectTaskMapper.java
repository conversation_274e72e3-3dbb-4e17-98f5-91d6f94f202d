package com.sinitek.bnzg.supervision.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public interface SupProjectTaskMapper extends BaseMapper<SupProjectTask> {

    List<Long> findProjectTaskIdByProjectId(@Param("projectId") Long projectId);

    void deleteByIds(@Param("ids")List<Long> ids);

    void resetTaskRepeatRule(@Param("allTaskIds") List<Long> allTaskIds);
}
