package com.sinitek.bnzg.supervision.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.bnzg.supervision.project.dao.SupProjectFlowLogDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskDispatchDAO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectFlowLogResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectFlowLogSaveDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskDispatchResultDTO;
import com.sinitek.bnzg.supervision.project.entity.SupProjectFlowLog;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectFlowLogTypeEnum;
import com.sinitek.bnzg.supervision.project.service.ISupProjectFlowLogService;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@Service
@Setter(onMethod = @__({@Autowired}))
@Slf4j
public class SupProjectFlowLogServiceImpl implements ISupProjectFlowLogService {

    private SupProjectFlowLogDAO supProjectFlowLogDAO;

    private IOrgService orgService;

    private SupProjectTaskDispatchDAO projectTaskDispatchDAO;

    @Override
    public List<SupProjectFlowLogResultDTO> findSupProjectFlowLog(Long projectId) {
        if (projectId == null) {
            log.info("查询项目流转记录时，项目id为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SupProjectFlowLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupProjectFlowLog::getProjectId, projectId);
        queryWrapper.orderByDesc(SupProjectFlowLog::getDealTime);
        queryWrapper.orderByDesc(SupProjectFlowLog::getId);
        List<SupProjectFlowLog> supProjectFlowLogs = supProjectFlowLogDAO.list(queryWrapper);
        if (CollectionUtils.isEmpty(supProjectFlowLogs)){
            return new ArrayList<>();
        }

        List<String> operatorIds = supProjectFlowLogs.stream().map(SupProjectFlowLog::getOperatorId).collect(Collectors.toList());
        Map<String, String> orgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(operatorIds);
        List<Long> taskDispatchFlowLogIds = supProjectFlowLogs.stream().filter(
                item -> Objects.equals(item.getType(), SupProjectFlowLogTypeEnum.AUTOMATIC_TASK_DISTRIBUTION.getValue())
        ).map(SupProjectFlowLog::getId).collect(Collectors.toList());
        List<SupProjectTaskDispatchResultDTO> taskDispatchResultDTOList = projectTaskDispatchDAO.findByFlowLogIds(taskDispatchFlowLogIds);
        Map<Long, List<SupProjectTaskDispatchResultDTO>> taskDispatchResultMap = taskDispatchResultDTOList.stream()
                .collect(Collectors.groupingBy(SupProjectTaskDispatchResultDTO::getFlowLogId));
        return supProjectFlowLogs.stream().map(item -> {
            SupProjectFlowLogResultDTO supProjectFlowLogResultDTO = new SupProjectFlowLogResultDTO();
            BeanUtils.copyProperties(item, supProjectFlowLogResultDTO);
            String operatorId = item.getOperatorId();
            if ("0".equals(operatorId)){
                supProjectFlowLogResultDTO.setOperatorName("系统");
            } else {
                supProjectFlowLogResultDTO.setOperatorName(MapUtils.getString(orgIdAndNameMap, operatorId, ""));
            }
            SupProjectFlowLogTypeEnum supProjectFlowLogTypeEnum = SupProjectFlowLogTypeEnum.getByValue(item.getType());
            supProjectFlowLogResultDTO.setTypeName(Objects.nonNull(supProjectFlowLogTypeEnum) ? supProjectFlowLogTypeEnum.getName() : "");
            if (Objects.equals(supProjectFlowLogTypeEnum, SupProjectFlowLogTypeEnum.AUTOMATIC_TASK_DISTRIBUTION)){
                List<SupProjectTaskDispatchResultDTO> taskDispatchResultList = taskDispatchResultMap.getOrDefault(item.getId(), new ArrayList<>());
                List<SupProjectTaskDispatchResultDTO> failedTaskDispatchResultList =  taskDispatchResultList.stream()
                        .filter(taskDispatchResult -> Objects.equals(taskDispatchResult.getIsDispatch(), CommonBooleanEnum.FALSE.getValue()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(failedTaskDispatchResultList)){
                    supProjectFlowLogResultDTO.setDispatchFailCount(failedTaskDispatchResultList.size());
                    supProjectFlowLogResultDTO.setDispatchFailList(failedTaskDispatchResultList);
                }
                List<SupProjectTaskDispatchResultDTO> successTaskDispatchResultList =  taskDispatchResultList.stream()
                        .filter(taskDispatchResult -> Objects.equals(taskDispatchResult.getIsDispatch(), CommonBooleanEnum.TRUE.getValue()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(successTaskDispatchResultList)){
                    supProjectFlowLogResultDTO.setDispatchSuccessCount(successTaskDispatchResultList.size());
                    supProjectFlowLogResultDTO.setDispatchSuccessList(successTaskDispatchResultList);
                }
            }
            return supProjectFlowLogResultDTO;
        }).collect(Collectors.toList());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateSupProjectFlowLog(SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO) {
        if (Objects.isNull(supProjectFlowLogSaveDTO)) {
            log.warn("保存项目流转记录参数为空，保存失败");
            return;
        }
        SupProjectFlowLog supProjectFlowLog = new SupProjectFlowLog();
        BeanUtils.copyProperties(supProjectFlowLogSaveDTO, supProjectFlowLog);
        supProjectFlowLogDAO.saveOrUpdate(supProjectFlowLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSupProjectFlowLog(SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO) {
        SupProjectFlowLog supProjectFlowLog = new SupProjectFlowLog();
        BeanUtils.copyProperties(supProjectFlowLogSaveDTO, supProjectFlowLog);
        supProjectFlowLogDAO.saveOrUpdate(supProjectFlowLog);
        return supProjectFlowLog.getId();
    }
}
