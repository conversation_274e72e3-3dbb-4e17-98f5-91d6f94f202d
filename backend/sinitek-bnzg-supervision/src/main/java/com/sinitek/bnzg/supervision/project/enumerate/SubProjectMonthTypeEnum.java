package com.sinitek.bnzg.supervision.project.enumerate;

/**
 *
 * 每月类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public enum SubProjectMonthTypeEnum {

    LAST_MONTH(1, "${上月}"),
    THIS_MONTH(2, "${本月}"),
    NEXT_MONTH(3, "${下月}");

    private Integer code;

    private String name;

    SubProjectMonthTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SubProjectMonthTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }

}
