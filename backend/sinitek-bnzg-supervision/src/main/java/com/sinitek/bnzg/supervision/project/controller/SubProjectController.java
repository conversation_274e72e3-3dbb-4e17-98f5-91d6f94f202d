package com.sinitek.bnzg.supervision.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.supervision.project.dto.SupProjectDepIdsResultDTO;
import com.sinitek.bnzg.common.service.IBnzgOrgService;
import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.service.ISupProjectService;
import com.sinitek.bnzg.supervision.project.support.SubProjectResultFormat;
import com.sinitek.sirm.common.log.enumrate.BusinLogType;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.log.annotation.SirmLog;
import com.sinitek.sirm.org.entity.Employee;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.Arrays;
import java.util.List;
import static com.sinitek.bnzg.supervision.project.constant.SupProjectConstant.PROJECT_MANAGER_MODULE_NAME;
import static com.sinitek.bnzg.supervision.project.constant.SupProjectConstant.PROJECT_MANAGER_OPERATE_TYPE;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "督办系统 - 项目管理")
public class SubProjectController {


    public static final BusinLogType PROJECT_MANAGER = new BusinLogType(PROJECT_MANAGER_MODULE_NAME,
            PROJECT_MANAGER_OPERATE_TYPE , Arrays.asList("新增/编辑项目","新增/编辑/删除项目任务计划"), false, 0);

    private ISupProjectService subProjectService;

    private SubProjectResultFormat subProjectResultFormat;

    private IBnzgOrgService bnzgOrgService;

    @ApiOperation(value = "项目新增/编辑")
    @PostMapping(path = "/save-or-update")
    @SirmLog(moduleName = PROJECT_MANAGER_MODULE_NAME,
            operateType = PROJECT_MANAGER_OPERATE_TYPE,
            desc = "'{' +'${currentOrgName}' + '}' + (#dto.getId() == null ? '新增' : '修改了') + '项目[' + {#dto.getTitle()} + ']'" )
    public RequestResult<Long> saveOrUpdate(@RequestBody @Validated SupProjectSaveDTO dto) {
        Long id = subProjectService.saveOrUpdate(dto);
        return new RequestResult<>(id);
    }

    @ApiOperation("项目详情-项目信息")
    @GetMapping(path = "/get-by-id")
    public RequestResult<SupProjectDetailResultDTO> getById(Long id) {
        SupProjectDetailResultDTO dto = subProjectService.getById(id);
        return new RequestResult<>(dto);
    }

    @ApiOperation("项目详情-项目信息")
    @GetMapping(path = "/detail")
    public RequestResult<SupProjectDetailResultDTO> detail(Long id) {
        SupProjectDetailResultDTO dto = subProjectService.detail(id);
        return new RequestResult<>(dto);
    }

    @ApiOperation(value = "全部项目查询")
    @GetMapping(path = "/search")
    public TableResult<SupProjectSearchResultDTO> search(SupProjectSearchParamDTO dto) {
        IPage<SupProjectSearchResultDTO> result = subProjectService.search(dto);
        return dto.build(result,this.subProjectResultFormat);
    }

    @ApiOperation(value = "根据部门orgId查询部门负责人")
    @GetMapping(path = "/find-department-chief")
    public RequestResult<List<Employee>> findDepartmentChief(String unitId) {
        List<Employee> departmentChief = bnzgOrgService.findDepartmentChief(unitId);
        return new RequestResult<>(departmentChief);
    }

    @ApiOperation(value = "项目提交前检验")
    @PostMapping(path = "/check-submit")
    public RequestResult<List<SupProjectCheckResultDTO>> checkSubmit(@RequestBody @Validated SupProjectSubmitDTO dto) {
        List<SupProjectCheckResultDTO> result = subProjectService.checkSubmit(dto);
        return new RequestResult<>(result);
    }

    @ApiOperation(value = "项目提交")
    @PostMapping(path = "/submit")
    public RequestResult<Void> submit(@RequestBody @Validated SupProjectSubmitDTO dto) {
        subProjectService.submit(dto);
        return new RequestResult<>();
    }

    @ApiOperation(value = "查询当前登录人可选所属部门")
    @GetMapping("/load-sup-dep-ids")
    public RequestResult<SupProjectDepIdsResultDTO> loadSupDepIds() {
        return new RequestResult<>(this.subProjectService.loadSupDepIds());
    }

    @ApiOperation(value = "项目复制")
    @GetMapping(path = "/copy")
    public RequestResult<Void> copy(@RequestParam @ApiParam (value = "项目ID") Long id) {
        subProjectService.copy(id);
        return new RequestResult<>();
    }


}


