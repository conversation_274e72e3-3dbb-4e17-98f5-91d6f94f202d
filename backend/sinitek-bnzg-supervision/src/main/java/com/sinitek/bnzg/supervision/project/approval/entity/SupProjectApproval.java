package com.sinitek.bnzg.supervision.project.approval.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_approval")
@ApiModel(value = "项目审批-实体")
public class SupProjectApproval extends BaseEntity {

    public static final String ENTITY_NAME = "SUP_PROJECT_APPROVAL";
    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverId;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    /**
     * 状态。1： 已处理  0：待处理
     */
    @ApiModelProperty(value = "状态。1： 已处理  0：待处理")
    private Integer status;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    private String opinion;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    private String applicantId;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applicationTime;

    /**
     * 申请备注
     */
    @ApiModelProperty(value = "申请备注")
    private String remark;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 审批状态。1：通过 ，0：驳回
     */
    @ApiModelProperty(value = "审批状态。1：通过 ，0：驳回")
    private Integer approveStatus;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

}
