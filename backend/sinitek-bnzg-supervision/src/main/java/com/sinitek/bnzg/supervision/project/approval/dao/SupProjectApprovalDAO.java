package com.sinitek.bnzg.supervision.project.approval.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.supervision.project.approval.entity.SupProjectApproval;
import com.sinitek.bnzg.supervision.project.approval.mapper.SupProjectApprovalMapper;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalPO;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchPO;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchResultPO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Service
public class SupProjectApprovalDAO extends ServiceImpl<SupProjectApprovalMapper,SupProjectApproval> {

    public List<SupProjectApprovalPO> findSupProjectApprovalByProjectIds(List<Long> projectIds){
        if (CollectionUtils.isEmpty(projectIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SupProjectApproval> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SupProjectApproval::getProjectId,projectIds);
        List<SupProjectApproval> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(item -> {
            SupProjectApprovalPO supProjectApprovalPO = new SupProjectApprovalPO();
            BeanUtils.copyProperties(item, supProjectApprovalPO);
            return supProjectApprovalPO;
        }).collect(Collectors.toList());

    }

    public IPage<SupProjectApprovalSearchResultPO> search(Page<SupProjectApprovalSearchResultPO> page, SupProjectApprovalSearchPO param) {
        return this.baseMapper.search(page, param);
    }
}
