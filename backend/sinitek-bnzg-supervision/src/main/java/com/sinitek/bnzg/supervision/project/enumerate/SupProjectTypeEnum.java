package com.sinitek.bnzg.supervision.project.enumerate;

import java.util.Objects;
import lombok.Getter;

/**
 * 项目类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/6
 */
@Getter
public enum SupProjectTypeEnum {

    REGULATION_REPORT(1, "监管报送"),
    SPECIAL_WORK(2, "专项工作"),
    PERIODIC_INSPECTION(3, "定期检视"),
    RATING(4, "监管评级");

    private final Integer value;
    private final String name;

    SupProjectTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public boolean checkEquals(Integer typeValue) {
        return Objects.equals(typeValue, this.value);
    }

    public static SupProjectTypeEnum getByValue(Integer value) {
        for (SupProjectTypeEnum typeEnum : SupProjectTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

}
