package com.sinitek.bnzg.supervision.project.controller;

import com.sinitek.bnzg.supervision.project.dto.SupProjectFlowLogResultDTO;
import com.sinitek.bnzg.supervision.project.service.ISupProjectFlowLogService;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project/sup-project-flow-log")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "任务督办 - 项目流转记录")
public class SupProjectFlowLogController {

    private ISupProjectFlowLogService supProjectFlowLogService;

    @ApiOperation(value = "查询项目流转记录")
    @GetMapping("/list")
    public RequestResult<List<SupProjectFlowLogResultDTO>> listSupProjectFlowLog(Long projectId) {
        List<SupProjectFlowLogResultDTO> resultDTOList = supProjectFlowLogService.findSupProjectFlowLog(projectId);
        return new RequestResult<>(resultDTOList);
    }
}
