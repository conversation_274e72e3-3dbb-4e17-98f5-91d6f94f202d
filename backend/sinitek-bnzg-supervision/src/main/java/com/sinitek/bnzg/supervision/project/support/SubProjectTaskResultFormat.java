package com.sinitek.bnzg.supervision.project.support;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.project.constant.SupProjectConstant;
import com.sinitek.bnzg.supervision.project.dao.*;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTagResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskResultDTO;
import com.sinitek.bnzg.supervision.project.entity.*;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectDayTypeEnum;
import com.sinitek.bnzg.supervision.project.service.ISupProjectTaskService;
import com.sinitek.bnzg.supervision.project.util.SupProjectUtil;
import com.sinitek.bnzg.supervision.tag.dto.SupTagDTO;
import com.sinitek.bnzg.supervision.tag.service.ISupTagService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.common.utils.TimeUtil;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormatBase;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.remind.dto.RemindRepeatRuleDTO;
import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import com.sinitek.sirm.remind.service.ISirmRemindService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import static com.sinitek.bnzg.supervision.common.enumation.TaskParticipantEnum.*;
import static com.sinitek.bnzg.supervision.project.enumerate.SubProjectRepeatTypeEnum.NOT_REPEAT;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Component
public class SubProjectTaskResultFormat implements ITableResultFormatBase<SupProjectTaskResultDTO,SupProjectTaskDTO> {

    @Autowired
    private ISirmRemindService remindService;

    @Autowired
    private SupProjectTaskParticipantDAO projectTaskParticipantDAO;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private SupProjectTaskTagRelaDAO projectTaskTagRelaDAO;

    @Autowired
    private ISupTagService tagService;

    @Autowired
    private ISupProjectTaskService projectTaskService;

    @Autowired
    private SupProjectTaskReportSubjectDAO projectTaskReportSubjectDAO;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private SupProjectDAO projectDAO;

    @Autowired
    private SupProjectTaskLinkDAO projectTaskLinkDAO;

    @Override
    public List<SupProjectTaskResultDTO> format(List<SupProjectTaskDTO> data) {
        if (CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        Long projectId = data.get(0).getProjectId();
        SupProject project = projectDAO.getById(projectId);
        List<SupProjectTask> projectTasks = projectTaskService.findByProjectId(projectId);
        List<Long> projectTaskIds = projectTasks.stream().map(SupProjectTask::getId).collect(Collectors.toList());
        List<SupProjectTaskParticipant> projectTaskParticipants = projectTaskParticipantDAO.list(new QueryWrapper<SupProjectTaskParticipant>()
                .lambda().in(SupProjectTaskParticipant::getProjectTaskId, projectTaskIds));
        Map<Long, List<SupProjectTaskParticipant>> participantMapByProjectTaskId = projectTaskParticipants.stream()
                .collect(Collectors.groupingBy(SupProjectTaskParticipant::getProjectTaskId));
        // 提取部门ID和员工ID列表，并去重
        List<String> allOrgIds = projectTaskParticipants.stream()
                .flatMap(participant -> Stream.of(participant.getDepId(), participant.getEmpId()))
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> orgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(allOrgIds);
        //任务标签
        List<SupProjectTaskTagRela> projectTaskTagRelaList = projectTaskTagRelaDAO.list(new QueryWrapper<SupProjectTaskTagRela>().lambda().in(SupProjectTaskTagRela::getProjectTaskId, projectTaskIds));
        Map<Long, List<SupProjectTaskTagRela>> projectTagRelaMap = projectTaskTagRelaList.stream().collect(Collectors.groupingBy(SupProjectTaskTagRela::getProjectTaskId));
        List<Long> tagIds = projectTaskTagRelaList.stream().map(SupProjectTaskTagRela::getTagId).collect(Collectors.toList());
        List<SupTagDTO> supTagDTOList = tagService.listByIds(tagIds);
        Map<Long, SupTagDTO> tagMap = supTagDTOList.stream().collect(Collectors.toMap(SupTagDTO::getId, tag -> tag));
        //上报主体
        List<SupProjectTaskReportSubject> projectTaskReportSubjectList = projectTaskReportSubjectDAO.list(new QueryWrapper<SupProjectTaskReportSubject>().lambda().in(SupProjectTaskReportSubject::getProjectTaskId, projectTaskIds));
        Map<Long, List<SupProjectTaskReportSubject>> projectTaskReportSubjectMap = projectTaskReportSubjectList.stream().collect(Collectors.groupingBy(SupProjectTaskReportSubject::getProjectTaskId));
        Map<String, String> reportSubjectMap = this.enumService.getSirmEnumByCataLogAndType(
                SupConstant.DEFAULT_CATALOG, SupConstant.SUP_REPORT_SUBJECT);
        //前置任务
        List<SupProjectTaskLink> projectTaskLinkList = projectTaskLinkDAO.list(new QueryWrapper<SupProjectTaskLink>().lambda().in(SupProjectTaskLink::getProjectTaskId, projectTaskIds));
        Map<Long, List<SupProjectTaskLink>> projectTaskLinkMap = projectTaskLinkList.stream().collect(Collectors.groupingBy(SupProjectTaskLink::getProjectTaskId));
        return data.stream()
                .map(task -> mapToResultDTO(task, participantMapByProjectTaskId, projectTagRelaMap, tagMap,
                        orgIdAndNameMap,projectTaskReportSubjectMap,reportSubjectMap,project,projectTaskLinkMap,projectTasks))
                .collect(Collectors.toList());
    }


    private SupProjectTaskResultDTO mapToResultDTO(SupProjectTaskDTO projectTaskDTO,
                                                   Map<Long, List<SupProjectTaskParticipant>> participantMapByProjectTaskId,
                                                   Map<Long, List<SupProjectTaskTagRela>> projectTagRelaMap,
                                                   Map<Long, SupTagDTO> tagMap,
                                                   Map<String, String> orgIdAndNameMap,
                                                   Map<Long, List<SupProjectTaskReportSubject>> projectTaskReportSubjectMap,
                                                   Map<String, String> reportSubjectMap,
                                                   SupProject project, Map<Long, List<SupProjectTaskLink>> projectTaskLinkMap,
                                                   List<SupProjectTask> projectTasks) {
        SupProjectTaskResultDTO supProjectTaskResultDTO = new SupProjectTaskResultDTO();
        BeanUtils.copyProperties(projectTaskDTO, supProjectTaskResultDTO);
        // 设置任务重复频率
        supProjectTaskResultDTO.setRepeatType(project.getRepeatType());
        // 设置开始日期、结束日期
        setupDateInfo(supProjectTaskResultDTO);
        // 设置上报主体
        setupReportSubject(supProjectTaskResultDTO,projectTaskReportSubjectMap,reportSubjectMap);
        // 设置参与人参与部门
        setupParticipants(supProjectTaskResultDTO, participantMapByProjectTaskId, orgIdAndNameMap);
        // 设置标签
        setupTags(supProjectTaskResultDTO, projectTagRelaMap, tagMap);
        // 设置前置任务
        setupPreTask(supProjectTaskResultDTO, projectTaskLinkMap,projectTasks);
        // 设置子集任务
        setupChildren(projectTaskDTO.getChildren(), supProjectTaskResultDTO, participantMapByProjectTaskId,
                projectTagRelaMap, tagMap, orgIdAndNameMap,projectTaskReportSubjectMap,reportSubjectMap
                , project,projectTaskLinkMap,projectTasks);

        return supProjectTaskResultDTO;
    }

    private void setupPreTask(SupProjectTaskResultDTO supProjectTaskResultDTO, Map<Long, List<SupProjectTaskLink>> projectTaskLinkMap, List<SupProjectTask> projectTasks) {
        List<SupProjectTaskLink> projectTaskLinks = projectTaskLinkMap.get(supProjectTaskResultDTO.getId());
        if (CollectionUtils.isNotEmpty(projectTaskLinks)) {
            List<Long> preLinkTaskIds = projectTaskLinks
                    .stream()
                    .map(SupProjectTaskLink::getLinkPreProjectTaskId)
                    .collect(Collectors.toList());
            List<SupProjectTask> preTasks = projectTasks
                    .stream()
                    .filter(projectTask -> preLinkTaskIds.contains(projectTask.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(preTasks)) {
                supProjectTaskResultDTO.setPreTaskName(preTasks.stream().map(SupProjectTask::getName).collect(Collectors.joining(",")));
            }
        }
    }

    private void setupReportSubject(SupProjectTaskResultDTO supProjectTaskResultDTO, Map<Long, List<SupProjectTaskReportSubject>> projectTaskReportSubjectMap, Map<String, String> reportSubjectMap) {
        List<SupProjectTaskReportSubject> supProjectTaskReportSubjects = projectTaskReportSubjectMap.get(supProjectTaskResultDTO.getId());
        if (CollectionUtils.isNotEmpty(supProjectTaskReportSubjects)) {
            String reportSubjectNames = supProjectTaskReportSubjects.stream().map(SupProjectTaskReportSubject::getSubjectVal).map(reportSubjectMap::get).collect(Collectors.joining(","));
            supProjectTaskResultDTO.setReportSubjectNames(reportSubjectNames);
        }
    }

    private void setupDateInfo(SupProjectTaskResultDTO supProjectTaskResultDTO) {
        if (NOT_REPEAT.getValue().equals(supProjectTaskResultDTO.getRepeatType())) {
            supProjectTaskResultDTO.setStartDateDesc(TimeUtil.formatDate(supProjectTaskResultDTO.getStartDate(), "yyyy年MM月dd日"));
            supProjectTaskResultDTO.setEndDateDesc(TimeUtil.formatDate(supProjectTaskResultDTO.getEndDate(), "yyyy年MM月dd日"));
        } else {
            Integer repeatType = supProjectTaskResultDTO.getRepeatType();
            Integer endDayType = supProjectTaskResultDTO.getEndDayType();
            Integer taskDurationDays = supProjectTaskResultDTO.getTaskDurationDays();
            if (Objects.nonNull(repeatType) && !Objects.equals(NOT_REPEAT.getValue(), repeatType)) {
                List<RemindRepeatRuleDTO> remindRepeatRuleDTOS = remindService.findRepeatRulesBySourceIdAndName(supProjectTaskResultDTO.getId(), SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
                if (CollectionUtils.isNotEmpty(remindRepeatRuleDTOS)) {
                    RemindRepeatRuleDTO remindRepeatRuleDTO = remindRepeatRuleDTOS.get(0);
                    RepeatTimeDTO repeatTimeDTO = remindService.analysisCronExpression(remindRepeatRuleDTO);
                    supProjectTaskResultDTO.setStartDateDesc(SupProjectUtil.buildStartDateDesc(repeatTimeDTO, repeatType));
                    supProjectTaskResultDTO.setEndDateDesc("开始日期后" + taskDurationDays + "个" + (Objects.nonNull(SupProjectDayTypeEnum.getByCode(endDayType).getDesc()) ? SupProjectDayTypeEnum.getByCode(endDayType).getDesc() : ""));
                }
            }
        }
    }

    private void setupParticipants( SupProjectTaskResultDTO supProjectTaskResultDTO,
                                          Map<Long, List<SupProjectTaskParticipant>> participantMapByProjectTaskId,
                                          Map<String, String> orgIdAndNameMap) {
        if (CollectionUtils.isNotEmpty(participantMapByProjectTaskId.get(supProjectTaskResultDTO.getId()))) {
            List<SupProjectTaskParticipant> projectTaskParticipantList = participantMapByProjectTaskId.get(supProjectTaskResultDTO.getId());
            Map<Integer, List<SupProjectTaskParticipant>> participantMapByType = projectTaskParticipantList.stream().collect(Collectors.groupingBy(SupProjectTaskParticipant::getType));

            setupLeadParticipant(supProjectTaskResultDTO, participantMapByType, orgIdAndNameMap);
            setupOrganizingParticipant(supProjectTaskResultDTO, participantMapByType, orgIdAndNameMap);
            setupSuperviseParticipant(supProjectTaskResultDTO, participantMapByType, orgIdAndNameMap);
            setupMonitoringParticipant(supProjectTaskResultDTO, participantMapByType, orgIdAndNameMap);
        }
    }

    private  void setupLeadParticipant(SupProjectTaskResultDTO supProjectTaskResultDTO,
                                             Map<Integer, List<SupProjectTaskParticipant>> participantMapByType,
                                             Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> leadPointList = participantMapByType.get(MAIN_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(leadPointList)) {
            SupProjectTaskParticipant leadParticipant = leadPointList.get(0);
            supProjectTaskResultDTO.setLeadEmpId(leadParticipant.getEmpId());
            supProjectTaskResultDTO.setLeadEmpName(orgIdAndNameMap.get(leadParticipant.getEmpId()));
            supProjectTaskResultDTO.setLeadDeptId(leadParticipant.getDepId());
            supProjectTaskResultDTO.setLeadDeptName(orgIdAndNameMap.get(leadParticipant.getDepId()));
        }
    }

    private void setupOrganizingParticipant(SupProjectTaskResultDTO supProjectTaskResultDTO,
                                                   Map<Integer, List<SupProjectTaskParticipant>> participantMapByType,
                                                   Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> organizingPointList = participantMapByType.get(ASSISTING_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(organizingPointList)) {
            supProjectTaskResultDTO.setOrganizingDeptIds(organizingPointList.stream().map(SupProjectTaskParticipant::getDepId).collect(Collectors.toList()));
            supProjectTaskResultDTO.setOrganizingDeptNames(organizingPointList.stream().map(point -> orgIdAndNameMap.get(point.getDepId())).collect(Collectors.joining(",")));
            supProjectTaskResultDTO.setOrganizingEmpIds(organizingPointList.stream().map(SupProjectTaskParticipant::getEmpId).collect(Collectors.toList()));
            supProjectTaskResultDTO.setOrganizingEmpNames(organizingPointList.stream().map(point -> orgIdAndNameMap.get(point.getEmpId())).collect(Collectors.joining(",")));
        }
    }

    private void setupSuperviseParticipant(SupProjectTaskResultDTO supProjectTaskResultDTO,
                                                  Map<Integer, List<SupProjectTaskParticipant>> participantMapByType,
                                                  Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> supervisePointList = participantMapByType.get(SUPERVISE_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(supervisePointList)) {
            supProjectTaskResultDTO.setSuperviseDeptIds(supervisePointList.stream().map(SupProjectTaskParticipant::getDepId).collect(Collectors.toList()));
            supProjectTaskResultDTO.setSuperviseDeptNames(supervisePointList.stream().map(point -> orgIdAndNameMap.get(point.getDepId())).collect(Collectors.joining(",")));
            supProjectTaskResultDTO.setSuperviseEmpIds(supervisePointList.stream().map(SupProjectTaskParticipant::getEmpId).collect(Collectors.toList()));
            supProjectTaskResultDTO.setSuperviseEmpNames(supervisePointList.stream().map(point -> orgIdAndNameMap.get(point.getEmpId())).collect(Collectors.joining(",")));
        }
    }

    private void setupMonitoringParticipant(SupProjectTaskResultDTO supProjectTaskResultDTO,
                                                   Map<Integer, List<SupProjectTaskParticipant>> participantMapByType,
                                                   Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> monitoringPointList = participantMapByType.get(WATCHING_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(monitoringPointList)) {
            supProjectTaskResultDTO.setMonitoringDeptIds(monitoringPointList.stream().map(SupProjectTaskParticipant::getDepId).collect(Collectors.toList()));
            supProjectTaskResultDTO.setMonitoringDeptNames(monitoringPointList.stream().map(point -> orgIdAndNameMap.get(point.getDepId())).collect(Collectors.joining(",")));
            supProjectTaskResultDTO.setMonitoringEmpIds(monitoringPointList.stream().map(SupProjectTaskParticipant::getEmpId).collect(Collectors.toList()));
            supProjectTaskResultDTO.setMonitoringEmpNames(monitoringPointList.stream().map(point -> orgIdAndNameMap.get(point.getEmpId())).collect(Collectors.joining(",")));
        }
    }

    private void setupTags(SupProjectTaskResultDTO supProjectTaskResultDTO,
                                  Map<Long, List<SupProjectTaskTagRela>> projectTagRelaMap,
                                  Map<Long, SupTagDTO> tagMap) {
        List<SupProjectTaskTagRela> supProjectTaskTagRelas = projectTagRelaMap.get(supProjectTaskResultDTO.getId());
        if (CollectionUtils.isNotEmpty(supProjectTaskTagRelas)) {
            supProjectTaskResultDTO.setTags(supProjectTaskTagRelas.stream().map(supProjectTaskTagRela -> {
                SupProjectTagResultDTO supProjectTagResultDTO = new SupProjectTagResultDTO();
                supProjectTagResultDTO.setTagId(supProjectTaskTagRela.getTagId());
                supProjectTagResultDTO.setTagName(tagMap.get(supProjectTaskTagRela.getTagId()).getName());
                return supProjectTagResultDTO;
            }).collect(Collectors.toList()));
        }
    }

    private void setupChildren(List<LcBaseModel> children, SupProjectTaskResultDTO supProjectTaskResultDTO,
                               Map<Long, List<SupProjectTaskParticipant>> participantMapByProjectTaskId,
                               Map<Long, List<SupProjectTaskTagRela>> projectTagRelaMap,
                               Map<Long, SupTagDTO> tagMap,
                               Map<String, String> orgIdAndNameMap,
                               Map<Long, List<SupProjectTaskReportSubject>> projectTaskReportSubjectMap,
                               Map<String, String> reportSubjectMap,
                               SupProject project,Map<Long, List<SupProjectTaskLink>> projectTaskLinkMap,
                               List<SupProjectTask> projectTasks) {
        if (CollectionUtils.isNotEmpty(children)) {
            List<SupProjectTaskResultDTO> childList =  children.stream().map(child -> {
                String json = JsonUtil.toJsonString(child);
                SupProjectTaskResultDTO childResultDTO = JsonUtil.jsonCopy(json, SupProjectTaskResultDTO.class);
                childResultDTO.setRepeatType(project.getRepeatType());
                setupDateInfo(childResultDTO);
                setupReportSubject(childResultDTO, projectTaskReportSubjectMap, reportSubjectMap);
                setupPreTask(childResultDTO, projectTaskLinkMap, projectTasks);
                setupParticipants(childResultDTO, participantMapByProjectTaskId, orgIdAndNameMap);
                setupTags(childResultDTO, projectTagRelaMap, tagMap);
                setupChildren((List<LcBaseModel>)child.get("children"), childResultDTO, participantMapByProjectTaskId, projectTagRelaMap, tagMap,
                        orgIdAndNameMap, projectTaskReportSubjectMap, reportSubjectMap, project,projectTaskLinkMap,projectTasks);
                return childResultDTO;
            }).collect(Collectors.toList());
            supProjectTaskResultDTO.setChildren(childList);
        }else {
            supProjectTaskResultDTO.setChildren(new ArrayList<>());
        }
    }

}
