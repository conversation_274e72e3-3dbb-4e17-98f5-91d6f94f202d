package com.sinitek.bnzg.supervision.project.remind.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 消息提醒收件人 Entity
 *
 * <AUTHOR>
 * date 2025-02-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_message_remind_receiver")
@ApiModel(value = "消息提醒收件人-实体")
public class SupMessageRemindReceiver extends BaseEntity {

    /**
     * 消息ID
     */
    @ApiModelProperty(value = "消息ID")
    private Long messageId;

    /**
     * 收件人ID
     */
    @ApiModelProperty(value = "收件人ID")
    private String receiverId;

}

