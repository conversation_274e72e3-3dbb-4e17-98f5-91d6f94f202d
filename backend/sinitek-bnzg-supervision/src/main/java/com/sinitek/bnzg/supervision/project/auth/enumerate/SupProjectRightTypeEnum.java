package com.sinitek.bnzg.supervision.project.auth.enumerate;

import com.alibaba.druid.util.StringUtils;
import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
public enum SupProjectRightTypeEnum {

    VIEW(SupProjectAuthConstant.RIGHT_TYPE_VIEW, "查询"),
    EDIT(SupProjectAuthConstant.RIGHT_TYPE_EDIT, "编辑");

    private final String value;

    private final String label;
    SupProjectRightTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabelByValue(String value) {
        for (SupProjectRightTypeEnum typeEnum : SupProjectRightTypeEnum.values()) {
            if (StringUtils.equals(typeEnum.getValue(), value)) {
                return typeEnum.getLabel();
            }
        }
        return null;
    }
}
