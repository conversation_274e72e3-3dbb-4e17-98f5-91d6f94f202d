package com.sinitek.bnzg.supervision.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * 项目任务参与人 - 基础DTO
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ApiModel(value = "项目任务参与人 - 基础DTO")
public class SupProjectTaskParticipantSaveDTO {


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String deptId;

    /**
     * 员工id集合
     */
    @ApiModelProperty(value = "员工id")
    private List<String> empIds;

}
