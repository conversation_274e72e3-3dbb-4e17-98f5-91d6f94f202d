package com.sinitek.bnzg.supervision.project.remind.controller;

import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindResultDTO;
import com.sinitek.bnzg.supervision.project.remind.service.ISupMessageRemindService;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project/remind/sup-message-remind")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "任务督办 - 消息提醒")
public class SupMessageRemindController {

    private ISupMessageRemindService supMessageRemindService;

    @ApiOperation(value = "查询处理结果")
    @GetMapping("/get-deal-result")
    public RequestResult<MessageRemindResultDTO> getDealResult(Long id, Long participantId) {
        MessageRemindResultDTO messageRemindResultDTO = supMessageRemindService.getDealResultById(id, participantId);
        return new RequestResult<>(messageRemindResultDTO);
    }
}
