package com.sinitek.bnzg.supervision.project.service;

import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkDetailResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkSaveDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkSearchResultDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
public interface ISupProjectTaskLinkService {

    List<SupProjectTaskLinkSearchResultDTO> findPreTask(Long taskId);

    SupProjectTaskLinkDetailResultDTO detail(Long taskId);

    void save(SupProjectTaskLinkSaveDTO dto);
}
