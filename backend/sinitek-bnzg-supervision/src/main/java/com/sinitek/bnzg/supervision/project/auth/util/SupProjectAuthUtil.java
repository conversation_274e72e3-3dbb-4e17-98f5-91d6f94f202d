package com.sinitek.bnzg.supervision.project.auth.util;

import com.sinitek.bnzg.supervision.common.util.SupUtil;
import com.sinitek.bnzg.supervision.project.auth.aspect.SupProjectAuthFilterAspect;
import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;
import com.sinitek.bnzg.supervision.project.auth.properties.SupProjectAuthProperties;
import com.sinitek.bnzg.supervision.project.auth.service.ISupProjectAuthService;
import com.sinitek.bnzg.supervision.project.auth.support.SupProjectAuthContext;
import com.sinitek.bnzg.supervision.project.dao.SupProjectDAO;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.org.entity.Employee;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
@Slf4j
public class SupProjectAuthUtil {

    public static Set<Long> findAuthedProjectIds(String authType){

        boolean isIgnoreAuth = SupProjectAuthUtil.ignoreAuthFilter(authType);
        if (isIgnoreAuth) {
            SupProjectDAO supProjectDAO = SpringFactory.getBean(SupProjectDAO.class);
            List<SupProject> supProjects = supProjectDAO.list();
            if (CollectionUtils.isNotEmpty(supProjects)){
                Set<Long> ids = supProjects.stream().map(SupProject::getId).collect(Collectors.toSet());
                log.info("有权限的项目ids: {}", ids);
                return ids;
            } else {
                return new HashSet<>();
            }
        }
        ISupProjectAuthService supProjectAuthService = SpringFactory.getBean(ISupProjectAuthService.class);
        Set<Long> projectsAuth = supProjectAuthService.findProjectsAuth(authType);
        log.info("有权限的项目ids: {}", projectsAuth);
        return projectsAuth;
    }


    /**
     * 从上下文获取所有单授权的项目
     * @return   有权限的项目
     */
    public static Set<Long> findAuthedSupProjectIdsByContext() {
        SupProjectAuthContext context = SpringFactory.getBean(SupProjectAuthFilterAspect.class).getContext();
        if (Objects.isNull(context)){
            log.warn("当前项目权限上下文为空，请检查方法是否使用注解SupProjectAuthFilter\n {}", (Object) Thread.currentThread().getStackTrace());
            return new HashSet<>();
        }
        return context.getProjectIds();
    }


    public static boolean ignoreAuthFilter(String authType) {

        if (SupProjectAuthConstant.RIGHT_TYPE_IGNORE.equals(authType)) {
            return true;
        }

        // 当前用户不存在，跳过
        String orgId = CurrentUserFactory.getOrgId();
        boolean userExist = StringUtils.isNotBlank(orgId);
        if (!userExist){
            return true;
        }
        // 是管理员，跳过
        boolean isAdmin = Optional.ofNullable(CurrentUserFactory.isAdmin()).orElse(false);
        if (isAdmin) {
            return true;
        }
        // 产品权限开关没打开，跳过
        boolean enable = SpringFactory.getBean(SupProjectAuthProperties.class).isEnable();

        if (!enable) {
            return true;
        }

        //判断该用户是否属于合规部
        List<Employee> complianceEmployees = SupUtil.findComplianceEmployees();
        List<String> empIds = complianceEmployees.stream().map(Employee::getId).collect(Collectors.toList());
        boolean isComplianceEmployee = empIds.contains(orgId);
        if (isComplianceEmployee) {
            return true;
        }
        return false;
    }
}
