package com.sinitek.bnzg.supervision.project.dto;

import lombok.Data;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目消息提醒 - 基础DTO
 *
 * <AUTHOR>
 * date 2025-01-22
 */
@Data
@ApiModel(value = "项目消息提醒 - 基础DTO")
public class SupProjectRemindMessageBaseDTO {

    @ApiModelProperty(value = "项目id,关联sup_project.id", required = true)
    private Long projectId;

    @ApiModelProperty(value = "任务id,关联sup_task.id", required = true)
    private Long taskId;

    @ApiModelProperty(value = "类型 0-待发送 1-已发送")
    private Integer status;

    @ApiModelProperty(value = "提醒日期")
    private Date remindDate;

}