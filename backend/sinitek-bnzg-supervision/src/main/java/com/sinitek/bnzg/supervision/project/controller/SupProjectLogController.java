package com.sinitek.bnzg.supervision.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.supervision.project.dto.SupProjectLogResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectLogSearchDTO;
import com.sinitek.bnzg.supervision.project.service.ISupProjectLogService;
import com.sinitek.bnzg.supervision.project.util.SupProjectLogUtil;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project/sup-project-log")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "任务督办 - 项目操作日志")
public class SupProjectLogController {

    private ISupProjectLogService supProjectLogService;

    @ApiOperation(value = "查询项目操作日志分页列表")
    @GetMapping("/search")
    public TableResult<SupProjectLogResultDTO> listSupProjectLog(SupProjectLogSearchDTO dto) {
        IPage<SupProjectLogResultDTO> resultPage = supProjectLogService.searchSupProjectLog(dto);
        return dto.build(resultPage, SupProjectLogUtil.getSupProjectLogResult());
    }
}
