package com.sinitek.bnzg.supervision.project.enumerate;

/**
 *
 * 每周类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public enum SubProjectYearTypeEnum {

    LAST_YEAR(1, "${去一年}"),
    THIS_YEAR(2, "${本年}"),
    NEXT_YEAR(3, "${下一年}");

    private Integer code;

    private String name;

    SubProjectYearTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SubProjectYearTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }

}
