package com.sinitek.bnzg.supervision.project.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目任务参与人 Entity
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_task_participant")
@ApiModel(value = "项目任务参与人-实体")
public class SupProjectTaskParticipant extends BaseEntity {

    /**
     * 项目任务id
     */
    @ApiModelProperty(value = "项目任务id")
    private Long projectTaskId;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private Integer type;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String depId;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id")
    private String empId;

}
