package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_log_detail")
@ApiModel(value = "项目操作日志详情-实体")
public class SupProjectLogDetail extends BaseEntity {

    /**
     * 项目操作日志id
     */
    @ApiModelProperty(value = "项目操作日志id")
    private Long logId;

    /**
     * 业务字段标识
     */
    @ApiModelProperty(value = "业务字段标识")
    private String bizFieldCode;

    /**
     * 业务字段名称
     */
    @ApiModelProperty(value = "业务字段名称")
    private String bizFieldName;

    /**
     * 值对应类型
     */
    @ApiModelProperty(value = "值对应类型")
    private String valueClazz;

    /**
     * 旧值
     */
    @ApiModelProperty(value = "旧值")
    private String oldValue;

    /**
     * 旧值格式化
     */
    @ApiModelProperty(value = "旧值格式化")
    private String oldFormatValue;

    /**
     * 新值
     */
    @ApiModelProperty(value = "新值")
    private String newValue;

    /**
     * 新值格式化
     */
    @ApiModelProperty(value = "新值格式化")
    private String newFormatValue;

}
