package com.sinitek.bnzg.supervision.project.approval.enumerate;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
public enum SupProjectApprovalTypeEnum {
    APPROVAL(1, "立项"),
    ALTERATION(2, "变更"),
    TERMINATION(3, "终止");

    private Integer code;

    private String name;

    SupProjectApprovalTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SupProjectApprovalTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }
}
