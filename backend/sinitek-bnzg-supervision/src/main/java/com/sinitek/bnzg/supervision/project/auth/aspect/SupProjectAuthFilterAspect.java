package com.sinitek.bnzg.supervision.project.auth.aspect;

import com.sinitek.bnzg.supervision.project.auth.annotation.SupProjectAuthFilter;
import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;
import com.sinitek.bnzg.supervision.project.auth.enumerate.SupProjectRightTypeEnum;
import com.sinitek.bnzg.supervision.project.auth.message.SupProjectAuthMessage;
import com.sinitek.bnzg.supervision.project.auth.service.ISupProjectAuthService;
import com.sinitek.bnzg.supervision.project.auth.support.JstlParser;
import com.sinitek.bnzg.supervision.project.auth.support.SupProjectAuthContext;
import com.sinitek.bnzg.supervision.project.auth.util.SupProjectAuthUtil;
import com.sinitek.bnzg.supervision.project.dao.SupProjectDAO;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.sirm.common.constant.BaseConstant;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
@Slf4j
@Aspect
@Component
public class SupProjectAuthFilterAspect extends AbstractSupProjectAuthAspect{

    @Autowired
    private ISupProjectAuthService supProjectAuthService;

    @Autowired
    private SupProjectDAO supProjectDAO;

    @Around("@annotation(supProjectAuthFilter)")
    public Object supProjectAuthFilter(ProceedingJoinPoint joinPoint, SupProjectAuthFilter supProjectAuthFilter) throws Throwable {
        String rightType = supProjectAuthFilter.rightType();
        SupProjectAuthContext context = this.getContext();
        if (Objects.isNull(context) || !SupProjectAuthConstant.RIGHT_TYPE_IGNORE.equals(context.getRightType())) {
            this.setRightType(rightType);
        }
        if (SupProjectAuthUtil.ignoreAuthFilter(rightType)) {
            return joinPoint.proceed();
        }
        //获取有权限的项目ids
        Set<Long> projectIds = supProjectAuthService.findProjectsAuth(rightType);
        context = this.getContext();
        context.setProjectIds(projectIds);
        this.rightTypeContext.set(context);
        String projectIdStr = supProjectAuthFilter.projectId();
        if (StringUtils.isBlank(projectIdStr)) {
            return joinPoint.proceed();
        }
        try {
            String name = CurrentUserFactory.getOrgName();
            projectIdStr = JstlParser.generateKeyBySpEl(projectIdStr, joinPoint);
            Long projectId = -1L;
            Set<Long> paramProjectIds = new HashSet<>();
            if (StringUtils.isNotBlank(projectIdStr) && projectIdStr.contains(BaseConstant.COMMA)) {
                paramProjectIds = Arrays.stream(projectIdStr.split(BaseConstant.COMMA))
                        .filter(Objects::nonNull)
                        .map(Long::parseLong)
                        .collect(Collectors.toSet());
            } else {
                projectId = NumberTool.safeToLong(projectIdStr, -1L);
            }
            String methodName = joinPoint.getSignature().toString();
            //没有权限的项目id
            Set<Long> notContainedElements = new HashSet<>();
            if (CollectionUtils.isEmpty(paramProjectIds) && projectId == -1L) {
                log.warn("用于权限校验的项目id为空，请确认输入参数是否正常，校验点{}", methodName);
                throw new BussinessException(SupProjectAuthMessage.INPUT_PROJECT_WARN);
            } else {
                if (projectIds.contains(projectId)) {
                    return joinPoint.proceed();
                }
                if (CollectionUtils.isNotEmpty(paramProjectIds)) {
                    notContainedElements = paramProjectIds.stream()
                            .filter(element -> !projectIds.contains(element))
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(notContainedElements)) {
                        return joinPoint.proceed();
                    }
                }

                String projectName = "";
                if (projectId != -1) {
                    SupProject supProject = supProjectDAO.getById(projectId);
                    projectName = Objects.nonNull(supProject) ? supProject.getTitle() : "";
                } else if (CollectionUtils.isNotEmpty(notContainedElements)) {
                    List<SupProject> supProjects = supProjectDAO.listByIds(notContainedElements);
                    if (CollectionUtils.isNotEmpty(supProjects)) {
                        projectName = supProjects.stream().map(SupProject::getTitle).collect(Collectors.joining(GlobalConstant.CHINESE_COMMA));
                    }
                }
                throw new BussinessException(SupProjectAuthMessage.NO_PROJECT_AUTH, name, projectName, SupProjectRightTypeEnum.getLabelByValue(rightType));
            }

        } finally {
            this.removeContext();
        }
    }

}
