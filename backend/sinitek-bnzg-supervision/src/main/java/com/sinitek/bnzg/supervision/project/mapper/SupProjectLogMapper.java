package com.sinitek.bnzg.supervision.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.supervision.project.entity.SupProjectLog;
import com.sinitek.bnzg.supervision.project.po.SupProjectLogResultPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectLogSearchPO;
import com.sinitek.bnzg.supervision.task.approve.po.TaskApprovalSearchResultPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
public interface SupProjectLogMapper extends BaseMapper<SupProjectLog> {


    IPage<SupProjectLogResultPO> searchSupProjectLog(Page<TaskApprovalSearchResultPO> page, @Param("param")SupProjectLogSearchPO param);
}
