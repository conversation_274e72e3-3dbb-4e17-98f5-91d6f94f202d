package com.sinitek.bnzg.supervision.project.approval.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Data
@ApiModel(value = "项目审批-DTO")
public class SupProjectApprovalDetailDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    @ApiModelProperty(value = "操作类型名称")
    private String operateTypeName;

    @ApiModelProperty(value = "审批意见")
    private String opinion;

    @ApiModelProperty(value = "申请人id")
    private String applicantId;

    @ApiModelProperty(value = "申请人名称")
    private String applicantName;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    private Date applicationTime;

    @ApiModelProperty(value = "申请备注")
    private String remark;

    @ApiModelProperty(value = "项目id")
    private Long projectId;
}
