package com.sinitek.bnzg.supervision.project.approval.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
@ApiModel(value = "项目审批-保存DTO")
public class SupProjectApprovalSaveDTO {

    @ApiModelProperty(value = "申请备注")
    private String remark;

    @ApiModelProperty(value = "项目Id")
    private Long projectId;

    @ApiModelProperty(value = "申请人id")
    private String applicantId;

    @ApiModelProperty(value = "审批人id")
    private String approverId;

    @ApiModelProperty(value = "操作类型")
    private Integer operateType;
}
