package com.sinitek.bnzg.supervision.project.approval.support;

import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalDetailDTO;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import org.apache.commons.collections.MapUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/7
 */
public class SupProjectApprovalResultFormat {

    public static void format(SupProjectApprovalDetailDTO dto) {
        IEnumService enumService = SpringFactory.getBean(IEnumService.class);

        // 审批类型
        Map<String, String> projectApprovalTypeMap = enumService.getSirmEnumByCataLogAndType(
                SupConstant.DEFAULT_CATALOG, SupConstant.SUP_PROJECT_APPROVAL_TYPE);
        dto.setOperateTypeName(MapUtils.getString(projectApprovalTypeMap,String.valueOf(dto.getOperateType()), ""));
        // 项目审批人和操作人
        List<String> list = Arrays.asList(dto.getApplicantId());
        IOrgService orgService = SpringFactory.getBean(IOrgService.class);
        Map<String, String> orgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(list);
        dto.setApplicantName(MapUtils.getString(orgIdAndNameMap,dto.getApplicantId(), ""));
    }
}
