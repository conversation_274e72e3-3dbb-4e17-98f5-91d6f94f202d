package com.sinitek.bnzg.supervision.project.remind.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParamDTO;
import com.sinitek.bnzg.supervision.common.util.SupHolidayUtil;
import com.sinitek.bnzg.supervision.project.dao.SupProjectRemindConfigDAO;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.bnzg.supervision.project.enumerate.SubProjectHolidayStrategyEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectDateRemindTypeEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectDayTypeEnum;
import com.sinitek.bnzg.supervision.project.po.SupProjectRemindConfigPO;
import com.sinitek.bnzg.supervision.project.remind.constant.SupMessageRemindConstant;
import com.sinitek.bnzg.supervision.project.remind.dao.SupMessageRemindDAO;
import com.sinitek.bnzg.supervision.project.remind.entity.SupMessageRemind;
import com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindStatusEnum;
import com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindTypeEnum;
import com.sinitek.bnzg.supervision.project.service.ISupProjectService;
import com.sinitek.bnzg.supervision.task.dto.TaskBaseDTO;
import com.sinitek.bnzg.supervision.task.log.status.event.SupTaskProgressChangeEvent;
import com.sinitek.bnzg.supervision.task.service.ITaskService;
import com.sinitek.sirm.common.event.annotation.SiniCubeEventListener;
import com.sinitek.sirm.routine.holiday.service.IHolidaysService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Component
@Slf4j
@Setter(onMethod = @__({@Autowired}))
public class SupTaskProgressChangeEventListenerOnRemind {

    private ITaskService taskService;

    private ISupProjectService supProjectService;

    private SupProjectRemindConfigDAO supProjectRemindConfigDAO;

    private SupMessageRemindDAO supMessageRemindDAO;

    private IHolidaysService holidaysService;

    /**
     * 监听任务从未开始变为开始，存储需要提醒的信息
     *
     */

    @SiniCubeEventListener
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listener(SupTaskProgressChangeEvent<T> event) {
        T source = event.getSource();
        if (!(source instanceof RecordChangeLogBatchAddParamDTO)) {
            return;
        }

        RecordChangeLogBatchAddParamDTO recordChangeLogBatchAddParamDTO = (RecordChangeLogBatchAddParamDTO) source;
        Collection<Long> foreignKeys = recordChangeLogBatchAddParamDTO.getForeignKeys();
        if (CollectionUtils.isEmpty(foreignKeys)) {
            return;
        }

        List<Long> taskIds = new ArrayList<>(foreignKeys);
        List<TaskBaseDTO> tasksByIds = taskService.findTasksByIds(taskIds);
        if (CollectionUtils.isEmpty(tasksByIds)) {
            return;
        }

        List<Long> projectIds = tasksByIds.stream()
                .map(TaskBaseDTO::getProjectId)
                .collect(Collectors.toList());

        List<SupProject> supProjects = supProjectService.findProjectsByIds(projectIds);
        Map<Long, SupProject> supProjectMap = supProjects.stream()
                .collect(Collectors.toMap(SupProject::getId, item -> item));

        List<SupProjectRemindConfigPO> supProjectRemindConfigs = supProjectRemindConfigDAO.findByProjectIds(projectIds);
        Map<Long, List<SupProjectRemindConfigPO>> groupedByProjectId = supProjectRemindConfigs.stream()
                .collect(Collectors.groupingBy(SupProjectRemindConfigPO::getProjectId));

        List<SupMessageRemind> supMessageReminds = new ArrayList<>();
        Date now = DateUtils.truncate(new Date(), Calendar.DATE);

        for (TaskBaseDTO task : tasksByIds) {
            Long projectId = task.getProjectId();
            SupProject supProject = supProjectMap.get(projectId);
            List<SupProjectRemindConfigPO> remindConfigs = groupedByProjectId.get(projectId);

            // 添加任务开始时间提醒
            supMessageReminds.add(createStartTaskMessageRemind(task, now));

            // 添加任务结束前提醒
            if (shouldAddEndTaskRemind(supProject, remindConfigs)) {
                supMessageReminds.addAll(createEndTaskMessageReminds(task, supProject, remindConfigs, now));
            }
        }

        supMessageRemindDAO.saveBatch(supMessageReminds);
    }

    private SupMessageRemind createStartTaskMessageRemind(TaskBaseDTO task, Date now) {
        SupMessageRemind remind = new SupMessageRemind();
        remind.setSourceName(SupMessageRemindConstant.SUP_TASK_START_REMIND_SOURCE_NAME);
        remind.setSourceId(task.getId());
        remind.setStartDate(task.getStartDate());
        remind.setEndDate(task.getEndDate());
        remind.setStatus(SupMessageRemindStatusEnum.UNSENT.getCode());
        remind.setSendMode(SupMessageRemindConstant.EMAIL_SEND_MODE);
        remind.setType(SupMessageRemindTypeEnum.TASK_START_REMINDER.getCode());
        remind.setName(task.getName());
        remind.setRemindDate(now);
        return remind;
    }

    private boolean shouldAddEndTaskRemind(SupProject supProject, List<SupProjectRemindConfigPO> remindConfigs) {
        return supProject != null && supProject.getRemindType() != null && supProject.getRemindType() != 0
                && CollectionUtils.isNotEmpty(remindConfigs);
    }

    private List<SupMessageRemind> createEndTaskMessageReminds(TaskBaseDTO task, SupProject supProject,
                                                               List<SupProjectRemindConfigPO> remindConfigs, Date now) {
        List<SupMessageRemind> reminds = new ArrayList<>();
        Integer holidayStrategy = supProject.getHolidayStrategy();

        remindConfigs.stream()
                .filter(config -> SupProjectDateRemindTypeEnum.END_DATE.getCode().equals(config.getDateRemindType()))
                .forEach(config -> {
                    Date remindDate = calculateRemindDate(task.getEndDate(), config.getAdvanceDays(), config.getDayType(), holidayStrategy, now);
                    if (remindDate != null && remindDate.after(now)) {
                        reminds.add(createEndTaskMessageRemind(task, supProject, remindDate));
                    }
                });

        return reminds;
    }

    private Date calculateRemindDate(Date endDate, Integer advanceDays, Integer dayType, Integer holidayStrategy, Date now) {
        Date remindDate = DateUtils.addDays(endDate, -advanceDays);

        if (SupProjectDayTypeEnum.NATURAL_DAY.getCode().equals(dayType)) {
            if (SubProjectHolidayStrategyEnum.NORMAL.getCode().equals(holidayStrategy)) {
                return now.before(remindDate) ? remindDate : null;
            } else if (SubProjectHolidayStrategyEnum.SKIP.getCode().equals(holidayStrategy)) {
                return !holidaysService.checkHolidays(remindDate) && now.before(remindDate) ? remindDate : null;
            } else if (SubProjectHolidayStrategyEnum.EXTRACT.getCode().equals(holidayStrategy)) {
                if (holidaysService.checkHolidays(remindDate)) {
                    Date preWorkDay = SupHolidayUtil.getPreWorkDayByBaseDate(remindDate);
                    return now.before(preWorkDay) ? preWorkDay : null;
                } else {
                    return now.before(remindDate) ? remindDate : null;
                }
            } else {
                if (holidaysService.checkHolidays(remindDate)) {
                    Date posWorkDay = SupHolidayUtil.getPosWorkDayByBaseDate(remindDate);
                    return now.before(posWorkDay) ? posWorkDay : null;
                } else {
                    return now.before(remindDate) ? remindDate : null;
                }
            }
        } else {
            return SupHolidayUtil.addWorkDays(endDate, -advanceDays);
        }

    }

    private SupMessageRemind createEndTaskMessageRemind(TaskBaseDTO task, SupProject supProject, Date remindDate) {
        SupMessageRemind remind = new SupMessageRemind();
        remind.setSourceName(SupMessageRemindConstant.SUP_TASK_END_REMIND_SOURCE_NAME);
        remind.setSourceId(task.getId());
        remind.setStartDate(task.getStartDate());
        remind.setEndDate(task.getEndDate());
        remind.setStatus(SupMessageRemindStatusEnum.UNSENT.getCode());
        remind.setSendMode(supProject.getRemindType());
        remind.setType(SupMessageRemindTypeEnum.TASK_END_REMINDER.getCode());
        remind.setName(task.getName());
        remind.setRemindDate(remindDate);
        return remind;
    }
}
