package com.sinitek.bnzg.supervision.project.message;

/**
 * 项目 MessageCode
 *
 * <AUTHOR>
 * date 2024-12-28
 */
public class SupProjectMessage {

    private SupProjectMessage() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 标题不能超过50个字符
     */
    public static final String TITLE_CAN_NOT_EXCEED = "com.sinitek.sup.project.title_can_not_exceed";

    /**
     * 任务名称不能超过50个字符
     */
    public static final String TASK_NAME_CAN_NOT_EXCEED = "com.sinitek.sup.project.task_name_can_not_exceed";

    /**
     * 制度发文/监管要求/其他任务背景不能超过500个字符
     */
    public static final String SOURCE_DESCRIPTION_CAN_NOT_EXCEED = "com.sinitek.sup.project.source_description_can_not_exceed";

    /**
    * 项目描述不能超过500个字符
    */
    public static final String PROJECT_BRIEF_CAN_NOT_EXCEED = "com.sinitek.sup.project.project_brief_can_not_exceed";

    /**
     * 任务描述不能超过500个字符
     */
    public static final String TASK_BRIEF_CAN_NOT_EXCEED = "com.sinitek.sup.project.task_brief_can_not_exceed";

    /**
     * 任务处理要求不能超过500个字符
     */
    public static final String TASK_REQUIREMENTS_CAN_NOT_EXCEED = "com.sinitek.sup.project.task_requirements_can_not_exceed";

    /**
     * 上传文档要求不能超过500个字符
     */
    public static final String ATTACHMENT_REQUIREMENTS_CAN_NOT_EXCEED = "com.sinitek.sup.project.attachment_requirements_can_not_exceed";

    /**
    * 项目标题已存在
    */
    public static final String TITLE_ALREADY_EXISTS = "com.sinitek.sup.project.title_already_exists";


    /**
    * 项目id不能为空
    */
    public static final String PROJECT_ID_CAN_NOT_BE_NULL = "com.sinitek.sup.project.project_id_can_not_be_null";

    /**
    * 项目类型不能为空
    */
    public static final String TYPE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.type_can_not_be_null";

    /**
    * 项目标题不能为空
    */
    public static final String TITLE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.title_can_not_be_null";

    /**
    * 上报主体不能为空
    */
    public static final String REPORT_SUBJECTS_CAN_NOT_BE_NULL = "com.sinitek.sup.project.report_subjects_can_not_be_null";

    /**
    * 生效开始日期不能为空
    */
    public static final String START_DATE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.start_date_can_not_be_null";

    /**
    * 重复频率不能为空
    */
    public static final String REPEAT_TYPE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.repeat_type_can_not_be_null";

    /**
    * 一星期中某天不能为空
    */
    public static final String DAY_OF_WEEK_CAN_NOT_BE_NULL = "com.sinitek.sup.project.day_of_week_can_not_be_null";

    /**
    * 一个月中某天不能为空
    */
    public static final String DAY_OF_MONTH_CAN_NOT_BE_NULL = "com.sinitek.sup.project.day_of_month_can_not_be_null";

    /**
    * 季度内第几月不能为空
    */
    public static final String MONTH_NUMBER_CAN_NOT_BE_NULL = "com.sinitek.sup.project.month_number_can_not_be_null";

    /**
    * 季度内第几月不能超过3
    */
    public static final String MONTH_NUMBER_CAN_NOT_EXCEED = "com.sinitek.sup.project.month_number_can_not_exceed";

    /**
    * 月份不能为空
    */
    public static final String MONTH_OF_YEAR_CAN_NOT_BE_NULL = "com.sinitek.sup.project.month_of_year_can_not_be_null";

    /**
    * 月份不能超过6
    */
    public static final String MONTH_OF_YEAR_CAN_NOT_EXCEED = "com.sinitek.sup.project.month_of_year_can_not_exceed";

    /**
    * 任务持续天数不能为空
    */
    public static final String TASK_DURATION_DAYS_CAN_NOT_BE_NULL= "com.sinitek.sup.project.task_duration_days_can_not_be_null";

    /**
    * 结束日类型不能为空
    */
    public static final String END_DAY_TYPE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.end_day_type_can_not_be_null";

    /**
    * 任务名称不能为空
    */
    public static final String TASK_NAME_CAN_NOT_BE_NULL = "com.sinitek.sup.project.task_name_can_not_be_null";

    /**
    * 主办部门不能为空
    */
    public static final String LEAD_DEPT_ID_CAN_NOT_BE_NULL = "com.sinitek.sup.project.lead_dept_id_can_not_be_null";

    /**
    * 主办人不能为空
    */
    public static final String LEAD_EMP_ID_CAN_NOT_BE_NULL = "com.sinitek.sup.project.lead_emp_id_can_not_be_null";

    /**
    * 审批人不能为空
    */
    public static final String APPROVER_ID_CAN_NOT_BE_NULL = "com.sinitek.sup.project.approver_id_can_not_be_null";

    /**
    * 状态仅为[草稿]的项目允许编辑操作
    */
    public static final String PROJECT_EDIT_PERMISSION_DENIED = "com.sinitek.sup.project.project_edit_permission_denied";

    /**
    * 开始日期提醒配置不能超过[{0}]条
    */
    public static final String PROJECT_START_REMIND_CONFIG_ERROR = "com.sinitek.sup.project.project_start_remind_config_error";

    /**
    * 存在重复的结束日期提醒配置
    */
    public static final String PROJECT_END_REMIND_CONFIG_ERROR = "com.sinitek.sup.project.project_end_remind_config_error";

    /**
    * 重复频率:[{0}]，未找到对应重复频率枚举类
    */
    public static final String REPEAT_TYPE_NOT_FOUND = "com.sinitek.sup.project.repeat_type_not_found";

    /**
    * 所属部门不能为空
    */
    public static final String DEP_ID_CAN_NOT_BE_NULL = "com.sinitek.sup.project.dep_id_can_not_be_null";

    /**
    * 节假日策略不能为空
    */
    public static final String HOLIDAY_STRATEGY_CAN_NOT_BE_NULL = "com.sinitek.sup.project.holiday_strategy_can_not_be_null";

    /**
    * 任务名称参数不能为空
    */
    public static final String TASK_NAME_PARAM_CAN_NOT_BE_NULL = "com.sinitek.sup.project.task_name_param_can_not_be_null";

    /**
    * 项目[{0}]数据不存在
    */
    public static final String PROJECT_NOT_EXIST = "com.sinitek.sup.project.project_not_exist";

    /**
    * 仅状态为[草稿]的项目可以提交
    */
    public static final String PROJECT_SUBMIT_STATUS_ERROR = "com.sinitek.sup.project.project_submit_status_error";


    /**
    * 提醒方式不能为空
    */
    public static final String REMIND_TYPE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.remind_type_can_not_be_null";

    /**
    * 项目生效结束日期不能早于项目生效开始日期
    */
    public static final String END_DATE_CAN_NOT_EARLY_THAN_START_DATE = "com.sinitek.sup.project.end_date_can_not_early_than_start_date";


    /**
    * 项目类型不允许修改
    */
    public static final String PROJECT_TYPE_CHANGE_PERMISSION_DENIED = "com.sinitek.sup.project.project_type_change_permission_denied";

    /**
    * 重复频率为[从不]时,任务开始日期不能为空
    */
    public static final String TASK_START_DATE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.task_start_date_can_not_be_null";

    /**
    * 重复频率为[从不]时，任务结束日期不能为空
    */
    public static final String TASK_END_DATE_CAN_NOT_BE_NULL = "com.sinitek.sup.project.task_end_date_can_not_be_null";

    /**
    * 当前任务开始结束日期与项目重复频率不一致，请重新打开新增或编辑任务页面后操作
    */
    public static final String REPEAT_TYPE_NOT_MATCH = "com.sinitek.sup.project.repeat_type_not_match";

    /**
    * 项目[{0}]，存在未配置开始结束日期任务，请配置完整后提交
    */
    public static final String PROJECT_TASK_NOT_CONFIGURED = "com.sinitek.sup.project.project_task_not_configured";

    /**
    * 父任务id:[{}]，未配置任务开始结束日期规则，无法预览任务
    */
    public static final String PROJECT_TASK_PARENT_REMIND_RULE_NOT_EXIST = "com.sinitek.sup.project.project_task_parent_remind_rule_not_exist";

    /**
    * 重复频率为[从不]时，任务结束日期不能小于开始日期
    */
    public static final String TASK_END_DATE_LIMIT = "com.sinitek.sup.project.task_end_date_limit";

    /**
    * 任务名称[{0}]已存在
    */
    public static final String TASK_NAME_ALREADY_EXISTS = "com.sinitek.sup.project.task_name_already_exists";
}
