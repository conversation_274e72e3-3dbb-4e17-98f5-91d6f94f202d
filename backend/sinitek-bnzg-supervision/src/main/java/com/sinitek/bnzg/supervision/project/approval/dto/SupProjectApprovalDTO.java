package com.sinitek.bnzg.supervision.project.approval.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/4
 */
@Data
public class SupProjectApprovalDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "审批状态。1：通过 ，0：驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "审批意见")
    private String opinion;

}
