package com.sinitek.bnzg.supervision.project.enumerate;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Getter
public enum SupProjectStatusEnum {

    DRAFT(10, "草稿"),
    IN_PROGRESS(20, "立项中"),
    APPROVED(30, "已立项"),
    FAILED(40, "立项失败"),
    CHANGING(50, "变更中"),
    TERMINATING(60, "终止中"),
    TERMINATED(70, "已终止");

    private final Integer value;

    private final String name;


    SupProjectStatusEnum(Integer value , String name) {
        this.name = name;
        this.value = value;
    }

    public static SupProjectStatusEnum getByValue(Integer value) {
        for (SupProjectStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}
