package com.sinitek.bnzg.supervision.project.approval.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Data
@ApiModel(value = "项目审批-PO")
public class SupProjectApprovalPO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "审批人id")
    private String approverId;

    @ApiModelProperty(value = "审批类型")
    private Integer type;

    @ApiModelProperty(value = "审批状态")
    private Integer status;

    @ApiModelProperty(value = "审批意见")
    private String opinion;

    @ApiModelProperty(value = "申请人id")
    private String applicantId;

    @ApiModelProperty(value = "申请日期")
    @JsonFormat
    private Date applicationDate;

    @ApiModelProperty(value = "申请备注")
    private String remark;

    @ApiModelProperty(value = "项目id")
    private Long projectId;
}
