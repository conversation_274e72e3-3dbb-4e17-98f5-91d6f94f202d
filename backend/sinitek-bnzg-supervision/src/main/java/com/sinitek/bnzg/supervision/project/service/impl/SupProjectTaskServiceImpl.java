package com.sinitek.bnzg.supervision.project.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.common.util.SupHolidayUtil;
import com.sinitek.bnzg.supervision.doc.constant.SupDocumentConstant;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentBaseDTO;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentEditAndUpdateParamDTO;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentSaveParamDTO;
import com.sinitek.bnzg.supervision.doc.service.ISupDocumentService;
import com.sinitek.bnzg.supervision.project.constant.SupProjectConstant;
import com.sinitek.bnzg.supervision.project.dao.*;
import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.entity.*;
import com.sinitek.bnzg.supervision.project.enumerate.SubProjectRepeatTypeEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SubProjectTaskStatusEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectDayTypeEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectTypeEnum;
import com.sinitek.bnzg.supervision.project.message.SupProjectMessage;
import com.sinitek.bnzg.supervision.project.service.ISupProjectTaskService;
import com.sinitek.bnzg.supervision.project.support.SupProjectSupport;
import com.sinitek.bnzg.supervision.project.util.SupProjectUtil;
import com.sinitek.bnzg.supervision.tag.dto.SupTagDTO;
import com.sinitek.bnzg.supervision.tag.service.ISupTagService;
import com.sinitek.bnzg.supervision.task.po.SupTagResultPO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.TimeUtil;
import com.sinitek.sirm.common.web.RequestContext;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import com.sinitek.sirm.lowcode.model.dto.LcIdAndIdListDTO;
import com.sinitek.sirm.lowcode.model.dto.LcTreeListParamDTO;
import com.sinitek.sirm.lowcode.sdk.service.ITreeModelOperateSDKService;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.remind.dto.RemindRepeatRuleDTO;
import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import com.sinitek.sirm.remind.service.ISirmRemindService;
import com.sinitek.sirm.remind.support.ISirmRemindSupportService;
import com.sinitek.sirm.routine.holiday.service.IHolidaysService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import com.sinitek.spirit.businlogger.entity.BusinLogger;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import static com.sinitek.bnzg.supervision.common.enumation.TaskParticipantEnum.*;
import static com.sinitek.bnzg.supervision.doc.constant.SupDocTypeConstant.PROJECT_PLAN_ATTACHMENT;
import static com.sinitek.bnzg.supervision.project.constant.SupProjectConstant.PROJECT_MANAGER_MODULE_NAME;
import static com.sinitek.bnzg.supervision.project.constant.SupProjectConstant.PROJECT_MANAGER_OPERATE_TYPE;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
@Service
@Setter(onMethod = @__({@Autowired}))
@Slf4j
public class SupProjectTaskServiceImpl implements ISupProjectTaskService {

    private SupProjectTaskDAO projectTaskDAO;

    private ITreeModelOperateSDKService treeModelOperateSDKService;

    private SupProjectDAO projectDAO;

    private SupProjectTaskReportSubjectDAO projectTaskReportSubjectDAO;

    private SupProjectTaskTagRelaDAO projectTaskTagRelaDAO;

    private ISirmRemindService remindService;

    private SupProjectTaskParticipantDAO projectTaskParticipantDAO;

    private IEnumService enumService;

    private ISupTagService tagService;

    private IOrgService orgService;

    private ISirmRemindSupportService remindSupportService;

    private IHolidaysService holidaysService;

    private ISupDocumentService documentService;

    private ISupProjectTaskService self;

    private IAttachmentService attachmentService;

    private SupProjectSupport projectSupport;

    @Override
    public Long saveOrUpdate(SupProjectTaskSaveDTO dto) {
        Long projectId = dto.getProjectId();
        SupProject project = projectDAO.getById(projectId);
        if (ObjectUtil.isEmpty(project)){
            log.error("项目id:[{}],数据不存在", projectId);
            throw new BussinessException(SupProjectMessage.PROJECT_NOT_EXIST, projectId);
        }
        // 检查同一个项目下是否存在重复的任务名称
        checkTaskNameExist(dto.getTaskName(), dto.getId() , dto.getProjectId());
        //检查非空字段
        if (!SupProjectTypeEnum.PERIODIC_INSPECTION.getValue().equals(project.getType())) {
            SupProjectUtil.validateRepeatFields(dto.getRepeatType(), dto.getDayOfWeek(), dto.getDayOfMonth(),
                    dto.getMonthNumber(), dto.getMonthOfYear(), dto.getTaskDurationDays(),
                    dto.getEndDayType(),dto.getStartDate(), dto.getEndDate());
            SupProjectUtil.validateTaskNameParam(dto.getTaskNameParam(), dto.getRepeatType());
            if (!Objects.equals(dto.getRepeatType(),project.getRepeatType())){
                log.error("当前任务开始结束日期重复频率配置与项目重复频率不一致，请重新打开新增或编辑任务页面后操作");
                throw new BussinessException(SupProjectMessage.REPEAT_TYPE_NOT_MATCH);
            }
        }
        SupProjectTaskDTO supProjectTaskDTO = new SupProjectTaskDTO();
        BeanUtils.copyProperties(dto, supProjectTaskDTO);
        //todo 处理项目任务状态 暂时都为新增状态
        supProjectTaskDTO.setStatus(SubProjectTaskStatusEnum.NEW.getCode());
        //处理任务名称
        String name = SupProjectUtil.handleTaskName(dto.getRepeatType(), dto.getTaskNameParam(), dto.getTaskName(), project.getType());
        supProjectTaskDTO.setName(name);
        Long projectTaskId = treeModelOperateSDKService.saveOrUpdate(supProjectTaskDTO).getData();
        //保存上报主体
        projectTaskReportSubjectDAO.deleteByProjectTaskId(projectTaskId);
        if (CollectionUtils.isNotEmpty(dto.getReportSubjects())) {
            List<Integer> reportSubjects = dto.getReportSubjects();
            List<SupProjectTaskReportSubject> projectTaskReportSubjectList = reportSubjects.stream().map(subject -> {
                SupProjectTaskReportSubject projectTaskReportSubject = new SupProjectTaskReportSubject();
                projectTaskReportSubject.setProjectTaskId(projectTaskId);
                projectTaskReportSubject.setSubjectVal(subject);
                return projectTaskReportSubject;
            }).collect(Collectors.toList());
            projectTaskReportSubjectDAO.saveBatch(projectTaskReportSubjectList);
        }
        //保存标签
        List<SupProjectTaskTagRela> taskTagRelaList = projectTaskTagRelaDAO.findByProjectTaskId(projectTaskId);
        projectTaskTagRelaDAO.deleteByProjectTaskId(projectTaskId);
        if (CollectionUtils.isNotEmpty(dto.getTagIds())) {
            projectTaskTagRelaDAO.saveBatch(dto.getTagIds().stream().map(tagId -> {
                SupProjectTaskTagRela projectTaskTagRela = new SupProjectTaskTagRela();
                projectTaskTagRela.setProjectTaskId(projectTaskId);
                projectTaskTagRela.setTagId(tagId);
                return projectTaskTagRela;
            }).collect(Collectors.toList()));
            // 同步修改下级任务标签
            Boolean syncTagFlag = CommonBooleanEnum.isTrue(dto.getSyncTagFlag());
            if (syncTagFlag && IdUtil.isDataId(dto.getId())){
                List<Long> subTaskIds =  new ArrayList<>();
                collectTaskIdsRecursively(dto.getId(),subTaskIds);
                subTaskIds.remove(dto.getId());
                if (CollectionUtils.isNotEmpty(subTaskIds)){
                    Set<Long> oldTagIds = CollectionUtils.isEmpty(taskTagRelaList) ? Collections.emptySet() :
                            taskTagRelaList.stream().map(SupProjectTaskTagRela::getTagId).collect(Collectors.toSet());
                    Set<Long> newTagIds = CollectionUtils.isEmpty(dto.getTagIds()) ? Collections.emptySet() :
                            dto.getTagIds().stream().map(Long::valueOf).collect(Collectors.toSet());
                    List<Long> addedTagIds = new ArrayList<>();
                    List<Long> removedTagIds = new ArrayList<>();
                    for (Long tagId : newTagIds) {
                        if (!oldTagIds.contains(tagId)) {
                            addedTagIds.add(tagId);
                        }
                    }
                    for (Long tagId : oldTagIds) {
                        if (!newTagIds.contains(tagId)) {
                            removedTagIds.add(tagId);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(addedTagIds)){
                        List<SupProjectTaskTagRela> syncProjectTaskTagRelaList = buildProjectTaskTagReals(subTaskIds, addedTagIds);
                        projectTaskTagRelaDAO.remove(Wrappers.<SupProjectTaskTagRela>lambdaQuery()
                                .in(SupProjectTaskTagRela::getProjectTaskId, subTaskIds)
                                .in(SupProjectTaskTagRela::getTagId, addedTagIds));
                        projectTaskTagRelaDAO.saveBatch(syncProjectTaskTagRelaList);
                    }
                    if (CollectionUtils.isNotEmpty(removedTagIds)){
                        projectTaskTagRelaDAO.remove(Wrappers.<SupProjectTaskTagRela>lambdaQuery()
                                .in(SupProjectTaskTagRela::getProjectTaskId, subTaskIds)
                                .in(SupProjectTaskTagRela::getTagId, removedTagIds));
                    }
                }
            }
        }
        //保存重复频率
        Integer repeatType = dto.getRepeatType();
        if (Objects.nonNull(repeatType) && !SubProjectRepeatTypeEnum.NOT_REPEAT.getValue().equals(repeatType)
                && !SupProjectTypeEnum.PERIODIC_INSPECTION.getValue().equals(project.getType())) {
            remindService.unBindRepeatRuleByIdAndName(projectTaskId, SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
            //组装 RepeatTimeDTO
            RepeatTimeDTO repeatTimeDTO = SupProjectUtil.buildRepeatTimeDTO(repeatType, project.getStartDate(), project.getEndDate(),
                    dto.getDayOfWeek(), dto.getDayOfMonth(), dto.getMonthNumber(), dto.getMonthOfYear());
            remindService.bindRemindRepeatRule(repeatTimeDTO, projectTaskId, SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
        }
        //保存任务参与人
        projectTaskParticipantDAO.deleteByProjectTaskId(projectTaskId);
        projectTaskParticipantDAO.save(SupProjectUtil.buildProjectTaskParticipant(projectTaskId, MAIN_TYPE.getValue(), dto.getLeadEmpId(), dto.getLeadDeptId()));
        if (CollectionUtils.isNotEmpty(dto.getOrganizingParticipantDTOs())) {
            List<SupProjectTaskParticipantSaveDTO> organizingParticipantDTOs = dto.getOrganizingParticipantDTOs();
            organizingParticipantDTOs.forEach(organizingParticipantDTO -> {
                List<String> empIds = organizingParticipantDTO.getEmpIds();
                List<SupProjectTaskParticipant> projectTaskParticipantList = empIds.stream()
                        .map(empId -> SupProjectUtil.buildProjectTaskParticipant(projectTaskId, ASSISTING_TYPE.getValue(), empId, organizingParticipantDTO.getDeptId()))
                        .collect(Collectors.toList());
                projectTaskParticipantDAO.saveBatch(projectTaskParticipantList);
            });
        }
        if (StringUtils.isNotEmpty(dto.getSupervisingDeptId()) && StringUtils.isNotEmpty(dto.getSupervisingEmpId())) {
            projectTaskParticipantDAO.save(SupProjectUtil.buildProjectTaskParticipant(projectTaskId, SUPERVISE_TYPE.getValue(), dto.getSupervisingEmpId(), dto.getSupervisingDeptId()));
        }
        if (CollectionUtils.isNotEmpty(dto.getMonitoringParticipantDTOs())) {
            List<SupProjectTaskParticipantSaveDTO> monitoringParticipantDTOs = dto.getMonitoringParticipantDTOs();
            monitoringParticipantDTOs.forEach(monitoringParticipantDTO -> {
                List<String> empIds = monitoringParticipantDTO.getEmpIds();
                List<SupProjectTaskParticipant> projectTaskParticipantList = empIds.stream()
                        .map(empId -> SupProjectUtil.buildProjectTaskParticipant(projectTaskId, WATCHING_TYPE.getValue(), empId, monitoringParticipantDTO.getDeptId()))
                        .collect(Collectors.toList());
                projectTaskParticipantDAO.saveBatch(projectTaskParticipantList);
            });
        }
        //保存附件
        UploadDTO projectUpload = dto.getProjectUpload();
        if (ObjectUtil.isNotEmpty(projectUpload) && (CollectionUtils.isNotEmpty(projectUpload.getUploadFileList()) || CollectionUtils.isNotEmpty(projectUpload.getRemoveFileList()))) {
            SupDocumentSaveParamDTO supDocumentSaveParamDTO = SupProjectUtil.buildSupDocumentSaveParamDTO(projectTaskId,SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME,project.getId(),
                    project.getDepId(),projectUpload, PROJECT_PLAN_ATTACHMENT);
            List<SupDocumentBaseDTO> docList = documentService.findExistsByTaskIdAndDocType(projectTaskId, supDocumentSaveParamDTO.getDocType());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(docList)) {
                // 更新已有文档
                SupDocumentEditAndUpdateParamDTO updateParamDTO = createUpdateParamDTO(
                        docList.get(0), projectUpload, project.getDepId(), CurrentUserFactory.getOrgId());
                documentService.updateDoc(updateParamDTO);
            } else {
                // 保存新文档
                documentService.saveDoc(supDocumentSaveParamDTO);
            }
        }
        return projectTaskId;
    }

    @Override
    public void checkTaskNameExist(String taskName, Long ignoreId, Long projectId) {
        QueryWrapper<SupProjectTask> supProjectQueryWrapper = new QueryWrapper<>();
        supProjectQueryWrapper.lambda()
                .eq(SupProjectTask::getTaskName, taskName)
                .eq(SupProjectTask::getProjectId, projectId);
        SupProjectTask projectTask = projectTaskDAO.getOne(supProjectQueryWrapper);
        if (Objects.isNull(projectTask)) {
            return;
        }
        if (!(IdUtil.isDataId(ignoreId) && ignoreId.equals(projectTask.getId()))) {
            log.error("任务名称:[{}]已存在", taskName);
            throw new BussinessException(SupProjectMessage.TASK_NAME_ALREADY_EXISTS, taskName);
        }
    }

    private List<SupProjectTaskTagRela> buildProjectTaskTagReals(List<Long> projectTaskIdList, List<Long> tagIds) {
        List<SupProjectTaskTagRela> syncProjectTaskTagRelaList = new ArrayList<>();
        if (CollectionUtils.isEmpty(tagIds) || CollectionUtils.isEmpty(projectTaskIdList)) {
            return syncProjectTaskTagRelaList;
        }
        for (Long taskId : projectTaskIdList) {
            for (Long tagId : tagIds) {
                SupProjectTaskTagRela projectTaskTagRela = new SupProjectTaskTagRela();
                projectTaskTagRela.setProjectTaskId(taskId);
                projectTaskTagRela.setTagId(tagId);
                syncProjectTaskTagRelaList.add(projectTaskTagRela);
            }
        }
        return syncProjectTaskTagRelaList;
    }

    /**
     * 创建更新文档的参数
     */
    private SupDocumentEditAndUpdateParamDTO createUpdateParamDTO(
            SupDocumentBaseDTO existingDoc, UploadDTO upload,String depId, String operatorId) {
        SupDocumentEditAndUpdateParamDTO updateParamDTO = new SupDocumentEditAndUpdateParamDTO();
        updateParamDTO.setId(existingDoc.getId());
        updateParamDTO.setUpload(upload);
        updateParamDTO.setSourceId(existingDoc.getSourceId());
        updateParamDTO.setSourceName(existingDoc.getSourceName());
        updateParamDTO.setDeptId(depId);
        updateParamDTO.setOperatorId(operatorId);
        updateParamDTO.setOpTime(new Date());
        updateParamDTO.setAllowClearUploadFile(true);
        return updateParamDTO;
    }

    @Override
    public SupProjectTaskDetailResultDTO getById(Long id) {
        SupProjectTaskDetailResultDTO result = new SupProjectTaskDetailResultDTO();
        SupProjectTask projectTask = projectTaskDAO.getById(id);
        BeanUtils.copyProperties(projectTask, result);
        SupProject project =  projectDAO.getById(projectTask.getProjectId());
        //设置上级任务名称
        Long parentId = result.getParentId();
        if (IdUtil.isDataId(parentId)) {
            SupProjectTask parentTask = projectTaskDAO.getById(parentId);
            result.setParentName(parentTask.getName());
        }
        //设置上报主体
        List<SupProjectTaskReportSubject> projectTaskReportSubjects = projectTaskReportSubjectDAO.list(new QueryWrapper<SupProjectTaskReportSubject>().lambda().eq(SupProjectTaskReportSubject::getProjectTaskId, id));
        if (CollectionUtils.isNotEmpty(projectTaskReportSubjects)) {
            Map<String, String> reportSubjectMap = this.enumService.getSirmEnumByCataLogAndType(
                    SupConstant.DEFAULT_CATALOG, SupConstant.SUP_REPORT_SUBJECT);
            List<Integer> reportSubjects = projectTaskReportSubjects.stream().map(SupProjectTaskReportSubject::getSubjectVal).collect(Collectors.toList());
            String reportSubjectNames = Optional.ofNullable(reportSubjects)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(subjectVal -> MapUtils.getString(reportSubjectMap, subjectVal, ""))
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.joining(","));
            result.setReportSubjects(reportSubjects);
            result.setReportSubjectNames(reportSubjectNames);
        }
        //设置任务标签
        List<SupProjectTaskTagRela> projectTaskTagRelaList = projectTaskTagRelaDAO.list(new QueryWrapper<SupProjectTaskTagRela>().lambda().eq(SupProjectTaskTagRela::getProjectTaskId, id));
        if (CollectionUtils.isNotEmpty(projectTaskTagRelaList)) {
            List<Long> tagIds = projectTaskTagRelaList.stream().map(projectTaskTagRela -> projectTaskTagRela.getTagId()).collect(Collectors.toList());
            List<SupTagDTO> supTagDTOList = tagService.listByIds(tagIds);
            result.setTags(supTagDTOList.stream().map(tag -> new SupProjectTagResultDTO(tag.getId(), tag.getName())).collect(Collectors.toList()));
            result.setTagIds(tagIds);
        }
        //设置优先级名称
        Map<String, String> priorityMap = this.enumService.getSirmEnumByCataLogAndType(
                SupConstant.DEFAULT_CATALOG, SupConstant.SUP_PRIORITY);
        result.setPriorityName(MapUtils.getString(priorityMap, result.getPriority(), ""));
        //设置开始时间描述、结束时间描述
        List<RemindRepeatRuleDTO> remindRepeatRuleDTOS = remindService.findRepeatRulesBySourceIdAndName(id, SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
        if (CollectionUtils.isNotEmpty(remindRepeatRuleDTOS)) {
            RemindRepeatRuleDTO remindRepeatRuleDTO = remindRepeatRuleDTOS.get(0);
            RepeatTimeDTO repeatTimeDTO = remindService.analysisCronExpression(remindRepeatRuleDTO);
            Integer[] dayOfWeek = repeatTimeDTO.getDayOfWeek();
            if (ArrayUtils.isNotEmpty(dayOfWeek)){
                result.setDayOfWeek(dayOfWeek[0]);
            }
            Integer[] dayOfMonth = repeatTimeDTO.getDayOfMonth();
            if (ArrayUtils.isNotEmpty(dayOfMonth)){
                result.setDayOfMonth(dayOfMonth[0]);
            }
            Integer[] monthOfYear = repeatTimeDTO.getMonthOfYear();
            if (ArrayUtils.isNotEmpty(monthOfYear)){
                result.setMonthOfYear(monthOfYear[0]);
            }
            result.setMonthNumber(repeatTimeDTO.getMonthNumber());
            result.setStartDateDesc(SupProjectUtil.buildStartDateDesc(repeatTimeDTO, result.getRepeatType()));
            result.setEndDateDesc("开始日期后" + result.getTaskDurationDays() + "个" + SupProjectDayTypeEnum.getByCode(result.getEndDayType()).getDesc());
        }else {
            result.setStartDateDesc(TimeUtil.formatDate(result.getStartDate(), "yyyy年MM月dd日"));
            result.setEndDateDesc(TimeUtil.formatDate(result.getEndDate(), "yyyy年MM月dd日"));
        }
        //设置任务参与人
        List<SupProjectTaskParticipant> projectTaskParticipants = projectTaskParticipantDAO.list(new QueryWrapper<SupProjectTaskParticipant>().lambda().eq(SupProjectTaskParticipant::getProjectTaskId, id));
        if (CollectionUtils.isNotEmpty(projectTaskParticipants)) {
            // 提取部门ID和员工ID列表，并去重
            List<String> allOrgIds = projectTaskParticipants.stream()
                    .flatMap(participant -> Stream.of(participant.getDepId(), participant.getEmpId()))
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> orgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(allOrgIds);
            Map<Integer, List<SupProjectTaskParticipant>> projectTaskParticipantMap = projectTaskParticipants.stream()
                    .collect(Collectors.groupingBy(SupProjectTaskParticipant::getType));
            setLeader(projectTaskParticipantMap, result, orgIdAndNameMap);
            setOrganizing(projectTaskParticipantMap, result, orgIdAndNameMap);
            setSupervising(projectTaskParticipantMap, result, orgIdAndNameMap);
            setMonitoring(projectTaskParticipantMap, result, orgIdAndNameMap);
        }
        //设置项目附件
        SupDocumentBaseDTO threadLatestDoc = documentService.getThreadLatestDoc(projectTask.getProjectId(), id, PROJECT_PLAN_ATTACHMENT);
        if (Objects.nonNull(threadLatestDoc)){
            result.setAttachmentId(threadLatestDoc.getId());
        }
        //设置重复频率
        result.setRepeatType(project.getRepeatType());
        result.setRepeatTypeName(Objects.nonNull(result.getRepeatType()) ? SubProjectRepeatTypeEnum.getByValue(result.getRepeatType()).getName() : "");
        Boolean syncTagFlag = CommonBooleanEnum.isTrue(result.getSyncTagFlag());
        result.setSyncTagFlagName(syncTagFlag ? "是" : "否");
        //设置项目名称
        result.setProjectName(project.getTitle());
        return result;
    }

    private void setLeader(Map<Integer, List<SupProjectTaskParticipant>> projectTaskParticipantMap, SupProjectTaskDetailResultDTO result, Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> leaderTaskParticipants = projectTaskParticipantMap.get(MAIN_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(leaderTaskParticipants)) {
            SupProjectTaskParticipant supProjectTaskParticipant = leaderTaskParticipants.get(0);
            result.setLeadDeptId(supProjectTaskParticipant.getDepId());
            result.setLeadDeptName(MapUtils.getString(orgIdAndNameMap, supProjectTaskParticipant.getDepId(), ""));
            result.setLeadEmpId(supProjectTaskParticipant.getEmpId());
            result.setLeadEmpName(MapUtils.getString(orgIdAndNameMap, supProjectTaskParticipant.getEmpId(), ""));
        }
    }

    private void setOrganizing(Map<Integer, List<SupProjectTaskParticipant>> projectTaskParticipantMap, SupProjectTaskDetailResultDTO result, Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> organizingParticipants = projectTaskParticipantMap.get(ASSISTING_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(organizingParticipants)) {
            List<SupProjectTaskParticipantResultDTO> collect = organizingParticipants.stream()
                    .collect(Collectors.groupingBy(SupProjectTaskParticipant::getDepId))
                    .entrySet().stream()
                    .map(entry -> {
                        SupProjectTaskParticipantResultDTO dto = new SupProjectTaskParticipantResultDTO();
                        dto.setDeptId(entry.getKey());
                        dto.setDeptName(MapUtils.getString(orgIdAndNameMap, entry.getKey(), ""));
                        dto.setEmpIds(entry.getValue().stream().map(SupProjectTaskParticipant::getEmpId).collect(Collectors.toList()));
                        dto.setEmpNames(String.join(",", dto.getEmpIds().stream().map(orgIdAndNameMap::get).collect(Collectors.toList())));
                        return dto;
                    })
                    .collect(Collectors.toList());
            result.setOrganizingParticipantDTOs(collect);
        }
    }

    private void setSupervising(Map<Integer, List<SupProjectTaskParticipant>> projectTaskParticipantMap, SupProjectTaskDetailResultDTO result, Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> supervisingParticipants = projectTaskParticipantMap.get(SUPERVISE_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(supervisingParticipants)) {
            SupProjectTaskParticipant supProjectTaskParticipant = supervisingParticipants.get(0);
            result.setSupervisingDeptId(supProjectTaskParticipant.getDepId());
            result.setSupervisingDeptName(MapUtils.getString(orgIdAndNameMap, supProjectTaskParticipant.getDepId(), ""));
            result.setSupervisingEmpId(supProjectTaskParticipant.getEmpId());
            result.setSupervisingEmpName(MapUtils.getString(orgIdAndNameMap, supProjectTaskParticipant.getEmpId(), ""));
        }
    }

    private void setMonitoring(Map<Integer, List<SupProjectTaskParticipant>> projectTaskParticipantMap, SupProjectTaskDetailResultDTO result, Map<String, String> orgIdAndNameMap) {
        List<SupProjectTaskParticipant> monitoringParticipantDTOs = projectTaskParticipantMap.get(WATCHING_TYPE.getValue());
        if (CollectionUtils.isNotEmpty(monitoringParticipantDTOs)) {
            List<SupProjectTaskParticipantResultDTO> collect = monitoringParticipantDTOs.stream()
                    .collect(Collectors.groupingBy(SupProjectTaskParticipant::getDepId))
                    .entrySet().stream()
                    .map(entry -> {
                        SupProjectTaskParticipantResultDTO dto = new SupProjectTaskParticipantResultDTO();
                        dto.setDeptId(entry.getKey());
                        dto.setDeptName(MapUtils.getString(orgIdAndNameMap, entry.getKey(), ""));
                        dto.setEmpIds(entry.getValue().stream().map(SupProjectTaskParticipant::getEmpId).collect(Collectors.toList()));
                        dto.setEmpNames(String.join(",", dto.getEmpIds().stream().map(orgIdAndNameMap::get).collect(Collectors.toList())));
                        return dto;
                    })
                    .collect(Collectors.toList());
            result.setMonitoringParticipantDTOs(collect);
        }
    }

    @Override
    public List<SupProjectTaskDTO> list(Long projectId) {
        LcTreeListParamDTO lcTreeListParamDTO = new LcTreeListParamDTO();
        lcTreeListParamDTO.setId(0L);
        RequestResult<List<SupProjectTaskDTO>> listRequestResult = treeModelOperateSDKService.listTree(SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME, lcTreeListParamDTO, SupProjectTaskDTO::new);
        List<SupProjectTaskDTO> data = listRequestResult.getData();
        if (CollectionUtils.isEmpty(data)){
            return new ArrayList<>();
        }
        return data.stream().filter(dto -> dto.getProjectId().equals(projectId)).collect(Collectors.toList());
    }

    @Override
    public List<Long> findProjectTaskIdByProjectId(Long projectId) {
        return this.projectTaskDAO.findProjectTaskIdByProjectId(projectId);
    }

    @Override
    public List<SupProjectTask> findByProjectId(Long projectId) {
        return this.projectTaskDAO.findByProjectId(projectId);
    }

    @Override
    public List<SupProjectTaskPreviewResultDTO> preview(SupProjectTaskPreviewDTO dto) {
        List<SupProjectTaskPreviewResultDTO> result = new ArrayList<>();
        Long projectId = dto.getProjectId();
        SupProject project = projectDAO.getById(projectId);
        if (SupProjectTypeEnum.PERIODIC_INSPECTION.getValue().equals(project.getType())) {
            return result;
        }
        SupProjectUtil.validateRepeatFields(dto.getRepeatType(), dto.getDayOfWeek(), dto.getDayOfMonth(),
                dto.getMonthNumber(), dto.getMonthOfYear(), dto.getTaskDurationDays(),
                dto.getEndDayType(), dto.getStartDate(), dto.getEndDate());
        SupProjectUtil.validateTaskNameParam(dto.getTaskNameParam(), dto.getRepeatType());
        SubProjectRepeatTypeEnum repeatTypeEnum = SubProjectRepeatTypeEnum.getByValue(dto.getRepeatType());
        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();
        Date projectStartDate = project.getStartDate();
        Date projectEndDate = project.getEndDate();

        if (repeatTypeEnum == SubProjectRepeatTypeEnum.NOT_REPEAT) {
            handleNonRepeatingTask(dto, result, startDate, endDate, projectStartDate, projectEndDate, project);
        } else {
            handleRepeatingTask(dto, result, project, repeatTypeEnum, projectStartDate, projectEndDate);
        }

        // 条件过滤
        Date previewStartDate = dto.getPreviewStartDate();
        Date previewEndDate = dto.getPreviewEndDate();
        return result.stream().filter(item -> {
            if (Objects.isNull(previewStartDate) && Objects.isNull(previewEndDate)) {
                return true;
            } else if (Objects.isNull(previewStartDate)) {
                return item.getStartDate().before(previewEndDate) || item.getStartDate().equals(previewEndDate);
            } else if (Objects.isNull(previewEndDate)) {
                return item.getEndDate().after(previewStartDate) || item.getEndDate().equals(previewStartDate);
            } else {
                return (item.getEndDate().after(previewStartDate) || item.getEndDate().equals(previewStartDate))
                        && (item.getStartDate().before(previewEndDate) || item.getStartDate().equals(previewEndDate));
            }
        }).collect(Collectors.toList());
    }


    private void handleNonRepeatingTask(SupProjectTaskPreviewDTO dto, List<SupProjectTaskPreviewResultDTO> result, Date startDate, Date endDate, Date projectStartDate, Date projectEndDate, SupProject project) {
        Long parentId = dto.getParentId();
        if (IdUtil.isDataId(parentId)){
            boolean inDispatchRange = projectSupport.checkNoRepeatParentTaskDispatch(parentId, project);
            if (!inDispatchRange){
                return;
            }
        }
        int startYear = DateUtils.toCalendar(startDate).get(Calendar.YEAR);
        if (startYear > TimeUtil.getSysCurrYear()) {
            return;
        }

        boolean isStartValid = startDate.equals(projectStartDate) || startDate.after(projectStartDate);
        boolean isEndValid = projectEndDate == null || startDate.equals(projectEndDate) || startDate.before(projectEndDate);

        if (isStartValid && isEndValid) {
            result.add(SupProjectUtil.buildTaskPreviewResultDTO(dto.getTaskName(), startDate, endDate));
        }
    }

    private void handleRepeatingTask(SupProjectTaskPreviewDTO dto, List<SupProjectTaskPreviewResultDTO> result, SupProject project, SubProjectRepeatTypeEnum repeatTypeEnum, Date projectStartDate, Date projectEndDate) {
        int currYear = TimeUtil.getSysCurrYear();
        int projectStartYear = DateUtils.toCalendar(projectStartDate).get(Calendar.YEAR);
        projectEndDate = Optional.ofNullable(projectEndDate).orElseGet(() -> TimeUtil.formatDate(currYear + SupProjectConstant.YEAR_LAST_DAY, GlobalConstant.TIME_FORMAT_TEN));
        int projectEndYear = DateUtils.toCalendar(projectEndDate).get(Calendar.YEAR);

        if (projectStartYear > currYear || projectEndYear < currYear) {
            log.info("项目:[{}],项目生效日期范围内无预览任务数据", project.getId());
            return;
        }

        projectStartDate = projectStartYear < currYear ? TimeUtil.formatDate(currYear + SupProjectConstant.YEAR_FIRST_DAY, GlobalConstant.TIME_FORMAT_TEN) : projectStartDate;
        projectEndDate = projectEndYear > currYear ? TimeUtil.formatDate(currYear + SupProjectConstant.YEAR_LAST_DAY, GlobalConstant.TIME_FORMAT_TEN) : projectEndDate;

        RepeatTimeDTO repeatTimeDTO = SupProjectUtil.buildRepeatTimeDTO(dto.getRepeatType(), projectStartDate, projectEndDate,
                dto.getDayOfWeek(), dto.getDayOfMonth(), dto.getMonthNumber(), dto.getMonthOfYear());
        List<Date> allTriggerTimes = remindSupportService.findALLTriggerTimes(repeatTimeDTO, true, false);

        if (CollectionUtils.isEmpty(allTriggerTimes)) {
            log.info("项目:[{}],项目生效日期范围内无预览任务数据", project.getId());
            return;
        }

        List<Date> parentTriggerTimes = findParentTriggerTimes(dto.getParentId(), projectStartDate, projectEndDate);

        if (CollectionUtils.isEmpty(parentTriggerTimes)) {
            allTriggerTimes.forEach(taskStartDate -> addTaskToResult(dto, result, taskStartDate));
        } else {
            parentTriggerTimes.forEach(parentTriggerTime -> addTaskBasedOnParentTrigger(dto, result, allTriggerTimes, parentTriggerTime, repeatTypeEnum));
        }
    }


    private List<Date> findParentTriggerTimes(Long parentId, Date projectStartDate, Date projectEndDate) {
        if (IdUtil.isNotDataId(parentId)) {
            return Collections.emptyList();
        }

        List<RemindRepeatRuleDTO> parentRemindRepeatRuleDTOS = remindService.findRepeatRulesBySourceIdAndName(parentId, SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
        if (CollectionUtils.isEmpty(parentRemindRepeatRuleDTOS)) {
            log.error("父任务id:[{}]，未配置任务开始结束日期规则，无法预览任务", parentId);
            throw new BussinessException(SupProjectMessage.PROJECT_TASK_PARENT_REMIND_RULE_NOT_EXIST, parentId);
        }

        RemindRepeatRuleDTO parentRemindRepeatRuleDTO = parentRemindRepeatRuleDTOS.get(0);
        RepeatTimeDTO parentRepeatTimeDTO = remindService.analysisCronExpression(parentRemindRepeatRuleDTO);
        parentRepeatTimeDTO.setStartTime(projectStartDate);
        parentRepeatTimeDTO.setEndTime(projectEndDate);
        return remindSupportService.findALLTriggerTimes(parentRepeatTimeDTO, true, false);
    }

    private void addTaskToResult(SupProjectTaskPreviewDTO dto, List<SupProjectTaskPreviewResultDTO> result, Date taskStartDate) {
        Date taskEndDate = calculateTaskEndDate(taskStartDate, dto.getTaskDurationDays(), dto.getEndDayType());
        result.add(SupProjectUtil.buildTaskPreviewResultDTO(SupProjectUtil.buildTaskName(dto.getTaskName(), dto.getTaskNameParam(), taskStartDate, dto.getRepeatType()), taskStartDate, taskEndDate));
    }

    private void addTaskBasedOnParentTrigger(SupProjectTaskPreviewDTO dto, List<SupProjectTaskPreviewResultDTO> result, List<Date> allTriggerTimes, Date parentTriggerTime, SubProjectRepeatTypeEnum repeatTypeEnum) {
        Date taskStartDate = projectSupport.calculateSubTaskStartDate(parentTriggerTime, allTriggerTimes, repeatTypeEnum.getValue());

        if (Objects.nonNull(taskStartDate)) {
            Date taskEndDate = calculateTaskEndDate(taskStartDate, dto.getTaskDurationDays(), dto.getEndDayType());
            result.add(SupProjectUtil.buildTaskPreviewResultDTO(SupProjectUtil.buildTaskName(dto.getTaskName(), dto.getTaskNameParam(), taskStartDate, dto.getRepeatType()), taskStartDate, taskEndDate));
        }
    }

    private Date calculateTaskEndDate(Date taskStartDate, Integer taskDurationDays, Integer endDayType) {
        Date taskEndDate = DateUtils.addDays(taskStartDate, taskDurationDays);
        if (Objects.equals(SupProjectDayTypeEnum.WORK_DAY.getCode(),endDayType)){
            taskEndDate = SupHolidayUtil.addWorkDays(taskStartDate, taskDurationDays);
        }
        return taskEndDate;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(LcIdAndIdListDTO param) {
        List<Long> ids = param.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        List<Long> deleteTaskIds = new ArrayList<>();
        for (Long id : ids) {
            collectTaskIdsRecursively(id, deleteTaskIds);
        }
        deleteTaskIds = deleteTaskIds.stream().distinct().collect(Collectors.toList());
        List<SupProjectTask> tasks = projectTaskDAO.listByIds(deleteTaskIds);
        projectTaskDAO.deleteByIds(deleteTaskIds);
        String businessLogDesc = "{" + RequestContext.getCurrentUser().getDisplayName() + "}" + "删除了项目任务计划[" +
                tasks.stream().map(SupProjectTask::getTaskName).collect(Collectors.joining(",")) +
                "]";
        BusinLogger logger = RequestContext.log(PROJECT_MANAGER_MODULE_NAME, PROJECT_MANAGER_OPERATE_TYPE, businessLogDesc);
        if (logger != null) {
            logger.setEndTime(new Date());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetTaskRepeatRule(Long projectId) {
        List<Long> allTaskIds = self.findProjectTaskIdByProjectId(projectId);
        if (CollectionUtils.isEmpty(allTaskIds)){
            return;
        }
        projectTaskDAO.resetTaskRepeatRule(allTaskIds);
        for (Long taskId : allTaskIds) {
            remindService.unBindRepeatRuleByIdAndName(taskId, SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncProjectTaskTag(Long projectId, List<SupTagResultPO> oldTags, List<Long> newTagIds) {
        List<Long> oldTagIds = CollectionUtils.isEmpty(oldTags) ? Collections.emptyList() :
                oldTags.stream().map(SupTagResultPO::getTagId).collect(Collectors.toList());
        newTagIds = CollectionUtils.isEmpty(newTagIds) ? Collections.emptyList() : newTagIds;
        Set<Long> oldTagIdSet = new HashSet<>(oldTagIds);
        Set<Long> newTagIdSet = new HashSet<>(newTagIds);
        List<Long> addedTagIds = new ArrayList<>();
        List<Long> removedTagIds = new ArrayList<>();
        for (Long tagId : newTagIdSet) {
            if (!oldTagIdSet.contains(tagId)) {
                addedTagIds.add(tagId);
            }
        }
        for (Long tagId : oldTagIdSet) {
            if (!newTagIdSet.contains(tagId)) {
                removedTagIds.add(tagId);
            }
        }
        List<Long> projectTaskIdList = findProjectTaskIdByProjectId(projectId);
        if (CollectionUtils.isEmpty(projectTaskIdList)){
            return;
        }
        if (CollectionUtils.isNotEmpty(addedTagIds)){
            List<SupProjectTaskTagRela> syncProjectTaskTagRelaList = buildProjectTaskTagReals(projectTaskIdList, addedTagIds);
            projectTaskTagRelaDAO.remove(Wrappers.<SupProjectTaskTagRela>lambdaQuery()
                    .in(SupProjectTaskTagRela::getProjectTaskId, projectTaskIdList)
                    .in(SupProjectTaskTagRela::getTagId, addedTagIds));
            projectTaskTagRelaDAO.saveBatch(syncProjectTaskTagRelaList);
        }
        if (CollectionUtils.isNotEmpty(removedTagIds)){
            projectTaskTagRelaDAO.remove(Wrappers.<SupProjectTaskTagRela>lambdaQuery()
                    .in(SupProjectTaskTagRela::getProjectTaskId, projectTaskIdList)
                    .in(SupProjectTaskTagRela::getTagId, removedTagIds));
        }
    }

    @Override
    public void copyTaskByProjectId(Long projectId, Long copyProjectId) {
        List<SupProjectTask> projectTaskList =  this.findByProjectId(projectId);
        if (CollectionUtils.isEmpty(projectTaskList)){
            return;
        }
        // 过滤顶级任务
        List<SupProjectTask> rootTasks = projectTaskList
                .stream()
                .filter(item -> SupProjectConstant.SUP_PROJECT_TASK_PARENT_ID_ZERO.equals(item.getParentId()))
                .collect(Collectors.toList());
        for (SupProjectTask rootTask : rootTasks) {
            SupProjectTaskDetailResultDTO projectTaskDetailResultDTO = this.getById(rootTask.getId());
            SupProjectTaskSaveDTO supProjectTaskSaveDTO = new SupProjectTaskSaveDTO();
            BeanUtils.copyProperties(projectTaskDetailResultDTO, supProjectTaskSaveDTO);
            supProjectTaskSaveDTO.setProjectId(copyProjectId);
            supProjectTaskSaveDTO.setParentId(SupProjectConstant.SUP_PROJECT_TASK_PARENT_ID_ZERO);
            supProjectTaskSaveDTO.setId(null);
            // 任务附件
            Long attachmentId = projectTaskDetailResultDTO.getAttachmentId();
            List<Attachment> attachmentList = attachmentService.findAttachmentList(SupDocumentConstant.DEFAULT_SOURCE_NAME, attachmentId);
            if (CollectionUtils.isNotEmpty(attachmentList)) {
                UploadDTO uploadDTO = new UploadDTO();
                uploadDTO.setUploadFileList(AttachmentUtils.toNewUploadFileDtoList(attachmentList));
                supProjectTaskSaveDTO.setProjectUpload(uploadDTO);
            }
            Long rootTaskId = self.saveOrUpdate(supProjectTaskSaveDTO);
            copyChildTasks(projectTaskList, copyProjectId, rootTaskId, rootTask.getId());
        }
    }

    @Override
    public void copyChildTasks(List<SupProjectTask> projectTaskList, Long copyProjectId, Long newParentTaskId, Long oldParentTaskId) {
        List<SupProjectTask> childTasks = projectTaskList
                .stream()
                .filter(item -> oldParentTaskId.equals(item.getParentId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(childTasks)){
            return;
        }
        for (SupProjectTask childTask : childTasks) {
            SupProjectTaskDetailResultDTO projectTaskDetailResultDTO = this.getById(childTask.getId());
            SupProjectTaskSaveDTO childTaskSaveDTO = new SupProjectTaskSaveDTO();
            BeanUtils.copyProperties(projectTaskDetailResultDTO, childTaskSaveDTO);
            childTaskSaveDTO.setProjectId(copyProjectId);
            childTaskSaveDTO.setParentId(newParentTaskId);
            childTaskSaveDTO.setId(null);
            // 任务附件
            Long attachmentId = projectTaskDetailResultDTO.getAttachmentId();
            List<Attachment> attachmentList = attachmentService.findAttachmentList(SupDocumentConstant.DEFAULT_SOURCE_NAME, attachmentId);
            if (CollectionUtils.isNotEmpty(attachmentList)) {
                UploadDTO uploadDTO = new UploadDTO();
                uploadDTO.setUploadFileList(AttachmentUtils.toNewUploadFileDtoList(attachmentList));
                childTaskSaveDTO.setProjectUpload(uploadDTO);
            }
            Long savedChildTaskId = self.saveOrUpdate(childTaskSaveDTO);
            copyChildTasks(projectTaskList, copyProjectId, savedChildTaskId, childTask.getId());
        }
    }

    private void collectTaskIdsRecursively(Long id, List<Long> subTaskIds) {
        LcTreeListParamDTO lcTreeListParamDTO = new LcTreeListParamDTO();
        lcTreeListParamDTO.setId(id);
        RequestResult<List<SupProjectTaskDTO>> listRequestResult = treeModelOperateSDKService.listTree(SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME, lcTreeListParamDTO, SupProjectTaskDTO::new);
        List<SupProjectTaskDTO> data = listRequestResult.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            for (SupProjectTaskDTO projectTaskDTO : data) {
                subTaskIds.add(projectTaskDTO.getId());
                if (CollectionUtils.isNotEmpty(projectTaskDTO.getChildren())) {
                    for (LcBaseModel child : projectTaskDTO.getChildren()) {
                        collectTaskIdsRecursively(child.getId(), subTaskIds);
                    }
                }
            }
        }
    }
}
