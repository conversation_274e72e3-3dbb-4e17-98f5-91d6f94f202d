package com.sinitek.bnzg.supervision.project.auth.support;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupProjectAuthContext {

    @ApiModelProperty("权限类型")
    private String rightType;
    @ApiModelProperty("暂存授权的项目id")
    private Set<Long> projectIds;

    public SupProjectAuthContext(SupProjectAuthContext context) {
        BeanUtils.copyProperties(context, this);
    }
}
