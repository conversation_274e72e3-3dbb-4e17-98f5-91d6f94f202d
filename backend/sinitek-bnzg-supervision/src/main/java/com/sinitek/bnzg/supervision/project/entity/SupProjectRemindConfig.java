package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_remind_config")
@ApiModel(value = "项目提醒配置-实体")
public class SupProjectRemindConfig extends BaseEntity {

    /**
     * 提前提醒天数
     */
    @ApiModelProperty(value = "提前提醒天数")
    private Integer advanceDays;

    /**
     * 提醒日类型
     */
    @ApiModelProperty(value = "提醒日类型")
    private Integer dayType;

    /**
     * 日期提醒类型
     */
    @ApiModelProperty(value = "日期提醒类型")
    private Integer dateRemindType;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

}
