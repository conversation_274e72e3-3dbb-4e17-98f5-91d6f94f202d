package com.sinitek.bnzg.supervision.project.remind.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.bnzg.supervision.common.enumation.TaskParticipantEnum;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalResultDTO;
import com.sinitek.bnzg.supervision.project.approval.service.ISupProjectApprovalService;
import com.sinitek.bnzg.supervision.project.remind.constant.SupMessageRemindConstant;
import com.sinitek.bnzg.supervision.project.remind.dao.SupMessageRemindDAO;
import com.sinitek.bnzg.supervision.project.remind.dao.SupMessageRemindReceiverDAO;
import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindDTO;
import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindResultDTO;
import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindSendDTO;
import com.sinitek.bnzg.supervision.project.remind.entity.SupMessageRemind;
import com.sinitek.bnzg.supervision.project.remind.entity.SupMessageRemindReceiver;
import com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindStatusEnum;
import com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindTypeEnum;
import com.sinitek.bnzg.supervision.project.remind.message.SupProjectRemindMessage;
import com.sinitek.bnzg.supervision.project.remind.properties.SupProjectMessageRemindProperties;
import com.sinitek.bnzg.supervision.project.remind.service.ISupMessageRemindService;
import com.sinitek.bnzg.supervision.project.remind.support.SupMessageRemindTemplate;
import com.sinitek.bnzg.supervision.task.dto.TaskParticipantBaseDTO;
import com.sinitek.bnzg.supervision.task.dto.TaskWfExampleDealResultDTO;
import com.sinitek.bnzg.supervision.task.dto.TaskWfExampleDealSearchDTO;
import com.sinitek.bnzg.supervision.task.entity.TaskParticipant;
import com.sinitek.bnzg.supervision.task.service.ITaskService;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.sirm.common.message.template.dto.MessageContextDTO;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.message.template.service.IMessageTemplateExtService;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */
@Service
@Setter(onMethod = @__({@Autowired}))
@Slf4j
public class SupMessageRemindServiceImpl implements ISupMessageRemindService {

    private SupMessageRemindDAO supMessageRemindDAO;

    private SupMessageRemindReceiverDAO supMessageRemindReceiverDAO;

    private SupMessageRemindTemplate messageRemindTemplate;

    private ITaskService taskService;

    private IMessageTemplateExtService messageTemplateExtService;

    private SupProjectMessageRemindProperties supProjectMessageRemindProperties;

    private IOrgService orgService;

    private ISupProjectApprovalService supProjectApprovalService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMessageRemind(MessageRemindDTO messageRemindDTO) {
        if (Objects.isNull(messageRemindDTO)){
            log.warn("参数为空，创建消息提醒失败");
            return;
        }
        List<String> receiverIds = messageRemindDTO.getReceiverIds();
        if (CollectionUtils.isEmpty(receiverIds)){
            log.warn("消息收件人为空，创建消息提醒失败；messageRemindDTO[{}]", messageRemindDTO);
            return;
        }
        SupMessageRemind supMessageRemind = new SupMessageRemind();
        BeanUtils.copyProperties(messageRemindDTO,supMessageRemind);
        //今天的任务明天提醒
        supMessageRemind.setRemindDate(new Date());
        supMessageRemind.setSendMode(SupMessageRemindConstant.EMAIL_SEND_MODE);
        supMessageRemind.setStatus(SupMessageRemindStatusEnum.UNSENT.getCode());
        supMessageRemindDAO.saveOrUpdate(supMessageRemind);

        //保存收件人信息
        List<SupMessageRemindReceiver> supMessageRemindReceivers = new ArrayList<>();
        receiverIds.forEach(item -> {
            SupMessageRemindReceiver supMessageRemindReceiver = new SupMessageRemindReceiver();
            supMessageRemindReceiver.setMessageId(supMessageRemind.getId());
            supMessageRemindReceiver.setReceiverId(item);
            supMessageRemindReceivers.add(supMessageRemindReceiver);
        });
        supMessageRemindReceiverDAO.saveOrUpdateBatch(supMessageRemindReceivers);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMessageRemind(String sourceName, Long sourceId, Integer type) {
        LambdaQueryWrapper<SupMessageRemind> supMessageRemindLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supMessageRemindLambdaQueryWrapper.eq(SupMessageRemind::getSourceId, sourceId);
        supMessageRemindLambdaQueryWrapper.eq(SupMessageRemind::getSourceName, sourceName);
        supMessageRemindLambdaQueryWrapper.eq(SupMessageRemind::getType, type);
        supMessageRemindLambdaQueryWrapper.eq(SupMessageRemind::getStatus, SupMessageRemindStatusEnum.UNSENT.getCode());
        List<SupMessageRemind> supMessageReminds = supMessageRemindDAO.list(supMessageRemindLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(supMessageReminds)){
            log.info("通过参数查询未发送的提醒消息记录不存在，删除失败；sourceName：{}，sourceId：{}， type：{}", sourceName, sourceId, type);
            return;
        }
        List<Long> ids = supMessageReminds.stream().map(BaseEntity::getId).collect(Collectors.toList());
        supMessageRemindDAO.removeByIds(ids);
        LambdaQueryWrapper<SupMessageRemindReceiver> supMessageRemindReceiverLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supMessageRemindReceiverLambdaQueryWrapper.in(SupMessageRemindReceiver::getMessageId,ids);
        supMessageRemindReceiverDAO.remove(supMessageRemindReceiverLambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMessageRemind() {

        // 查询待发送的消息
        LambdaQueryWrapper<SupMessageRemind> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(SupMessageRemind::getRemindDate, new Date())
                .eq(SupMessageRemind::getStatus, SupMessageRemindStatusEnum.UNSENT.getCode());
        List<SupMessageRemind> supMessageReminds = supMessageRemindDAO.list(wrapper);

        if (CollectionUtils.isEmpty(supMessageReminds)) {
            log.info("没有待发送的消息");
            return;
        }

        // 提取消息ID和任务ID
        List<Long> ids = supMessageReminds.stream().map(SupMessageRemind::getId).collect(Collectors.toList());
        List<Long> startOrEndRemindTaskIds = supMessageReminds.stream()
                .filter(item -> item.getType().equals(SupMessageRemindTypeEnum.TASK_START_REMINDER.getCode())
                        || item.getType().equals(SupMessageRemindTypeEnum.TASK_END_REMINDER.getCode()))
                .map(SupMessageRemind::getSourceId)
                .collect(Collectors.toList());

        // 查询任务参与者
        List<TaskParticipantBaseDTO> pendingParticipantByTaskIds = taskService.findPendingParticipantByTaskIds(startOrEndRemindTaskIds);
        Map<Long, List<TaskParticipantBaseDTO>> taskParticipantBaseDTOMap = pendingParticipantByTaskIds.stream()
                .collect(Collectors.groupingBy(TaskParticipantBaseDTO::getTaskId));

        // 查询消息接收者
        LambdaQueryWrapper<SupMessageRemindReceiver> receiverLambdaQueryWrapper = new LambdaQueryWrapper<>();
        receiverLambdaQueryWrapper.in(SupMessageRemindReceiver::getMessageId, ids);
        List<SupMessageRemindReceiver> supMessageRemindReceivers = supMessageRemindReceiverDAO.list(receiverLambdaQueryWrapper);
        Map<Long, List<SupMessageRemindReceiver>> supMessageRemindReceiverMap = supMessageRemindReceivers.stream()
                .collect(Collectors.groupingBy(SupMessageRemindReceiver::getMessageId));
        List<String> submitIds = supMessageReminds.stream().map(SupMessageRemind::getSubmitId).collect(Collectors.toList());
        Map<String, String> orgNameMapByOrgIdList = orgService.getOrgNameMapByOrgIdList(submitIds);
        // 初始化邮件和系统消息列表
        List<MessageRemindSendDTO> emailMessageRemindSendDTOS = new ArrayList<>();
        List<MessageRemindSendDTO> messageRemindSendDTOS = new ArrayList<>();
        String hostAddr = CommonSettingUtil.getHostAddr();
        String messageRemindProcessUrl = supProjectMessageRemindProperties.getMessageRemindProcessUrl();
        // 处理每条消息
        for (SupMessageRemind item : supMessageReminds) {
            Long id = item.getId();
            Integer type = item.getType();
            Integer sendMode = item.getSendMode();
            String name = item.getName();
            Long sourceId = item.getSourceId();
            Date startDate = item.getStartDate();
            Date endDate = item.getEndDate();
            String submitId = item.getSubmitId();
            String submitName = MapUtils.getString(orgNameMapByOrgIdList, submitId, "");
            Date submitTime = item.getSubmitTime();
            String result = String.format(messageRemindProcessUrl,id);
            String url = String.format("%s#%s",hostAddr,result);

            if (taskParticipantBaseDTOMap.containsKey(sourceId)) {
                List<TaskParticipantBaseDTO> taskParticipantBaseDTOS = taskParticipantBaseDTOMap.get(sourceId);
                if (CollectionUtils.isEmpty(taskParticipantBaseDTOS)) {
                    continue;
                }

                // 处理任务提醒
                taskParticipantBaseDTOS.forEach(taskParticipantBaseDTO -> {
                    MessageRemindSendDTO messageRemindSendDTO = createMessageRemindSendDTO(id, type, sendMode, name, startDate, endDate, taskParticipantBaseDTO, url, submitName);
                    if (type.equals(SupMessageRemindTypeEnum.TASK_START_REMINDER.getCode())) {
                        emailMessageRemindSendDTOS.add(messageRemindSendDTO);
                    } else {
                        if (sendMode.equals(SupMessageRemindConstant.EMAIL_AND_REMIND_SEND_MODE)) {
                            emailMessageRemindSendDTOS.add(messageRemindSendDTO);
                            messageRemindSendDTOS.add(createSystemMessageRemindSendDTO(id, type, name, startDate, endDate, taskParticipantBaseDTO));
                        } else if (sendMode.equals(SupMessageRemindConstant.EMAIL_SEND_MODE)) {
                            emailMessageRemindSendDTOS.add(messageRemindSendDTO);
                        } else {
                            messageRemindSendDTOS.add(createSystemMessageRemindSendDTO(id, type, name, startDate, endDate, taskParticipantBaseDTO));
                        }
                    }
                });
            } else {
                List<SupMessageRemindReceiver> supMessageRemindReceiverList = supMessageRemindReceiverMap.get(id);
                if (CollectionUtils.isEmpty(supMessageRemindReceiverList)) {
                    continue;
                }
                supMessageRemindReceiverList.forEach(receiver -> {
                    MessageRemindSendDTO messageRemindSendDTO = new MessageRemindSendDTO();
                    messageRemindSendDTO.setUrl(url);
                    messageRemindSendDTO.setId(id);
                    messageRemindSendDTO.setType(type);
                    messageRemindSendDTO.setSendMode(SupMessageRemindConstant.EMAIL_SEND_MODE);
                    messageRemindSendDTO.setName(name);
                    messageRemindSendDTO.setReceiverId(receiver.getReceiverId());
                    messageRemindSendDTO.setSubmitId(submitId);
                    messageRemindSendDTO.setSubmitName(submitName);
                    messageRemindSendDTO.setSubmitTime(submitTime);

                    emailMessageRemindSendDTOS.add(messageRemindSendDTO);
                });
            }
            item.setStatus(SupMessageRemindStatusEnum.SENT.getCode());
        }

        // 发送邮件
        if (CollectionUtils.isNotEmpty(emailMessageRemindSendDTOS)) {
            Map<String, List<MessageRemindSendDTO>> messageRemindMap = emailMessageRemindSendDTOS.stream()
                    .collect(Collectors.groupingBy(MessageRemindSendDTO::getReceiverId));
            messageRemindMap.forEach((receiverId, messageList) -> {
                String content = messageRemindTemplate.getSupMessageRemindTemplate(messageList);
                String format = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
                MessageContextDTO messageContextDTO = createMessageContextDTO(receiverId, content, SupMessageRemindConstant.EMAIL_SEND_MODE, "百年资管合规督办" + format + "任务提醒");
                messageTemplateExtService.sendMessage(messageContextDTO);
            });
        }

        // 发送系统消息
        if (CollectionUtils.isNotEmpty(messageRemindSendDTOS)) {
            messageRemindSendDTOS.forEach(item -> {
                MessageContextDTO messageContextDTO = createMessageContextDTO(item.getReceiverId(),
                        "请处理《" + item.getName() + "》任务，任务角色为主办，结束日为" + DateFormatUtils.format(item.getEndDate(), "yyyy-MM-dd"),
                        SupMessageRemindConstant.REMIND_SEND_MODE, "《" + item.getName() + "》任务处理提醒");
                messageTemplateExtService.sendMessage(messageContextDTO);
            });
        }

        //更新消息状态
        supMessageRemindDAO.saveOrUpdateBatch(supMessageReminds);


    }

    @Override
    public MessageRemindResultDTO getDealResultById(Long id, Long participantId) {
        SupMessageRemind supMessageRemind = supMessageRemindDAO.getById(id);
        if (Objects.isNull(supMessageRemind)) {
            log.error("通过id无法查询到消息发送记录 id：{}", id);
            throw new BussinessException(SupProjectRemindMessage.MESSAGE_LINK_ERROR);
        }
        Integer type = supMessageRemind.getType();
        SupMessageRemindTypeEnum supMessageRemindTypeEnum = SupMessageRemindTypeEnum.fromCode(type);
        if (supMessageRemindTypeEnum == null) {
            log.error("邮件提醒消息类型有误 supMessageRemind：{}", supMessageRemind);
            throw new BussinessException(SupProjectRemindMessage.MESSAGE_LINK_ERROR);
        }
        MessageRemindResultDTO messageRemindResultDTO = new MessageRemindResultDTO();
        String sourceName = supMessageRemind.getSourceName();
        Long sourceId = supMessageRemind.getSourceId();
        switch (supMessageRemindTypeEnum) {
            // 项目审批
            case PROJECT_APPROVAL:
                SupProjectApprovalResultDTO supProjectApprovalResult = supProjectApprovalService.getSupProjectApprovalResult(sourceName, sourceId, type);
                if (Objects.nonNull(supProjectApprovalResult)) {
                    checkTaskApprover(supProjectApprovalResult.getOperatorId());
                    messageRemindResultDTO.setUrl(supProjectApprovalResult.getUrl());
                    messageRemindResultDTO.setDealFlag(supProjectApprovalResult.getDealFlag());
                }
                break;
            // 任务分解审批
            case TASK_DECOMPOSITION_APPROVAL:
                break;
            // 任务分解审批被驳回
            case TASK_DECOMPOSITION_APPROVAL_REJECT:
                break;
            // 任务处理主办审批
            case TASK_PROCESSING_HOST_APPROVAL:
            // 任务处理督办审批
            case TASK_PROCESSING_SUPERVISION_APPROVAL:
            // 任务处理审批驳回
            case TASK_PROCESSING_APPROVAL_REJECT:
            // 任务主办退回协办处理结果
            case TASK_HOST_RETURN_ASSIST_PROCESS_RESULT:
                buildTaskWfExampleDealResultDTO(sourceId, sourceName, type,messageRemindResultDTO);
                break;
            // 任务延期审批
            case TASK_DELAY_APPROVAL:

                break;
            // 任务延期审批驳回
            case TASK_DELAY_APPROVAL_REJECT:

                break;
            // 任务终止审批
            case TASK_TERMINATION_APPROVAL:

                break;
            // 任务终止审批驳回
            case TASK_TERMINATION_APPROVAL_REJECT:

                break;
            // 任务变更审批
            case TASK_CHANGE_APPROVAL:

                break;
            // 任务变更审批驳回
            case TASK_CHANGE_APPROVAL_REJECT:
                break;
             // 任务移交审批
            case TASK_TRANSFER_APPROVAL:

                break;
             // 任务移交审批驳回
            case TASK_TRANSFER_APPROVAL_REJECT:
                // todo
                break;
             // 任务开始提醒
            case TASK_START_REMINDER:
             // 任务结束前提醒
            case TASK_END_REMINDER:
                buildTaskWfExampleDealResultDTO(participantId, TaskParticipant.ENTITY_NAME, type,messageRemindResultDTO);
                break;
            // 人员离职提醒
            case PERSONNEL_DEPARTURE_REMINDER:
                // todo
                break;
            // 人员入职提醒
            case PERSONNEL_ENTRY_REMINDER:
                // todo
        }

        return messageRemindResultDTO;
    }

    private void buildTaskWfExampleDealResultDTO(Long sourceId, String sourceName, Integer type,MessageRemindResultDTO messageRemindResultDTO){
        TaskWfExampleDealSearchDTO taskWfExampleDealDTO = new TaskWfExampleDealSearchDTO();
        taskWfExampleDealDTO.setSourceId(sourceId);
        taskWfExampleDealDTO.setSourceName(sourceName);
        taskWfExampleDealDTO.setType(type);
        TaskWfExampleDealResultDTO taskWfExampleDealResult = taskService.loadTaskWfExampleDealResult(taskWfExampleDealDTO);
        if (Objects.nonNull(taskWfExampleDealResult)) {
            checkTaskApprover(taskWfExampleDealResult.getOperatorId());
            messageRemindResultDTO.setUrl(taskWfExampleDealResult.getUrl());
            messageRemindResultDTO.setDealFlag(taskWfExampleDealResult.getDealFlag());
        }
    }

    /**
     * 校验审批人和当前用户是否一致
     * @param approverId 审批人id
     */
    private void checkTaskApprover(String approverId){
        String orgId = CurrentUserFactory.getOrgId();
        if (!orgId.equals(approverId)){
            throw new BussinessException(SupProjectRemindMessage.MESSAGE_LINK_APPRVOER_ERROR);
        }
    }

    private MessageRemindSendDTO createMessageRemindSendDTO(Long id, Integer type, Integer sendMode, String name, Date startDate, Date endDate, TaskParticipantBaseDTO taskParticipantBaseDTO, String url, String summitName) {
        MessageRemindSendDTO messageRemindSendDTO = new MessageRemindSendDTO();
        String linkUrl = String.format("%s&participantId=%s",url,taskParticipantBaseDTO.getId());
        messageRemindSendDTO.setUrl(linkUrl);
        messageRemindSendDTO.setId(id);
        messageRemindSendDTO.setType(type);
        messageRemindSendDTO.setSendMode(sendMode);
        messageRemindSendDTO.setName(name);
        messageRemindSendDTO.setStartDate(startDate);
        messageRemindSendDTO.setEndDate(endDate);
        messageRemindSendDTO.setSubmitName(summitName);
        messageRemindSendDTO.setRoleName(TaskParticipantEnum.MAIN_TYPE.getValue().equals(taskParticipantBaseDTO.getType()) ? "主办" : "协办");
        messageRemindSendDTO.setReceiverId(taskParticipantBaseDTO.getEmpId());
        return messageRemindSendDTO;
    }

    private MessageRemindSendDTO createSystemMessageRemindSendDTO(Long id, Integer type, String name, Date startDate, Date endDate, TaskParticipantBaseDTO taskParticipantBaseDTO) {
        MessageRemindSendDTO remindSendDTO = new MessageRemindSendDTO();
        remindSendDTO.setId(id);
        remindSendDTO.setType(type);
        remindSendDTO.setSendMode(SupMessageRemindConstant.REMIND_SEND_MODE);
        remindSendDTO.setName(name);
        remindSendDTO.setStartDate(startDate);
        remindSendDTO.setEndDate(endDate);
        remindSendDTO.setRoleName(TaskParticipantEnum.MAIN_TYPE.getValue().equals(taskParticipantBaseDTO.getType()) ? "主办" : "协办");
        remindSendDTO.setReceiverId(taskParticipantBaseDTO.getEmpId());
        return remindSendDTO;
    }

    private MessageContextDTO createMessageContextDTO(String receiverId, String content, Integer sendMode, String title) {
        MessageContextDTO messageContextDTO = new MessageContextDTO();
        List<MessageReceiverTemplateDTO> messageReceiverTemplateDTOS = new ArrayList<>();
        MessageReceiverTemplateDTO messageReceiverTemplateDTO = new MessageReceiverTemplateDTO();
        messageReceiverTemplateDTO.setEmpId(receiverId);
        messageReceiverTemplateDTOS.add(messageReceiverTemplateDTO);
        messageContextDTO.setReceivers(messageReceiverTemplateDTOS);
        messageContextDTO.setContent(content);
        messageContextDTO.setSendMode(sendMode);
        messageContextDTO.setTitle(title);
        return messageContextDTO;
    }
}
