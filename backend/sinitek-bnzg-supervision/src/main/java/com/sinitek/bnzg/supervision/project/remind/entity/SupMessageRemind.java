package com.sinitek.bnzg.supervision.project.remind.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_message_remind")
@ApiModel(value = "消息提醒-实体")
public class SupMessageRemind extends BaseEntity {

    /**
     * 来源名称
     */
    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    /**
     * 来源记录ID
     */
    @ApiModelProperty(value = "来源记录ID")
    private Long sourceId;

    /**
     * 发送方式
     */
    @ApiModelProperty(value = "发送方式")
    private Integer sendMode;

    /**
     * 状态: 0-待发送, 1-已发送
     */
    @ApiModelProperty(value = "状态: 0-待发送, 1-已发送")
    private Integer status;

    /**
     * 提醒类型
     */
    @ApiModelProperty(value = "提醒类型")
    private Integer type;

    /**
     * 提醒日期
     */
    @ApiModelProperty(value = "提醒日期")
    private Date remindDate;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 提交人ID
     */
    @ApiModelProperty(value = "提交人ID")
    private String submitId;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endDate;


}
