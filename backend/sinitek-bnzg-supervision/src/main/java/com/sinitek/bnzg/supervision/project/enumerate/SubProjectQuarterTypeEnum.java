package com.sinitek.bnzg.supervision.project.enumerate;

/**
 *
 * 每周类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public enum SubProjectQuarterTypeEnum {

    LAST_QUARTER(1, "${上季度}"),
    THIS_QUARTER(2, "${本季度}"),
    NEXT_QUARTER(3, "${下季度}");

    private Integer code;

    private String name;

    SubProjectQuarterTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SubProjectQuarterTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }

}
