package com.sinitek.bnzg.supervision.project.approval.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.supervision.project.approval.dto.*;
import com.sinitek.bnzg.supervision.project.approval.service.ISupProjectApprovalService;
import com.sinitek.bnzg.supervision.project.approval.util.SupProjectApprovalUtil;
import com.sinitek.sirm.common.web.RequestContext;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project/approval/sup-project-approval")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "任务督办 - 项目审批")
public class SupProjectApprovalController {

    private ISupProjectApprovalService supProjectApprovalService;

    @ApiOperation(value = "查询项目审批分页列表")
    @GetMapping("/search")
    public TableResult<SupProjectApprovalSearchResultDTO> search(SupProjectApprovalSearchDTO dto) {
        String userId = RequestContext.getCurrentUser().getOrgId();
        dto.setApproverId(userId);
        IPage<SupProjectApprovalSearchResultDTO> result = supProjectApprovalService.search(dto);
        return dto.build(result, SupProjectApprovalUtil.getSupProjectApprovalSearchResult());
    }


    @ApiOperation(value = "项目审批")
    @PostMapping("/approve")
    public RequestResult<Void> approveSupProject(@RequestBody SupProjectApprovalDTO dto) {
        supProjectApprovalService.approveSupProject(dto);
        return RequestResult.success();
    }

    @ApiOperation(value = "项目批量审批")
    @PostMapping("/batch-approve")
    public RequestResult<Void> batchApproveSupProject(@RequestBody SupProjectBatchApprovalDTO dto) {
        supProjectApprovalService.batchApproveSupProject(dto);
        return RequestResult.success();
    }


    @ApiOperation(value = "查询审批详情")
    @GetMapping("/get-by-id")
    public RequestResult<SupProjectApprovalDetailDTO> loadDetail(Long id) {
        SupProjectApprovalDetailDTO dto = this.supProjectApprovalService.loadDetail(id);
        return new RequestResult<>(dto);
    }






}
