package com.sinitek.bnzg.supervision.project.service;

import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTask;
import com.sinitek.bnzg.supervision.task.po.SupTagResultPO;
import com.sinitek.sirm.lowcode.model.dto.LcIdAndIdListDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public interface ISupProjectTaskService {

    /**
    * 新增/编辑任务
    */
    Long saveOrUpdate(SupProjectTaskSaveDTO dto);

    void checkTaskNameExist(String taskName, Long id, Long projectId);

    /**
    * 查询任务详情
    */
    SupProjectTaskDetailResultDTO getById(Long id);

    /**
    * 查询任务列表
    */
    List<SupProjectTaskDTO> list(Long projectId);

    /**
    * 根据项目id查询任务id集合
    */
    List<Long> findProjectTaskIdByProjectId(Long projectId);

    /**
    * 根据项目id查询任务
    */
    List<SupProjectTask> findByProjectId(Long projectId);

    /**
    * 预览任务
    */
    List<SupProjectTaskPreviewResultDTO> preview(SupProjectTaskPreviewDTO dto);

    /**
    * 删除任务
    */
    void delete(LcIdAndIdListDTO param);

    /**
    * 重置任务重复频率规则
    */
    void resetTaskRepeatRule(Long projectId);

    /**
    * 同步任务标签
    */
    void syncProjectTaskTag(Long projectId, List<SupTagResultPO> oldTags, List<Long> tagIds);

    /**
    * 任务复制
    */
    void copyTaskByProjectId(Long projectId, Long copyProjectId);

    /**
    * 复制子级任务
    */
    void copyChildTasks(List<SupProjectTask> projectTaskList, Long copyProjectId, Long newParentTaskId, Long oldParentTaskId);
}
