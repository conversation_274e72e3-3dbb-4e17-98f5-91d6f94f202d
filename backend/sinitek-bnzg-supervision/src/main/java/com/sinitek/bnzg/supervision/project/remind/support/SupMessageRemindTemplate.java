package com.sinitek.bnzg.supervision.project.remind.support;

import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindSendDTO;
import com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindTypeEnum;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/17
 */
@Slf4j
@Component
public class SupMessageRemindTemplate {

    @Autowired
    private Configuration configuration;


    public String getSupMessageRemindTemplate(List<MessageRemindSendDTO> messageRemindSendDTOS){
        if (CollectionUtils.isEmpty(messageRemindSendDTOS)) {
            return "";
        }
        Map<Integer, List<MessageRemindSendDTO>> messageRemindMap = messageRemindSendDTOS.stream().collect(Collectors.groupingBy(MessageRemindSendDTO::getType));

        Map<String, Object> processMap = new HashMap<>();
        processMap.put("projectApprovalFlag", false);
        processMap.put("taskDecompositionApprovalFlag", false);
        processMap.put("taskProcessingHostApprovalFlag", false);
        processMap.put("taskProcessingSupervisionApprovalFlag", false);
        processMap.put("taskProcessingApprovalRejectFlag", false);
        processMap.put("taskHostReturnAssistProcessResultFlag", false);
        processMap.put("taskDelayApprovalFlag", false);
        processMap.put("taskDelayApprovalRejectFlag", false);
        processMap.put("taskTerminationApprovalFlag", false);
        processMap.put("taskTerminationApprovalRejectFlag", false);
        processMap.put("taskChangeApprovalFlag", false);
        processMap.put("taskChangeApprovalRejectFlag", false);
        processMap.put("taskTransferApprovalFlag", false);
        processMap.put("taskTransferApprovalRejectFlag", false);
        processMap.put("taskReminderFlag", false);
        processMap.put("personnelDepartureReminderFlag", false);
        processMap.put("personnelEntryReminderFlag", false);
        processMap.put("taskDecompositionApprovalRejectFlag", false);
        for (Map.Entry<Integer, List<MessageRemindSendDTO>> entry : messageRemindMap.entrySet()){
            Integer type = entry.getKey();
            List<MessageRemindSendDTO> messageRemindSendDTOList = entry.getValue();
            SupMessageRemindTypeEnum supMessageRemindTypeEnum = SupMessageRemindTypeEnum.fromCode(type);
            if (supMessageRemindTypeEnum == null) {
                continue;
            }
            if (CollectionUtils.isEmpty(messageRemindSendDTOList)) {
                continue;
            }
            ArrayList<MessageRemindSendDTO> messageRemindDTOS = new ArrayList<>();
            switch (supMessageRemindTypeEnum) {
                case PROJECT_APPROVAL:
                    processMap.put("projectApprovalFlag", true);
                    processMap.put("projectApprovals", messageRemindSendDTOList);
                    break;
                case TASK_DECOMPOSITION_APPROVAL:
                    processMap.put("taskDecompositionApprovalFlag", true);
                    processMap.put("taskDecompositionApprovals", messageRemindSendDTOList);
                    break;
                case TASK_DECOMPOSITION_APPROVAL_REJECT:
                    processMap.put("taskDecompositionApprovalRejectFlag", true);
                    processMap.put("taskDecompositionApprovalRejects", messageRemindSendDTOList);
                    break;
                case TASK_PROCESSING_HOST_APPROVAL:
                    processMap.put("taskProcessingHostApprovalFlag", true);
                    processMap.put("taskProcessingHostApprovals", messageRemindSendDTOList);
                    break;
                case TASK_PROCESSING_SUPERVISION_APPROVAL:
                    processMap.put("taskProcessingSupervisionApprovalFlag", true);
                    processMap.put("taskProcessingSupervisionApprovals", messageRemindSendDTOList);
                    break;
                case TASK_PROCESSING_APPROVAL_REJECT:
                    processMap.put("taskProcessingApprovalRejectFlag", true);
                    processMap.put("taskProcessingApprovalRejects", messageRemindSendDTOList);
                    break;
                case TASK_HOST_RETURN_ASSIST_PROCESS_RESULT:
                    processMap.put("taskHostReturnAssistProcessResultFlag", true);
                    processMap.put("taskHostReturnAssistProcessResults", messageRemindSendDTOList);
                    break;
                case TASK_DELAY_APPROVAL:
                    processMap.put("taskDelayApprovalFlag", true);
                    processMap.put("taskDelayApprovals", messageRemindSendDTOList);
                    break;
                case TASK_DELAY_APPROVAL_REJECT:
                    processMap.put("taskDelayApprovalRejectFlag", true);
                    processMap.put("taskDelayApprovalRejects", messageRemindSendDTOList);
                    break;
                case TASK_TERMINATION_APPROVAL:
                    processMap.put("taskTerminationApprovalFlag", true);
                    processMap.put("taskTerminationApprovals", messageRemindSendDTOList);
                    break;
                case TASK_TERMINATION_APPROVAL_REJECT:
                    processMap.put("taskTerminationApprovalRejectFlag", true);
                    processMap.put("taskTerminationApprovalRejects", messageRemindSendDTOList);
                    break;
                case TASK_CHANGE_APPROVAL:
                    processMap.put("taskChangeApprovalFlag", true);
                    processMap.put("taskChangeApprovals", messageRemindSendDTOList);
                    break;
                case TASK_CHANGE_APPROVAL_REJECT:
                    processMap.put("taskChangeApprovalRejectFlag", true);
                    processMap.put("taskChangeApprovalRejects", messageRemindSendDTOList);
                    break;
                case TASK_TRANSFER_APPROVAL:
                    processMap.put("taskTransferApprovalFlag", true);
                    processMap.put("taskTransferApprovals", messageRemindSendDTOList);
                    break;
                case TASK_TRANSFER_APPROVAL_REJECT:
                    processMap.put("taskTransferApprovalRejectFlag", true);
                    processMap.put("taskTransferApprovalRejects", messageRemindSendDTOList);
                    break;
                case TASK_START_REMINDER:
                    messageRemindDTOS.addAll(messageRemindSendDTOList);
                    break;
                case TASK_END_REMINDER:
                    messageRemindDTOS.addAll(messageRemindSendDTOList);
                    break;
                case PERSONNEL_DEPARTURE_REMINDER:
                    processMap.put("personnelDepartureReminderFlag", true);
                    processMap.put("personnelDepartureReminders", messageRemindSendDTOList);
                    break;
                case PERSONNEL_ENTRY_REMINDER:
                    processMap.put("personnelEntryReminderFlag", true);
                    processMap.put("personnelEntryReminders", messageRemindSendDTOList);
            }
            if (CollectionUtils.isNotEmpty(messageRemindDTOS)) {
                processMap.put("taskReminderFlag", true);
                processMap.put("taskReminders", messageRemindDTOS);
            }
        }
            Template template = null;
        try {
            template = configuration.getTemplate("sup-message-remind.ftl");
        } catch (IOException e) {
            log.error("解析模板失败，messageRemindSendDTOS{}",messageRemindSendDTOS, e);
            return "";
        }
        StringWriter writer = new StringWriter();
        try {
            template.process(processMap, writer);
        } catch (TemplateException | IOException e) {
            log.error("模版填充失败，messageRemindSendDTOS{}",messageRemindSendDTOS, e);
            return "";
        }
        return writer.toString();
    }


}
