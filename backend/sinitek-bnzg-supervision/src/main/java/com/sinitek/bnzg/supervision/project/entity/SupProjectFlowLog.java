package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_flow_log")
@ApiModel(value = "项目流转记录-实体")
public class SupProjectFlowLog extends BaseEntity {

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private String operatorId;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private Date dealTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String brief;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;


    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    private Integer approvalResult;


    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;

}
