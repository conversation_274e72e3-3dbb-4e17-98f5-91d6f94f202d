package com.sinitek.bnzg.supervision.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * date 2025/2/20
 */
@Data
@ApiModel(value = "项目任务派发 - 返回DTO")
public class SupProjectTaskDispatchResultDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "流转信息id")
    private Long flowLogId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date endDate;

    @ApiModelProperty(value = "是否成功派发, 0 否 1 是")
    private Integer isDispatch;
}
