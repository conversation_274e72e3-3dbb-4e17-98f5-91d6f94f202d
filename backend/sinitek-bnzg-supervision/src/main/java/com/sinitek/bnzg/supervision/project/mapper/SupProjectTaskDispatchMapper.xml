<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinitek.bnzg.supervision.project.mapper.SupProjectTaskDispatchMapper">
    <select id="findByFlowLogIds" resultType="com.sinitek.bnzg.supervision.project.po.SupProjectTaskDispatchPO">
        select
        id,
        flow_log_id,
        task_name,
        start_date,
        end_date,
        is_dispatch
        from sup_project_task_dispatch
        where 1=1
        <if test="flowLogIds != null and flowLogIds.size() > 0">
            and flow_log_id in
            <foreach collection="flowLogIds" item="flowLogId" open="(" separator="," close=")">
                #{flowLogId}
            </foreach>
        </if>
    </select>
</mapper>