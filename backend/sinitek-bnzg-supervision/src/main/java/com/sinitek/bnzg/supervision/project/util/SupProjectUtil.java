package com.sinitek.bnzg.supervision.project.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentSaveParamDTO;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalSaveDTO;
import com.sinitek.bnzg.supervision.project.constant.SupProjectConstant;
import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTask;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTaskParticipant;
import com.sinitek.bnzg.supervision.project.enumerate.*;
import com.sinitek.bnzg.supervision.project.message.SupProjectMessage;
import com.sinitek.bnzg.supervision.project.po.SupProjectSearchParamPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectSearchResultPO;
import com.sinitek.bnzg.supervision.task.dto.TaskCreateDTO;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.common.utils.TimeUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.stream.Collectors;

import static com.sinitek.bnzg.supervision.project.enumerate.SubProjectRepeatTypeEnum.NOT_REPEAT;
import static com.sinitek.bnzg.supervision.project.enumerate.SupProjectTypeEnum.PERIODIC_INSPECTION;

/**
 *
 * 项目管理 - 工具类
 *
 * <AUTHOR>
 * @date 2024/12/30
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class SupProjectUtil {

    private static final int QUARTER_MONTH_NUM = 3;

    private static final int HALF_YEAR_MONTH_NUM = 6;

    public static SupProjectSearchParamPO makeSearchParamDTO2PO(SupProjectSearchParamDTO dto) {
        Integer type = dto.getType();
        String title = dto.getTitle();
        List<String> depIds = dto.getDepIds();
        List<Integer> reportSubjects = dto.getReportSubjects();
        List<Integer> status = dto.getStatus();
        List<String> approverIds = dto.getApproverIds();
        List<Long> tagIds = dto.getTagIds();
        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();

        SupProjectSearchParamPO supProjectSearchParamPO = new SupProjectSearchParamPO();

        if (ObjectUtil.isNotEmpty(type)){
            supProjectSearchParamPO.setType(type);
        }

        List<String> titles = CommonStringUtil.toSearchStrList(title);
        if (CollUtil.isNotEmpty(titles)) {
            supProjectSearchParamPO.setTitles(titles);
        }

        if (CollUtil.isNotEmpty(depIds)) {
            supProjectSearchParamPO.setDepIds(depIds);
        }

        if (CollUtil.isNotEmpty(reportSubjects)) {
            supProjectSearchParamPO.setReportSubjects(reportSubjects);
        }

        if (CollUtil.isNotEmpty(status)) {
            supProjectSearchParamPO.setStatus(status);
        }

        if (CollUtil.isNotEmpty(approverIds)) {
            supProjectSearchParamPO.setApproverIds(approverIds);
        }

        if (CollUtil.isNotEmpty(tagIds)) {
            supProjectSearchParamPO.setTagIds(tagIds);
        }

        if (ObjectUtil.isNotEmpty(startDate)) {
            supProjectSearchParamPO.setStartDate(startDate);
        }

        if (ObjectUtil.isNotEmpty(endDate)) {
            supProjectSearchParamPO.setEndDate(endDate);
        }

        supProjectSearchParamPO.setOrderName(dto.getOrderName());
        supProjectSearchParamPO.setOrderType(dto.getOrderType());
        supProjectSearchParamPO.setPageIndex(dto.getPageIndex());
        supProjectSearchParamPO.setPageSize(dto.getPageSize());
        return supProjectSearchParamPO;
    }

    public static SupProjectSearchResultDTO makeSupProjectSearchResultPO2DTO(SupProjectSearchResultPO po) {
        return LcConvertUtil.convert(po, SupProjectSearchResultDTO::new);
    }

    /**
    * 校验重复频率字段
    */
    public static void validateRepeatFields(Integer repeatType,Integer dayOfWeek, Integer dayOfMonth,
                                            Integer monthNumber,Integer monthOfYear, Integer taskDurationDays,Integer endDayType,
                                            Date startDate, Date endDate) {
        if (Objects.isNull(repeatType)){
            log.error("任务重复频率不能为空");
            throw new BussinessException(SupProjectMessage.REPEAT_TYPE_CAN_NOT_BE_NULL);
        }
        SubProjectRepeatTypeEnum repeatTypeEnum = SubProjectRepeatTypeEnum.getByValue(repeatType);
        switch (repeatTypeEnum) {
            case NOT_REPEAT:
                if (Objects.isNull(startDate)){
                    log.error("重复频率为[从不]时，任务开始日期不能为空");
                    throw new BussinessException(SupProjectMessage.TASK_START_DATE_CAN_NOT_BE_NULL);
                }
                if (Objects.isNull(endDate)){
                    log.error("重复频率为[从不]时，任务结束日期不能为空");
                    throw new BussinessException(SupProjectMessage.TASK_END_DATE_CAN_NOT_BE_NULL);
                }
                if (endDate.before(startDate)) {
                    log.error("重复频率为[从不]时，任务结束日期不能小于开始日期");
                    throw new BussinessException(SupProjectMessage.TASK_END_DATE_LIMIT);
                }
                break;
            case REPEAT_BY_WEEK:
                if (Objects.isNull(dayOfWeek)) {
                    log.error("重复频率为每周时，一周内某天不能为空");
                    throw new BussinessException(SupProjectMessage.DAY_OF_WEEK_CAN_NOT_BE_NULL);
                }
                break;
            case REPEAT_BY_MONTH:
                if (Objects.isNull(dayOfMonth)) {
                    log.error("重复频率为每月时，一月内某天不能为空");
                    throw new BussinessException(SupProjectMessage.DAY_OF_MONTH_CAN_NOT_BE_NULL);
                }
                break;
            case REPEAT_BY_QUARTER:
                if (Objects.isNull(monthNumber)) {
                    log.error("重复频率为每季度时，季度内某月不能为空");
                    throw new BussinessException(SupProjectMessage.MONTH_NUMBER_CAN_NOT_BE_NULL);
                } else if (monthNumber > SupProjectConstant.SUP_PROJECT_QUARTER_MONTH) {
                    log.error("重复频率为每季度时，季度内某月不能大于3");
                    throw new BussinessException(SupProjectMessage.MONTH_NUMBER_CAN_NOT_EXCEED);
                }
                if (Objects.isNull(dayOfMonth)) {
                    log.error("重复频率为每季度时，一月内某天不能为空");
                    throw new BussinessException(SupProjectMessage.DAY_OF_MONTH_CAN_NOT_BE_NULL);
                }
                break;
            case REPEAT_BY_YEAR:
                if (Objects.isNull(monthOfYear)) {
                    log.error("重复频率为每年时，月份不能为空");
                    throw new BussinessException(SupProjectMessage.MONTH_OF_YEAR_CAN_NOT_BE_NULL);
                }
                if (Objects.isNull(dayOfMonth)) {
                    log.error("重复频率为每年时，一月内某天不能为空");
                    throw new BussinessException(SupProjectMessage.DAY_OF_MONTH_CAN_NOT_BE_NULL);
                }
                break;
            case REPEAT_BY_HALF_YEAR:
                if (Objects.isNull(monthOfYear)) {
                    log.error("重复频率为每半年时，月份不能为空");
                    throw new BussinessException(SupProjectMessage.MONTH_OF_YEAR_CAN_NOT_BE_NULL);
                }
                if (Objects.isNull(dayOfMonth)) {
                    log.error("重复频率为每半年时，一月内某天不能为空");
                    throw new BussinessException(SupProjectMessage.DAY_OF_MONTH_CAN_NOT_BE_NULL);
                }
                if (monthOfYear > SupProjectConstant.SUP_PROJECT_REPEAT_TIME_SIX_MONTH) {
                    log.error("重复频率为每半年时，月份不能大于6");
                    throw new BussinessException(SupProjectMessage.MONTH_OF_YEAR_CAN_NOT_EXCEED);
                }
                break;
        }
        if (Objects.isNull(taskDurationDays) && !NOT_REPEAT.getValue().equals(repeatType)) {
            log.error("任务持续天数不能为空");
            throw new BussinessException(SupProjectMessage.TASK_DURATION_DAYS_CAN_NOT_BE_NULL);
        }
        if (Objects.isNull(endDayType) && !NOT_REPEAT.getValue().equals(repeatType)) {
            log.error("结束日类型不能为空");
            throw new BussinessException(SupProjectMessage.END_DAY_TYPE_CAN_NOT_BE_NULL);
        }
    }

    /**
    * 处理任务名称
    */
    public static String handleTaskName(Integer repeatType,Integer taskNameParam,String taskName, Integer type) {
        if (PERIODIC_INSPECTION.getValue().equals(type)) {
            return taskName;
        }
        SubProjectRepeatTypeEnum repeatTypeEnum = SubProjectRepeatTypeEnum.getByValue(repeatType);
        switch (repeatTypeEnum) {
            case REPEAT_BY_WEEK:
                return SubProjectWeekTypeEnum.getName(taskNameParam) + taskName;
            case REPEAT_BY_MONTH:
                return SubProjectMonthTypeEnum.getName(taskNameParam) + taskName;
            case REPEAT_BY_QUARTER:
                return SubProjectQuarterTypeEnum.getName(taskNameParam) + taskName;
            case REPEAT_BY_YEAR:
                return SubProjectYearTypeEnum.getName(taskNameParam) + taskName;
            case REPEAT_BY_HALF_YEAR:
                return SubProjectHalfYearTypeEnum.getName(taskNameParam) + taskName;
            default:
                return taskName;
        }
    }

    /**
    * 构建框架重复频率参数
    */
    public static RepeatTimeDTO buildRepeatTimeDTO(Integer repeatType,Date startDate,Date endDate,
                                                    Integer dayOfWeek,Integer dayOfMonth,Integer monthNumber,Integer monthOfYear) {
        RepeatTimeDTO repeatTimeDTO = new RepeatTimeDTO();
        repeatTimeDTO.setFrequency(1);
        repeatTimeDTO.setHolidayStrategy(0);
        repeatTimeDTO.setStartTime(startDate);
        repeatTimeDTO.setEndTime(endDate);
        repeatTimeDTO.setMonthType(0);
        repeatTimeDTO.setQuarterType(0);
        repeatTimeDTO.setYearType(0);
        if (Objects.equals(SubProjectRepeatTypeEnum.REPEAT_BY_HALF_YEAR.getValue(), repeatType)) {
            repeatTimeDTO.setRepeatType(SubProjectRepeatTypeEnum.REPEAT_BY_YEAR.getValue());
            repeatTimeDTO.setMonthOfYear(Objects.nonNull(monthOfYear) ? new Integer[]{monthOfYear, monthOfYear + SupProjectConstant.SUP_PROJECT_REPEAT_TIME_SIX_MONTH} : new Integer[]{});
        } else {
            repeatTimeDTO.setRepeatType(repeatType);
            repeatTimeDTO.setMonthOfYear(Objects.nonNull(monthOfYear) ? new Integer[]{monthOfYear} : new Integer[]{});
        }
        repeatTimeDTO.setDayOfWeek(Objects.nonNull(dayOfWeek) ? new Integer[]{dayOfWeek} : new Integer[]{});
        repeatTimeDTO.setDayOfMonth(Objects.nonNull(dayOfMonth) ? new Integer[]{dayOfMonth} : new Integer[]{});
        repeatTimeDTO.setMonthNumber(monthNumber);
        return repeatTimeDTO;
    }

    /**
    * 构建任务参与人
    */
    public static SupProjectTaskParticipant buildProjectTaskParticipant(Long projectTaskId, Integer typeValue, String empId, String depId) {
        SupProjectTaskParticipant projectTaskParticipant = new SupProjectTaskParticipant();
        projectTaskParticipant.setProjectTaskId(projectTaskId);
        projectTaskParticipant.setType(typeValue);
        projectTaskParticipant.setEmpId(empId);
        projectTaskParticipant.setDepId(depId);
        return projectTaskParticipant;
    }

    /**
    * 构建任务开始时间描述
    */
    public static String buildStartDateDesc(RepeatTimeDTO repeatTimeDTO, Integer repeatType) {
        switch (SubProjectRepeatTypeEnum.getByValue(repeatType)) {
            case REPEAT_BY_WEEK:
                return "每" + SubProjectWeekEnum.getByValue(repeatTimeDTO.getDayOfWeek()[0]).getName();
            case REPEAT_BY_MONTH:
                return "每月" + repeatTimeDTO.getDayOfMonth()[0] + "日";
            case REPEAT_BY_QUARTER:
                return "每季第" + repeatTimeDTO.getMonthNumber() + "个月" + repeatTimeDTO.getDayOfMonth()[0] + "日";
            case REPEAT_BY_HALF_YEAR:
                return "每半年第" + repeatTimeDTO.getMonthOfYear()[0] + "个月" + repeatTimeDTO.getDayOfMonth()[0] + "日";
            case REPEAT_BY_YEAR:
                return "每年" + repeatTimeDTO.getMonthOfYear()[0] + "月" + repeatTimeDTO.getDayOfMonth()[0] + "日";
            default:
                return "";
        }
    }

    /**
    * 检查项目数据是否存在
    */
    public static void checkProjectExist(SupProject project,Long id){
        if (Objects.isNull(project)){
            log.error("项目id:[{}]，数据不存在",id);
            throw new BussinessException(SupProjectMessage.PROJECT_NOT_EXIST,id);
        }
    }

    /**
    * 检查项目是否可以提交
    */
    public static void checkProjectSubmit(SupProject submitProject) {
        Integer status = submitProject.getStatus();
        if (!SupProjectStatusEnum.DRAFT.getValue().equals(status)){
            log.error("仅状态为[草稿]的项目可以提交");
            throw new BussinessException(SupProjectMessage.PROJECT_SUBMIT_STATUS_ERROR);
        }
    }

    /**
    * 构建任务实例名称
    */
    public static String buildTaskName(String taskName, Integer taskNameParam, Date taskStartDate, Integer repeatType) {
        SubProjectRepeatTypeEnum repeatTypeEnum = SubProjectRepeatTypeEnum.getByValue(repeatType);
        switch (repeatTypeEnum) {
            case REPEAT_BY_WEEK:
                if (SubProjectWeekTypeEnum.LAST_WEEK.getCode().equals(taskNameParam)) {
                    String lastWeekInfo = getWeekInfo(taskStartDate, -1);
                    return lastWeekInfo + taskName;
                } else if (SubProjectWeekTypeEnum.THIS_WEEK.getCode().equals(taskNameParam)) {
                    String thisWeekInfo = getWeekInfo(taskStartDate, 0);
                    return thisWeekInfo + taskName;
                } else {
                    String thisWeekInfo = getWeekInfo(taskStartDate, 1);
                    return thisWeekInfo + taskName;
                }
            case REPEAT_BY_MONTH:
                if (SubProjectMonthTypeEnum.LAST_MONTH.getCode().equals(taskNameParam)) {
                    String lastMonthInfo = getMonthInfo(taskStartDate, -1);
                    return lastMonthInfo + taskName;
                } else if (SubProjectMonthTypeEnum.THIS_MONTH.getCode().equals(taskNameParam)) {
                    String thisMonthInfo = getMonthInfo(taskStartDate, 0);
                    return thisMonthInfo + taskName;
                } else {
                    String nextMonthInfo = getMonthInfo(taskStartDate, 1);
                    return nextMonthInfo + taskName;
                }
            case REPEAT_BY_QUARTER:
                if (SubProjectQuarterTypeEnum.LAST_QUARTER.getCode().equals(taskNameParam)) {
                    String lastQuarterInfo = getQuarterInfo(taskStartDate, -1);
                    return lastQuarterInfo + taskName;
                } else if (SubProjectQuarterTypeEnum.THIS_QUARTER.getCode().equals(taskNameParam)) {
                    String thisQuarterInfo = getQuarterInfo(taskStartDate, 0);
                    return thisQuarterInfo + taskName;
                } else {
                    String nextQuarterInfo = getQuarterInfo(taskStartDate, 1);
                    return nextQuarterInfo + taskName;
                }
            case REPEAT_BY_YEAR:
                if (SubProjectYearTypeEnum.LAST_YEAR.getCode().equals(taskNameParam)) {
                    String lastYearInfo = getYearInfo(taskStartDate, -1);
                    return lastYearInfo + taskName;
                } else if (SubProjectYearTypeEnum.THIS_YEAR.getCode().equals(taskNameParam)) {
                    String thisYearInfo = getYearInfo(taskStartDate, 0);
                    return thisYearInfo + taskName;
                } else {
                    String nextYearInfo = getYearInfo(taskStartDate, 1);
                    return nextYearInfo + taskName;
                }
            case REPEAT_BY_HALF_YEAR:
                if (SubProjectHalfYearTypeEnum.LAST_HALF_YEAR.getCode().equals(taskNameParam)) {
                    String lastHalfYearInfo = getHalfYearInfo(taskStartDate, -1);
                    return lastHalfYearInfo + taskName;
                } else if (SubProjectHalfYearTypeEnum.THIS_HALF_YEAR.getCode().equals(taskNameParam)) {
                    String thisHalfYearInfo = getHalfYearInfo(taskStartDate, 0);
                    return thisHalfYearInfo + taskName;
                } else {
                    String nextHalfYearInfo = getHalfYearInfo(taskStartDate, 1);
                    return nextHalfYearInfo + taskName;
                }
            default:
                return taskName;
        }
    }

    public static String getWeekInfo(Date date, int offset) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        localDate = localDate.plusWeeks(offset);
        int year = localDate.get(IsoFields.WEEK_BASED_YEAR);
        int week = localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        return year + "年第" + week + "周";
    }

    public static String getMonthInfo(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, offset);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        return year + "年第" + month + "月";
    }

    public static String getQuarterInfo(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, offset * QUARTER_MONTH_NUM);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int quarter = (month - 1) / QUARTER_MONTH_NUM + 1;
        return year + "年第" + quarter + "季度";
    }

    public static String getYearInfo(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, offset);
        int year = calendar.get(Calendar.YEAR);
        return year + "年";
    }

    public static String getHalfYearInfo(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, offset * HALF_YEAR_MONTH_NUM);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int halfYear = (month - 1) / HALF_YEAR_MONTH_NUM + 1;
        if (halfYear == 1) {
            return year + "年上半年";
        } else {
            return year + "年下半年";
        }
    }

    /**
    * 校验任务名称参数
    */
    public static void validateTaskNameParam(Integer taskNameParam, Integer repeatType) {
        if (Objects.isNull(taskNameParam) && !NOT_REPEAT.getValue().equals(repeatType)) {
            log.error("任务名称参数不能为空");
            throw new BussinessException(SupProjectMessage.TASK_NAME_PARAM_CAN_NOT_BE_NULL);
        }
    }

    /**
    * 构建任务预览返回DTO
    */
    public static SupProjectTaskPreviewResultDTO buildTaskPreviewResultDTO(String taskName, Date startDate, Date endDate) {
        SupProjectTaskPreviewResultDTO subProjectTaskPreviewResultDTO = new SupProjectTaskPreviewResultDTO();
        subProjectTaskPreviewResultDTO.setStartDate(startDate);
        subProjectTaskPreviewResultDTO.setEndDate(endDate);
        subProjectTaskPreviewResultDTO.setTaskName(taskName);
        return subProjectTaskPreviewResultDTO;
    }

    /**
    * 构建文档保存参数
    */
    public static SupDocumentSaveParamDTO buildSupDocumentSaveParamDTO(Long sourceId,String sourceName,Long projectId,String deptId,
                                                                       UploadDTO projectUpload,Integer docType) {
        SupDocumentSaveParamDTO supDocumentSaveParamDTO = new SupDocumentSaveParamDTO();
        supDocumentSaveParamDTO.setSourceId(sourceId);
        supDocumentSaveParamDTO.setSourceName(sourceName);
        supDocumentSaveParamDTO.setProjectId(projectId);
        supDocumentSaveParamDTO.setUpload(projectUpload);
        supDocumentSaveParamDTO.setOpTime(new Date());
        supDocumentSaveParamDTO.setOperatorId(CurrentUserFactory.getOrgId());
        supDocumentSaveParamDTO.setDeptId(deptId);
        supDocumentSaveParamDTO.setDocType(docType);
        return supDocumentSaveParamDTO;
    }

    /**
    * 构建审批信息DTO
    */
    public static SupProjectApprovalSaveDTO buildSupProjectApprovalSaveDTO(String remark,Long projectId, String applicantId,
                                                                           String approveId,Integer operateType) {
        SupProjectApprovalSaveDTO approvalSaveDTO = new SupProjectApprovalSaveDTO();
        approvalSaveDTO.setRemark(remark);
        approvalSaveDTO.setProjectId(projectId);
        approvalSaveDTO.setApplicantId(applicantId);
        approvalSaveDTO.setApproverId(approveId);
        approvalSaveDTO.setOperateType(operateType);
        return approvalSaveDTO;
    }


    /**
     * 检查项目信息
     */
    public static void checkProjectFields(SupProjectSaveDTO dto) {
        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();
        if (Objects.nonNull(endDate) && startDate.after(endDate)) {
            log.error("项目生效结束日期不能早于项目生效开始日期");
            throw new BussinessException(SupProjectMessage.END_DATE_CAN_NOT_EARLY_THAN_START_DATE);
        }
        if (PERIODIC_INSPECTION.getValue().equals(dto.getType())){
            return;
        }
        if (Objects.isNull(dto.getRepeatType())){
            log.error("重复频率不能为空");
            throw new BussinessException(SupProjectMessage.REPEAT_TYPE_CAN_NOT_BE_NULL);
        }
        if (Objects.isNull(dto.getHolidayStrategy())){
            log.error("节假日策略不能为空");
            throw new BussinessException(SupProjectMessage.HOLIDAY_STRATEGY_CAN_NOT_BE_NULL);
        }
        List<SupProjectRemindConfigBaseDTO> remindConfigs = dto.getRemindConfigs();
        if (CollectionUtils.isEmpty(remindConfigs)) {
            return;
        }
        //检查提醒方式
        if (Objects.isNull(dto.getRemindType())){
            log.error("提醒方式不能为空");
            throw new BussinessException(SupProjectMessage.REMIND_TYPE_CAN_NOT_BE_NULL);
        }
        List<SupProjectRemindConfigBaseDTO> startDateRemindConfigs = remindConfigs.stream()
                .filter(remindConfig -> SupProjectDateRemindTypeEnum.START_DATE.getCode().equals(remindConfig.getDateRemindType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(startDateRemindConfigs) && startDateRemindConfigs.size() > SupProjectConstant.SUP_PROJECT_START_DATE_REMIND_SIZE) {
            log.error("开始日期提醒配置不能超过{}条", SupProjectConstant.SUP_PROJECT_START_DATE_REMIND_SIZE);
            throw new BussinessException(SupProjectMessage.PROJECT_START_REMIND_CONFIG_ERROR, SupProjectConstant.SUP_PROJECT_START_DATE_REMIND_SIZE);
        }
        List<SupProjectRemindConfigBaseDTO> endDateRemindConfigs = remindConfigs.stream()
                .filter(remindConfig -> SupProjectDateRemindTypeEnum.END_DATE.getCode().equals(remindConfig.getDateRemindType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(endDateRemindConfigs)) {
            return;
        }
        List<String> uniqueIdentifiers = new ArrayList<>();
        List<SupProjectRemindConfigBaseDTO> duplicateConfigs = new ArrayList<>();
        for (SupProjectRemindConfigBaseDTO config : endDateRemindConfigs) {
            String identifier = config.getAdvanceDays() + "_" + config.getDayType();
            if (uniqueIdentifiers.contains(identifier)) {
                duplicateConfigs.add(config);
            } else {
                uniqueIdentifiers.add(identifier);
            }
        }
        if (!duplicateConfigs.isEmpty()) {
            log.error("存在重复的结束日期提醒配置");
            throw new BussinessException(SupProjectMessage.PROJECT_END_REMIND_CONFIG_ERROR);
        }
    }

    /**
     * 检查项目编辑内容
     */
    public static void checkProjectEditContent(SupProject supProject, SupProjectSaveDTO dto) {
        Integer status = supProject.getStatus();
        if (!SupProjectStatusEnum.DRAFT.getValue().equals(status)){
            log.warn("状态仅为[草稿]的项目允许编辑操作");
            throw new BussinessException(SupProjectMessage.PROJECT_EDIT_PERMISSION_DENIED);
        }
        if (!Objects.equals(dto.getType(),supProject.getType())){
            log.warn("项目类型不允许修改");
            throw new BussinessException(SupProjectMessage.PROJECT_TYPE_CHANGE_PERMISSION_DENIED);
        }
    }

    /**
    * 判断两个日期是否为同一周
    */
    public static boolean isSameWeek(Date parentTaskStartDate, Date subTaskStartDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parentTaskStartDate);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int weekOfParentTask = calendar.get(Calendar.WEEK_OF_YEAR);
        calendar.setTime(subTaskStartDate);
        int weekOfSubTask = calendar.get(Calendar.WEEK_OF_YEAR);
        return weekOfParentTask == weekOfSubTask;
    }

    /**
    *  获取同一周的日期
    */
    public static Date getSameWeekDate(Date parentTaskStartDate, List<Date> allSubTriggerTimes) {
        if (Objects.isNull(parentTaskStartDate)|| CollectionUtils.isEmpty(allSubTriggerTimes)) {
            return null;
        }
        for (Date subTriggerTime : allSubTriggerTimes) {
            if (isSameWeek(parentTaskStartDate, subTriggerTime)) {
                return subTriggerTime;
            }
        }
        return null;
    }

    public static boolean isSameMonth(Date parentTaskStartDate, Date subTaskStartDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parentTaskStartDate);
        int monthOfParentTask = calendar.get(Calendar.MONTH);
        calendar.setTime(subTaskStartDate);
        int monthOfSubTask = calendar.get(Calendar.MONTH);
        return monthOfParentTask == monthOfSubTask;
    }

    public static Date getSameMonthDate(Date parentTaskStartDate, List<Date> allSubTriggerTimes) {
        if (Objects.isNull(parentTaskStartDate)|| CollectionUtils.isEmpty(allSubTriggerTimes)) {
            return null;
        }
        for (Date subTriggerTime : allSubTriggerTimes) {
            if (isSameMonth(parentTaskStartDate, subTriggerTime)) {
                return subTriggerTime;
            }
        }
        return null;
    }

    public static boolean isInDispatchRange(SupProjectTask projectTask, SupProject project) {
        int sysCurrYear = TimeUtil.getSysCurrYear();
        Date projectStartDate = project.getStartDate();
        Date projectEndDate = project.getEndDate();
        Date taskStartDate = projectTask.getStartDate();
        int startYear = DateUtils.toCalendar(taskStartDate).get(Calendar.YEAR);
        if (startYear > sysCurrYear) {
            return false;
        }
        boolean afterOrEqualStart = taskStartDate.compareTo(projectStartDate) >= 0;
        if (projectEndDate == null) {
            return afterOrEqualStart;
        }
        boolean beforeOrEqualEnd = taskStartDate.compareTo(projectEndDate) <= 0;
        return afterOrEqualStart && beforeOrEqualEnd;
    }

    public static Date adjustProjectStartDate(Date projectStartDate, int currYear) {
        int projectStartYear = DateUtils.toCalendar(projectStartDate).get(Calendar.YEAR);
        if (projectStartYear > currYear) {
            return null;
        }
        return projectStartYear < currYear ? TimeUtil.formatDate(currYear + SupProjectConstant.YEAR_FIRST_DAY, GlobalConstant.TIME_FORMAT_TEN) : projectStartDate;
    }

    public static Date adjustProjectEndDate(Date projectEndDate, int currYear) {
        if (projectEndDate == null) {
            return TimeUtil.formatDate(currYear + SupProjectConstant.YEAR_LAST_DAY, GlobalConstant.TIME_FORMAT_TEN);
        }
        int projectEndYear = DateUtils.toCalendar(projectEndDate).get(Calendar.YEAR);
        if (projectEndYear < currYear) {
            return null;
        }
        return projectEndYear > currYear ? TimeUtil.formatDate(currYear + SupProjectConstant.YEAR_LAST_DAY, GlobalConstant.TIME_FORMAT_TEN) : projectEndDate;
    }

    public static List<SupProjectTask> filterRootRepeatTasks(List<SupProjectTask> allRepeatTasks) {
        return allRepeatTasks.stream()
                .filter(task -> SupProjectConstant.SUP_PROJECT_TASK_PARENT_ID_ZERO.equals(task.getParentId()))
                .collect(Collectors.toList());
    }

    public static TaskCreateDTO buildTaskCreateDTO(SupProjectTask projectTask, SupProject project, Long parentTaskId, Integer requireApprove){
        TaskCreateDTO taskCreateDTO = new TaskCreateDTO();
        taskCreateDTO.setProjectId(project.getId());
        taskCreateDTO.setProjectName(project.getTitle());
        taskCreateDTO.setType(project.getType());
        taskCreateDTO.setParentId(parentTaskId);
        taskCreateDTO.setSourceDescription(projectTask.getSourceDescription());
        taskCreateDTO.setBrief(projectTask.getTaskBrief());
        taskCreateDTO.setTaskRequirements(projectTask.getTaskRequirements());
        taskCreateDTO.setAttachmentRequirements(projectTask.getAttachmentRequirements());
        taskCreateDTO.setPriority(projectTask.getPriority());
        taskCreateDTO.setRequireApprove(requireApprove);
        return taskCreateDTO;
    }

    public static Date getSameQuarterDate(Date parentTaskStartDate, List<Date> allSubTriggerTimes) {
        if (Objects.isNull(parentTaskStartDate)|| CollectionUtils.isEmpty(allSubTriggerTimes)) {
            return null;
        }
        for (Date subTriggerTime : allSubTriggerTimes) {
            if (isSameQuarter(parentTaskStartDate, subTriggerTime)) {
                return subTriggerTime;
            }
        }
        return null;
    }

    private static boolean isSameQuarter(Date parentTaskStartDate, Date subTriggerTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parentTaskStartDate);
        int quarterOfParentTask = calendar.get(Calendar.MONTH) / QUARTER_MONTH_NUM + 1;
        calendar.setTime(subTriggerTime);
        int quarterOfSubTask = calendar.get(Calendar.MONTH) / QUARTER_MONTH_NUM + 1;
        return quarterOfParentTask == quarterOfSubTask;
    }

    public static Date getSameHalfYearDate(Date parentTaskStartDate, List<Date> allSubTriggerTimes) {
        if (Objects.isNull(parentTaskStartDate)|| CollectionUtils.isEmpty(allSubTriggerTimes)) {
            return null;
        }
        for (Date subTriggerTime : allSubTriggerTimes) {
            if (isSameHalfYear(parentTaskStartDate, subTriggerTime)) {
                return subTriggerTime;
            }
        }
        return null;
    }

    private static boolean isSameHalfYear(Date parentTaskStartDate, Date subTriggerTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parentTaskStartDate);
        int halfYearOfParentTask = calendar.get(Calendar.MONTH) / HALF_YEAR_MONTH_NUM + 1;
        calendar.setTime(subTriggerTime);
        int halfYearOfSubTask = calendar.get(Calendar.MONTH) / HALF_YEAR_MONTH_NUM + 1;
        return halfYearOfParentTask == halfYearOfSubTask;
    }

    public static Date getSameYearDate(Date parentTaskStartDate, List<Date> allSubTriggerTimes) {
        if (Objects.isNull(parentTaskStartDate)|| CollectionUtils.isEmpty(allSubTriggerTimes)) {
            return null;
        }
        for (Date subTriggerTime : allSubTriggerTimes) {
            if (isSameYear(parentTaskStartDate, subTriggerTime)) {
                return subTriggerTime;
            }
        }
        return null;
    }

    private static boolean isSameYear(Date parentTaskStartDate, Date subTriggerTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parentTaskStartDate);
        int yearOfParentTask = calendar.get(Calendar.YEAR);
        calendar.setTime(subTriggerTime);
        int yearOfSubTask = calendar.get(Calendar.YEAR);
        return yearOfParentTask == yearOfSubTask;
    }

}
