package com.sinitek.bnzg.supervision.project.enumerate;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
public enum SubProjectWeekEnum {


    MONDAY("周一", 2),

    TUESDAY("周二", 3),

    WEDNESDAY("周三", 4),

    THURSDAY("周四", 5),

    FRIDAY("周五", 6),

    SATURDAY("周六", 7),

    SUNDAY("周日", 1);

    private final String name;
    private final int value;

    SubProjectWeekEnum (String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public static SubProjectWeekEnum getByValue(Integer value) {
        for (SubProjectWeekEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }


}
