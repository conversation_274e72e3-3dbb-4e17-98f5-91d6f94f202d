package com.sinitek.bnzg.supervision.project.enumerate;

/**
 *
 * 日期类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
public enum SupProjectDayTypeEnum {

    NATURAL_DAY(1,"自然日"),
    WORK_DAY(2,"工作日");

    private Integer code;

    private String desc;

    SupProjectDayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SupProjectDayTypeEnum getByCode(Integer code) {
        for (SupProjectDayTypeEnum item : SupProjectDayTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
