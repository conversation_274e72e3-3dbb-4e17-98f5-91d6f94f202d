package com.sinitek.bnzg.supervision.project.approval.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@Data
@ApiModel(value = "项目审批-结果DTO")
public class SupProjectApprovalResultDTO {

    @ApiModelProperty("是否已经处理完成")
    private Boolean dealFlag;

    @ApiModelProperty("处理/查看url")
    private String url;

    @ApiModelProperty("审批人/处理人")
    private String operatorId;
}
