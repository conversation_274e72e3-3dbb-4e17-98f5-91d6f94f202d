package com.sinitek.bnzg.supervision.project.auth.controller;

import com.sinitek.bnzg.supervision.project.auth.service.ISupProjectAuthService;
import io.swagger.annotations.Api;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project/auth")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "督办系统 - 项目权限")
public class SupProjectAuthController {

    private ISupProjectAuthService supProjectAuthService;
}
