package com.sinitek.bnzg.supervision.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@Data
@ApiModel(value = "项目流转记录-返回DTO")
@EqualsAndHashCode(callSuper = true)
public class SupProjectFlowLogResultDTO extends SupProjectFlowLogBaseDTO{

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "任务派发成功数量")
    private Integer dispatchSuccessCount = 0;

    @ApiModelProperty(value = "任务派发失败数量")
    private Integer dispatchFailCount = 0;

    @ApiModelProperty(value = "任务派发成功列表")
    private List<SupProjectTaskDispatchResultDTO> dispatchSuccessList;

    @ApiModelProperty(value = "任务派发失败列表")
    private List<SupProjectTaskDispatchResultDTO> dispatchFailList;

}
