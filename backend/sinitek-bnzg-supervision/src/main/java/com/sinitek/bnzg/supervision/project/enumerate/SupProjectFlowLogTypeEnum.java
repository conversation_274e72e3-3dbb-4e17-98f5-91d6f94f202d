package com.sinitek.bnzg.supervision.project.enumerate;

/**
 * <AUTHOR>
 * @date 2025/1/7
 */
public enum SupProjectFlowLogTypeEnum {

    AUTOMATIC_TASK_DISTRIBUTION("派发任务", 1),
    PROJECT_APPROVAL("立项审批", 2),
    SUBMIT_PROJECT_APPLICATION("提交立项申请", 3);


    private final Integer value;
    private final String name;


    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    SupProjectFlowLogTypeEnum(final String name, final Integer value) {
        this.name = name;
        this.value = value;
    }

    public static SupProjectFlowLogTypeEnum getByValue(Integer value) {
        for (SupProjectFlowLogTypeEnum typeEnum : SupProjectFlowLogTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

}
