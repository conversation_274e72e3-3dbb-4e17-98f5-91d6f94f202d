package com.sinitek.bnzg.supervision.project.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目任务依赖关系 Entity
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_task_link")
@ApiModel(value = "项目任务依赖关系-实体")
public class SupProjectTaskLink extends BaseEntity {

    /**
     * 项目任务id
     */
    @ApiModelProperty(value = "项目任务id")
    private Long projectTaskId;

    /**
     * 关联的项目前置任务id
     */
    @ApiModelProperty(value = "关联的项目前置任务id")
    private Long linkPreProjectTaskId;

}
