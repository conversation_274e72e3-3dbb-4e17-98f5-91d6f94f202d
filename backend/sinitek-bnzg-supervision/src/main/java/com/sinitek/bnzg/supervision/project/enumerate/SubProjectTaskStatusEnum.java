package com.sinitek.bnzg.supervision.project.enumerate;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public enum SubProjectTaskStatusEnum {

    NEW(1, "新增"),
    EDIT(2, "编辑"),
    DELETE(3, "删除"),
    EFFECTIVE(4, "已生效");

    private Integer code;

    private String name;

    SubProjectTaskStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SubProjectTaskStatusEnum statusEnum : SubProjectTaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
