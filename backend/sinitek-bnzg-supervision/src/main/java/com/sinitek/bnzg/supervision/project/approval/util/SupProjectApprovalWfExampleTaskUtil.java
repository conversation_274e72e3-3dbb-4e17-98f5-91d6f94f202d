package com.sinitek.bnzg.supervision.project.approval.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sinitek.bnzg.supervision.project.approval.entity.SupProjectApproval;
import com.sinitek.bnzg.supervision.project.approval.enumerate.SupProjectApprovalTypeEnum;
import com.sinitek.bnzg.supervision.project.dao.SupProjectDAO;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.cloud.workflow.enumerate.WorkflowStepOwnerStatus;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.workflow.entity.WfExampletask;
import com.sinitek.sirm.workflow.service.IWfExampletaskService;
import com.sinitek.sirm.workflow.service.claim.ExampleTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/23
 */
@Slf4j
@Component
public class SupProjectApprovalWfExampleTaskUtil {

    @Autowired
    private IWfExampletaskService wfExampletaskService;

    @Autowired
    private SupProjectDAO supProjectDAO;

    @Transactional(rollbackFor = Exception.class)
    public void createExampleTask(SupProjectApproval approval) {
        ExampleTaskQuery query = new ExampleTaskQuery();
        query.setSourceId(approval.getId());
        query.setSourceEntity(SupProjectApproval.ENTITY_NAME);
        List<WfExampletask> wfExampletasks = wfExampletaskService.findExampleTask(
                query);

        if (CollUtil.isEmpty(wfExampletasks)) {

            // 生成待办任务
            WfExampletask exampleTask = new WfExampletask();
            exampleTask.setSourceEntity(SupProjectApproval.ENTITY_NAME);
            exampleTask.setSourceId(approval.getId());
            // 处理人id
            exampleTask.setDealerId(approval.getApproverId());
            // 任务发布人id
            exampleTask.setOrginerId(CurrentUserFactory.getOrgId());
            // 待处理
            exampleTask.setStatus(WorkflowStepOwnerStatus.WF_OWNER_PEND.getEnumItemValue());
            exampleTask.setStartTime(new Date());
            String opTimeStr = DateUtil.format(new Date(), GlobalConstant.TIME_FORMAT_THIRTEEN);
            Integer type = approval.getOperateType();
            String typeName = SupProjectApprovalTypeEnum.getName(type);
            SupProject supProject = supProjectDAO.getById(approval.getProjectId());
            String title = supProject.getTitle();
            exampleTask.setRemarks("项目管理");
            exampleTask.setProcessName("项目审批");
            exampleTask.setProcessStepName("《" + title + "》"+ typeName + "审批");
            String format = String.format(
                    "请处理%s于%s提交的《%s》项目%s审批",
                    CurrentUserFactory.getOrgName(),
                    opTimeStr,
                    title,
                    typeName);
            exampleTask.setDescription(format);
            // 优先级
            exampleTask.setSort(0);
            this.wfExampletaskService.saveWfExampleTask(exampleTask);
        }else{
            List<Long> wfTaskIds = wfExampletasks.stream().map(WfExampletask::getObjId)
                    .collect(Collectors.toList());
            log.info("项目审批[{}]创建,存在待办任务 {} ,不再重复创建待办任务", approval.getId(),
                    wfTaskIds);
        }

    }


    /**
     * 处理督办任务待办任务
     * @param approval
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitExampleTask(SupProjectApproval approval) {
        ExampleTaskQuery query = new ExampleTaskQuery();
        query.setSourceId(approval.getId());
        query.setSourceEntity(SupProjectApproval.ENTITY_NAME);
        List<WfExampletask> wfExampletasks = wfExampletaskService.findExampleTask(
                query);
        if (CollUtil.isNotEmpty(wfExampletasks)) {
            int size = wfExampletasks.size();
            log.info(
                    "根据sourceid:[{}],sourceEntity:[{}]找到[{}]条待办任务",
                    approval.getId(), SupProjectApproval.ENTITY_NAME, size);
            wfExampletasks.forEach(dto -> {
                Long objId = dto.getObjId();
                Integer oldStatus = dto.getStatus();
                if (Objects.equals(oldStatus,
                        WorkflowStepOwnerStatus.WF_OWNER_PROCESS.getEnumItemValue())) {
                    log.info(
                            "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为已完成,无需更新",
                            approval.getId(), SupProjectApproval.ENTITY_NAME, objId);
                } else {
                    // 结束日期
                    dto.setEndTime(new Date());
                    // 处理完成
                    dto.setStatus(
                            WorkflowStepOwnerStatus.WF_OWNER_PROCESS.getEnumItemValue());
                    wfExampletaskService.saveWfExampleTask(dto);
                    log.info(
                            "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为[{}]," +
                                    "当前审批任务已提交,将待办任务更新为已完成",
                            approval.getId(), SupProjectApproval.ENTITY_NAME, objId, oldStatus);
                }
            });
        }else {
            log.warn(
                    "审批完成时,根据sourceid:[{}],sourceEntity:[{}]找不到对应的待办任务",
                    approval.getId(), SupProjectApproval.ENTITY_NAME);
        }
    }
}
