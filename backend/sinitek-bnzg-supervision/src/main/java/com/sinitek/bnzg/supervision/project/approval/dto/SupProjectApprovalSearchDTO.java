package com.sinitek.bnzg.supervision.project.approval.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "任务督办-项目审批-分页查询DTO")
public class SupProjectApprovalSearchDTO extends PageDataParam {

    @ApiModelProperty(value = "类型 1 监管报送, 2 专项工作, 3 定期检视, 4 监管评级")
    private Integer type;

    @ApiModelProperty(value = "项目标题,支持多关键字筛选，通过空格隔开")
    private String title;

    @ApiModelProperty(value = "上报主体枚举值集合")
    private List<Integer> reportSubjects;

    @ApiModelProperty(value = "任务标签id集合")
    private List<Long> tagIds;

    @ApiModelProperty(value = "所属部门id集合")
    private List<String> depIds;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    @ApiModelProperty(value = "审批结果 1通过 0驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "状态。1： 已处理  0：待处理")
    private Integer status;

    @ApiModelProperty(value = "审批人id")
    private String approverId;
}
