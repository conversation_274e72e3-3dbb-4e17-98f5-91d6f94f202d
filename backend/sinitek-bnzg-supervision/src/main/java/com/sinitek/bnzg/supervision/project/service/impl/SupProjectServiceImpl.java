package com.sinitek.bnzg.supervision.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParamDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectDepIdsResultDTO;
import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.doc.constant.SupDocumentConstant;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentBaseDTO;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentEditAndUpdateParamDTO;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentSaveParamDTO;
import com.sinitek.bnzg.supervision.doc.service.ISupDocumentService;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalSaveDTO;
import com.sinitek.bnzg.supervision.project.approval.enumerate.SupProjectApprovalTypeEnum;
import com.sinitek.bnzg.supervision.project.approval.service.ISupProjectApprovalService;
import com.sinitek.bnzg.supervision.project.auth.annotation.SupProjectAuthFilter;
import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;
import com.sinitek.bnzg.supervision.project.constant.SupProjectConstant;
import com.sinitek.bnzg.supervision.project.dao.*;
import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.entity.*;
import com.sinitek.bnzg.supervision.project.enumerate.*;
import com.sinitek.bnzg.supervision.project.message.SupProjectMessage;
import com.sinitek.bnzg.supervision.project.po.SupProjectRemindConfigPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectReportSubjectPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectSearchParamPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectSearchResultPO;
import com.sinitek.bnzg.supervision.project.service.ISupProjectFlowLogService;
import com.sinitek.bnzg.supervision.project.service.ISupProjectService;
import com.sinitek.bnzg.supervision.project.service.ISupProjectTaskService;
import com.sinitek.bnzg.supervision.project.support.SupProjectSupport;
import com.sinitek.bnzg.supervision.project.util.SupProjectRemindConfigUtil;
import com.sinitek.bnzg.supervision.project.util.SupProjectUtil;
import com.sinitek.bnzg.supervision.task.dto.TaskCreateDTO;
import com.sinitek.bnzg.supervision.task.dto.TaskParticipantBaseDTO;
import com.sinitek.bnzg.supervision.task.enumerate.TaskProgress;
import com.sinitek.bnzg.supervision.task.log.status.util.SupTaskProgressChangeEventPublishUtil;
import com.sinitek.bnzg.supervision.task.po.SupTagResultPO;
import com.sinitek.bnzg.supervision.task.service.ITaskService;
import com.sinitek.data.mybatis.base.AbstractCommonBaseEntity;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.message.cache.MessageCache;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.um.RequestUser;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.TimeUtil;
import com.sinitek.sirm.common.web.RequestContext;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.org.entity.Department;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import com.sinitek.spirit.businlogger.entity.BusinLogger;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import static com.sinitek.bnzg.supervision.doc.constant.SupDocTypeConstant.PROJECT_ATTACHMENT;
import static com.sinitek.bnzg.supervision.project.constant.SupProjectConstant.PROJECT_MANAGER_MODULE_NAME;
import static com.sinitek.bnzg.supervision.project.constant.SupProjectConstant.PROJECT_MANAGER_OPERATE_TYPE;
import static com.sinitek.bnzg.supervision.project.enumerate.SubProjectRepeatTypeEnum.NOT_REPEAT;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Service
@Setter(onMethod = @__({@Autowired}))
@Slf4j
public class SupProjectServiceImpl implements ISupProjectService {

    private SupProjectDAO projectDAO;

    private SupProjectRemindConfigDAO projectRemindConfigDAO;

    private SupProjectReportSubjectDAO projectReportSubjectDAO;

    private SupProjectTagRelaDAO projectTagRelaDAO;

    private IEnumService enumService;

    private SupProjectRemindConfigDAO remindConfigDAO;

    private IOrgService orgService;

    private ITaskService taskService;

    private ISupProjectTaskService projectTaskService;

    private ISupProjectApprovalService projectApprovalService;

    private ISupDocumentService documentService;

    private IAttachmentService attachmentService;

    private ISupProjectService self;

    private ISupProjectFlowLogService supProjectFlowLogService;

    private SupProjectSupport projectSupport;

    private SupProjectTaskDispatchDAO projectTaskDispatchDAO;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveOrUpdate(SupProjectSaveDTO dto) {
        //检查标题是否重复
        checkTitleExist(dto.getTitle(),dto.getId());
        //检查项目信息
        SupProjectUtil.checkProjectFields(dto);
        SupProject supProject = new SupProject();
        if (IdUtil.isDataId(dto.getId())){
            supProject = projectDAO.getById(dto.getId());
            SupProjectUtil.checkProjectExist(supProject,dto.getId());
            SupProjectUtil.checkProjectEditContent(supProject,dto);
            // 重置所有任务重复频率
            if (!Objects.equals(dto.getRepeatType(),supProject.getRepeatType())){
                projectTaskService.resetTaskRepeatRule(dto.getId());
            }
            boolean syncTag = CommonBooleanEnum.isTrue(dto.getSyncTagFlag());
            if (syncTag){
                List<SupTagResultPO> oldTags = projectTagRelaDAO.findByProjectId(dto.getId());
                projectTaskService.syncProjectTaskTag(dto.getId(),oldTags,dto.getTagIds());
            }
            //todo 更变操作也可以进行编辑，后续补充
        }
        BeanUtils.copyProperties(dto,supProject);
        supProject.setStatus(SupProjectStatusEnum.DRAFT.getValue());
        projectDAO.saveOrUpdate(supProject);
        Long projectId = supProject.getId();
        // 处理项目提醒规则
        projectRemindConfigDAO.deleteByProjectId(projectId);
        projectRemindConfigDAO.batchSaveProjectRemindConfig(dto.getRemindConfigs(),projectId);
        // 处理项目上报主体
        projectReportSubjectDAO.deleteByProjectId(projectId);
        projectReportSubjectDAO.batchSaveProjectReportSubject(dto.getReportSubjects(),projectId);
        // 处理项目任务标签
        projectTagRelaDAO.deleteByProjectId(projectId);
        projectTagRelaDAO.batchSaveProjectTagRela(dto.getTagIds(),projectId);
        // 处理项目附件
        UploadDTO projectUpload = dto.getProjectUpload();
        if (ObjectUtil.isNotEmpty(projectUpload) && (CollectionUtils.isNotEmpty(projectUpload.getUploadFileList()) || CollectionUtils.isNotEmpty(projectUpload.getRemoveFileList()))) {
            SupDocumentSaveParamDTO supDocumentSaveParamDTO = SupProjectUtil.buildSupDocumentSaveParamDTO(supProject.getId(),SupProjectConstant.SUP_PROJECT_SOURCE_NAME,supProject.getId(),
                    supProject.getDepId(),projectUpload, PROJECT_ATTACHMENT);
            List<SupDocumentBaseDTO> docList = documentService.findExistsByTaskIdAndDocType(supProject.getId(), supDocumentSaveParamDTO.getDocType());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(docList)) {
                // 更新已有文档
                SupDocumentEditAndUpdateParamDTO updateParamDTO = createUpdateParamDTO(
                        docList.get(0), projectUpload, supProject.getDepId(), CurrentUserFactory.getOrgId());
                documentService.updateDoc(updateParamDTO);
            } else {
                // 保存新文档
                documentService.saveDoc(supDocumentSaveParamDTO);
            }
        }
        return supProject.getId();
    }

    /**
     * 创建更新文档的参数
     */
    private SupDocumentEditAndUpdateParamDTO createUpdateParamDTO(
            SupDocumentBaseDTO existingDoc, UploadDTO upload,String depId, String operatorId) {
        SupDocumentEditAndUpdateParamDTO updateParamDTO = new SupDocumentEditAndUpdateParamDTO();
        updateParamDTO.setId(existingDoc.getId());
        updateParamDTO.setUpload(upload);
        updateParamDTO.setSourceId(existingDoc.getSourceId());
        updateParamDTO.setSourceName(existingDoc.getSourceName());
        updateParamDTO.setDeptId(depId);
        updateParamDTO.setOperatorId(operatorId);
        updateParamDTO.setOpTime(new Date());
        updateParamDTO.setAllowClearUploadFile(true);
        return updateParamDTO;
    }


    @Override
    public void checkTitleExist(String title, Long ignoreId) {
        QueryWrapper<SupProject> supProjectQueryWrapper = new QueryWrapper<>();
        supProjectQueryWrapper.lambda().eq(SupProject::getTitle, title);
        SupProject supProject = projectDAO.getOne(supProjectQueryWrapper);
        if (Objects.isNull(supProject)) {
            return;
        }
        if (!(IdUtil.isDataId(ignoreId) && ignoreId.equals(supProject.getId()))) {
            log.error("项目标题:[{}]已存在", title);
            throw new BussinessException(SupProjectMessage.TITLE_ALREADY_EXISTS, title);
        }
    }

    @Override
    @SupProjectAuthFilter(rightType = SupProjectAuthConstant.RIGHT_TYPE_EDIT, projectId = "#id")
    public SupProjectDetailResultDTO getById(Long id) {
        SupProjectDetailResultDTO supProjectDetailDTO = new SupProjectDetailResultDTO();
        SupProject supProject = projectDAO.getById(id);
        BeanUtils.copyProperties(supProject, supProjectDetailDTO);
        //设置项目类型
        supProjectDetailDTO.setTypeName(Objects.nonNull(SupProjectTypeEnum.getByValue(supProjectDetailDTO.getType())) ? SupProjectTypeEnum.getByValue(supProjectDetailDTO.getType()).getName() : "");
        //设置上报主体
        List<SupProjectReportSubjectPO> reportSubjectPOList =  projectReportSubjectDAO.findByProjectId(id);
        if (CollectionUtils.isNotEmpty(reportSubjectPOList)){
            Map<String, String> reportSubjectMap = this.enumService.getSirmEnumByCataLogAndType(
                    SupConstant.DEFAULT_CATALOG, SupConstant.SUP_REPORT_SUBJECT);
            List<Integer> reportSubjects = reportSubjectPOList.stream().map(SupProjectReportSubjectPO::getSubjectVal).collect(Collectors.toList());
            String reportSubjectNames = Optional.ofNullable(reportSubjects)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(subjectVal -> MapUtils.getString(reportSubjectMap, subjectVal, ""))
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.joining(","));
            supProjectDetailDTO.setReportSubjects(reportSubjects);
            supProjectDetailDTO.setReportSubjectNames(reportSubjectNames);
        }
        //设置任务标签
        List<SupTagResultPO> supTagResultPOS = projectTagRelaDAO.findByProjectId(supProjectDetailDTO.getId());
        if (CollectionUtils.isNotEmpty(supTagResultPOS)){
            List<SupProjectTagResultDTO> supTagResultDTOS = supTagResultPOS.stream().map(supTagResultPO -> {
                SupProjectTagResultDTO supTagResultDTO = new SupProjectTagResultDTO();
                supTagResultDTO.setTagId(supTagResultPO.getTagId());
                supTagResultDTO.setTagName(supTagResultPO.getName());
                return supTagResultDTO;
            }).collect(Collectors.toList());
            supProjectDetailDTO.setTags(supTagResultDTOS);
            supProjectDetailDTO.setTagIds(supTagResultPOS.stream().map(SupTagResultPO::getTagId).collect(Collectors.toList()));
        }
        supProjectDetailDTO.setRepeatTypeName(Objects.nonNull(supProjectDetailDTO.getRepeatType()) ? SubProjectRepeatTypeEnum.getByValue(supProjectDetailDTO.getRepeatType()).getName() : "");
        //设置提醒时间
        List<SupProjectRemindConfigPO> remindConfigPOs = remindConfigDAO.findByProjectId(supProjectDetailDTO.getId());
        if (CollectionUtils.isNotEmpty(remindConfigPOs)){
            supProjectDetailDTO.setRemindConfigs(remindConfigPOs.stream().map(SupProjectRemindConfigUtil::makeSearchResultPO2DTO).collect(Collectors.toList()));
            List<SupProjectRemindConfigBaseDTO> startRemindConfigPOs = supProjectDetailDTO.getRemindConfigs().stream()
                    .filter(remindConfig -> remindConfig.getDateRemindType().equals(SupProjectDateRemindTypeEnum.START_DATE.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(startRemindConfigPOs)){
                supProjectDetailDTO.setStartRemindTimeDesc("开始日期提醒");
            }
            List<SupProjectRemindConfigBaseDTO> endRemindConfigPOs = supProjectDetailDTO.getRemindConfigs().stream()
                    .filter(remindConfig -> remindConfig.getDateRemindType().equals(SupProjectDateRemindTypeEnum.END_DATE.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(endRemindConfigPOs)){
                StringBuilder endRemindTimeDesc = new StringBuilder();
                for (SupProjectRemindConfigBaseDTO endRemindConfig : endRemindConfigPOs) {
                    String dayTypeName = Objects.nonNull(endRemindConfig.getDayType()) ? SupProjectDayTypeEnum.getByCode(endRemindConfig.getDayType()).getDesc() : "";
                    endRemindTimeDesc.append("结束日期前").append(endRemindConfig.getAdvanceDays()).append("个").append(dayTypeName).append("提醒").append("\n");
                }
                supProjectDetailDTO.setEndRemindTimeDesc(endRemindTimeDesc.toString());
            }
        }
        //设置提醒方式
        Integer remindType = supProjectDetailDTO.getRemindType();
        if (Objects.nonNull(remindType)){
            Map<Integer, String> sendModeMap = MessageCache.getSendModeMap();
            String remindTypeName = sendModeMap.entrySet().stream()
                    .filter(entry -> (remindType & entry.getKey()) == entry.getKey())
                    .map(Map.Entry::getValue)
                    .collect(Collectors.joining("，"));
            supProjectDetailDTO.setRemindTypeName(remindTypeName);
        }
        //设置节假日策略
        Integer holidayStrategy = supProjectDetailDTO.getHolidayStrategy();
        supProjectDetailDTO.setHolidayStrategyName(Objects.nonNull(holidayStrategy) ? SubProjectHolidayStrategyEnum.getByCode(holidayStrategy).getName() : "");
        //设置审批人,所属部门
        String approveId = supProjectDetailDTO.getApproverId();
        String depId = supProjectDetailDTO.getDepId();
        Map<String, String> orgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(Arrays.asList(approveId, depId));
        supProjectDetailDTO.setApproverName(StringUtils.isNotEmpty(approveId) ? MapUtils.getString(orgIdAndNameMap, approveId, "") : "");
        supProjectDetailDTO.setDepName(StringUtils.isNotEmpty(depId) ? MapUtils.getString(orgIdAndNameMap, depId, "") : "");
        //设置项目状态
        supProjectDetailDTO.setStatusName(Objects.nonNull(SupProjectStatusEnum.getByValue(supProjectDetailDTO.getStatus())) ? SupProjectStatusEnum.getByValue(supProjectDetailDTO.getStatus()).getName() : "");
        //设置项目附件
        SupDocumentBaseDTO threadLatestDoc = documentService.getThreadLatestDoc(id, id, PROJECT_ATTACHMENT);
        if (Objects.nonNull(threadLatestDoc)){
            supProjectDetailDTO.setAttachmentId(threadLatestDoc.getId());
        }
        Boolean syncTagFlag = CommonBooleanEnum.isTrue(supProjectDetailDTO.getSyncTagFlag());
        supProjectDetailDTO.setSyncTagFlagName(syncTagFlag ? "是" : "否");
        return supProjectDetailDTO;
    }


    @Override
    @SupProjectAuthFilter
    public IPage<SupProjectSearchResultDTO> search(SupProjectSearchParamDTO dto) {
        SupProjectSearchParamPO param =  SupProjectUtil.makeSearchParamDTO2PO(dto);
        Page<SupProjectSearchResultPO> page ;
        if (AbstractCommonBaseEntity.getUpdateTimeStampName().equals(param.getOrderName())){
            page = param.buildPage();
        }else{
            page = param.buildPage(true);
        }
        IPage<SupProjectSearchResultPO> result = this.projectDAO.search(page,param);
        List<SupProjectSearchResultPO> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return new Page<>();
        }
        //收集所有projectIds
        List<Long> projectIds = records.stream().map(SupProjectSearchResultPO::getId).collect(Collectors.toList());
        //查询上报主体
        List<SupProjectReportSubjectPO> projectReportSubjects = projectReportSubjectDAO.findByProjectIds(projectIds);
        Map<Long, List<SupProjectReportSubjectPO>> reportSubjectPOMap = projectReportSubjects.stream()
                .collect(Collectors.groupingBy(SupProjectReportSubjectPO::getProjectId));
        //查询任务标签
        List<SupProjectTagRela> supProjectTagRelaList = projectTagRelaDAO.findByProjectIds(projectIds);
        Map<Long, List<SupProjectTagRela>> projectTagRelaMap = supProjectTagRelaList.stream()
                .collect(Collectors.groupingBy(SupProjectTagRela::getProjectId));
        result.getRecords().stream()
                .filter(Objects::nonNull)
                .forEach(record -> {
                    //设置上报主体
                    List<SupProjectReportSubjectPO> supProjectReportSubjectPOS = reportSubjectPOMap.get(record.getId());
                    if (CollectionUtils.isNotEmpty(supProjectReportSubjectPOS)) {
                        record.setReportSubjects(supProjectReportSubjectPOS.stream().
                                map(SupProjectReportSubjectPO::getSubjectVal).
                                collect(Collectors.toList()));
                    }
                    //设置任务标签
                    List<SupProjectTagRela> projectTagRelaList =  projectTagRelaMap.get(record.getId());
                    if (CollectionUtils.isNotEmpty(projectTagRelaList)){
                        record.setTagIds(projectTagRelaList.stream()
                                .map(SupProjectTagRela::getTagId)
                                .collect(Collectors.toList()));
                    }
                });
        return result.convert(SupProjectUtil::makeSupProjectSearchResultPO2DTO);
    }

    @Override
    public List<SupProject> findProjectsByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        return this.projectDAO.listByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(SupProjectSubmitDTO dto) {
        SupProject submitProject = this.projectDAO.getById(dto.getId());
        SupProjectUtil.checkProjectExist(submitProject, dto.getId());
        SupProjectUtil.checkProjectSubmit(submitProject);
        List<SupProjectTask> projectTasks = projectTaskService.findByProjectId(dto.getId());
        List<SupProjectTask> tasks = projectTasks.stream()
                .filter(task -> Objects.isNull(task.getRepeatType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tasks)){
           log.error("项目id:[{}]，存在未配置开始结束日期任务，请配置完整后提交", dto.getId());
           throw new BussinessException(SupProjectMessage.PROJECT_TASK_NOT_CONFIGURED, dto.getId());
        }
        Boolean approveFlag = CommonBooleanEnum.isTrue(dto.getApproveFlag());
        // 生成立项申请流转信息
        SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO = new SupProjectFlowLogSaveDTO();
        supProjectFlowLogSaveDTO.setProjectId(dto.getId());
        supProjectFlowLogSaveDTO.setDealTime(new Date());
        RequestUser currentUser = RequestContext.getCurrentUser();
        supProjectFlowLogSaveDTO.setOperatorId(Objects.isNull(currentUser) ? "": currentUser.getOrgId());
        supProjectFlowLogSaveDTO.setType(SupProjectFlowLogTypeEnum.SUBMIT_PROJECT_APPLICATION.getValue());
        if (approveFlag){
            // 立项申请流转信息描述
            supProjectFlowLogSaveDTO.setBrief("操作说明：【 " + dto.getOperationDesc() + " 】；" + "【需要审批】");
            supProjectFlowLogService.saveOrUpdateSupProjectFlowLog(supProjectFlowLogSaveDTO);
            // 生成审批信息
            SupProjectApprovalSaveDTO approvalSaveDTO = SupProjectUtil.buildSupProjectApprovalSaveDTO(dto.getOperationDesc(), dto.getId(),CurrentUserFactory.getOrgId(),
                    submitProject.getApproverId(),SupProjectApprovalTypeEnum.APPROVAL.getCode());
            projectApprovalService.saveSupProjectApproval(approvalSaveDTO);
            // 更新项目状态为立项中
            submitProject.setStatus(SupProjectStatusEnum.IN_PROGRESS.getValue());
        }else {
            // 立项申请流转信息描述
            supProjectFlowLogSaveDTO.setBrief("操作说明：【 " + dto.getOperationDesc() + " 】；" + "【无需审批】");
            supProjectFlowLogService.saveOrUpdateSupProjectFlowLog(supProjectFlowLogSaveDTO);
            // 更新项目状态为已立项
            submitProject.setStatus(SupProjectStatusEnum.APPROVED.getValue());
            // 创建任务实例
            self.createTask(dto.getId());
        }

        this.projectDAO.updateById(submitProject);
        BusinLogger logger = RequestContext.log(PROJECT_MANAGER_MODULE_NAME, PROJECT_MANAGER_OPERATE_TYPE,
                "{" + RequestContext.getCurrentUser().getDisplayName() + "}" + "提交了项目[" + submitProject.getTitle() + "]" + (approveFlag ? "，需要审批" : "，无需审批"));
        if (logger != null) {
            logger.setEndTime(new Date());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createTask(Long projectId){
        SupProject project = this.projectDAO.getById(projectId);
        List<SupProjectTask> allTasks = projectTaskService.findByProjectId(projectId);
        if (CollectionUtils.isEmpty(allTasks)){
            log.info("项目id:[{}]无任务计划数据，无需生成任务实例",projectId);
            return ;
        }
        // 已开始任务实例id集合
        List<Long> startedTaskIds = new ArrayList<>();
        // 生成失败任务实例集合
        List<SupProjectTaskCreateDTO> failDispatchTaskList = new ArrayList<>();
        // 生成成功任务实例集合
        List<SupProjectTaskCreateDTO> successDispatchTaskList =  new ArrayList<>();
        if (NOT_REPEAT.getValue().equals(project.getRepeatType())){
            self.createNotRepeatTask(allTasks,project,startedTaskIds,failDispatchTaskList,successDispatchTaskList);
        } else {
            self.createRepeatTask(allTasks,project,startedTaskIds,failDispatchTaskList,successDispatchTaskList);
        }
        // 生成派发任务流转信息
        SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO = new SupProjectFlowLogSaveDTO();
        supProjectFlowLogSaveDTO.setProjectId(projectId);
        supProjectFlowLogSaveDTO.setBrief("项目自动派发");
        supProjectFlowLogSaveDTO.setDealTime(new Date());
        supProjectFlowLogSaveDTO.setOperatorId("0");
        supProjectFlowLogSaveDTO.setType(SupProjectFlowLogTypeEnum.AUTOMATIC_TASK_DISTRIBUTION.getValue());
        Long flowLogId = supProjectFlowLogService.saveSupProjectFlowLog(supProjectFlowLogSaveDTO);
        // 保存派发失败任务实例
        projectTaskDispatchDAO.batchSave(flowLogId,failDispatchTaskList);
        // 保存派发成功任务实例
        projectTaskDispatchDAO.batchSave(flowLogId,successDispatchTaskList);
        // 已开始任务实例事件抛出
        if (CollectionUtils.isNotEmpty(startedTaskIds)){
            Map<Long, Integer> idAndOldProgressMap = new HashMap<>(startedTaskIds.size());
            startedTaskIds.forEach(id -> {
                idAndOldProgressMap.put(id, TaskProgress.NOT_STARTED.getValue());
            });
            SupTaskProgressChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParamDTO.<Integer>builder()
                            .foreignKeys(startedTaskIds)
                            .oldValueMap(idAndOldProgressMap)
                            .newValue(TaskProgress.PENDING.getValue())
                            .remark("派发任务处理已开始任务实例")
                            .operatorId(CurrentUserFactory.getOrgId())
                            .opTime(TimeUtil.getSysDateAsDate())
                            .build());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createNotRepeatTask(List<SupProjectTask> allNoRepeatTasks, SupProject project, List<Long> startedTaskIds,
                                    List<SupProjectTaskCreateDTO> failDispatchTaskList, List<SupProjectTaskCreateDTO> successDispatchTaskList) {
        // 过滤符合派发范围内的根任务计划
        List<SupProjectTask> rootGenerateTasks = allNoRepeatTasks.stream()
                .filter(task -> SupProjectConstant.SUP_PROJECT_TASK_PARENT_ID_ZERO.equals(task.getParentId()))
                .filter(task -> SupProjectUtil.isInDispatchRange(task, project)).collect(Collectors.toList());
        // 过滤不符合派发范围内的根任务计划
        List<SupProjectTask> rootNoGenerateTasks = allNoRepeatTasks.stream()
                .filter(task -> SupProjectConstant.SUP_PROJECT_TASK_PARENT_ID_ZERO.equals(task.getParentId()))
                .filter(task -> !SupProjectUtil.isInDispatchRange(task, project)).collect(Collectors.toList());
        // 记录生成失败任务实例
        if (CollectionUtils.isNotEmpty(rootNoGenerateTasks)){
            for (SupProjectTask rootNoGenerateTask : rootNoGenerateTasks) {
                recordFailGenerateTask(rootNoGenerateTask,project,failDispatchTaskList,allNoRepeatTasks);
            }
        }
        if (CollectionUtils.isEmpty(rootGenerateTasks)){
            log.info("非重复项目:[{}],没有需要生成实例的根任务实例",project.getId());
            return;
        }
        List<Long> allNoRepeatTaskIds = allNoRepeatTasks.stream().map(SupProjectTask::getId).collect(Collectors.toList());
        // 查询并缓存任务相关数据
        Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap = projectSupport.queryAndCacheReportSubjects(allNoRepeatTaskIds);
        Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap = projectSupport.queryAndCacheTaskTags(allNoRepeatTaskIds);
        Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap = projectSupport.queryAndCacheTaskParticipants(allNoRepeatTaskIds);
        Map<Long, List<Attachment>> allAttachmentsMap = projectSupport.queryAndCacheAttachments(allNoRepeatTaskIds);
        rootGenerateTasks.forEach(rootTask -> self.createSubNotRepeatTasks(rootTask, project, null,allNoRepeatTasks,
                allProjectReportSubjectMap,allProjectTaskTagRelaMap,allProjectTaskParticipantMap,allAttachmentsMap,
                startedTaskIds,failDispatchTaskList,successDispatchTaskList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createSubNotRepeatTasks(SupProjectTask rootTask, SupProject project, Long parentTaskId, List<SupProjectTask> allNoRepeatTasks,
                                        Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap,
                                        Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap,
                                        Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap,
                                        Map<Long, List<Attachment>> allAttachmentsMap,
                                        List<Long> startedTaskIds,
                                        List<SupProjectTaskCreateDTO> failDispatchTaskList,
                                        List<SupProjectTaskCreateDTO> successDispatchTaskList) {
        TaskCreateDTO taskCreateDTO = SupProjectUtil.buildTaskCreateDTO(rootTask, project, parentTaskId, CommonBooleanEnum.FALSE.getValue());
        taskCreateDTO.setStartDate(rootTask.getStartDate());
        taskCreateDTO.setEndDate(rootTask.getEndDate());
        taskCreateDTO.setName(rootTask.getTaskName());
        projectSupport.setTaskParticipantsAndAttachments(taskCreateDTO, rootTask, allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap, allAttachmentsMap);
        // 生成实例任务
        Long currentTaskId;
        try {
            currentTaskId = taskService.createTask(taskCreateDTO);
            // 记录成功生成任务
            SupProjectTaskCreateDTO createDTO = new SupProjectTaskCreateDTO();
            createDTO.setTaskName(taskCreateDTO.getName());
            createDTO.setStartDate(taskCreateDTO.getStartDate());
            createDTO.setEndDate(taskCreateDTO.getEndDate());
            createDTO.setIsDispatch(CommonBooleanEnum.TRUE.getValue());
            successDispatchTaskList.add(createDTO);
            // 判断开始日期是否小于等于当前日期
            if (taskCreateDTO.getStartDate().compareTo(new Date()) <= 0) {
                startedTaskIds.add(currentTaskId);
            }
        } catch (Exception e) {
            // 记录当前失败任务
            recordFailGenerateTask(rootTask, project, failDispatchTaskList, allNoRepeatTasks);
            log.error("任务实例创建失败 任务名称:[{}] 异常信息:", rootTask.getTaskName(), e);
            // 终止当前任务分支的后续处理
            return;
        }
        // 过滤符合条件生成的子任务计划
        List<SupProjectTask> childTasks = allNoRepeatTasks.stream()
                .filter(task -> rootTask.getId().equals(task.getParentId()))
                .filter(task -> SupProjectUtil.isInDispatchRange(task, project))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(childTasks)) {
            childTasks.forEach(childTask -> self.createSubNotRepeatTasks(childTask, project, currentTaskId, allNoRepeatTasks,
                    allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap, allAttachmentsMap, startedTaskIds, failDispatchTaskList, successDispatchTaskList));
        }
        // 过滤不符合条件生成的子任务计划
        List<SupProjectTask> noChildTasks = allNoRepeatTasks.stream()
                .filter(task -> rootTask.getId().equals(task.getParentId()))
                .filter(task -> !SupProjectUtil.isInDispatchRange(task, project))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noChildTasks)) {
            for (SupProjectTask rootNoGenerateTask : noChildTasks) {
                recordFailGenerateTask(rootNoGenerateTask, project, failDispatchTaskList, allNoRepeatTasks);
            }
        }
    }

    /**
     * 记录派发失败任务
     *
     * @param rootNoGenerateTask    根级未生成任务
     * @param project               项目信息
     * @param failDispatchTaskList  派发失败任务列表，用于存储最终结果
     * @param allNoRepeatTasks      所有不重复的任务列表
     */
    private void recordFailGenerateTask(SupProjectTask rootNoGenerateTask, SupProject project,
                                        List<SupProjectTaskCreateDTO> failDispatchTaskList, List<SupProjectTask> allNoRepeatTasks) {
        // 使用 Map 存储每个父任务的子任务列表
        Map<Long, List<SupProjectTask>> childTaskMap = new HashMap<>();
        for (SupProjectTask task : allNoRepeatTasks) {
            childTaskMap.computeIfAbsent(task.getParentId(), k -> new ArrayList<>()).add(task);
        }
        // 递归处理任务
        processTask(rootNoGenerateTask, project, failDispatchTaskList, childTaskMap);

    }

    private void processTask(SupProjectTask task, SupProject project,
                             List<SupProjectTaskCreateDTO> failDispatchTaskList, Map<Long, List<SupProjectTask>> childTaskMap) {
        // 判断当前任务是否在派发范围内
        if (SupProjectUtil.isInDispatchRange(task, project)) {
            SupProjectTaskCreateDTO taskCreateDTO = new SupProjectTaskCreateDTO();
            taskCreateDTO.setTaskName(task.getTaskName());
            taskCreateDTO.setStartDate(task.getStartDate());
            taskCreateDTO.setEndDate(task.getEndDate());
            taskCreateDTO.setIsDispatch(CommonBooleanEnum.FALSE.getValue());
            failDispatchTaskList.add(taskCreateDTO);
        }
        // 获取当前任务的子任务列表
        List<SupProjectTask> childTaskList = childTaskMap.getOrDefault(task.getId(), new ArrayList<>());
        // 如果子任务列表为空，直接返回
        if (CollectionUtils.isEmpty(childTaskList)) {
            return;
        }
        // 遍历子任务列表
        for (SupProjectTask childTask : childTaskList) {
            // 递归处理子任务
            processTask(childTask, project, failDispatchTaskList, childTaskMap);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createRepeatTask(List<SupProjectTask> allRepeatTasks, SupProject project, List<Long> startedTaskIds,
                                 List<SupProjectTaskCreateDTO> failDispatchTaskList,
                                 List<SupProjectTaskCreateDTO> successDispatchTaskList) {
        int currYear = TimeUtil.getSysCurrYear();
        Date projectStartDate = SupProjectUtil.adjustProjectStartDate(project.getStartDate(), currYear);
        Date projectEndDate = SupProjectUtil.adjustProjectEndDate(project.getEndDate(), currYear);
        if (Objects.isNull(projectStartDate) || Objects.isNull(projectEndDate)) {
            log.info("项目:[{}],当前生成任务实例年份不在项目生效范围内，暂不生成实例任务", project.getId());
            return;
        }
        // 收集所有任务计划id
        List<Long> allProjectTaskIds = allRepeatTasks.stream().map(BaseEntity::getId).collect(Collectors.toList());
        // 查询并缓存任务相关数据
        Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap = projectSupport.queryAndCacheReportSubjects(allProjectTaskIds);
        Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap = projectSupport.queryAndCacheTaskTags(allProjectTaskIds);
        Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap = projectSupport.queryAndCacheTaskParticipants(allProjectTaskIds);
        Map<Long, List<Attachment>> allAttachmentsMap = projectSupport.queryAndCacheAttachments(allProjectTaskIds);
        // 处理顶级任务
        List<SupProjectTask> rootTasks = SupProjectUtil.filterRootRepeatTasks(allRepeatTasks);
        for (SupProjectTask rootTask : rootTasks) {
            projectSupport.handleRootRepeatTask(allRepeatTasks,rootTask, project, projectStartDate, projectEndDate,
                    allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap, allAttachmentsMap,
                    startedTaskIds,failDispatchTaskList, successDispatchTaskList);
        }
    }



    @Override
    @SupProjectAuthFilter(projectId = "#id")
    public SupProjectDetailResultDTO detail(Long id) {
        return self.getById(id);
    }

    @Override
    public List<SupProjectCheckResultDTO> checkSubmit(SupProjectSubmitDTO dto) {
        List<SupProjectCheckResultDTO> result = new ArrayList<>();
        List<SupProjectTask> projectTasks =  projectTaskService.findByProjectId(dto.getId());
        SupProject supProject = projectDAO.getById(dto.getId());
        if (CollectionUtils.isEmpty(projectTasks)){
            return result;
        }
        List<SupProjectTask> tasks = projectTasks.stream()
                .filter(task -> Objects.isNull(task.getRepeatType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tasks)){
            return result;
        }
        return tasks.stream().map(task -> {
            SupProjectCheckResultDTO resultDTO = new SupProjectCheckResultDTO();
            BeanUtils.copyProperties(task, resultDTO);
            resultDTO.setRepeatType(supProject.getRepeatType());
            return resultDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public SupProjectDepIdsResultDTO loadSupDepIds() {
        SupProjectDepIdsResultDTO result = new SupProjectDepIdsResultDTO();
        String orgId = CurrentUserFactory.getOrgId();
        //判断当前使用是否是管理员
        Boolean isAdmin = orgService.isAdmin(orgId);
        if (isAdmin){
            result.setLimitDepFlag(false);
            return result;
        }
        // 获取 合规部门id
        String depId = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE, BnzgSettingConstant.COMPLIANCE_DEPT_ORGID);
        if (StringUtils.isBlank(depId)) {
            result.setLimitDepFlag(true);
        }
        List<Employee> complianceEmployees = orgService.findEmployeesByOrgId(depId);
        List<String> empIds = complianceEmployees.stream().map(Employee::getId).collect(Collectors.toList());
        boolean isComplianceEmployee = empIds.contains(orgId);
        if (isComplianceEmployee) {
            result.setLimitDepFlag(false);
            return result;
        }
        result.setLimitDepFlag(true);
        List<Department> unitsByEmpId = orgService.findUnitsByEmpId(orgId);
        if (CollUtil.isNotEmpty(unitsByEmpId)) {
            List<String> posIds = unitsByEmpId.stream()
                    .map(Department::getOrgid)
                    .distinct()
                    .collect(Collectors.toList());
            result.setDeptIds(posIds);
        }else {
            List<String> deptIds = new ArrayList<>();
            deptIds.add("-1");
            result.setDeptIds(deptIds);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long id) {
        SupProjectDetailResultDTO project = this.getById(id);
        //复制项目基本信息
        SupProjectSaveDTO copyProject = new SupProjectSaveDTO();
        BeanUtils.copyProperties(project, copyProject);
        copyProject.setId(null);
        copyProject.setTitle(project.getTitle() + "-copy-" + TimeUtil.getSysDate(GlobalConstant.TIME_FORMAT_THIRTEEN));
        // 复制项目附件
        Long attachmentId = project.getAttachmentId();
        List<Attachment> attachmentList = attachmentService.findAttachmentList(SupDocumentConstant.DEFAULT_SOURCE_NAME, attachmentId);
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            UploadDTO uploadDTO = new UploadDTO();
            uploadDTO.setUploadFileList(AttachmentUtils.toNewUploadFileDtoList(attachmentList));
            copyProject.setProjectUpload(uploadDTO);
        }
        Long copyProjectId = self.saveOrUpdate(copyProject);
        // 复制项目任务计划
        projectTaskService.copyTaskByProjectId(id, copyProjectId);
    }

    private List<Integer> buildReportSubjectList(SupProjectTask projectTask, Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap) {
        List<SupProjectTaskReportSubject> projectTaskReportSubjectList = allProjectReportSubjectMap.get(projectTask.getId());
        return CollectionUtils.isNotEmpty(projectTaskReportSubjectList)?
                projectTaskReportSubjectList.stream().map(SupProjectTaskReportSubject::getSubjectVal).collect(Collectors.toList()) :
                null;
    }

    private List<Long> buildTagIdsList(SupProjectTask projectTask, Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap) {
        List<SupProjectTaskTagRela> projectTaskTagRelaList = allProjectTaskTagRelaMap.get(projectTask.getId());
        return CollectionUtils.isNotEmpty(projectTaskTagRelaList)?
                projectTaskTagRelaList.stream().map(SupProjectTaskTagRela::getTagId).collect(Collectors.toList()) :
                null;
    }

    private TaskParticipantBaseDTO buildMainParticipant(List<SupProjectTaskParticipant> mainParticipants) {
        if (CollectionUtils.isNotEmpty(mainParticipants)) {
            TaskParticipantBaseDTO mainParticipant = new TaskParticipantBaseDTO();
            mainParticipant.setDepId(mainParticipants.get(0).getDepId());
            mainParticipant.setEmpId(mainParticipants.get(0).getEmpId());
            mainParticipant.setType(mainParticipants.get(0).getType());
            return mainParticipant;
        }
        return null;
    }

    private List<TaskParticipantBaseDTO> buildAssistantParticipants(List<SupProjectTaskParticipant> assistantParticipants) {
        return CollectionUtils.isNotEmpty(assistantParticipants)?
                assistantParticipants.stream().map(assistantTask -> {
                    TaskParticipantBaseDTO taskParticipantBaseDTO = new TaskParticipantBaseDTO();
                    taskParticipantBaseDTO.setDepId(assistantTask.getDepId());
                    taskParticipantBaseDTO.setEmpId(assistantTask.getEmpId());
                    taskParticipantBaseDTO.setType(assistantTask.getType());
                    return taskParticipantBaseDTO;
                }).collect(Collectors.toList()) :
                null;
    }

    private TaskParticipantBaseDTO buildSuperviseParticipant(List<SupProjectTaskParticipant> superviseParticipants) {
        if (CollectionUtils.isNotEmpty(superviseParticipants)) {
            TaskParticipantBaseDTO taskParticipantBaseDTO = new TaskParticipantBaseDTO();
            taskParticipantBaseDTO.setDepId(superviseParticipants.get(0).getDepId());
            taskParticipantBaseDTO.setEmpId(superviseParticipants.get(0).getEmpId());
            taskParticipantBaseDTO.setType(superviseParticipants.get(0).getType());
            return taskParticipantBaseDTO;
        }
        return null;
    }

    private List<TaskParticipantBaseDTO> buildWatchingParticipant(List<SupProjectTaskParticipant> watchingParticipants) {
        return CollectionUtils.isNotEmpty(watchingParticipants)?
                watchingParticipants.stream().map(assistantTask -> {
                    TaskParticipantBaseDTO taskParticipantBaseDTO = new TaskParticipantBaseDTO();
                    taskParticipantBaseDTO.setDepId(assistantTask.getDepId());
                    taskParticipantBaseDTO.setEmpId(assistantTask.getEmpId());
                    taskParticipantBaseDTO.setType(assistantTask.getType());
                    return taskParticipantBaseDTO;
                }).collect(Collectors.toList()) :
                null;
    }

}
