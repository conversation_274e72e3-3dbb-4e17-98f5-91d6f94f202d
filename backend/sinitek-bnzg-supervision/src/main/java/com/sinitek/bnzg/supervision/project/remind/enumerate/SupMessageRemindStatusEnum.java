package com.sinitek.bnzg.supervision.project.remind.enumerate;

/**
 * <AUTHOR>
 * @date 2025/2/13
 */
public enum SupMessageRemindStatusEnum {

    SENT(1, "已发送"),
    UNSENT(0, "未发送");

    private Integer code;


    private String name;

    SupMessageRemindStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SupMessageRemindStatusEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }

}
