<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.supervision.project.auth.mapper.SupProjectAuthRangeMapper">

    <sql id="selectAuthedSupProjectIds">
        select sp.id from sup_project sp where sp.id = -1
        <if test="@org.apache.commons.collections.CollectionUtils@isNotEmpty(ids)">
            or sp.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <sql id="filterSupProjectRange" >
        <choose>
            <when test="@com.sinitek.bnzg.supervision.project.auth.util.SupProjectAuthUtil@ignoreAuthFilter('')"/>
            <otherwise>
                <bind name="ids" value="@com.sinitek.bnzg.supervision.project.auth.util.SupProjectAuthUtil@findAuthedSupProjectIdsByContext()"/>
                <choose>
                    <when test="@org.apache.commons.collections.CollectionUtils@isNotEmpty(ids)">
                        and
                        escapeVar(projectId) in (
                        <include refid="com.sinitek.bnzg.supervision.project.auth.mapper.SupProjectAuthRangeMapper.selectAuthedSupProjectIds"/>
                        )
                    </when>
                    <otherwise>
                        and escapeVar(projectId) = -1
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>



</mapper>
