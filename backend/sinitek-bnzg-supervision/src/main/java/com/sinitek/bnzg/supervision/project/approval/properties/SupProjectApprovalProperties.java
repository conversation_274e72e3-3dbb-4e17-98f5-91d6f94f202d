package com.sinitek.bnzg.supervision.project.approval.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/23
 */
@Data
@Slf4j
@Component
@ConfigurationProperties("bnzg.sup.project.auth.approval")
public class SupProjectApprovalProperties {


    /**
     * 审批处理地址
     */
    private String approvalProcessUrl = "/lowcode/form/form-render/project-approve?title=%s&id=%s&projectId=%s";

    /**
     * 审批详情查看地址
     */
    private String approvalShowUrl = "/lowcode/form/form-render/project-detail?id=%s&title=%s&approvalFlag=true";
}
