<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.supervision.project.mapper.SupProjectTagRelaMapper">

    <select id="findByProjectId" resultType="com.sinitek.bnzg.supervision.task.po.SupTagResultPO">
        select stt.tag_id ,stg.name as name  from sup_project_tag_rela stt
              left join sup_tag stg
                        on stt.tag_id = stg.id
        where stt.project_id = #{projectId}
        order by stt.id asc
    </select>
</mapper>