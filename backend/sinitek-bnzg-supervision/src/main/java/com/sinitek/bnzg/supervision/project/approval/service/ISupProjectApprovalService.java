package com.sinitek.bnzg.supervision.project.approval.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.supervision.project.approval.dto.*;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
public interface ISupProjectApprovalService {

    /**
     * 查询审批详情
     * @param projectId 项目id
     * @return
     */
    SupProjectApprovalDetailDTO loadDetail(Long projectId);

    /**
     * 审批列表
     * @param dto 查询参数
     * @return 列表结果
     */
    IPage<SupProjectApprovalSearchResultDTO> search(SupProjectApprovalSearchDTO dto);

    /**
     * 项目批量审批
     * @param dto 审批参数
     */
    void batchApproveSupProject(SupProjectBatchApprovalDTO dto);

    /**
     * 项目审批
     * @param dto 审批参数
     */
    void approveSupProject(SupProjectApprovalDTO dto);

    /**
     * 保存审批记录
     * @param dto 保存参数
     */
    void saveSupProjectApproval(SupProjectApprovalSaveDTO dto);

    /**
     * 查看项目审批结果
     * @param sourceName 来源名称
     * @param sourceId 来源id
     * @param type 消息发送类型
     * @return 处理结果和url
     */
    SupProjectApprovalResultDTO getSupProjectApprovalResult(String sourceName, Long sourceId, Integer type);
}
