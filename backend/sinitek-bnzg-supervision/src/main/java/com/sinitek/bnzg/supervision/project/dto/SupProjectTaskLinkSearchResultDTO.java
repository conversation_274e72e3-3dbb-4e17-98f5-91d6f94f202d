package com.sinitek.bnzg.supervision.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Data
@ApiModel(value = "项目任务依赖关系-前置任务查询-返回DTO")
public class SupProjectTaskLinkSearchResultDTO {

    @ApiModelProperty(value = "任务id")
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

}
