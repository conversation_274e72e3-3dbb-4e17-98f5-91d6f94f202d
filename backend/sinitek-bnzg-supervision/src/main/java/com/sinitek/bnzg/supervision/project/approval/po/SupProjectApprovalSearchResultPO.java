package com.sinitek.bnzg.supervision.project.approval.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@ApiModel(value = "任务督办-项目审批-分页查询DTO")
public class SupProjectApprovalSearchResultPO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目标题")
    private String title;

    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    @ApiModelProperty(value = "所属部门")
    private String depId;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "生效开始日期")
    private Date startDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "生效结束日期")
    private Date endDate;

    @ApiModelProperty(value = "申请人id")
    private String applicantId;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("申请时间")
    private Date applicationTime;

    @ApiModelProperty("审批结果")
    private Integer approveStatus;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date approveTime;

    @ApiModelProperty(value = "上报主体id")
    private List<Integer> reportSubjects;

    @ApiModelProperty(value = "任务标签id集合")
    private List<Long> tagIds;
}
