package com.sinitek.bnzg.supervision.project.approval.support;

import com.sinitek.bnzg.supervision.project.approval.dao.SupProjectApprovalDAO;
import com.sinitek.bnzg.supervision.project.approval.entity.SupProjectApproval;
import com.sinitek.bnzg.supervision.project.approval.properties.SupProjectApprovalProperties;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.workflow.support.claim.matterentity.AbstractMatterSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/23
 */
@Slf4j
@Component(SupProjectApproval.ENTITY_NAME)
public class SupProjectApprovalMatterSupport extends AbstractMatterSupport {

    @Autowired
    private SupProjectApprovalDAO supProjectApprovalDAO;


    @Autowired
    private SupProjectApprovalProperties supProjectApprovalProperties;

    @Override
    public boolean checkTask(Long sourceid, String sourcename) {
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getById(sourceid);
        if (Objects.isNull(supProjectApproval)){
            log.warn("通过sourceId[{}]查询审批记录信息为空", sourceid);
            return false;
        }
        if (Objects.equals(supProjectApproval.getStatus(), CommonBooleanEnum.TRUE.getValue())) {
            log.warn("sourceId: {},sourceName: {} 数据已审批,无法再次提交", sourceid, sourcename);
            return false;
        }

        if (!Objects.equals(supProjectApproval.getApproverId(), CurrentUserFactory.getOrgId())) {
            log.info("当前登陆人[{}]与审批人[{}]不一致",
                    CurrentUserFactory.getOrgId(), supProjectApproval.getApproverId());
            return false;
        }

        return true;
    }

    @Override
    public String getShowUrl(Long sourceid, String sourcename) {
        String approvalShowUrl = supProjectApprovalProperties.getApprovalShowUrl();
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getById(sourceid);
        if (Objects.isNull(supProjectApproval)){
            log.warn("通过sourceId[{}]查询审批记录信息为空",sourceid);
            return "";
        }
        Long projectId = supProjectApproval.getProjectId();
        String result = String.format(approvalShowUrl, projectId,"审批");
        log.info("sourceId: {},sourceName: {} 详情地址: {}", sourceid, sourcename, result);
        return result;
    }

    @Override
    public String getActionUrl(Long sourceid, String sourcename) {
        String approvalProcessUrl = supProjectApprovalProperties.getApprovalProcessUrl();
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getById(sourceid);
        if (Objects.isNull(supProjectApproval)){
            log.warn("通过sourceId[{}]查询审批记录信息为空",sourceid);
            return "";
        }
        Long projectId = supProjectApproval.getProjectId();
        String result = String.format(approvalProcessUrl, "审批",sourceid,projectId);
        log.info("sourceId: {},sourceName: {} 处理地址: {}", sourceid, sourcename, result);
        return result;
    }

    @Override
    public Map<String, String> getRemarksMap() {
        return new HashMap<>();
    }

    @Override
    public String getSourceEntity() {
        return "";
    }

    @Override
    public String getLinkBriefUrl(Long sourceid, String sourcename) {
        return "";
    }

    @Override
    public List<Map<String, String>> getFinishedOperations(Long sourceid, String sourcename) {
        return Collections.emptyList();
    }

    @Override
    public String terminateExampleTask(Long sourceid) {
        return "";
    }
}
