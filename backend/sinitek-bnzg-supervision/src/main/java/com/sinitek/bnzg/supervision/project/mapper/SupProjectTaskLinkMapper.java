package com.sinitek.bnzg.supervision.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTaskLink;
import com.sinitek.bnzg.supervision.project.po.SupProjectTaskLinkPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
public interface SupProjectTaskLinkMapper extends BaseMapper<SupProjectTaskLink> {

    List<SupProjectTaskLinkPO> findByTaskId(Long taskId);
}
