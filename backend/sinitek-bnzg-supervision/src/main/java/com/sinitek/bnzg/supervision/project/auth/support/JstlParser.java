package com.sinitek.bnzg.supervision.project.auth.support;

import com.sinitek.sirm.common.constant.BaseConstant;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
public class JstlParser {

    /**
     * 用于SpEL表达式解析.
     */
    private static final SpelExpressionParser parser = new SpelExpressionParser();

    /**
     * 用于获取方法参数定义名字.
     */
    private static final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    public static String generateKeyBySpEl(String spElString, ProceedingJoinPoint joinPoint) {
        if (StringUtils.isBlank(spElString) || !spElString.contains(BaseConstant.WELL_NUMBER_STR)) {
            return spElString;
        }
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        // 使用spring的DefaultParameterNameDiscoverer获取方法形参名数组
        String[] paramNames = Objects.requireNonNull(nameDiscoverer.getParameterNames(method));
        // 解析过后的Spring表达式对象
        Expression expression = parser.parseExpression(spElString);
        // spring的表达式上下文对象
        EvaluationContext context = new StandardEvaluationContext();
        // 通过joinPoint获取被注解方法的形参
        Object[] args = joinPoint.getArgs();
        // 给上下文赋值
        for(int i = 0 ; i < args.length ; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        Object value = expression.getValue(context);
        if (value != null) {
            if (value instanceof Collection) {
                return StringUtils.join((Collection<?>) value, BaseConstant.COMMA);
            }
            return value.toString();
        }
        return "";
    }
}
