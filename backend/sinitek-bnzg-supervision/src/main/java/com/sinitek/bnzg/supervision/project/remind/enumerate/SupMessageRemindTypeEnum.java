package com.sinitek.bnzg.supervision.project.remind.enumerate;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
public enum SupMessageRemindTypeEnum {

    PROJECT_APPROVAL(1, "项目审批"),
    TASK_DECOMPOSITION_APPROVAL(2, "任务分解审批"),
    TASK_PROCESSING_HOST_APPROVAL(3, "任务处理主办审批"),
    TASK_PROCESSING_SUPERVISION_APPROVAL(4, "任务处理督办审批"),
    TASK_PROCESSING_APPROVAL_REJECT(5, "任务处理审批驳回"),
    TASK_HOST_RETURN_ASSIST_PROCESS_RESULT(6, "任务主办退回协办处理结果"),
    TASK_DELAY_APPROVAL(7, "任务延期审批"),
    TASK_DELAY_APPROVAL_REJECT(8, "任务延期审批驳回"),
    TASK_TERMINATION_APPROVAL(9, "任务终止审批"),
    TASK_TERMINATION_APPROVAL_REJECT(10, "任务终止审批驳回"),
    TASK_CHANGE_APPROVAL(11, "任务变更审批"),
    TASK_CHANGE_APPROVAL_REJECT(12, "任务变更审批驳回"),
    TASK_TRANSFER_APPROVAL(13, "任务移交审批"),
    TASK_TRANSFER_APPROVAL_REJECT(14, "任务移交审批驳回"),
    TASK_START_REMINDER(15, "任务开始提醒"),
    TASK_END_REMINDER(16, "任务结束前提醒"),
    PERSONNEL_DEPARTURE_REMINDER(17, "人员离职提醒"),
    PERSONNEL_ENTRY_REMINDER(18, "人员入职提醒"),
    TASK_DECOMPOSITION_APPROVAL_REJECT(19, "任务分解审批被驳回");

    private Integer code;


    private String name;

    SupMessageRemindTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SupMessageRemindTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }


    public static SupMessageRemindTypeEnum fromCode(Integer code) {
        for (SupMessageRemindTypeEnum item : SupMessageRemindTypeEnum.values()) {
            if (Objects.equals(item.code, code)) {
                return item;
            }
        }
        return null;
    }


}
