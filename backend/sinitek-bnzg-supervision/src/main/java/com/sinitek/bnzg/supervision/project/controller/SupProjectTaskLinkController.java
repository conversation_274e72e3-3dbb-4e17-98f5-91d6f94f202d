package com.sinitek.bnzg.supervision.project.controller;

import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkDetailResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkSaveDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkSearchResultDTO;
import com.sinitek.bnzg.supervision.project.service.ISupProjectTaskLinkService;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project-task-link")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "督办系统 - 任务依赖管理")
public class SupProjectTaskLinkController {

    private ISupProjectTaskLinkService supProjectTaskLinkService;

    @ApiOperation(value = "查询前置任务复选框数据")
    @GetMapping("/find-pre-task")
    public RequestResult<List<SupProjectTaskLinkSearchResultDTO>> findPreTask(@RequestParam @ApiParam(value = "任务id", required = true) Long taskId) {
        List<SupProjectTaskLinkSearchResultDTO> result = supProjectTaskLinkService.findPreTask(taskId);
        return new RequestResult<>(result);
    }

    @ApiOperation(value = "查询任务前置任务详情")
    @GetMapping("/detail")
    public RequestResult<SupProjectTaskLinkDetailResultDTO> detail(@RequestParam @ApiParam(value = "任务id") Long taskId) {
        SupProjectTaskLinkDetailResultDTO result = supProjectTaskLinkService.detail(taskId);
        return new RequestResult<>(result);
    }

    @ApiOperation(value = "保存任务前置任务")
    @PostMapping("/save")
    public RequestResult<Void> save(@RequestBody SupProjectTaskLinkSaveDTO dto) {
        supProjectTaskLinkService.save(dto);
        return new RequestResult<>();
    }

}
