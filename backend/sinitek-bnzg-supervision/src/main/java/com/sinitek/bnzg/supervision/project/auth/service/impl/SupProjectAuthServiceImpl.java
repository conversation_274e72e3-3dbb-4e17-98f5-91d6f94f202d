package com.sinitek.bnzg.supervision.project.auth.service.impl;

import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;
import com.sinitek.bnzg.supervision.project.auth.service.ISupProjectAuthService;
import com.sinitek.bnzg.supervision.project.dao.SupProjectDAO;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.org.entity.Department;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.org.service.IRightService;
import com.sinitek.spirit.right.server.entity.RightAuth;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Service
@Slf4j
@Setter(onMethod = @__({@Autowired}))
public class SupProjectAuthServiceImpl implements ISupProjectAuthService {


    SupProjectDAO supProjectDAO;

    IOrgService orgService;

    IRightService rightService;

    @Override
    public Set<Long> findProjectsAuth(String authType) {

        // 权限判断
        String[] rightTypes = determineRightTypes(authType);

        String orgId = CurrentUserFactory.getOrgId();

        // 获取项目权限
        List<RightAuth> projectRightAuthList = rightService.findAuthedObjects(orgId, null, SupProjectAuthConstant.RIGHT_DEFINE_KEY_SUP_PROJECT, rightTypes, null, false);
        Set<Long> rejectProjectIds = new HashSet<>();
        Set<Long> authProjectIds = new HashSet<>();

        projectRightAuthList.forEach(rightAuth -> processRightAuth(rightAuth, rejectProjectIds, authProjectIds));

        // 获取项目类型权限
        List<RightAuth> projectTypeRightAuthList = rightService.findAuthedObjects(orgId, null, SupProjectAuthConstant.RIGHT_DEFINE_KEY_SUP_PROJECT_TYPE, rightTypes, null, false);
        Set<Integer> rejectProjectTypes = new HashSet<>();
        Set<Integer> authProjectTypes = new HashSet<>();

        projectTypeRightAuthList.forEach(rightAuth -> processRightAuthType(rightAuth, rejectProjectTypes, authProjectTypes));

        // 获取用户所属部门
        List<String> deptIds = orgService.findUnitsByEmpId(orgId).stream()
                .map(Department::getOrgid)
                .collect(Collectors.toList());

        // 项目数据处理
        List<SupProject> supProjects = supProjectDAO.list();
        Set<Long> defaultAuthProductIds = new HashSet<>();
        Set<Long> authTypeProductIds = new HashSet<>();
        Set<Long> rejectTypeProductIds = new HashSet<>();

        supProjects.forEach(item -> {
            Long id = item.getId();
            Integer type = item.getType();
            if (item.getApproverId().equals(orgId) || deptIds.contains(item.getDepId())) {
                defaultAuthProductIds.add(id);
            }
            if (rejectProjectTypes.contains(type)) {
                rejectTypeProductIds.add(id);
            }
            if (authProjectTypes.contains(type)) {
                authTypeProductIds.add(id);
            }
        });

        // 去掉类型授权中在单个产品中拒绝的
        authTypeProductIds.removeAll(rejectProjectIds);

        // 去掉默认中在页面配置拒绝的
        defaultAuthProductIds.removeAll(rejectProjectIds);
        defaultAuthProductIds.removeAll(rejectTypeProductIds);

        // 合并授权集合
        Set<Long> mergedProductIds = new HashSet<>();
        mergedProductIds.addAll(authTypeProductIds);
        mergedProductIds.addAll(defaultAuthProductIds);
        mergedProductIds.addAll(authProjectIds);

        // 返回最终的授权集合
        return mergedProductIds;
    }

    private static String[] determineRightTypes(String authType) {
        if (SupProjectAuthConstant.RIGHT_TYPE_VIEW.equalsIgnoreCase(authType)) {
            return new String[]{SupProjectAuthConstant.RIGHT_TYPE_VIEW, SupProjectAuthConstant.RIGHT_TYPE_EDIT};
        }
        return new String[]{SupProjectAuthConstant.RIGHT_TYPE_EDIT};
    }

    private static void processRightAuth(RightAuth rightAuth, Set<Long> rejectProjectIds, Set<Long> authProjectIds) {
        Long projectId = NumberTool.safeToLong(rightAuth.getObjectKey(), null);
        if (projectId != null) {
            if (CommonBooleanEnum.TRUE.getValue().equals(rightAuth.getRejectFlag())) {
                rejectProjectIds.add(projectId);
            } else {
                authProjectIds.add(projectId);
            }
        }
    }

    private static void processRightAuthType(RightAuth rightAuth, Set<Integer> rejectProjectTypes, Set<Integer> authProjectTypes) {
        Integer projectType = NumberTool.safeToInteger(rightAuth.getObjectKey(), null);
        if (CommonBooleanEnum.TRUE.getValue().equals(rightAuth.getRejectFlag())) {
            rejectProjectTypes.add(projectType);
        } else {
            authProjectTypes.add(projectType);
        }
    }
}
