package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
@ApiModel(value = "项目任务派发-实体")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_task_dispatch")
public class SupProjectTaskDispatch extends BaseEntity {

    @ApiModelProperty(value = "流转信息id")
    private Long flowLogId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "是否成功派发, 0 否 1 是")
    private Integer isDispatch;

}
