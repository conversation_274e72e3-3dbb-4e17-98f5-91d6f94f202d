<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.supervision.project.approval.mapper.SupProjectApprovalMapper">
    <select id="search" resultType="com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchResultPO">
        select
            spa.id,
            spa.project_id,
            spa.approver_id,
            spa.operate_type,
            spa.applicant_id ,
            spa.approve_status ,
            spa.application_time ,
            spa.approve_time,
            sp.title,
            sp.dep_id,
            sp.start_date,
            sp.end_date
        from sup_project_approval spa
        join sup_project sp on spa.project_id = sp.id
        where spa.approver_id = #{param.approverId}
          and spa.status = #{param.status}
        <if test="param.type != null and param.type > 0">
            and sp.type = #{param.type}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.titles)">
            <!--项目标题查询-->
            and
            <foreach collection="param.titles" index="index" item="title" open="(" separator="or" close=")">
                sp.title like CONCAT('%', #{title}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.reportSubjects)">
            <!-- 上报主体查询 -->
            and sp.id in
            (select distinct project_id from sup_project_report_subject where subject_val IN
            <foreach collection="param.reportSubjects" index="index" item="reportSubject" open="(" separator="," close=")">
                #{reportSubject}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.tagIds)">
            <!--任务标签查询-->
            and sp.id in
            (select sptr.project_id from sup_project_tag_rela sptr where sptr.tag_id in
            <foreach collection="param.tagIds" index="index" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.depIds)">
            <!--项目状态查询-->
            and sp.dep_id in
            <foreach collection="param.depIds" index="index" item="depId" open="(" separator="," close=")">
                #{depId}
            </foreach>
        </if>
        <if test="param.startDate != null">
            and sp.end_date <![CDATA[ >= ]]> #{param.startDate}
        </if>
        <if test="param.endDate != null">
            and sp.start_date <![CDATA[ <= ]]> #{param.endDate}
        </if>
        <if test="param.operateType != null">
            and spa.operate_type = #{param.operateType}
        </if>
        <if test="param.approveStatus != null">
            and spa.approve_status = #{param.approveStatus}
        </if>

        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName) and param.status == 0">
            order by spa.application_time desc
        </if>

        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName) and param.status == 1">
            order by spa.approve_time desc
        </if>
    </select>
</mapper>