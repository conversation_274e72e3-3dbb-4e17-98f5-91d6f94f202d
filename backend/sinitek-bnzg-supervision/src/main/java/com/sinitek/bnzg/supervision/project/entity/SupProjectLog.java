package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_log")
@ApiModel(value = "项目操作日志-实体")
public class SupProjectLog extends BaseEntity {

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 旧数据
     */
    @ApiModelProperty(value = "旧数据")
    private String oldData;

    /**
     * 新数据
     */
    @ApiModelProperty(value = "新数据")
    private String newData;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private String operatorId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 来源名称
     */
    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    /**
     * 来源记录ID
     */
    @ApiModelProperty(value = "来源记录ID")
    private Long sourceId;

    /**
     * 线索id
     */
    @ApiModelProperty(value = "线索id")
    private Long threadId;

    /**
     * 最新记录标识
     */
    @ApiModelProperty(value = "最新记录标识")
    private Integer threadLatestFlag;

}
