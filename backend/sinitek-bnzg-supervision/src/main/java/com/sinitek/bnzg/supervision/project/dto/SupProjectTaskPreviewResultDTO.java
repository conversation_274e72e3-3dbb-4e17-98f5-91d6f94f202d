package com.sinitek.bnzg.supervision.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *
 * 任务预览 - 返回DTO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@ApiModel(value = "任务预览 - 返回DTO")
public class SupProjectTaskPreviewResultDTO {

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy年MM月dd日")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy年MM月dd日")
    private Date endDate;

}
