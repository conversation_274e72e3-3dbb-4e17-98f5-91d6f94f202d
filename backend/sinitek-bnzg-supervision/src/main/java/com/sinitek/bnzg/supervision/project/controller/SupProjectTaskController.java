package com.sinitek.bnzg.supervision.project.controller;

import com.sinitek.bnzg.supervision.project.constant.SupProjectConstant;
import com.sinitek.bnzg.supervision.project.dto.*;
import com.sinitek.bnzg.supervision.project.service.ISupProjectTaskService;
import com.sinitek.bnzg.supervision.project.support.SubProjectTaskResultFormat;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.log.annotation.SirmLog;
import com.sinitek.sirm.lowcode.model.dto.LcIdAndIdListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/supervision/project/task")
@Setter(onMethod = @__({@Autowired}))
@Api(tags = "督办系统 - 项目任务管理")
public class SupProjectTaskController {

    private ISupProjectTaskService projectTaskService;

    private SubProjectTaskResultFormat projectTaskResultFormat;

    @ApiOperation(value = "项目任务新增/编辑")
    @PostMapping(path = "/save-or-update")
    @SirmLog(moduleName = SupProjectConstant.PROJECT_MANAGER_MODULE_NAME,
            operateType = SupProjectConstant.PROJECT_MANAGER_OPERATE_TYPE,
            desc = "'{' +'${currentOrgName}' + '}' + (#dto.getId() == null ? '新增' : '修改了') + '项目任务计划[' + {#dto.getTaskName()} + ']'" )
    public RequestResult<Long> saveOrUpdate(@RequestBody @Validated SupProjectTaskSaveDTO dto) {
        return new RequestResult<>(projectTaskService.saveOrUpdate(dto));
    }

    @ApiOperation(value = "项目任务详情查询")
    @GetMapping(path = "/get-by-id")
    public RequestResult<SupProjectTaskDetailResultDTO> getById(Long id) {
        return new RequestResult<>(projectTaskService.getById(id));
    }


    @ApiOperation(value = "项目任务表查询")
    @GetMapping(path = "/list")
    public RequestResult<List<SupProjectTaskResultDTO>> list(Long projectId) {
        List<SupProjectTaskDTO> projectTasks = projectTaskService.list(projectId);
        return new RequestResult<>(projectTaskResultFormat.format(projectTasks));
    }

    @ApiOperation(value = "任务预览")
    @PostMapping(path = "/preview")
    public TableResult<SupProjectTaskPreviewResultDTO> preview(@RequestBody @Validated SupProjectTaskPreviewDTO dto) {
        List<SupProjectTaskPreviewResultDTO> preview = projectTaskService.preview(dto);
        return dto.build(preview);
    }

    @ApiOperation(value = "删除任务")
    @PostMapping(path = "/delete")
    public RequestResult<Void> delete(@RequestBody LcIdAndIdListDTO param) {
        projectTaskService.delete(param);
        return new RequestResult<>();
    }

}
