package com.sinitek.bnzg.supervision.project.entity;

import com.sinitek.data.mybatis.base.BaseAuditEntity;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * 项目 Entity
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project")
@ApiModel(value = "项目-实体")
public class SupProject extends BaseAuditEntity {

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 制度发文/监管要求/其他任务背景
     */
    @ApiModelProperty(value = "制度发文/监管要求/其他任务背景")
    private String sourceDescription;

    /**
     * 项目描述
     */
    @ApiModelProperty(value = "项目描述")
    private String projectBrief;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String depId;

    /**
     * 项目审批人
     */
    @ApiModelProperty(value = "项目审批人")
    private String approverId;

    /**
     * 生效开始日期
     */
    @ApiModelProperty(value = "生效开始日期")
    private Date startDate;

    /**
     * 生效结束日期
     */
    @ApiModelProperty(value = "生效结束日期")
    private Date endDate;

    /**
     * 提醒方式
     */
    @ApiModelProperty(value = "提醒方式")
    private Integer remindType;

    /**
     * 节假日策略
     */
    @ApiModelProperty(value = "节假日策略")
    private Integer holidayStrategy;

    /**
     * 重复频率类型
     */
    @ApiModelProperty(value = "重复频率类型")
    private Integer repeatType;

    /**
    * 日志id,关联sup_project_log.id
    */
    @ApiModelProperty(value = "日志id")
    private Long logId;

    /**
    * 是否同步修改下级标签, 0否 1是
    */
    @ApiModelProperty(value = "是否同步修改下级标签, 0否 1是")
    private Integer syncTagFlag;

}
