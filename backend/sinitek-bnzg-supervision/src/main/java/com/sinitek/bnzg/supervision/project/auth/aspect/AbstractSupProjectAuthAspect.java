package com.sinitek.bnzg.supervision.project.auth.aspect;

import com.sinitek.bnzg.supervision.project.auth.support.SupProjectAuthContext;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
public class AbstractSupProjectAuthAspect {

    final ThreadLocal<SupProjectAuthContext> rightTypeContext = new ThreadLocal<>();

    public SupProjectAuthContext getContext() {
        if (ObjectUtils.allNotNull(rightTypeContext, rightTypeContext.get())) {
            return rightTypeContext.get();
        }
        return null;
    }

    public void setContext(SupProjectAuthContext context) {
        this.rightTypeContext.set(context);
    }

    public String getRightType() {
        SupProjectAuthContext context = this.getContext();
        if (context != null) {
            return context.getRightType();
        }
        return null;
    }

    public void setRightType(String rightType) {
        SupProjectAuthContext context = this.getContext();
        if (context == null) {
            context = new SupProjectAuthContext();
        }
        context.setRightType(rightType);
        rightTypeContext.set(context);
    }

    public void removeContext() {
        if (rightTypeContext.get() != null) {
            rightTypeContext.remove();
        }
    }
}
