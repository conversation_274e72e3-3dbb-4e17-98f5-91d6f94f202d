package com.sinitek.bnzg.supervision.project.remind.job;

import com.sinitek.bnzg.supervision.project.remind.service.ISupMessageRemindService;
import com.sinitek.sirm.common.support.BaseScheduleJob;
import com.sinitek.sirm.common.support.ScheduleJobContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@Slf4j
public class SupMessageRemindJob extends BaseScheduleJob {

    @Autowired
    private ISupMessageRemindService supMessageRemindService;

    @Override
    public void scheduleJob(ScheduleJobContext context) {
        log.info("SupMessageRemindJob:ready to sendMessageRemind!");
        try {
            supMessageRemindService.sendMessageRemind();
        } catch (Exception e) {
            log.error("SupMessageRemindJob:sendMessageRemind error!{}", context, e);
        }

    }
}
