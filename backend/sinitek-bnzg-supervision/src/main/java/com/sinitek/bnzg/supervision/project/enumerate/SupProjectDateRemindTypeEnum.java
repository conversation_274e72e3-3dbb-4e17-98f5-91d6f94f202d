package com.sinitek.bnzg.supervision.project.enumerate;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
public enum SupProjectDateRemindTypeEnum {

    START_DATE(1, "开始日期"),
    END_DATE(2, "结束日期");

    private Integer code;
    private String name;

    SupProjectDateRemindTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SupProjectDateRemindTypeEnum getByCode(Integer code) {
        for (SupProjectDateRemindTypeEnum item : SupProjectDateRemindTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
