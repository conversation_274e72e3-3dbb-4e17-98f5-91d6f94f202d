package com.sinitek.bnzg.supervision.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTaskDispatch;
import com.sinitek.bnzg.supervision.project.po.SupProjectTaskDispatchPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20
 */
public interface SupProjectTaskDispatchMapper extends BaseMapper<SupProjectTaskDispatch> {

    List<SupProjectTaskDispatchPO> findByFlowLogIds(@Param("flowLogIds") List<Long> flowLogIds);

}
