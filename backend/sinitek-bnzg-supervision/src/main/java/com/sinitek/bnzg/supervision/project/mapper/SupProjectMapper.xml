<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.supervision.project.mapper.SupProjectMapper">

    <select id="search" resultType="com.sinitek.bnzg.supervision.project.po.SupProjectSearchResultPO">
        select
        sp.id,
        sp.title,
        sp.dep_id,
        sp.start_date,
        sp.end_date,
        sp.repeat_type,
        sp.status,
        sp.approver_id,
        sp.updater_id,
        sp.updatetimestamp
        from sup_project sp
        where 1=1
        <if test="param.type != null and param.type > 0">
            and sp.type = #{param.type}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.titles)">
            <!--项目标题查询-->
            and
            <foreach collection="param.titles" index="index" item="title" open="(" separator="or" close=")">
                sp.title like CONCAT('%', #{title}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.reportSubjects)">
            <!-- 上报主体查询 -->
            and sp.id in
            (select distinct project_id from sup_project_report_subject where subject_val IN
            <foreach collection="param.reportSubjects" index="index" item="reportSubject" open="(" separator="," close=")">
                #{reportSubject}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.status)">
            <!--项目状态查询-->
            and sp.status in
            <foreach collection="param.status" index="index" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.tagIds)">
            <!--任务标签查询-->
            and sp.id in
            (select sptr.project_id from sup_project_tag_rela sptr where sptr.tag_id in
            <foreach collection="param.tagIds" index="index" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.approverIds)">
            <!--审批人查询-->
            and sp.approver_id in
            <foreach collection="param.approverIds" index="index" item="approverId" open="(" separator="," close=")">
                #{approverId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.depIds)">
                <!--所属部门查询-->
                and sp.dep_id in
                <foreach collection="param.depIds" index="index" item="depId" open="(" separator="," close=")">
                    #{depId}
                </foreach>
        </if>
        <if test="param.startDate != null">
            AND (sp.end_date <![CDATA[ >= ]]> #{param.startDate} or sp.end_date is null)
        </if>
        <if test="param.endDate != null">
            AND sp.start_date <![CDATA[ <= ]]> #{param.endDate}
        </if>
        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
            order by sp.updatetimestamp desc
        </if>
        <bind name="projectId" value="'sp.id'"/>
        <include refid="com.sinitek.bnzg.supervision.project.auth.mapper.SupProjectAuthRangeMapper.filterSupProjectRange"/>

    </select>

</mapper>

