package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/01/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_task_tag_rela")
@ApiModel(value = "项目任务标签关联-实体")
public class SupProjectTaskTagRela extends BaseEntity {

    /**
     * 项目任务id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectTaskId;

    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签id")
    private Long tagId;

}
