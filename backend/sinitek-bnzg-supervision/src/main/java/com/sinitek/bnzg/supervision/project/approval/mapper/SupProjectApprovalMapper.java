package com.sinitek.bnzg.supervision.project.approval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.supervision.project.approval.entity.SupProjectApproval;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchPO;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchResultPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
public interface SupProjectApprovalMapper extends BaseMapper<SupProjectApproval> {

    IPage<SupProjectApprovalSearchResultPO> search(Page<SupProjectApprovalSearchResultPO> page, @Param("param")SupProjectApprovalSearchPO param);
}
