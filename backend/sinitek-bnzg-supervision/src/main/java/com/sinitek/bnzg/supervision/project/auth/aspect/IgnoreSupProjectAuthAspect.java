package com.sinitek.bnzg.supervision.project.auth.aspect;

import com.sinitek.bnzg.supervision.project.auth.annotation.IgnoreSupProjectAuth;
import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;
import com.sinitek.bnzg.supervision.project.auth.support.SupProjectAuthContext;
import com.sinitek.sirm.common.spring.SpringFactory;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/22
 */
@Slf4j
@Aspect
@Component
public class IgnoreSupProjectAuthAspect {

    @Around("@within(ignoreSupProjectAuth) || @annotation(ignoreSupProjectAuth)")
    public Object ignoreSupProjectAuthAspect(ProceedingJoinPoint joinPoint, IgnoreSupProjectAuth ignoreSupProjectAuth) throws Throwable {
        SupProjectAuthFilterAspect aspect = SpringFactory.getBean(SupProjectAuthFilterAspect.class);
        Object proceed;
        try {
            SupProjectAuthContext context = new SupProjectAuthContext();
            context.setRightType(SupProjectAuthConstant.RIGHT_TYPE_IGNORE);
            aspect.setContext(context);
            proceed = joinPoint.proceed();
        } finally {
            aspect.removeContext();
        }
        return proceed;
    }
}
