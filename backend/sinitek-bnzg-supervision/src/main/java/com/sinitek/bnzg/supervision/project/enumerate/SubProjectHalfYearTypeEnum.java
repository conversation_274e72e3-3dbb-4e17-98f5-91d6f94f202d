package com.sinitek.bnzg.supervision.project.enumerate;

/**
 *
 * 每半年类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public enum SubProjectHalfYearTypeEnum {

    LAST_HALF_YEAR(1, "${上一半年}"),
    THIS_HALF_YEAR(2, "${当前半年}"),
    NEXT_HALF_YEAR(3, "${下一半年}");

    private Integer code;

    private String name;

    SubProjectHalfYearTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SubProjectHalfYearTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }

}
