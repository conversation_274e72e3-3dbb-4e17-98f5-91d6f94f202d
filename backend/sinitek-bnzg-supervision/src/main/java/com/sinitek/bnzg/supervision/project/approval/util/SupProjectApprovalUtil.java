package com.sinitek.bnzg.supervision.project.approval.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalSearchDTO;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalSearchResultDTO;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchPO;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchResultPO;
import com.sinitek.bnzg.supervision.project.approval.support.SupProjectApprovalSearchResultFormat;
import com.sinitek.sirm.common.sirmenum.support.EnumTableResultFormat;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.framework.frontend.support.MultiTableResultFormat;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import com.sinitek.sirm.org.support.EmpNameTableResultFormat;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/11
 */
public class SupProjectApprovalUtil {

    public static SupProjectApprovalSearchPO makeSearchParamDTO2PO(SupProjectApprovalSearchDTO dto) {
        SupProjectApprovalSearchPO supProjectApprovalSearchPO = new SupProjectApprovalSearchPO();
        BeanUtils.copyProperties(dto, supProjectApprovalSearchPO);

        List<String> titles = CommonStringUtil.toSearchStrList(dto.getTitle());
        if (CollUtil.isNotEmpty(titles)) {
            supProjectApprovalSearchPO.setTitles(titles);
        }
        return supProjectApprovalSearchPO;
    }


    public static SupProjectApprovalSearchResultDTO makeSupProjectApprovalSearchResultPO2DTO(SupProjectApprovalSearchResultPO po) {
        return LcConvertUtil.convert(po, SupProjectApprovalSearchResultDTO::new);
    }

    public static ITableResultFormat<SupProjectApprovalSearchResultDTO> getSupProjectApprovalSearchResult(){
        List<ITableResultFormat<SupProjectApprovalSearchResultDTO>> list = Arrays.asList(
                new SupProjectApprovalSearchResultFormat<>(),
                new EnumTableResultFormat<>("approveStatus", "approveStatusName",
                        SupConstant.DEFAULT_CATALOG, SupConstant.SUP_PROJECT_APPROVAL_STATUS),
                new EnumTableResultFormat<>("operateType", "operateTypeName",
                        SupConstant.DEFAULT_CATALOG, SupConstant.SUP_PROJECT_APPROVAL_TYPE),
                new EmpNameTableResultFormat<>("depId", "depName"),
                new EmpNameTableResultFormat<>("applicantId", "applicantName")
        );
        return MultiTableResultFormat.build(list);
    }
}
