package com.sinitek.bnzg.supervision.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 提醒规则 - DTO
 *
 * <AUTHOR>
 * @date 2024/12/30
 */
@Data
@ApiModel(value = "提醒规则 - DTO")
public class SupProjectRemindConfigBaseDTO {

    @ApiModelProperty(value = "提醒前天数")
    private Integer advanceDays;

    @ApiModelProperty(value = "提醒日类型 1 自然日 , 2 工作日")
    private Integer dayType;

    @ApiModelProperty(value = "日期提醒类型 1 开始日期 , 2 结束日期")
    private Integer dateRemindType;

}
