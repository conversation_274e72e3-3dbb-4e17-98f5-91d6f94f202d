package com.sinitek.bnzg.supervision.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.bnzg.supervision.project.po.SupProjectSearchParamPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectSearchResultPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
public interface SupProjectMapper extends BaseMapper<SupProject> {

    IPage<SupProjectSearchResultPO> search(Page<SupProjectSearchResultPO> page,@Param("param") SupProjectSearchParamPO param);

}
