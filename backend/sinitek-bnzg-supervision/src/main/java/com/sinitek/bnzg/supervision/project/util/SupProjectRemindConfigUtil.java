package com.sinitek.bnzg.supervision.project.util;

import com.sinitek.bnzg.supervision.project.dto.SupProjectRemindConfigBaseDTO;
import com.sinitek.bnzg.supervision.project.po.SupProjectRemindConfigPO;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;

/**
 * <AUTHOR>
 * @date 2025/1/16
 */
public class SupProjectRemindConfigUtil {

    public static SupProjectRemindConfigBaseDTO makeSearchResultPO2DTO(
            SupProjectRemindConfigPO po) {
        return LcConvertUtil.convert(po, SupProjectRemindConfigBaseDTO::new);
    }

}
