package com.sinitek.bnzg.supervision.project.enumerate;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
public enum SubProjectHolidayStrategyEnum {

    NORMAL(0, "正常触发"),
    SKIP(1, "不处理"),
    EXTRACT(2, "提前"),
    POSTPONE(3, "顺延");

    Integer code;

    String name;

    SubProjectHolidayStrategyEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SubProjectHolidayStrategyEnum getByCode(Integer code) {
        for (SubProjectHolidayStrategyEnum e : SubProjectHolidayStrategyEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
