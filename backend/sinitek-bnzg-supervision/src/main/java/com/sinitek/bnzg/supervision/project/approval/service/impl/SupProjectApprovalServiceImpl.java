package com.sinitek.bnzg.supervision.project.approval.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.supervision.project.approval.dao.SupProjectApprovalDAO;
import com.sinitek.bnzg.supervision.project.approval.dto.*;
import com.sinitek.bnzg.supervision.project.approval.entity.SupProjectApproval;
import com.sinitek.bnzg.supervision.project.approval.enumerate.SupProjectApprovalTypeEnum;
import com.sinitek.bnzg.supervision.project.approval.message.SupProjectApprovalMessage;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchPO;
import com.sinitek.bnzg.supervision.project.approval.po.SupProjectApprovalSearchResultPO;
import com.sinitek.bnzg.supervision.project.approval.properties.SupProjectApprovalProperties;
import com.sinitek.bnzg.supervision.project.approval.service.ISupProjectApprovalService;
import com.sinitek.bnzg.supervision.project.approval.support.SupProjectApprovalResultFormat;
import com.sinitek.bnzg.supervision.project.approval.util.SupProjectApprovalUtil;
import com.sinitek.bnzg.supervision.project.approval.util.SupProjectApprovalWfExampleTaskUtil;
import com.sinitek.bnzg.supervision.project.dao.SupProjectDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectReportSubjectDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTagRelaDAO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectFlowLogSaveDTO;
import com.sinitek.bnzg.supervision.project.entity.SupProject;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTagRela;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectFlowLogTypeEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectStatusEnum;
import com.sinitek.bnzg.supervision.project.po.SupProjectReportSubjectPO;
import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindDTO;
import com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindTypeEnum;
import com.sinitek.bnzg.supervision.project.remind.service.ISupMessageRemindService;
import com.sinitek.bnzg.supervision.project.service.ISupProjectFlowLogService;
import com.sinitek.bnzg.supervision.project.service.ISupProjectService;
import com.sinitek.sirm.common.message.template.dto.MessageContextDTO;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.message.template.service.IMessageTemplateExtService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.web.RequestContext;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Service
@Setter(onMethod = @__({@Autowired}))
@Slf4j
public class SupProjectApprovalServiceImpl  implements ISupProjectApprovalService {

    private SupProjectApprovalDAO supProjectApprovalDAO;

    private SupProjectDAO supProjectDAO;

    private SupProjectReportSubjectDAO projectReportSubjectDAO;

    private SupProjectTagRelaDAO projectTagRelaDAO;

    private ISupProjectApprovalService supProjectApprovalService;

    private ISupProjectService supProjectService;

    private ISupProjectFlowLogService supProjectFlowLogService;

    private SupProjectApprovalWfExampleTaskUtil supProjectApprovalWfExampleTaskUtil;

    private IMessageTemplateExtService messageTemplateExtService;

    private ISupMessageRemindService supMessageRemindService;

    private SupProjectApprovalProperties supProjectApprovalProperties;


    @Override
    public SupProjectApprovalDetailDTO loadDetail(Long id) {
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getById(id);
        if (Objects.isNull(supProjectApproval)){
            return null;
        }
        SupProjectApprovalDetailDTO supProjectApprovalDTO = new SupProjectApprovalDetailDTO();
        BeanUtils.copyProperties(supProjectApproval, supProjectApprovalDTO);
        SupProjectApprovalResultFormat.format(supProjectApprovalDTO);
        return supProjectApprovalDTO;
    }

    @Override
    public IPage<SupProjectApprovalSearchResultDTO> search(SupProjectApprovalSearchDTO dto) {
        SupProjectApprovalSearchPO param = SupProjectApprovalUtil.makeSearchParamDTO2PO(dto);
        Page<SupProjectApprovalSearchResultPO> page = param.buildPage(true);
        IPage<SupProjectApprovalSearchResultPO> result = supProjectApprovalDAO.search(page, param);
        List<SupProjectApprovalSearchResultPO> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return result.convert(SupProjectApprovalUtil::makeSupProjectApprovalSearchResultPO2DTO);
        }
        //收集所有projectIds
        List<Long> projectIds = records.stream().map(SupProjectApprovalSearchResultPO::getProjectId).collect(Collectors.toList());
        //查询上报主体
        List<SupProjectReportSubjectPO> projectReportSubjects = projectReportSubjectDAO.findByProjectIds(projectIds);
        Map<Long, List<SupProjectReportSubjectPO>> reportSubjectPOMap = projectReportSubjects.stream()
                .collect(Collectors.groupingBy(SupProjectReportSubjectPO::getProjectId));
        //查询任务标签
        List<SupProjectTagRela> supProjectTagRelaList = projectTagRelaDAO.list(Wrappers.<SupProjectTagRela>lambdaQuery()
                .in(SupProjectTagRela::getProjectId, projectIds));
        Map<Long, List<SupProjectTagRela>> projectTagRelaMap = supProjectTagRelaList.stream()
                .collect(Collectors.groupingBy(SupProjectTagRela::getProjectId));
        records.stream()
                .filter(Objects::nonNull)
                .forEach(record -> {
                    //设置上报主体
                    List<SupProjectReportSubjectPO> supProjectReportSubjectPOS = reportSubjectPOMap.get(record.getProjectId());
                    if (CollectionUtils.isNotEmpty(supProjectReportSubjectPOS)){
                        List<Integer> subjectVal = supProjectReportSubjectPOS.stream().
                                map(SupProjectReportSubjectPO::getSubjectVal).
                                collect(Collectors.toList());
                        record.setReportSubjects(subjectVal);
                    }
                    //设置任务标签
                    List<SupProjectTagRela> projectTagRelaList =  projectTagRelaMap.get(record.getProjectId());
                    if (CollectionUtils.isNotEmpty(projectTagRelaList)){
                        List<Long> tagIds = projectTagRelaList.stream()
                                .map(SupProjectTagRela::getTagId)
                                .collect(Collectors.toList());
                        record.setTagIds(tagIds);
                    }
                });
        return result.convert(SupProjectApprovalUtil::makeSupProjectApprovalSearchResultPO2DTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchApproveSupProject(SupProjectBatchApprovalDTO dto) {
        List<Long> ids = dto.getIds();
        Integer approveStatus = dto.getApproveStatus();
        String opinion = dto.getOpinion();
        ids.forEach(item -> {
            SupProjectApprovalDTO supProjectApprovalDTO = new SupProjectApprovalDTO();
            supProjectApprovalDTO.setId(item);
            supProjectApprovalDTO.setApproveStatus(approveStatus);
            supProjectApprovalDTO.setOpinion(opinion);
            supProjectApprovalService.approveSupProject(supProjectApprovalDTO);
        });

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveSupProject(SupProjectApprovalDTO dto) {
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getById(dto.getId());
        if (Objects.isNull(supProjectApproval)){
            log.error("通过id查询项目审批详情为空; dto: {}", dto);
            throw new BussinessException(SupProjectApprovalMessage.PROJECT_APPROVAL_RECORD_NOT_EXIST);
        }
        String approverId = supProjectApproval.getApproverId();
        String orgId = CurrentUserFactory.getOrgId();
        if (!Objects.equals(approverId, orgId)) {
            log.error("项目审批人和当前用户不一致，审批失败; approverId: {}，orgId：{}", approverId, orgId);
            throw new BussinessException(SupProjectApprovalMessage.PROJECT_APPROVER_ERROR);
        }


        Integer status = supProjectApproval.getStatus();
        if (Objects.equals(status, CommonBooleanEnum.TRUE.getValue())) {
            //项目已经被审批
            log.warn("改项目已经被审批dto: {}", dto);
            throw new BussinessException(SupProjectApprovalMessage.PROJECT_HAVE_BEEN_APPROVED);
        }
        // 审批状态。1：通过 ，0：驳回
        Long projectId = supProjectApproval.getProjectId();
        SupProject supProject = supProjectDAO.getById(projectId);
        Integer approveStatus = dto.getApproveStatus();
        boolean createTaskFlag = false;
        if (Objects.equals(approveStatus, CommonBooleanEnum.TRUE.getValue())) {
            // 审批通过
            Integer operateType = supProjectApproval.getOperateType();
            if (operateType.equals(SupProjectApprovalTypeEnum.APPROVAL.getCode())
            || operateType.equals(SupProjectApprovalTypeEnum.ALTERATION.getCode())) {
                supProject.setStatus(SupProjectStatusEnum.APPROVED.getValue());
                createTaskFlag = true;
            } else {
                supProject.setStatus(SupProjectStatusEnum.TERMINATED.getValue());
                // todo 终止还未开始的任务实例
            }
        } else {
            // 驳回
            //更新项目状态
            Integer operateType = supProjectApproval.getOperateType();
            if (operateType.equals(SupProjectApprovalTypeEnum.APPROVAL.getCode())){
                supProject.setStatus(SupProjectStatusEnum.FAILED.getValue());
            } else {
                // 终止审批和变更审批被驳回，项目状态都变更为已立项
                supProject.setStatus(SupProjectStatusEnum.APPROVED.getValue());
            }
        }
        supProjectDAO.saveOrUpdate(supProject);
        supProjectApproval.setStatus(CommonBooleanEnum.TRUE.getValue());
        supProjectApproval.setApproveStatus(approveStatus);
        supProjectApproval.setOpinion(dto.getOpinion());
        supProjectApproval.setApproveTime(new Date());
        supProjectApprovalDAO.saveOrUpdate(supProjectApproval);
        //保存流转记录
        String opinion = dto.getOpinion();
        SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO = new SupProjectFlowLogSaveDTO();
        supProjectFlowLogSaveDTO.setApprovalResult(approveStatus);
        supProjectFlowLogSaveDTO.setApprovalOpinion(opinion);
        supProjectFlowLogSaveDTO.setProjectId(projectId);
        supProjectFlowLogSaveDTO.setDealTime(new Date());
        supProjectFlowLogSaveDTO.setOperatorId(RequestContext.getCurrentUser().getOrgId());
        supProjectFlowLogSaveDTO.setType(SupProjectFlowLogTypeEnum.PROJECT_APPROVAL.getValue());
        String brief = "审批结果:" + (Objects.equals(approveStatus, CommonBooleanEnum.TRUE.getValue()) ? "通过" : "驳回") +
                (StringUtils.isNotBlank(dto.getOpinion()) ? ("，审批意见:" + opinion) : "");
        supProjectFlowLogSaveDTO.setBrief(brief);
        supProjectFlowLogService.saveOrUpdateSupProjectFlowLog(supProjectFlowLogSaveDTO);

        //更新我的事宜
        supProjectApprovalWfExampleTaskUtil.submitExampleTask(supProjectApproval);

        //更新消息提醒
        supMessageRemindService.deleteMessageRemind(SupProjectApproval.ENTITY_NAME,supProjectApproval.getId(),SupMessageRemindTypeEnum.PROJECT_APPROVAL.getCode());

        //生成任务实例
        if (createTaskFlag) {
            supProjectService.createTask(projectId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSupProjectApproval(SupProjectApprovalSaveDTO dto) {
        Long projectId = dto.getProjectId();
        LambdaQueryWrapper<SupProjectApproval> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupProjectApproval::getProjectId, projectId);
        queryWrapper.eq(SupProjectApproval::getStatus, CommonBooleanEnum.FALSE.getValue());
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getOne(queryWrapper, false);
        if (Objects.nonNull(supProjectApproval)){
            Integer operateType = supProjectApproval.getOperateType();
            String name = SupProjectApprovalTypeEnum.getName(operateType);
            throw new BussinessException(SupProjectApprovalMessage.PROJECT_APPROVING,name);
        }
        supProjectApproval = new SupProjectApproval();
        BeanUtils.copyProperties(dto, supProjectApproval);
        supProjectApproval.setStatus(CommonBooleanEnum.FALSE.getValue());
        supProjectApproval.setApplicationTime(new Date());
        supProjectApprovalDAO.save(supProjectApproval);
        //生成我的代办
        supProjectApprovalWfExampleTaskUtil.createExampleTask(supProjectApproval);
        SupProject supProject = supProjectDAO.getById(projectId);
        //发送我的消息
        MessageContextDTO messageContextDTO = new MessageContextDTO();
        messageContextDTO.setCode("SUP_PROJECT_APPROVAL_REMIND");

        List<MessageReceiverTemplateDTO> messageReceiverTemplateDTOS = new ArrayList<>();
        MessageReceiverTemplateDTO messageReceiverTemplateDTO = new MessageReceiverTemplateDTO();
        messageReceiverTemplateDTO.setEmpId(supProjectApproval.getApproverId());
        messageReceiverTemplateDTOS.add(messageReceiverTemplateDTO);
        messageContextDTO.setReceivers(messageReceiverTemplateDTOS);
        Map<String, Object> param = new HashMap<>();
        param.put("supProjectTitle",supProject.getTitle());
        param.put("operateTypeName", SupProjectApprovalTypeEnum.getName(supProjectApproval.getOperateType()));
        param.put("submitTime", DateUtil.format(new Date(), GlobalConstant.TIME_FORMAT_THIRTEEN));
        param.put("operatorName", CurrentUserFactory.getOrgName());
        messageContextDTO.setParams(param);
        messageTemplateExtService.sendMessage(messageContextDTO);
        // 邮件临时表存一份
        MessageRemindDTO messageRemindDTO = new MessageRemindDTO();
        messageRemindDTO.setType(SupMessageRemindTypeEnum.PROJECT_APPROVAL.getCode());
        messageRemindDTO.setSourceId(supProjectApproval.getId());
        messageRemindDTO.setSourceName(SupProjectApproval.ENTITY_NAME);
        messageRemindDTO.setSubmitId(supProjectApproval.getApplicantId());
        messageRemindDTO.setSubmitTime(new Date());
        messageRemindDTO.setName(supProject.getTitle());
        List<String> receiverIds = new ArrayList<>();
        receiverIds.add(supProjectApproval.getApproverId());
        messageRemindDTO.setReceiverIds(receiverIds);
        supMessageRemindService.createMessageRemind(messageRemindDTO);
    }

    @Override
    public SupProjectApprovalResultDTO getSupProjectApprovalResult(String sourceName, Long sourceId, Integer type) {
        SupProjectApproval supProjectApproval = supProjectApprovalDAO.getById(sourceId);
        if (Objects.isNull(supProjectApproval)) {
            return null;
        }
        SupProjectApprovalResultDTO supProjectApprovalResultDTO = new SupProjectApprovalResultDTO();
        String url = "";
        if (CommonBooleanEnum.FALSE.getValue().equals(supProjectApproval.getStatus())) {
            // 获取处理url
            String approvalProcessUrl = supProjectApprovalProperties.getApprovalProcessUrl();
            Long projectId = supProjectApproval.getProjectId();
            url = String.format(approvalProcessUrl, "审批",supProjectApproval.getId(),projectId);
            supProjectApprovalResultDTO.setDealFlag(false);
        } else {
            // 获取详情url
            String approvalShowUrl = supProjectApprovalProperties.getApprovalShowUrl();
            Long projectId = supProjectApproval.getProjectId();
            url = String.format(approvalShowUrl, projectId,"审批");
            supProjectApprovalResultDTO.setDealFlag(true);
        }
        supProjectApprovalResultDTO.setUrl(url);
        supProjectApprovalResultDTO.setOperatorId(supProjectApproval.getApproverId());
        return supProjectApprovalResultDTO;
    }
}
