package com.sinitek.bnzg.supervision.project.enumerate;

/**
 *
 * 每周类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public enum SubProjectWeekTypeEnum {

    LAST_WEEK(1, "${上周}"),
    THIS_WEEK(2, "${本周}"),
    NEXT_WEEK(3, "${下周}");

    private Integer code;

    private String name;

    SubProjectWeekTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (SubProjectWeekTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return "";
    }

}
