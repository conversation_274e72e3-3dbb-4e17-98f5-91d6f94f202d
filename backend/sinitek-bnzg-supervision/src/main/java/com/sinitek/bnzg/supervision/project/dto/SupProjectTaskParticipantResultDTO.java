package com.sinitek.bnzg.supervision.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * 项目任务参与人 - 返回DTO
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@ApiModel(value = "项目任务参与人 - 基础DTO")
public class SupProjectTaskParticipantResultDTO {


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String deptId;

    /**
    * 部门名称
    */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 员工id集合
     */
    @ApiModelProperty(value = "员工id")
    private List<String> empIds;

    /**
    * 员工名称
    */
    @ApiModelProperty(value = "员工名称")
    private String empNames;
}
