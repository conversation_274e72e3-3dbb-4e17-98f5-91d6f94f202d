package com.sinitek.bnzg.supervision.project.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目消息提醒表 -返回DTO
 *
 * <AUTHOR>
 * date 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目消息提醒表-返回DTO")
public class SupProjectRemindMessageResultDTO extends SupProjectRemindMessageBaseDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

}