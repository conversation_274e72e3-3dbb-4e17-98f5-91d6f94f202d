<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.supervision.project.mapper.SupProjectLogMapper">
    <select id="searchSupProjectLog" resultType="com.sinitek.bnzg.supervision.project.po.SupProjectLogResultPO">
        select
            sp.title,
            spl.status,
            spl.operator_id,
            spl.createtimestamp as editTime,
            spl.approver_id,
            spld.id,
            spld.biz_field_name,
            spld.old_format_value,
            spld.new_format_value,
            spl.project_id
        from  sup_project_log_detail spld
        left join sup_project_log spl on spld.log_id = spl.id
        left join sup_project sp on spl.project_id = sp.id
        where spl.project_id = #{param.projectId}
        order by spld.updatetimestamp desc
    </select>
</mapper>