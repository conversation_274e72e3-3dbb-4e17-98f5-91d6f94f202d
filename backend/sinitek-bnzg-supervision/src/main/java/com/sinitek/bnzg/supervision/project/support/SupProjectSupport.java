package com.sinitek.bnzg.supervision.project.support;

import com.sinitek.bnzg.supervision.common.util.SupHolidayUtil;
import com.sinitek.bnzg.supervision.doc.constant.SupDocumentConstant;
import com.sinitek.bnzg.supervision.doc.dto.SupDocumentBaseDTO;
import com.sinitek.bnzg.supervision.doc.service.ISupDocumentService;
import com.sinitek.bnzg.supervision.project.constant.SupProjectConstant;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskParticipantDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskReportSubjectDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskTagRelaDAO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskCreateDTO;
import com.sinitek.bnzg.supervision.project.entity.*;
import com.sinitek.bnzg.supervision.project.enumerate.SubProjectRepeatTypeEnum;
import com.sinitek.bnzg.supervision.project.enumerate.SupProjectDayTypeEnum;
import com.sinitek.bnzg.supervision.project.util.SupProjectUtil;
import com.sinitek.bnzg.supervision.task.dto.TaskCreateDTO;
import com.sinitek.bnzg.supervision.task.dto.TaskParticipantBaseDTO;
import com.sinitek.bnzg.supervision.task.service.ITaskService;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.remind.dto.RemindRepeatRuleDTO;
import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import com.sinitek.sirm.remind.service.ISirmRemindService;
import com.sinitek.sirm.remind.support.ISirmRemindSupportService;
import com.sinitek.sirm.routine.holiday.service.IHolidaysService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.sinitek.bnzg.supervision.common.enumation.TaskParticipantEnum.*;

/**
 * <AUTHOR>
 * date 2025/2/19
 */
@Slf4j
@Component
public class SupProjectSupport {

    @Autowired
    private SupProjectTaskReportSubjectDAO projectTaskReportSubjectDAO;

    @Autowired
    private SupProjectTaskTagRelaDAO projectTaskTagRelaDAO;

    @Autowired
    private SupProjectTaskParticipantDAO projectTaskParticipantDAO;

    @Autowired
    private ISupDocumentService documentService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private ISirmRemindService remindService;

    @Autowired
    private ISirmRemindSupportService remindSupportService;

    @Autowired
    private IHolidaysService holidaysService;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private SupProjectSupport self;

    @Autowired
    private SupProjectTaskDAO projectTaskDAO;

    public Map<Long, List<SupProjectTaskReportSubject>> queryAndCacheReportSubjects(List<Long> allProjectTaskIds) {
        List<SupProjectTaskReportSubject> allProjectReportSubjects = projectTaskReportSubjectDAO.findByProjectTaskIds(allProjectTaskIds);
        return allProjectReportSubjects.stream().collect(Collectors.groupingBy(SupProjectTaskReportSubject::getProjectTaskId));
    }

    public Map<Long, List<SupProjectTaskTagRela>> queryAndCacheTaskTags(List<Long> allProjectTaskIds) {
        List<SupProjectTaskTagRela> allProjectTaskTags = projectTaskTagRelaDAO.findByProjectTaskIds(allProjectTaskIds);
        return allProjectTaskTags.stream().collect(Collectors.groupingBy(SupProjectTaskTagRela::getProjectTaskId));
    }

    public Map<Long, List<SupProjectTaskParticipant>> queryAndCacheTaskParticipants(List<Long> allProjectTaskIds) {
        List<SupProjectTaskParticipant> allProjectTaskParticipants = projectTaskParticipantDAO.findByProjectTaskIds(allProjectTaskIds);
        return allProjectTaskParticipants.stream().collect(Collectors.groupingBy(SupProjectTaskParticipant::getProjectTaskId));
    }

    public Map<Long, List<Attachment>> queryAndCacheAttachments(List<Long> allProjectTaskIds) {
        List<SupDocumentBaseDTO> allDocuments = documentService.findExistsByTaskIds(allProjectTaskIds);
        Map<Long, List<Attachment>> allAttachmentsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allDocuments)) {
            List<Long> allDocumentIds = allDocuments.stream().map(SupDocumentBaseDTO::getId).collect(Collectors.toList());
            List<Attachment> attachments = attachmentService.findAttachments(SupDocumentConstant.DEFAULT_SOURCE_NAME, null, allDocumentIds);
            Map<Long, List<Attachment>> allAttachmentMapBySourceId = attachments.stream().collect(Collectors.groupingBy(Attachment::getSourceId));
            allDocuments.forEach(document -> {
                if (allAttachmentMapBySourceId.containsKey(document.getId())) {
                    allAttachmentsMap.put(document.getSourceId(), allAttachmentMapBySourceId.get(document.getId()));
                }
            });
        }
        return allAttachmentsMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleRootRepeatTask(List<SupProjectTask> allRepeatTasks, SupProjectTask rootTask, SupProject project,
                                     Date projectStartDate, Date projectEndDate,
                                     Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap,
                                     Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap,
                                     Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap,
                                     Map<Long, List<Attachment>> allAttachmentsMap, List<Long> startedTaskIds,
                                     List<SupProjectTaskCreateDTO> failDispatchTaskList,
                                     List<SupProjectTaskCreateDTO> successDispatchTaskList) {
        TaskCreateDTO taskCreateDTO = SupProjectUtil.buildTaskCreateDTO(rootTask, project, null, CommonBooleanEnum.FALSE.getValue());
        List<Date> allRootTriggerTimes = calculateTriggerTimes(rootTask, projectStartDate, projectEndDate);
        recordFailGenerateTaskByRootRepeatType(allRootTriggerTimes,rootTask,project, projectStartDate, projectEndDate,allRepeatTasks,failDispatchTaskList);
        for (Date taskStartDate : allRootTriggerTimes) {
            setTaskDates(taskCreateDTO, taskStartDate, rootTask);
            taskCreateDTO.setName(SupProjectUtil.buildTaskName(rootTask.getTaskName(), rootTask.getTaskNameParam(), taskStartDate, rootTask.getRepeatType()));
            setTaskParticipantsAndAttachments(taskCreateDTO, rootTask, allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap, allAttachmentsMap);
            Long currentTaskId;
            try {
                currentTaskId = taskService.createTask(taskCreateDTO);
                SupProjectTaskCreateDTO createDTO = new SupProjectTaskCreateDTO();
                createDTO.setTaskName(taskCreateDTO.getName());
                createDTO.setStartDate(taskCreateDTO.getStartDate());
                createDTO.setEndDate(taskCreateDTO.getEndDate());
                createDTO.setIsDispatch(CommonBooleanEnum.TRUE.getValue());
                successDispatchTaskList.add(createDTO);
                if (taskCreateDTO.getStartDate().compareTo(new Date()) <= 0) {
                    startedTaskIds.add(currentTaskId);
                }
            } catch (Exception e) {
                // 记录当前失败任务与该任务的子任务
                recordFailGenerateTask(taskCreateDTO, rootTask, project, allRepeatTasks,
                        failDispatchTaskList,projectStartDate, projectEndDate);
                log.error("任务实例创建失败 任务名称:[{}] 异常信息:", taskCreateDTO.getName(), e);
                // 终止当前任务分支的后续处理
                continue;
            }
            // 处理子任务
            self.createSubRepeatTasks(allRepeatTasks, project, rootTask, currentTaskId, projectStartDate, projectEndDate,
                    allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap,
                    allAttachmentsMap, taskStartDate, startedTaskIds,failDispatchTaskList,successDispatchTaskList);
        }
    }

    private void recordFailGenerateTaskByRootRepeatType(List<Date> allRootTriggerTimes, SupProjectTask rootTask, SupProject project,
                                                        Date projectStartDate, Date projectEndDate, List<SupProjectTask> allRepeatTasks,
                                                        List<SupProjectTaskCreateDTO> failDispatchTaskList) {
        for (SupProjectTask task : allRepeatTasks) {
            if (Objects.equals(task.getParentId(), rootTask.getId())){
                List<Date> allSubTriggerTimes = calculateTriggerTimes(task, projectStartDate, projectEndDate);
                for (Date subTriggerTime : allSubTriggerTimes) {
                    Date subTaskStartDate = calculateSubTaskStartDate(subTriggerTime, allRootTriggerTimes, project.getRepeatType());
                    if (Objects.isNull(subTaskStartDate)){
                        // 子任务存在父任务没有的频率日期,记录该任务为失败任务
                        TaskCreateDTO taskCreateDTO = new TaskCreateDTO();
                        taskCreateDTO.setName(SupProjectUtil.buildTaskName(task.getTaskName(), task.getTaskNameParam(), subTriggerTime, task.getRepeatType()));
                        taskCreateDTO.setStartDate(subTriggerTime);
                        taskCreateDTO.setEndDate(calculateTaskEndDate(subTriggerTime, task.getTaskDurationDays(), project.getRepeatType()));
                        recordFailGenerateTask(taskCreateDTO,task,project,allRepeatTasks,failDispatchTaskList,projectStartDate, projectEndDate);
                    }

                }
            }
        }
    }

    private void recordFailGenerateTask(TaskCreateDTO taskCreateDTO, SupProjectTask rootTask,
                                        SupProject project, List<SupProjectTask> allRepeatTasks,
                                        List<SupProjectTaskCreateDTO> failDispatchTaskList,
                                        Date projectStartDate, Date projectEndDate) {
        SupProjectTaskCreateDTO supProjectTaskCreateDTO = new SupProjectTaskCreateDTO();
        supProjectTaskCreateDTO.setTaskName(taskCreateDTO.getName());
        supProjectTaskCreateDTO.setStartDate(taskCreateDTO.getStartDate());
        supProjectTaskCreateDTO.setEndDate(taskCreateDTO.getEndDate());
        supProjectTaskCreateDTO.setIsDispatch(CommonBooleanEnum.FALSE.getValue());
        failDispatchTaskList.add(supProjectTaskCreateDTO);
        for (SupProjectTask repeatTask : allRepeatTasks) {
            if (Objects.equals(repeatTask.getParentId(), rootTask.getId())) {
                recordFailGenerateSubTask(repeatTask, failDispatchTaskList, projectStartDate,
                        projectEndDate,taskCreateDTO.getStartDate(),project,allRepeatTasks);
            }
        }
    }

    private void recordFailGenerateSubTask(SupProjectTask repeatTask, List<SupProjectTaskCreateDTO> failDispatchTaskList,
                                           Date projectStartDate, Date projectEndDate, Date rootTaskStartDate,
                                           SupProject project, List<SupProjectTask> allRepeatTasks) {
        List<Date> allTriggerTimes = calculateTriggerTimes(repeatTask, projectStartDate, projectEndDate);
        Date subTaskStartDate = calculateSubTaskStartDate(rootTaskStartDate, allTriggerTimes, project.getRepeatType());
        if (Objects.nonNull(subTaskStartDate)) {
            SupProjectTaskCreateDTO supProjectTaskCreateDTO = new SupProjectTaskCreateDTO();
            supProjectTaskCreateDTO.setTaskName(SupProjectUtil.buildTaskName(repeatTask.getTaskName(), repeatTask.getTaskNameParam(), subTaskStartDate, repeatTask.getRepeatType()));
            supProjectTaskCreateDTO.setStartDate(subTaskStartDate);
            supProjectTaskCreateDTO.setEndDate(calculateTaskEndDate(subTaskStartDate, repeatTask.getTaskDurationDays(), project.getRepeatType()));
            supProjectTaskCreateDTO.setIsDispatch(CommonBooleanEnum.FALSE.getValue());
            failDispatchTaskList.add(supProjectTaskCreateDTO);
        }
        // 递归处理子任务
        for (SupProjectTask subTask : allRepeatTasks) {
            if (Objects.equals(subTask.getParentId(), repeatTask.getId())) {
                recordFailGenerateSubTask(subTask, failDispatchTaskList, projectStartDate,
                        projectEndDate, rootTaskStartDate, project, allRepeatTasks);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createSubRepeatTasks(List<SupProjectTask> allRepeatTasks, SupProject project, SupProjectTask parentTask,
                                     Long parentTaskId, Date projectStartDate, Date projectEndDate,
                                     Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap,
                                     Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap,
                                     Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap,
                                     Map<Long, List<Attachment>> allAttachmentsMap, Date parentTaskStartDate,
                                     List<Long> startedTaskIds,
                                     List<SupProjectTaskCreateDTO> failDispatchTaskList,
                                     List<SupProjectTaskCreateDTO> successDispatchTaskList) {
        List<SupProjectTask> subTasks = allRepeatTasks.stream()
                .filter(task -> parentTask.getId().equals(task.getParentId()))
                .collect(Collectors.toList());

        for (SupProjectTask subTask : subTasks) {
            TaskCreateDTO subTaskCreateDTO = SupProjectUtil.buildTaskCreateDTO(subTask, project, parentTaskId, 0);
            List<Date> allSubTriggerTimes = calculateTriggerTimes(subTask, projectStartDate, projectEndDate);
            Date subTaskStartDate = calculateSubTaskStartDate(parentTaskStartDate, allSubTriggerTimes, project.getRepeatType());
            if (subTaskStartDate == null) {
                // 记录该任务的子任务为失败任务
                for (SupProjectTask repeatTask : allRepeatTasks) {
                    if (Objects.equals(repeatTask.getParentId(), subTask.getId())) {
                        recordFailGenerateSubTask(subTask, failDispatchTaskList, projectStartDate,
                                projectEndDate, parentTaskStartDate, project, allRepeatTasks);
                    }
                }
                continue;
            }
            //正常生成任务实例
            setTaskDates(subTaskCreateDTO, subTaskStartDate, subTask);
            subTaskCreateDTO.setName(SupProjectUtil.buildTaskName(subTask.getTaskName(), subTask.getTaskNameParam(), subTaskStartDate, subTask.getRepeatType()));
            setTaskParticipantsAndAttachments(subTaskCreateDTO, subTask, allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap, allAttachmentsMap);
            Long currentTaskId;
            try {
                currentTaskId = taskService.createTask(subTaskCreateDTO);
                SupProjectTaskCreateDTO createDTO = new SupProjectTaskCreateDTO();
                createDTO.setTaskName(subTaskCreateDTO.getName());
                createDTO.setStartDate(subTaskCreateDTO.getStartDate());
                createDTO.setEndDate(subTaskCreateDTO.getEndDate());
                createDTO.setIsDispatch(CommonBooleanEnum.TRUE.getValue());
                successDispatchTaskList.add(createDTO);
                if (subTaskCreateDTO.getStartDate().compareTo(new Date()) <= 0) {
                    startedTaskIds.add(currentTaskId);
                }
            } catch (Exception e) {
                // 记录当前失败任务与该任务的子任务
                recordFailGenerateTask(subTaskCreateDTO, subTask, project, allRepeatTasks,
                        failDispatchTaskList,projectStartDate, projectEndDate);
                log.error("任务实例创建失败 任务名称:[{}] 异常信息:", subTaskCreateDTO.getName(), e);
                // 终止当前任务分支的后续处理
                continue;
            }
            // 递归处理子任务的子任务
            self.createSubRepeatTasks(allRepeatTasks, project, subTask, currentTaskId, projectStartDate, projectEndDate,
                    allProjectReportSubjectMap, allProjectTaskTagRelaMap, allProjectTaskParticipantMap, allAttachmentsMap,
                    parentTaskStartDate, startedTaskIds, failDispatchTaskList, successDispatchTaskList);
        }
    }

    public List<Date> calculateTriggerTimes (SupProjectTask task, Date projectStartDate, Date projectEndDate){
        List<RemindRepeatRuleDTO> repeatRule = remindService.findRepeatRulesBySourceIdAndName(task.getId(), SupProjectConstant.SUP_PROJECT_TASK_SOURCE_NAME);
        RemindRepeatRuleDTO remindRepeatRuleDTO = repeatRule.get(0);
        RepeatTimeDTO repeatTimeDTO = remindService.analysisCronExpression(remindRepeatRuleDTO);
        repeatTimeDTO.setStartTime(projectStartDate);
        repeatTimeDTO.setEndTime(projectEndDate);
        return remindSupportService.findALLTriggerTimes(repeatTimeDTO, true, false);
    }

    public void setTaskDates(TaskCreateDTO taskCreateDTO, Date taskStartDate, SupProjectTask task) {
        taskCreateDTO.setStartDate(taskStartDate);
        Integer taskDurationDays = task.getTaskDurationDays();
        Date taskEndDate = calculateTaskEndDate(taskStartDate, taskDurationDays, task.getEndDayType());
        taskCreateDTO.setEndDate(taskEndDate);
    }


    public Date calculateTaskEndDate(Date taskStartDate, Integer taskDurationDays, Integer endDayType) {
        if (SupProjectDayTypeEnum.NATURAL_DAY.getCode().equals(endDayType)) {
            return DateUtils.addDays(taskStartDate, taskDurationDays);
        } else {
            return SupHolidayUtil.addWorkDays(taskStartDate, taskDurationDays);
        }
    }

    public void setTaskParticipantsAndAttachments(TaskCreateDTO taskCreateDTO, SupProjectTask task,
                                                            Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap,
                                                            Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap,
                                                            Map<Long, List<SupProjectTaskParticipant>> allProjectTaskParticipantMap,
                                                            Map<Long, List<Attachment>> allAttachmentsMap) {
        // 设置上报主体
        taskCreateDTO.setReportSubject(buildReportSubjectList(task, allProjectReportSubjectMap));
        // 设置标签
        taskCreateDTO.setTagIds(buildTagIdsList(task, allProjectTaskTagRelaMap));
        // 设置参与人
        List<SupProjectTaskParticipant> allParticipants = allProjectTaskParticipantMap.get(task.getId());
        taskCreateDTO.setMainParticipant(buildMainParticipant(allParticipants.stream()
                .filter(participant -> participant.getType().equals(MAIN_TYPE.getValue()))
                .collect(Collectors.toList())));
        taskCreateDTO.setAssistingParticipant(buildAssistantParticipants(allParticipants.stream()
                .filter(participant -> participant.getType().equals(ASSISTING_TYPE.getValue()))
                .collect(Collectors.toList())));
        taskCreateDTO.setSuperviseParticipant(buildSuperviseParticipant(allParticipants.stream()
                .filter(participant -> participant.getType().equals(SUPERVISE_TYPE.getValue()))
                .collect(Collectors.toList())));
        taskCreateDTO.setWatchingParticipants(buildWatchingParticipant(allParticipants.stream()
                .filter(participant -> participant.getType().equals(WATCHING_TYPE.getValue()))
                .collect(Collectors.toList())));
        // 设置附件
        List<Attachment> attachments = allAttachmentsMap.get(task.getId());
        if (CollectionUtils.isNotEmpty(attachments)) {
            UploadDTO uploadDTO = new UploadDTO();
            uploadDTO.setUploadFileList(AttachmentUtils.toNewUploadFileDtoList(attachments));
            taskCreateDTO.setUpload(uploadDTO);
        }
    }

    public List<Integer> buildReportSubjectList(SupProjectTask projectTask, Map<Long, List<SupProjectTaskReportSubject>> allProjectReportSubjectMap) {
        List<SupProjectTaskReportSubject> projectTaskReportSubjectList = allProjectReportSubjectMap.get(projectTask.getId());
        return CollectionUtils.isNotEmpty(projectTaskReportSubjectList)?
                projectTaskReportSubjectList.stream().map(SupProjectTaskReportSubject::getSubjectVal).collect(Collectors.toList()) :
                null;
    }

    public List<Long> buildTagIdsList(SupProjectTask projectTask, Map<Long, List<SupProjectTaskTagRela>> allProjectTaskTagRelaMap) {
        List<SupProjectTaskTagRela> projectTaskTagRelaList = allProjectTaskTagRelaMap.get(projectTask.getId());
        return CollectionUtils.isNotEmpty(projectTaskTagRelaList)?
                projectTaskTagRelaList.stream().map(SupProjectTaskTagRela::getTagId).collect(Collectors.toList()) :
                null;
    }

    public TaskParticipantBaseDTO buildMainParticipant(List<SupProjectTaskParticipant> mainParticipants) {
        if (CollectionUtils.isNotEmpty(mainParticipants)) {
            TaskParticipantBaseDTO mainParticipant = new TaskParticipantBaseDTO();
            mainParticipant.setDepId(mainParticipants.get(0).getDepId());
            mainParticipant.setEmpId(mainParticipants.get(0).getEmpId());
            mainParticipant.setType(mainParticipants.get(0).getType());
            return mainParticipant;
        }
        return null;
    }

    public List<TaskParticipantBaseDTO> buildAssistantParticipants(List<SupProjectTaskParticipant> assistantParticipants) {
        return CollectionUtils.isNotEmpty(assistantParticipants)?
                assistantParticipants.stream().map(assistantTask -> {
                    TaskParticipantBaseDTO taskParticipantBaseDTO = new TaskParticipantBaseDTO();
                    taskParticipantBaseDTO.setDepId(assistantTask.getDepId());
                    taskParticipantBaseDTO.setEmpId(assistantTask.getEmpId());
                    taskParticipantBaseDTO.setType(assistantTask.getType());
                    return taskParticipantBaseDTO;
                }).collect(Collectors.toList()) :
                null;
    }

    public TaskParticipantBaseDTO buildSuperviseParticipant(List<SupProjectTaskParticipant> superviseParticipants) {
        if (CollectionUtils.isNotEmpty(superviseParticipants)) {
            TaskParticipantBaseDTO taskParticipantBaseDTO = new TaskParticipantBaseDTO();
            taskParticipantBaseDTO.setDepId(superviseParticipants.get(0).getDepId());
            taskParticipantBaseDTO.setEmpId(superviseParticipants.get(0).getEmpId());
            taskParticipantBaseDTO.setType(superviseParticipants.get(0).getType());
            return taskParticipantBaseDTO;
        }
        return null;
    }

    public List<TaskParticipantBaseDTO> buildWatchingParticipant(List<SupProjectTaskParticipant> watchingParticipants) {
        return CollectionUtils.isNotEmpty(watchingParticipants)?
                watchingParticipants.stream().map(assistantTask -> {
                    TaskParticipantBaseDTO taskParticipantBaseDTO = new TaskParticipantBaseDTO();
                    taskParticipantBaseDTO.setDepId(assistantTask.getDepId());
                    taskParticipantBaseDTO.setEmpId(assistantTask.getEmpId());
                    taskParticipantBaseDTO.setType(assistantTask.getType());
                    return taskParticipantBaseDTO;
                }).collect(Collectors.toList()) :
                null;
    }

    public Date calculateSubTaskStartDate(Date parentTaskStartDate, List<Date> allSubTriggerTimes, Integer repeatType) {
        if (Objects.equals(SubProjectRepeatTypeEnum.REPEAT_BY_WEEK.getValue(),repeatType)) {
            return SupProjectUtil.getSameWeekDate(parentTaskStartDate, allSubTriggerTimes);
        } else if (Objects.equals(SubProjectRepeatTypeEnum.REPEAT_BY_MONTH.getValue(),repeatType)){
            return SupProjectUtil.getSameMonthDate(parentTaskStartDate, allSubTriggerTimes);
        }else if (Objects.equals(SubProjectRepeatTypeEnum.REPEAT_BY_QUARTER.getValue(),repeatType)){
            return SupProjectUtil.getSameQuarterDate(parentTaskStartDate, allSubTriggerTimes);
        }else if (Objects.equals(SubProjectRepeatTypeEnum.REPEAT_BY_HALF_YEAR.getValue(),repeatType)){
            return SupProjectUtil.getSameHalfYearDate(parentTaskStartDate, allSubTriggerTimes);
        }else if (Objects.equals(SubProjectRepeatTypeEnum.REPEAT_BY_YEAR.getValue(),repeatType)){
            return SupProjectUtil.getSameYearDate(parentTaskStartDate, allSubTriggerTimes);
        }
        return null;
    }

    public boolean checkNoRepeatParentTaskDispatch(Long parentTaskId, SupProject project){
        SupProjectTask parentTask = projectTaskDAO.getById(parentTaskId);
        boolean inDispatchRange = SupProjectUtil.isInDispatchRange(parentTask, project);
        if (!inDispatchRange){
            return false;
        }
        if (Objects.equals(SupProjectConstant.SUP_PROJECT_TASK_PARENT_ID_ZERO,parentTask.getParentId())){
            return true;
        }else {
            return checkNoRepeatParentTaskDispatch(parentTask.getParentId(), project);
        }
    }

}
