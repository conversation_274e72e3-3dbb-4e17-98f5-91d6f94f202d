package com.sinitek.bnzg.supervision.project.remind.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
@Data
@Slf4j
@Component
@ConfigurationProperties("bnzg.sup.project.remind")
public class SupProjectMessageRemindProperties {

    /**
     * 消息提醒外部链接地址
     */
    private String messageRemindProcessUrl = "/lowcode/form/form-render/sup-message-remind?id=%s&title=处理任务";
}
