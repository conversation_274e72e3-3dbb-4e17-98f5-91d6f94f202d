package com.sinitek.bnzg.supervision.project.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.project.auth.constant.SupProjectAuthConstant;
import com.sinitek.bnzg.supervision.project.auth.util.SupProjectAuthUtil;
import com.sinitek.bnzg.supervision.project.dto.SupProjectSearchResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTagResultDTO;
import com.sinitek.bnzg.supervision.project.enumerate.SubProjectRepeatTypeEnum;
import com.sinitek.bnzg.supervision.tag.dto.SupTagDTO;
import com.sinitek.bnzg.supervision.tag.service.ISupTagService;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
@Component
public class SubProjectResultFormat extends SupProjectSearchResultDTO implements ITableResultFormat<SupProjectSearchResultDTO> {

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private ISupTagService tagService;


    @Override
    public List<SupProjectSearchResultDTO> format(List<SupProjectSearchResultDTO> data) {
        if (CollUtil.isEmpty(data)) {
            return data;
        }
        // 收集所有审批人id,最后更新人id,所属部门id
        List<String> allOrgIds = Stream.of(
                        data.stream().map(SupProjectSearchResultDTO::getApproverId).filter(Objects::nonNull).collect(Collectors.toList()),
                        data.stream().map(SupProjectSearchResultDTO::getUpdaterId).filter(Objects::nonNull).collect(Collectors.toList()),
                        data.stream().map(SupProjectSearchResultDTO::getDepId).filter(Objects::nonNull).collect(Collectors.toList())
                ).flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> orgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(allOrgIds);
        // 上报主体
        Map<String, String> reportSubjectMap = this.enumService.getSirmEnumByCataLogAndType(
                SupConstant.DEFAULT_CATALOG, SupConstant.SUP_REPORT_SUBJECT);
        // 项目状态
        Map<String, String> projectStatusMap = this.enumService.getSirmEnumByCataLogAndType(
                SupConstant.DEFAULT_CATALOG, SupConstant.SUP_PROJECT_STATUS);
        //任务标签
        List<Long> allTagIds = data.stream()
                .filter(Objects::nonNull)
                .map(SupProjectSearchResultDTO::getTagIds)
                .map(Optional::ofNullable)
                .map(opt -> opt.orElseGet(Collections::emptyList))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        List<SupTagDTO> supTagDTOS = tagService.listByIds(allTagIds);
        Map<Long, SupTagDTO> tagMap = supTagDTOS.stream()
                .collect(Collectors.toMap(SupTagDTO::getId, Function.identity()));
        //查询编辑权限
        Set<Long> authedProjectIds = SupProjectAuthUtil.findAuthedProjectIds(SupProjectAuthConstant.RIGHT_TYPE_EDIT);
        data.forEach(item -> {
            item.setEditAuthFlag(authedProjectIds.contains(item.getId()));
            // 设置上报主体
            String reportSubjectNames = Optional.ofNullable(item.getReportSubjects())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(reportSubjectMap, id, ""))
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.joining(","));
            item.setReportSubjectNames(reportSubjectNames);
            // 设置项目状态
            item.setStatusName(MapUtils.getString(projectStatusMap, item.getStatus(), ""));
            // 设置审批人
            item.setApproverName(MapUtils.getString(orgIdAndNameMap, item.getApproverId(), ""));
            // 设置重复频率
            item.setRepeatTypeName(Objects.nonNull(item.getRepeatType()) && Objects.nonNull(SubProjectRepeatTypeEnum.getByValue(item.getRepeatType())) ? SubProjectRepeatTypeEnum.getByValue(item.getRepeatType()).getName() : "");
            //设置任务标签
            List<Long> tagIds = item.getTagIds();
            if (CollUtil.isNotEmpty(tagIds)){
                List<SupProjectTagResultDTO> tags = tagIds.stream()
                        .map(tagMap::get)
                        .filter(Objects::nonNull)
                        .map(tag -> new SupProjectTagResultDTO(tag.getId(), tag.getName()))
                        .collect(Collectors.toList());
                item.setTags(tags);
            }
            // 设置最后更新人
            item.setUpdaterName(MapUtils.getString(orgIdAndNameMap, item.getUpdaterId(), ""));
            // 设置所属部门
            item.setDepName(MapUtils.getString(orgIdAndNameMap, item.getDepId(), ""));
        });
        return data;
    }
}
