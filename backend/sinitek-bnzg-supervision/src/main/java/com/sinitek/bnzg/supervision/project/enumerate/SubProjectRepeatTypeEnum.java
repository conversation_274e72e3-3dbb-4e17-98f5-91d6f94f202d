package com.sinitek.bnzg.supervision.project.enumerate;

import com.sinitek.bnzg.supervision.project.message.SupProjectMessage;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * 重复频率枚举
 *
 * <AUTHOR>
 * @date 2025/1/2
 */
@Getter
@Slf4j
public enum SubProjectRepeatTypeEnum {

    NOT_REPEAT("永不", 0),
    REPEAT_BY_WEEK("每周", 8),
    REPEAT_BY_MONTH("每月", 9),
    REPEAT_BY_QUARTER("每季", 10),
    REPEAT_BY_YEAR("每年", 11),
    REPEAT_BY_HALF_YEAR("每半年", 12);

    private final String name;
    private final Integer value;

    SubProjectRepeatTypeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public static SubProjectRepeatTypeEnum getByValue(Integer value) {
        for (SubProjectRepeatTypeEnum item : SubProjectRepeatTypeEnum.values()) {
            if (item.getValue().equals(value) ) {
                return item;
            }
        }
        log.error("重复频率:[{}]，未找到对应重复频率枚举类", value);
        throw new BussinessException(SupProjectMessage.REPEAT_TYPE_NOT_FOUND);
    }
}
