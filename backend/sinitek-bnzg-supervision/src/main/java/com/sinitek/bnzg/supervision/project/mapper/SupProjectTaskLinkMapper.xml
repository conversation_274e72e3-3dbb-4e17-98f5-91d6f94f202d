<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinitek.bnzg.supervision.project.mapper.SupProjectTaskLinkMapper" >

    <select id="findByTaskId" resultType="com.sinitek.bnzg.supervision.project.po.SupProjectTaskLinkPO">
        select
        project_task_id as id,
        link_pre_project_task_id as linkPerProjectTaskId
        from
        sup_project_task_link
        where project_task_id = #{taskId}
    </select>
</mapper>