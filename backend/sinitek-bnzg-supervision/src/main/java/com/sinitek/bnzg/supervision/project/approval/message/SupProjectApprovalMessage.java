package com.sinitek.bnzg.supervision.project.approval.message;

/**
 * <AUTHOR>
 * @date 2025/1/7
 */
public class SupProjectApprovalMessage {

    private SupProjectApprovalMessage() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 审批参数错误
     */
    public static final String APPROVAL_PARAM_ERROR = "com.sinitek.bnzg.supervision.project.approval.approval_param_error";

    /**
     * 项目已经被审批
     */
    public static final String PROJECT_HAVE_BEEN_APPROVED = "com.sinitek.bnzg.supervision.project.approval.project_have_been_approved";


    /**
     * 项目审批记录不存在
     */
    public static final String PROJECT_APPROVAL_RECORD_NOT_EXIST = "com.sinitek.bnzg.supervision.project.approval.project_approval_record_not_exist";


    /**
     * 该项目在审批中
     */
    public static final String PROJECT_APPROVING = "com.sinitek.bnzg.supervision.project.approval.project_approving";


    /**
     * 项目审批人和当前用户不一致
     */
    public static final String PROJECT_APPROVER_ERROR = "com.sinitek.bnzg.supervision.project.approval.project_approver_error";


}
