package com.sinitek.bnzg.supervision.project.approval.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTagResultDTO;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@ApiModel(value = "任务督办-项目审批-分页查询DTO")
public class SupProjectApprovalSearchResultDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目标题")
    private String title;

    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    @ApiModelProperty(value = "操作类型名称")
    private String operateTypeName;

    @ApiModelProperty(value = "所属部门")
    private String depId;

    @ApiModelProperty(value = "所属部门名称")
    private String depName;

    @ApiModelProperty(value = "上报主体id")
    private List<Integer> reportSubjects;

    @ApiModelProperty(value = "上报主体名称")
    private String reportSubjectNames;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "生效开始日期")
    private Date startDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "生效结束日期")
    private Date endDate;

    @ApiModelProperty(value = "任务标签id集合")
    private List<Long> tagIds;

    @ApiModelProperty(value = "任务标签-用于展示")
    private List<SupProjectTagResultDTO> tags;

    @ApiModelProperty(value = "申请人id")
    private String applicantId;

    @ApiModelProperty(value = "申请人名称")
    private String applicantName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("申请时间")
    private Date applicationTime;

    @ApiModelProperty("审批结果")
    private Integer approveStatus;

    @ApiModelProperty("审批结果名称")
    private String approveStatusName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date approveTime;
}