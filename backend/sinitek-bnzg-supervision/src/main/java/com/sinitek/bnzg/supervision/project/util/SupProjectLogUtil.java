package com.sinitek.bnzg.supervision.project.util;

import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.project.dto.SupProjectLogResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectLogSearchDTO;
import com.sinitek.bnzg.supervision.project.po.SupProjectLogResultPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectLogSearchPO;
import com.sinitek.sirm.common.sirmenum.support.EnumTableResultFormat;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.framework.frontend.support.MultiTableResultFormat;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import com.sinitek.sirm.org.support.EmpNameTableResultFormat;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SupProjectLogUtil {

    public static SupProjectLogSearchPO makeSearchParamDTO2PO(SupProjectLogSearchDTO dto){
        SupProjectLogSearchPO supProjectLogSearchPO = new SupProjectLogSearchPO();
        BeanUtils.copyProperties(dto, supProjectLogSearchPO);
        return supProjectLogSearchPO;
    }

    public static SupProjectLogResultDTO makeSearchResultPO2DTO(
            SupProjectLogResultPO po) {
        return LcConvertUtil.convert(po, SupProjectLogResultDTO::new);
    }

    public static ITableResultFormat<SupProjectLogResultDTO> getSupProjectLogResult(){
        List<ITableResultFormat<SupProjectLogResultDTO>> list = Arrays.asList(
                new EnumTableResultFormat<>("status", "statusName",
                        SupConstant.DEFAULT_CATALOG, SupConstant.SUP_PROJECT_LOG_STATUS),
                new EmpNameTableResultFormat<>("operatorId", "operatorName"),
                new EmpNameTableResultFormat<>("approverId", "approverName")
        );
        return MultiTableResultFormat.build(list);
    }
}
