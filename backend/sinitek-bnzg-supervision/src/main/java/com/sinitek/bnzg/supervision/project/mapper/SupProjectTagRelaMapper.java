package com.sinitek.bnzg.supervision.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTagRela;
import com.sinitek.bnzg.supervision.task.po.SupTagResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/28
 */
public interface SupProjectTagRelaMapper extends BaseMapper<SupProjectTagRela> {

    List<SupTagResultPO> findByProjectId(@Param("projectId") Long projectId);

}
