package com.sinitek.bnzg.supervision.project.dto;

import com.sinitek.bnzg.supervision.project.message.SupProjectMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 * 项目任务 - 详情DTO
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class SupProjectTaskDetailResultDTO extends SupProjectTaskBaseDTO{


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "上报主体")
    @NotEmpty(message = "{"+ SupProjectMessage.REPORT_SUBJECTS_CAN_NOT_BE_NULL +"}")
    private List<Integer> reportSubjects;

    @ApiModelProperty(value = "任务标签")
    private List<Long> tagIds;

    @ApiModelProperty(value = "一星期中某天")
    private Integer dayOfWeek;

    @ApiModelProperty(value = "一个月中某天")
    private Integer dayOfMonth;

    @ApiModelProperty(value = "季度内第几月")
    private Integer monthNumber;

    @ApiModelProperty("月份")
    private Integer monthOfYear;

    @ApiModelProperty(value = "主办部门id")
    @NotEmpty(message = "{"+ SupProjectMessage.LEAD_DEPT_ID_CAN_NOT_BE_NULL +"}")
    private String leadDeptId;

    @ApiModelProperty(value = "主办人id")
    @NotEmpty(message = "{"+ SupProjectMessage.LEAD_EMP_ID_CAN_NOT_BE_NULL +"}")
    private String leadEmpId;

    @ApiModelProperty(value = "协办人集合")
    private List<SupProjectTaskParticipantResultDTO> organizingParticipantDTOs;

    @ApiModelProperty(value = "督办部门id")
    private String supervisingDeptId;

    @ApiModelProperty(value = "督办人id")
    private String supervisingEmpId;

    @ApiModelProperty(value = "关注部门参与人集合")
    private List<SupProjectTaskParticipantResultDTO> monitoringParticipantDTOs;

    @ApiModelProperty(value = "上级任务名称")
    private String parentName;

    @ApiModelProperty(value = "上报主体名称")
    private String reportSubjectNames;

    @ApiModelProperty(value = "任务标签-用于展示")
    private List<SupProjectTagResultDTO> tags;

    @ApiModelProperty(value = "优先级名称")
    private String priorityName;

    @ApiModelProperty(value = "开始日期描述")
    private String startDateDesc;

    @ApiModelProperty(value = "结束日期描述")
    private String endDateDesc;

    @ApiModelProperty(value = "主办部门名称")
    private String leadDeptName;

    @ApiModelProperty(value = "主办人名称")
    private String leadEmpName;

    @ApiModelProperty(value = "督办部门名称")
    private String supervisingDeptName;

    @ApiModelProperty(value = "督办人名称")
    private String supervisingEmpName;

    @ApiModelProperty("附件id")
    private Long attachmentId;

    @ApiModelProperty("是否同步修改下级标签名称")
    private String syncTagFlagName;

    @ApiModelProperty("所属项目名称")
    private String projectName;

    @ApiModelProperty(value = "任务名称-(拼接参数)")
    private String name;

    @ApiModelProperty(value = "重复频率类型名称")
    private String repeatTypeName;

}
