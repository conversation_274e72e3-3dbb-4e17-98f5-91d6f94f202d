package com.sinitek.bnzg.supervision.project.approval.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
@ApiModel(value = "项目批量审批DTO")
public class SupProjectBatchApprovalDTO {

    @ApiModelProperty(value = "id集合")
    private List<Long> ids;

    @ApiModelProperty(value = "审批状态。1：通过 ，0：驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "审批意见")
    private String opinion;
}
