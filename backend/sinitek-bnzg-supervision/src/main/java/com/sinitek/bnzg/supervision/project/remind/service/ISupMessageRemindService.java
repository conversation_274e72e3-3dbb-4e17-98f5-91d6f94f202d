package com.sinitek.bnzg.supervision.project.remind.service;

import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindDTO;
import com.sinitek.bnzg.supervision.project.remind.dto.MessageRemindResultDTO;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */
public interface ISupMessageRemindService {

    /**
     * 创建消息提醒
     * @param messageRemindDTO 参数
     */
    void createMessageRemind(MessageRemindDTO messageRemindDTO);

    /**
     * 删除消息提醒
     * @param sourceName  来源名称
     * @param sourceId 来源id
     * @param type 类型
     * @see com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindTypeEnum
     */
    void deleteMessageRemind(String sourceName, Long sourceId, Integer type);

    /**
     * 发送消息提醒
     */
    void sendMessageRemind();

    /**
     *  获取处理结果
     */
    MessageRemindResultDTO getDealResultById(Long id,Long participantId);
}
