package com.sinitek.bnzg.supervision.project.remind.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */
@Data
@ApiModel(value = "消息提醒-基础DTO")
public class MessageRemindDTO {

    @ApiModelProperty(value = "来源名称")
    private String sourceName;

    @ApiModelProperty(value = "来源记录ID")
    private Long sourceId;

    /**
     * @see com.sinitek.bnzg.supervision.project.remind.enumerate.SupMessageRemindTypeEnum
     */
    @ApiModelProperty(value = "提醒类型")
    private Integer type;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "提交人ID")
    private String submitId;

    @ApiModelProperty(value = "项目或任务名称")
    private String name;

    @ApiModelProperty(value = "收件人id")
    private List<String> receiverIds;

}
