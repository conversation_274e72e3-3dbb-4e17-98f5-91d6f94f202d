package com.sinitek.bnzg.supervision.project.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.supervision.project.dao.SupProjectLogDAO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectLogResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectLogSearchDTO;
import com.sinitek.bnzg.supervision.project.po.SupProjectLogResultPO;
import com.sinitek.bnzg.supervision.project.po.SupProjectLogSearchPO;
import com.sinitek.bnzg.supervision.project.service.ISupProjectLogService;
import com.sinitek.bnzg.supervision.project.util.SupProjectLogUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/30
 */
@Slf4j
@Service
@Setter(onMethod = @__({@Autowired}))
public class SupProjectLogServiceImpl  implements ISupProjectLogService {

    private SupProjectLogDAO supProjectLogDAO;


    @Override
    public IPage<SupProjectLogResultDTO> searchSupProjectLog(SupProjectLogSearchDTO dto) {
        SupProjectLogSearchPO supProjectLogSearchPO = SupProjectLogUtil.makeSearchParamDTO2PO(dto);
        IPage<SupProjectLogResultPO> result =
                supProjectLogDAO.search(supProjectLogSearchPO.buildPage(), supProjectLogSearchPO);
        return   result.convert(SupProjectLogUtil::makeSearchResultPO2DTO);
    }
}
