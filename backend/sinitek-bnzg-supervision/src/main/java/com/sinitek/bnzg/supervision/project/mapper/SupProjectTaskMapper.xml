<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinitek.bnzg.supervision.project.mapper.SupProjectTaskMapper">
    <update id="resetTaskRepeatRule">
        update sup_project_task
        set repeat_type = null,
        task_duration_days = null,
        end_day_type = null,
        start_date = null,
        end_date = null,
        task_name_param = null
        where id in
        <foreach collection="allTaskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <delete id="deleteByIds">
        delete from sup_project_task
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="findProjectTaskIdByProjectId" resultType="java.lang.Long">
        select id
        from
        sup_project_task
        <where>
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
        </where>
    </select>
</mapper>