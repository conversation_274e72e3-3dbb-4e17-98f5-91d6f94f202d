package com.sinitek.bnzg.supervision.project.approval.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.supervision.common.constant.SupConstant;
import com.sinitek.bnzg.supervision.project.approval.dto.SupProjectApprovalSearchResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTagResultDTO;
import com.sinitek.bnzg.supervision.tag.dto.SupTagDTO;
import com.sinitek.bnzg.supervision.tag.service.ISupTagService;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
public class SupProjectApprovalSearchResultFormat <T extends SupProjectApprovalSearchResultDTO> implements ITableResultFormat<T> {
    @Override
    public List<T> format(List<T> data) {
        if (CollectionUtils.isEmpty(data)) {
            return data;
        }
        IEnumService enumService = SpringFactory.getBean(IEnumService.class);
        ISupTagService tagService = SpringFactory.getBean(ISupTagService.class);
        // 上报主体
        Map<String, String> reportSubjectMap = enumService.getSirmEnumByCataLogAndType(
                SupConstant.DEFAULT_CATALOG, SupConstant.SUP_REPORT_SUBJECT);
        //任务标签
        List<Long> allTagIds = data.stream()
                .map(SupProjectApprovalSearchResultDTO::getTagIds)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        List<SupTagDTO> supTagDTOS = tagService.listByIds(allTagIds);
        Map<Long, SupTagDTO> tagMap = supTagDTOS.stream()
                .collect(Collectors.toMap(SupTagDTO::getId, Function.identity()));
        data.forEach(item -> {
            // 设置上报主体
            String reportSubjectNames = Optional.ofNullable(item.getReportSubjects())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(reportSubjectMap, id, ""))
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.joining(","));
            item.setReportSubjectNames(reportSubjectNames);
            //设置任务标签
            List<Long> tagIds = item.getTagIds();
            if (CollUtil.isNotEmpty(tagIds)){
                List<SupProjectTagResultDTO> tags = tagIds.stream()
                        .map(tagMap::get)
                        .filter(Objects::nonNull)
                        .map(tag -> new SupProjectTagResultDTO(tag.getId(), tag.getName()))
                        .collect(Collectors.toList());
                item.setTags(tags);
            }
        });
        return data;
    }
}
