package com.sinitek.bnzg.supervision.project.service.impl;

import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskDAO;
import com.sinitek.bnzg.supervision.project.dao.SupProjectTaskLinkDAO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkDetailResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkSaveDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectTaskLinkSearchResultDTO;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTask;
import com.sinitek.bnzg.supervision.project.entity.SupProjectTaskLink;
import com.sinitek.bnzg.supervision.project.po.SupProjectTaskLinkPO;
import com.sinitek.bnzg.supervision.project.service.ISupProjectTaskLinkService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2025/2/7
 */
@Slf4j
@Service
@Setter(onMethod = @__({@Autowired}))
public class SupProjectTaskLinkServiceImpl implements ISupProjectTaskLinkService {

    private SupProjectTaskLinkDAO projectTaskLinkDAO;

    private SupProjectTaskDAO projectTaskDAO;

    @Override
    public List<SupProjectTaskLinkSearchResultDTO> findPreTask(Long taskId) {
        SupProjectTask projectTask = projectTaskDAO.getById(taskId);
        Long projectId = projectTask.getProjectId();
        List<SupProjectTask> projectTaskList = projectTaskDAO.findByProjectId(projectId);
        if (CollectionUtils.isEmpty(projectTaskList)){
            return Collections.emptyList();
        }
        return projectTaskList.stream()
                .filter(task -> !Objects.equals(task.getId(), taskId))
                .map(task -> {
                    SupProjectTaskLinkSearchResultDTO resultDTO = new SupProjectTaskLinkSearchResultDTO();
                    BeanUtils.copyProperties(task, resultDTO);
                    return resultDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public SupProjectTaskLinkDetailResultDTO detail(Long taskId) {
        SupProjectTaskLinkDetailResultDTO result = new SupProjectTaskLinkDetailResultDTO();
        SupProjectTask projectTask = projectTaskDAO.getById(taskId);
        BeanUtils.copyProperties(projectTask, result);
        List<SupProjectTaskLinkPO> projectTaskLinkPOList =projectTaskLinkDAO.findByTaskId(taskId);
        if (CollectionUtils.isEmpty(projectTaskLinkPOList)){
            result.setLinkPerProjectTaskIds(Collections.emptyList());
            return result;
        }
        result.setLinkPerProjectTaskIds(projectTaskLinkPOList.stream().map(SupProjectTaskLinkPO::getLinkPerProjectTaskId).collect(Collectors.toList()));
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(SupProjectTaskLinkSaveDTO dto) {
        projectTaskLinkDAO.deleteByTaskId(dto.getId());
        if (CollectionUtils.isEmpty(dto.getLinkPerProjectTaskIds())){
            return;
        }
        List<SupProjectTaskLink> projectTaskLinks = dto.getLinkPerProjectTaskIds().stream()
                .map(taskId -> {
                    SupProjectTaskLink projectTaskLink = new SupProjectTaskLink();
                    projectTaskLink.setProjectTaskId(dto.getId());
                    projectTaskLink.setLinkPreProjectTaskId(taskId);
                    return projectTaskLink;
                }).collect(Collectors.toList());
        projectTaskLinkDAO.saveBatch(projectTaskLinks);
        // todo 修改前置任务后，已派发任务中“未开始”状态的任务的钩稽关系重置
    }

}
