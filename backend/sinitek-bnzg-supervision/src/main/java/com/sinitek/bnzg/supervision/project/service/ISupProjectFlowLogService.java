package com.sinitek.bnzg.supervision.project.service;

import com.sinitek.bnzg.supervision.project.dto.SupProjectFlowLogResultDTO;
import com.sinitek.bnzg.supervision.project.dto.SupProjectFlowLogSaveDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31
 */
public interface ISupProjectFlowLogService {

    List<SupProjectFlowLogResultDTO>  findSupProjectFlowLog(Long projectId);


    void saveOrUpdateSupProjectFlowLog(SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO);

    Long saveSupProjectFlowLog(SupProjectFlowLogSaveDTO supProjectFlowLogSaveDTO);



}
