package com.sinitek.bnzg.supervision.project.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.sinitek.data.model.tree.entity.BaseNodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 项目任务 Entity
 *
 * <AUTHOR>
 * date 2025-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_task")
@ApiModel(value = "项目任务-实体")
public class SupProjectTask extends BaseNodeEntity {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 名称冗余
     */
    @ApiModelProperty(value = "名称冗余")
    private String nameVal;

    /**
     * 任务名称参数
     */
    @ApiModelProperty(value = "任务名称参数")
    private Integer taskNameParam;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 制度发文/监管要求/其他任务背景
     */
    @ApiModelProperty(value = "制度发文/监管要求/其他任务背景")
    private String sourceDescription;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    private String taskBrief;

    /**
     * 任务处理要求
     */
    @ApiModelProperty(value = "任务处理要求")
    private String taskRequirements;

    /**
     * 上传文档要求
     */
    @ApiModelProperty(value = "上传文档要求")
    private String attachmentRequirements;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    /**
     * 重复频率类型
     */
    @ApiModelProperty(value = "重复频率类型")
    private Integer repeatType;

    /**
     * 任务持续天数
     */
    @ApiModelProperty(value = "任务持续天数")
    private Integer taskDurationDays;

    /**
     * 结束日类型
     */
    @ApiModelProperty(value = "结束日类型")
    private Integer endDayType;

    /**
     * 项目任务状态
     */
    @ApiModelProperty(value = "项目任务状态")
    private Integer status;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer removeFlag;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
    * 任务开始日期
    */
    @ApiModelProperty(value = "任务开始日期")
    private Date startDate;

    /**
    * 任务结束日期
    */
    @ApiModelProperty(value = "任务结束日期")
    private Date endDate;

    /**
    * 是否同步修改下级标签 0 否 1 是
    */
    @ApiModelProperty("是否同步修改下级标签")
    private Integer syncTagFlag;

}
