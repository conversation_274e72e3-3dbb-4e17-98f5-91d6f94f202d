package com.sinitek.bnzg.supervision.project.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/01/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sup_project_task_report_subject")
@ApiModel(value = "项目任务上报主体-实体")
public class SupProjectTaskReportSubject extends BaseEntity {

    /**
     * 上报主体对应的枚举值
     */
    @ApiModelProperty(value = "上报主体对应的枚举值")
    private Integer subjectVal;

    /**
     * 项目任务id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectTaskId;

}
