<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinitek.bnzg</groupId>
    <artifactId>sinitek-bnzgsjhg</artifactId>
    <version>1.2.2</version>
  </parent>

  <artifactId>sinitek-bnzg-audit</artifactId>
  <packaging>jar</packaging>

  <properties>
    <poi-tl.version>1.12.2</poi-tl.version>
    <aspose-words.version>21.6</aspose-words.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.sinitek.bnzg</groupId>
      <artifactId>sinitek-bnzg-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.deepoove</groupId>
      <artifactId>poi-tl</artifactId>
      <version>${poi-tl.version}</version>
    </dependency>

    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <version>${aspose-words.version}</version>
      <classifier>jdk17</classifier>
    </dependency>

    <dependency>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sinitek-sirmapp</artifactId>
        <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.sinitek.sinicube</groupId>
      <artifactId>sinitek-lowcode-test-tools</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <scope>test</scope>
    </dependency>

    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
  </dependencies>

</project>
