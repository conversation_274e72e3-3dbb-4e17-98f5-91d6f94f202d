<?xml version="1.0" encoding="UTF-8"?>
<configuration status="warn" monitorInterval="30">

    <appenders>
        <Routing name="Routing">
          <Routes pattern="$${ctx:ROUTINGKEY}">
              <!-- This route is chosen if ThreadContext has no value for key ROUTINGKEY. -->
              <Route key="$${ctx:ROUTINGKEY}">
                <RollingFile name="rolling-default" fileName="${sys:catalina.home}/logs/sinitek-sirmapp_app.log"
                                               filePattern="${sys:catalina.home}/logs/$${date:yyyy-MM}/sinitek-sirmapp_app-%d{yyyy-MM-dd}-%i.log">
                    <PatternLayout pattern="[%p][SIRM][%pid,%T,%t][%d{yyyy-MM-dd HH:mm:ss,SSS}][%C{10}.%M:%L] - %m%n"/>
                    <Policies>
                        <TimeBasedTriggeringPolicy/>
                        <SizeBasedTriggeringPolicy size="20 MB"/>
                    </Policies>
                </RollingFile>
              </Route>
              <!-- This route is chosen if ThreadContext has a value for ROUTINGKEY
                   (other than the value 'special' which had its own route above).
                   The value dynamically determines the name of the log file. -->
              <Route>
                <RollingFile name="routingkey-sirmfile" fileName="${sys:catalina.home}/logs/sinitek-${ctx:ROUTINGKEY}-sirmapp_app.log"
                                         filePattern="${sys:catalina.home}/logs/$${date:yyyy-MM}/sinitek-${ctx:ROUTINGKEY}-sirmapp_app-%d{yyyy-MM-dd}-%i.log">
                    <PatternLayout pattern="[%p][SIRM][%pid,%T,%t][%d{yyyy-MM-dd HH:mm:ss,SSS}][%C{10}.%M:%L] - %m%n"/>
                    <Policies>
                        <TimeBasedTriggeringPolicy/>
                        <SizeBasedTriggeringPolicy size="20 MB"/>
                    </Policies>
                </RollingFile>
              </Route>
          </Routes>
      </Routing>
      <RollingFile name="sirmfile" fileName="${sys:catalina.home}/logs/sinitek-sirmapp_app.log"
                                     filePattern="${sys:catalina.home}/logs/$${date:yyyy-MM}/sinitek-sirmapp_app-%d{yyyy-MM-dd}-%i.log">
          <PatternLayout pattern="[%p][SIRM][%pid,%T,%t][%d{yyyy-MM-dd HH:mm:ss,SSS}][%C{10}.%M:%L] - %m%n"/>
          <Policies>
              <TimeBasedTriggeringPolicy/>
              <SizeBasedTriggeringPolicy size="20 MB"/>
          </Policies>
      </RollingFile>
      <console name="Console" target="SYSTEM_OUT">
          <PatternLayout pattern="[%p][SIRM][%pid,%T,%t][%d{yyyy-MM-dd HH:mm:ss,SSS}][%C{10}.%M:%L] - %m%n"/>
      </console>
    </appenders>
    <loggers>
        <logger name="com.sinitek" level="INFO" additivity="false" >
            <appender-ref ref="Console"/>
            <appender-ref ref="sirmfile"/>
        </logger>

        <logger name="SIRMLOG" level="DEBUG" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="sirmfile"/>
        </logger>

        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="sirmfile"/>
        </logger>

        <root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="sirmfile"/>
        </root>
    </loggers>

</configuration>
