server:
  port: 8096
  servlet:
    encoding:
      charset: utf-8
      enabled: true
      force: true

spring:
  profiles:
    active: dev
  mvc:
    servlet:
      load-on-startup: 1
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  application:
    name: BNZGAPP
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1

  cloud:
    nacos:
      discovery:
        enabled: false

  # 默认排除的自动配置类
#  autoconfigure:
#    exclude:
#      - com.sinitek.sirm.log.config.RequestLogAutoConfiguration
  quartz:
    job-store-type: jdbc
    properties:
      # 调度标识名,集群中每一个实例都必须使用相同的名称
      org.quartz.scheduler.instanceName: MyQuartzScheduler
      org.quartz.scheduler.instanceId: AUTO
      org.quartz.threadPool.threadCount: 15
      org.quartz.jobStore.isClustered: true
      org.quartz.jobStore.clusterCheckinInterval: 15000
      org.quartz.jobStore.misfireThreshold: 60000

mybatis-plus:
  configuration:
    # 开始状态打印Mybatis日志,默认注释
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    jdbc-type-for-null: 'null' #注意：单引号
  # 扫描该包下的 typehandler类，注册到Mybatis
  typeHandlersPackage: com.sinitek.data.mybatis.typehandlers
  global-config:
    # 是否关闭MP3.0自带的banner
    banner: true
    db-config:
      # keepGlobalFormat=true的字段会根据该值进行String.format,主要处理数据库关键字
      # '`%s`' 为mysql,  '%s' 为 oracle
      column-format: '`%s`'
      #主键类型
      id-type: 3
      #是否开启自定义的 IDGenerator策略
      id-customer-enable: false
      #是否开启兼容EntityName(使用Metadb_Entity表的记录进行兼容)
      compatible-entityname-enable: true
      #更新的时候是否判断设置为null,默认是跳过 null的更新的。现在设置为 null 也是可以更新。
      update-strategy: ignored

sinicube:
  independent: false
  cloud:
    ## 设置当前的服务类型, MASTER(主服务) 、MICROSERVICE(子服务,不配置默认为子服务)
    type: MASTER
  message:
    independent: false

  ## 加密算法配置
  encryption-algorithm:
    ## 非对称加密算法的配置
    asymmetric:
      algorithm: RSA
      rsa:
        defaultKey:
          privateKey: 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o='
          publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB'
    symmetry:
      algorithm: AES
      aes:
        defaultKey: 'sirmpasswordcryp'

  export:
    ## 全部导出获取接口数据时,是否使用系统中的主机域名地址作为前缀,默认为false
    use-host-address: false

  csrf:
    ## 是否启用跨站点请求验证,默认为false
    enable: false

    ## 信任的白名单列表
    whiteList:

  rsa:
    privateKey: 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o='
    publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB'

  i18n:
    # 是否开启国际化
    enable: false
    # 后端加载的message
    # basename: classpath:message/messages-common,classpath:message/messages-sirmapp,classpath:message/messages-workflow,classpath:message/messages-org
    # 默认的message编码
    defaultEncoding: UTF-8
    # code找不到对应的message时,是否默认使用code返回
    useCodeAsDefaultMessage : true
    # 默认的语言环境
    defaultLocale : zh_CN
    # 系统支持的语言环境
    supportedLocaleInfoList:
      - locale: zh_CN
        localeName: 简体中文
        localeChineseName: 简体中文
      - locale: zh_HK
        localeName: 繁體中文
        localeChineseName: 繁体中文
    # 语言环境切换时统一使用的headerName
    headerName: lang

  attachment:
    #附件存放地址
    store-home: ./sirmapp/attachstore/
    #附件存放方式 : db、file、fastDFS、minio
    store-type: db

  tempdir:
    #临时目录
    path: ./sirmapp/temp/

  business-log:
    # 日志路径
    log-file-root-path: ./sirmapp/logs/

  interceptors:
    frontend-api:
      excludePatterns:
        - /frontend/api/**
        - /test/**
        - /proxy/**

# knife4j相关的配置
knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    username: admin
    password: sinitek-swagger

service-auth:
  jwt:
    # 过期秒数
    expiration: 7200
    # jwt加密密钥
    secret: mySecret

## 全局加解密、验签名
sirm:
  encrypt:
    encryptStrategy: ALL
    signDebug: true
    debug: true
    responseEncryptUriIgnoreList: /frontend/api/login,/frontend/api/login-init,/frontend/api/ue/exec,/frontend/api/captcha,/frontend/api/security/public-key,/frontend/api/properties,doc.html,/sinicube/check/**
    requestDecyptUriIgnoreList: /frontend/api/login,/frontend/api/login-init,/frontend/api/ue/exec,/frontend/api/captcha,/frontend/api/security/public-key,/frontend/api/properties,doc.html,/sinicube/check/**
    ignoreSuffix: .html,.json

bnzg:
  global:
    aspose:
      license: CX7fJpdCR+8i4GhfrVQK82/XCQ5HC9/kvBUbvFRwrfhh2vGC8ncvCSurj1nYewFNgZlXxvQnrwqyEHHTvVKV8V88Lplwbm9tusHwKcw863iN84bEoGMI+U390YNzGRLenLr1vg52IekzgfqQyYR9R8dYEuOwjCCtv9C9gTodxzR8KAKxmUvGhMNMyjNmr6ei38bLu62SFqhu0ukGIutQ7dNooG6nLrwwIB1flR/7B/ZBO3T8tZTWiyQ/zSvfObX6cHyZywIwFC0sqFPEEK4sY9Bf35PFPQGPMyS6NvteOZtUdLz3txMYKM23u8yAfLQOFEsP8KaedohDFEYCdXBs5oh6WuXrEiVLwwfdTDU4bA1NCQ5bQ9P7uVTlCRcSk9PsQHdswlA5Gh5unEUMi3M4ElEFJ0fxb0/JUXJ9wHmmHUrFftmsUS75UZeLinNGelKuikZpDoA3tMP9UYUl0dVN9jg/BNXB/cY3h86wCDqzf4uU6PIUC3Thf4V9caCsbLdusr7xmm+HcndNuXc17fBPwavIXBaxERL+EkCaX38o7X002KzMOgmERIvRKDubVUvqQRyABbfemXtzC1TD2TUYns0e9b96k6Vmck7YBpINMxXjMbruJt1rWNqgRpPaWJN4lyPQqAJHdkAzsyI8aplJ1+h2Vx1G1EeDc3MgT0v0oyLb7xSt/MX8PJAptO9EyEwzSLiWRS5DmQfsh0vOvx2Ra/GwIRiYmSXde9Rwj9EMq5aS5bLzIqAlsIBjWyeDVNqfb7cXBNxYwTYTm4HdNlw91wwfIY11DmLeaFu5sXlDZEeQmwHEyEf7k6lmINv1vNJ7ApY9t/KvWraGXLNuaL01V5vKAX3cOxFpc0p/LJ+9Nfh0WSJoPH7W8EyyZNMzdsXH+Q0oNoOfPf3FGgsgpRmO4exG1OHFe2gJ6KuaRkehrcq1gPSGEOhPGUybXcgtKjwPOvowKe2fIvvB9a3bNSfYrDedpCsGjMDqMOrnPSl5lAEhpU9nYuEH8f13rD+JbDk7+MYoFD8kNBvbfremuY6aiz9wqRu/ttvounLVq4s68A+ijKCTP9ggPLP9A9EuHqabCvfBW+CfDPI9I+VWuyulmsSnO2VxfLd1bzShaJ/6sOi2QxFlhzAayWoWIAgW+R3RcG2A05bqODW5L0zCEeiTLzZa9F9AMaENouBV6soZbjW5qRLe9nGx1t0YU3w8ebxYkSoB3MIwMHRjHvhNSObuyjWdM14ER14nN7xcHX+y5Xm/8Zx8o9uoqVyw04xPy70LMw3yZvFE931ExBMXmwGeyWcp2D0HVh2ZH8jfGBwfQGpqkJJfhuxevSBRa9bGI3XL2iuDjz1iXDaQtPE0EZr6aFWrJtVjwxQbRtbQC7Bp1fo2JJviTw2WUBb7wxp/IVfuwAN/u/IzOf9DAPijncRUYnTUfn6X28KpPn4vPW7GczY/lNnUdP9bZO1TnDi1cKbWQvbqPg7Ug9cHzbcCJb3JX8w4gDAHNzTVs6/Or18WJKbR3KQ2y7s9dPX1mK84m2l4KRIf9GdjUoePz5Ufa6bJJYROBR40MTHC1C+xKKoDpR//OAHlyd3Gh4N1J4/HLAwBfdm5lt6tRsnqaGRT+qVT9UBUpI1gW2aKxBCKVn/tjnGOMuPAgffd2XTDrT/s7J0duk1l+p5NLrT9bXNj3LJcJaey7AGhcw5dRzFXGGQdRLYuPKUePZepOMMcSPJFvZP6k+lHaTFUuHsM6A7LHwsKA6Sz9tpT6sNOjVJX7PA69lBQaKJyCd0yLal4HUpQ0lMTwmL+u97avJWcomn3qbCtVnVrhxExXXyK/h2aeYTuCg7n4swYZtlUERVvAuGuKq1YP+XxyJhv7wItbY9eX3909NXSyUHOYbcuZsHb+z63Clc=
      key: asposeofwordkeys