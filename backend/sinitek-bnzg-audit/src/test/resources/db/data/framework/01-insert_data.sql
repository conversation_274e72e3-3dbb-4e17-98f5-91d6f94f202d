SET NAMES 'utf8';
SET FOREIGN_KEY_CHECKS=0;

INSERT INTO `sirm_groupsetting`(`objid`, `name`, `catalogcode`, `url`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `I18N`) VALUES (4, '菜单', 'frontend-rightreport', 'menu', 2, now(), now(), 467, 'SIRMGROUPSETTING', 'groupsetting.menu');
INSERT INTO `sirm_groupsetting`(`objid`, `name`, `catalogcode`, `url`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `I18N`) VALUES (6, '首页', 'frontend-rightreport', 'homepage', 3, now(), now(), 467, 'SIRMGROUPSETTING', 'groupsetting.home');

-- ----------------------------
-- Table structure for metadb_entity
-- ----------------------------
DELETE FROM `metadb_entity`;

-- ----------------------------
-- Records of metadb_entity
-- ----------------------------

INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('APPLOADER', 'Commom', 'APP启动加载项', 'SIRM_APPLOADER', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.loader.entity.IAppLoader', 'com.sinitek.sirm.common.loader.entity.impl.AppLoaderImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ATTACHMENT', 'Commom', '附件信息', 'SIRM_Attachment', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.attachment.entity.IAttachment', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('CACHEINFO', 'Commom', '缓存信息', 'SIRM_CACHEINFO', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.cache.entity.ICacheInfo', 'com.sinitek.sirm.common.cache.entity.CacheInfoImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('CALENDAR', 'OA', '会议日程', 'RT_Calendar', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ICalendar', 'com.sinitek.sirm.busin.routine.entity.CalendarImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('DEMOOUTAPPLY', 'DEMO', '外出申请demo表', 'DEMO_OUTAPPLY', null, '0', '0', '0', '0', 'com.sinitek.sirm.web.workflow.demo.dto.IDemoOutApply', 'com.sinitek.sirm.web.workflow.demo.dto.DemoOutApplyImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('DEMOWFTESTENTITY', 'DEMO', '测试工作流用实体表', 'DEMO_WFTESTENTITY', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.demo.entity.IWFTestEntity', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('EVENTACTIONPROPERTY', 'Commom', '事件行为属性表', 'SIRM_EVENTACTIONPROPERTY', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.engine.event.entity.IEventActionProperty', 'com.sinitek.sirm.common.engine.event.entity.EventActionPropertyImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('FUNCTIONGROUP', 'Commom', '功能分类表', 'SIRM_FUNCTIONGROUP', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.function.entity.IFunctionGroup', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('FUNCTIONINFO', 'Commom', '功能表', 'SIRM_FUNCTIONINFO', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.function.entity.IFunctionInfo', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('HOMEPAGECFG', 'sprtorg', '首页布局配置', 'Sirm_HomePageCfg', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.homepagecfg.entity.IHomePageCfg', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('JOBQUARTZCLASSREAL', 'Commom', '工作调度关联的类', 'Sirm_JobQuartzClassReal', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.quartz.entity.IJobQuartzClassReal', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('JOBQUARTZDETAILS', 'Commom', '工作调度执行详情', 'Sirm_JobQuartzDetails', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.quartz.entity.IJobQuartzDetails', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('JOBQUARTZGROUP', 'Commom', '调度分组', 'Sirm_JobQuartzGroup', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.quartz.entity.IJobQuartzGroup', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('JOBQUARTZREAL', 'Commom', '工作和调度关系表', 'sirm_JobQuartzReal', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.quartz.entity.IJobQuartzReal', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('JOBQUARTZTIME', 'Commom', '调度时间配置表', 'Sirm_JobQuartzTime', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.quartz.entity.IJobQuartzTime', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('MENUFUNCTIONRELA', 'Commom', '菜单功能对应关系', 'SIRM_MENUFUNCTIONRELA', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.menu.entity.IMenuFunctionRela', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('MESSAGERECEIVER', 'Commom', '消息接收人', 'SIRM_MESSAGERECEIVER', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IMessageReceiver', 'com.sinitek.sirm.busin.routine.entity.MessageReceiverImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('MESSAGETEMPLATE', 'Commom', '消息模板', 'SIRM_MESSAGETEMPLATE', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IMessageTemplate', 'com.sinitek.sirm.busin.routine.entity.MessageTemplateImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('MESSAGETEMPLATERELA', 'Commom', '消息模板关联表', 'Sirm_MessageTemplateRela', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IMessageTemplateRela', 'com.sinitek.sirm.busin.routine.entity.MessageTemplateRelaImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGAUTHORIZE', 'sprtorg', '部门组织结构授权表', 'ORG_AUTHORIZE', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgAuthorize', 'com.sinitek.sirm.org.busin.entity.OrgAuthorizeImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGMECHANISM', 'sprtorg', '机构信息主表', 'ORG_MECHANISM', null, '0', '0', '0', '0', 'com.sinitek.sirm.multitenant.mechanism.busin.entity.IOrgMechanism', 'com.sinitek.sirm.multitenant.mechanism.busin.entity.OrgMechanismImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGMECHANISMPROPERTY', 'sprtorg', '机构扩展属性表', 'ORG_MECHANISMPROPERTY', null, '0', '0', '0', '0', 'com.sinitek.sirm.multitenant.mechanism.busin.entity.IMechanismProperty', 'com.sinitek.sirm.multitenant.mechanism.busin.entity.MechanismPropertyImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGPROPERTY', 'sprtorg', '组织结构属性表', 'ORG_PROPERTY', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgProperty', 'com.sinitek.sirm.org.busin.entity.OrgPropertyImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGPSWHIS', 'sprtorg', '密码修改历史', 'ORG_PSWHIS', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgPswHis', 'com.sinitek.sirm.org.busin.entity.OrgPswHisImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGRELAEXTEND', 'sprtorg', '组织结构关系扩展表', 'SPRT_ORGRELAEXTEND', null, '0', '0', '0', '0', 'com.sinitek.spirit.org.server.entity.IOrgRelationInfo', null, null, null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGRELATION', 'sprtorg', '岗位关系表', 'ORG_RELATION', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgRelation', 'com.sinitek.sirm.org.busin.entity.OrgRelationImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGRELATIONSCHEME', 'sprtorg', '岗位方案表', 'ORG_RELATIONSCHEME', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgRelationScheme', 'com.sinitek.sirm.org.busin.entity.OrgRelationSchemeImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGRIGHTAUTH', 'sprtorg', '跨机构运维授权', 'ORG_RIGHTAUTH', null, '0', '0', '0', '0', 'com.sinitek.sirm.multitenant.mechanism.busin.entity.IOrgRightauth', 'com.sinitek.sirm.multitenant.mechanism.busin.entity.OrgRightauthImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGTEAMINDUSTRYRELA', 'sprtorg', '行业小组关联表', 'ORG_TEAMINDUSTRYRELA', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgTeamIndustryRela', 'com.sinitek.sirm.org.busin.entity.OrgTeamIndustryRelaImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('ORGlOGONLOG', 'sprtorg', '登录密码输入错误次数记录表', 'org_logonlog', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgLogonLog', 'com.sinitek.sirm.org.busin.entity.OrgLogonLogImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('OrgObject', 'sprtorg', '组织结构对象表', 'sprt_orgobject', null, '0', '0', '0', '1000', 'com.sinitek.spirit.org.server.entity.IOrgObject', 'com.sinitek.spirit.org.server.entity.OrgObjectImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('OrgRelationInfo', 'sprtorg', '组织结构图关系信息定义', 'sprt_orgrela', null, '0', '0', '0', '2000', 'com.sinitek.spirit.org.server.entity.IOrgRelationInfo', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('QUALIFYINFO', 'SPRT_UM', '用户资格证书表', 'UM_QUALIFYINFO', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IQualifyInfo', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RECEIVEMESSAGE', 'OA', '信息接收', 'RT_ReceiveMessage', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IReceiveMessage', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTAPPLYDOCAUTH', 'OA', '存储权限申请信息', 'RT_APPLYDOCAUTH', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTApplyDocAuth', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTDIRECTORY', 'OA', '文档目录表', 'RT_DIRECTORY', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTDirectory', 'com.sinitek.sirm.busin.routine.entity.RTDirectoryImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTDIRECTORYAPPEND', 'OA', '目录的附加信息', 'RT_DIRECTORY_APPEND', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTDirectoryAppend', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTDOCUMENTATTACHMENT', 'OA', '文档附件版本表', 'RT_DocumentAttachment', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTDocumentAttachment', 'com.sinitek.sirm.busin.routine.entity.RTDocumentAttachmentImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTDOCUMENTS', 'OA', '文档信息表', 'RT_DOCUMENTS', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTDocuments', 'com.sinitek.sirm.busin.routine.entity.RTDocumentsImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTDocumentAuth', 'OA', '文档管理权限表', 'RT_DocumentAuth', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTDocumentAuth', 'com.sinitek.sirm.busin.routine.entity.RTDocumentAuthImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTHOLIDAYS', 'OA', '节假日管理', 'RT_Holidays', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRTHolidays', 'com.sinitek.sirm.busin.routine.entity.RTHolidaysImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RTKeyReader', 'OA', '重点阅读人', 'RT_KeyReader', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.IRT_KeyReader', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RightAuth', 'SPRT_RIGHT', '权限授权数据', 'sprt_rightauth', null, '0', '0', '0', '2000', 'com.sinitek.spirit.right.server.entity.IRightAuth', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('RightDefine', 'SPRT_RIGHT', '权限定义信息', 'sprt_rightdef', null, '0', '0', '0', '2000', 'com.sinitek.spirit.right.server.entity.IRightDefine', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SEARCHFILTER', 'Commom', '查询过滤器', 'SIRM_SEARCHFILTER', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.searchfilter.entity.ISearchFilter', 'com.sinitek.sirm.common.searchfilter.entity.SearchFilterImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SENDMESSAGE', 'OA', '消息发送', 'RT_SendMessage', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ISendMessage', 'com.sinitek.sirm.busin.routine.entity.SendMessageImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SENDMESSAGECONTENT', 'OA', '消息发送内容', 'RT_SendMessageContent', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ISendMessageContent', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SENDMESSAGEDETAIL', 'OA', '消息发送结果', 'RT_SendMessageDetail', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ISendMessageDetail', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SETTING', 'Commom', '参数配置表', 'SIRM_SETTING', null, '0', '0', '0', '10000', 'com.sinitek.sirm.common.setting.entity.ISetting', 'com.sinitek.sirm.common.setting.entity.SettingImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SETTINGPAGEURL', 'Commom', '配置页面URL', 'CM_SETTINGPAGEURL', null, '0', '0', '0', '0', null, null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMENUM', 'Commom', '枚举表', 'sirm_enum', null, '0', '0', '0', '10000', 'com.sinitek.sirm.common.sirmenum.entity.ISirmEnum', 'com.sinitek.sirm.common.sirmenum.entity.SirmEnumImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMDATA', 'FW', '表单实例具体数据表', 'SIRM_FORMDATA', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormData', 'com.sinitek.sirm.form.entity.SirmFormDataImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMEXAMPLE', 'FW', '表单模板实例表', 'SIRM_FORMEXAMPLE', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormExample', 'com.sinitek.sirm.form.entity.SirmFormExampleImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMITEM', 'FW', '模板表单的表单项的信息', 'SIRM_FORMITEM', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormItem', 'com.sinitek.sirm.form.entity.SirmFormItemImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMQUERYCONDITION', 'FW', '表单模板查询条件表', 'SIRM_FORMQUERYCONDITION', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormQueryCondition', 'com.sinitek.sirm.form.entity.SirmFormQueryConditionImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMQUERYSCHEME', 'FW', '表单模板查询方案表', 'SIRM_FORMQUERYSCHEME', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormQueryScheme', 'com.sinitek.sirm.form.entity.SirmFormQuerySchemeImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMSHOWFIELD', 'FW', '表单模板查询展示字段表', 'SIRM_FORMSHOWFIELD', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormShowField', 'com.sinitek.sirm.form.entity.SirmFormShowFieldImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMSTEP', 'FW', '表单配置的流程步骤信息表', 'SIRM_FORMSTEP', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormStep', 'com.sinitek.sirm.form.entity.SirmFormStepImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMSTEPOWNER', 'FW', '表单流程步骤的处理人信息表', 'SIRM_FORMSTEPOWNER', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormStepOwner', 'com.sinitek.sirm.form.entity.SirmFormStepOwnerImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMFORMTEMPLATE', 'FW', '表单模板信息表', 'SIRM_FORMTEMPLATE', null, '0', '0', '0', '0', 'com.sinitek.sirm.form.entity.ISirmFormTemplate', 'com.sinitek.sirm.form.entity.SirmFormTemplateImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMINDEXQUEUE', 'Commom', '索引队列表', 'SIRM_INDEXQUEUE', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.search.entity.ISirmIndexQueue', 'com.sinitek.sirm.common.search.entity.SirmIndexQueueImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMJOBEXECUTELOG', 'Commom', '定时任务执行表', 'SIRM_JOBEXECUTELOG', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.quartz.entity.ISirmJobExecuteLog', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMSENDMESSAGEDETAIL', 'Commom', '发送消息明细表', 'SIRM_SENDMESSAGEDETAIL', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.message.entity.ISirmSendMessageDetail', 'com.sinitek.sirm.common.message.entity.SirmSendMessageDetailImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SIRMTAGINFO', 'Commom', '标签库', 'SIRM_TAGINFO', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.taginfo.entity.ISirmTagInfo', 'com.sinitek.sirm.common.taginfo.entity.SirmTagInfoImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SPECIALMENU', 'OA', '快捷菜单', 'RT_SPECIALMENU', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ISpeicalMenu', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SPRT_BUSINLOGGER', 'framework', '业务日志', null, null, '0', '0', '0', '0', 'com.sinitek.spirit.businlogger.IBusinLogger', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SPRT_LOGGERCONFIG', 'framework', '日志记录器配置', null, null, '0', '0', '0', '200', null, null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SPRT_SYSMENU', 'framework', '系统菜单', null, null, '0', '0', '0', '100', 'com.sinitek.spirit.web.sysmenu.ISysMenu', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SPRT_SYSTEM', 'framework', '系统表', null, null, '0', '0', '0', '10', 'com.sinitek.spirit.web.system.ISystem', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SPRT_WEBMODULE', 'framework', '网页模块', null, null, '0', '0', '0', '100', 'com.sinitek.spirit.web.webmodule.IWebmodule', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SUMMARY', 'OA', '小结信息', 'RT_Summary', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ISummary', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SUPPORTCENTER', 'Commom', '支持中心表', 'SIRM_SUPPORTCENTER', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.supportcenter.entity.ISupportCenter', 'com.sinitek.sirm.common.supportcenter.entity.SupportCenterImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SirmGroupSetting', 'Commom', '参数组配置', 'Sirm_GroupSetting', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.setting.entity.IGroupSetting', 'com.sinitek.sirm.common.setting.entity.GroupSettingImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('SirmSendMessage', 'Commom', '发送消息表', 'SIRM_SENDMESSAGE', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.message.entity.ISirmSendMessage', 'com.sinitek.sirm.common.message.entity.SirmSendMessageImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('TASK', 'OA', '任务信息表', 'RT_TASK', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ITask', 'com.sinitek.sirm.busin.routine.entity.TaskImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('TASKOWNER', 'OA', '任务处理人表', 'RT_TASKOWNER', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.routine.entity.ITaskOwner', 'com.sinitek.sirm.busin.routine.entity.TaskOwnerImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('TASKRELA', 'Project', '投资计划任务关联表', 'RT_TASKRELA', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.project.entity.ITaskRela', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('TENANTCONFIG', 'sprtorg', '配置需要机构Sql拦截的表', 'METADB_TENANTCONFIG', null, '0', '0', '0', '0', 'com.sinitek.sirm.multitenant.config.busin.entity.ITenantConfig', 'com.sinitek.sirm.multitenant.config.busin.entity.TenantConfigImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('USER', 'framework', '用户表', 'TUser', null, '1', '1', '0', '0', 'com.sinitek.sirm.busin.rschreport.entity.IUser', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('USEREXTENDINFO', 'sprtorg', '用户扩展属性表', 'ORG_USEREXTENDINFO', '', '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IOrgUserExtendInfo', 'com.sinitek.sirm.org.busin.entity.OrgUserExtendInfoImpl', '1', '', '');
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('USERUICONFIG', 'Commom', '用户界面配置信息', 'CM_USERUICONFIG', null, '0', '0', '0', '0', 'com.sinitek.sirm.common.um.entity.IUserUIConfig', 'com.sinitek.sirm.common.um.entity.UserUIConfigImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserInfo', 'SPRT_UM', '用户信息', 'UM_UserInfo', null, '0', '0', '0', '500', 'com.sinitek.spirit.um.server.userdb.UserInfo', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserKeepSignedIn', 'SPRT_UM', '保持登录状态信息表', 'UM_UserKeepSignedIn', null, '0', '0', '0', '500', 'com.sinitek.spirit.um.server.userdb.UserKeepSignedIn', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserProperty', 'SPRT_UM', '用户属性表', 'UM_UserProperty', null, '0', '0', '0', '500', 'com.sinitek.spirit.um.server.userdb.UserProperty', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserRole', 'SPRT_UM', '用户角色信息', 'UM_UserRole', null, '0', '0', '0', '500', 'com.sinitek.spirit.um.server.userdb.UserRole', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserScheme', 'SPRT_UM', '选人控件方案表', 'UM_USERSCHEME', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IUserScheme', 'com.sinitek.sirm.org.busin.entity.UserSchemeImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserSchemeRela', 'SPRT_UM', '选人控件方案关联表', 'UM_USERSCHEMERELA', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IUserSchemeRela', 'com.sinitek.sirm.org.busin.entity.UserSchemeRelaImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserSecurityQuestion', 'SPRT_UM', '用户密码安全问题', 'UM_UserSecurityQuestion', null, '0', '0', '0', '500', 'com.sinitek.spirit.um.server.userdb.UserSecurityQuestion', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('UserSynchronous', 'SPRT_UM', '用户同步临时表', 'um_UserSynchronous', null, '0', '0', '0', '0', 'com.sinitek.sirm.org.busin.entity.IUserSynchronous', 'com.sinitek.sirm.org.busin.entity.UserSynchronousImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFAGENTS', 'Workflow', '流程代理人表', 'WF_AGENTS', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowAgents', 'com.sinitek.sirm.busin.workflow.entity.WorkflowAgentsImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFAPPROVALPHRASE', 'Workflow', '流程常用短语', 'WF_APPROVALPHRASE', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowApprovalPhrase', 'com.sinitek.sirm.busin.workflow.entity.WorkflowApprovalPhraseImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLE', 'Workflow', '实例主表', 'WF_EXAMPLE', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExample', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLEASKFORBATCH', 'Workflow', '实例批次征求意见表', 'WF_EXAMPLEASKFORBATCH', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleAskForBatch', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleAskForBatchImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLEENTRY', 'Workflow', '实例实体表', 'WF_EXAMPLEENTRY', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleEntry', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleEntryImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLELIST', 'Workflow', '实例枚举表', 'WF_EXAMPLELIST', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleList', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleListImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLEPARA', 'Workflow', '实例变量表', 'WF_EXAMPLEPARA', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExamplePara', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleParaImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLERELATION', 'Workflow', '流程对应表', 'WF_EXAMPLERELATION', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleRelation', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleRelationImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLESTEP', 'Workflow', '实例步骤表', 'WF_EXAMPLESTEP', '', '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleStep', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleStepImpl', '1', '', '');
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLESTEPLINK', 'Workflow', '实例步骤链接表', 'WF_EXAMPLESTEPLINK', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleStepLink', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleStepLinkImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFEXAMPLESTEPOWNER', 'Workflow', '实例步骤处理人', 'WF_EXAMPLESTEPOWNER', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleStepOwner', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleStepOwnerImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFFLOWDOTS', 'Workflow', '流程路径上的点坐标', 'WF_FLOWDOTS', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.drawprocess.IWorkflowFlowDots', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFFLOWNODE', 'Workflow', '流程节点信息', 'WF_FLOWNODE', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.drawprocess.IWorkflowFlowNode', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFFLOWPATHS', 'Workflow', '流程节点路径', 'WF_FLOWPATHS', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.drawprocess.IWorkflowFlowPaths', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFFLOWPROPS', 'Workflow', '流程参数', 'WF_FLOWPROPS', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.drawprocess.IWorkflowFlowProps', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESS', 'Workflow', '流程主表', 'WF_PROCESS', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcess', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSEXTEND', 'Workflow', '流程扩展功能表', 'WF_PROCESSEXTEND', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessExtend', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessExtendImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSLIST', 'Workflow', '流程枚举表', 'WF_PROCESSLIST', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessList', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessListImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSMAIL', 'Workflow', '流程邮件表', 'WF_PROCESSMAIL', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessMail', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessMailImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSOWNER', 'Workflow', '流程处理人表', 'WF_PROCESSOWNER', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessOwner', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessOwnerImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSOWNERLINK', 'Workflow', '流程处理人关联表', 'WF_PROCESSOWNERLINK', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessOwnerLink', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessOwnerLinkImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSPARA', 'Workflow', '流程参数表', 'WF_PROCESSPARA', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessPara', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessParaImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSSTEP', 'Workflow', '流程步骤表', 'WF_PROCESSSTEP', '', '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessStep', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessStepImpl', '1', '', '');
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSSTEPDO', 'Workflow', '流程步骤执行表', 'WF_PROCESSSTEPDO', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessStepDo', null, '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSSTEPHISTORY', 'Workflow', '流程步骤历史表', 'WF_PROCESSSTEPHISTORY', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessStepHistory', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessStepHistoryImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSSTEPLINK', 'Workflow', '流程步骤连接表', 'WF_PROCESSSTEPLINK', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessStepLink', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessStepLinkImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSSTEPLINKDO', 'Workflow', '流程步骤执行表', 'WF_PROCESSSTEPLINKDO', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessStepLinkDo', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessStepLinkDoImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSSTEPLINKIF', 'Workflow', '流程步骤条件表', 'WF_PROCESSSTEPLINKIF', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessStepLinkIf', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessStepLinkIfImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSTIMEOUT', 'Workflow', '超时配置', 'WF_PROCESSTIMEOUT', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessTimeOut', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessTimeOutImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WFPROCESSURL', 'Workflow', '流程URL表', 'WF_PROCESSURL', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowProcessUrl', 'com.sinitek.sirm.busin.workflow.entity.WorkflowProcessUrlImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WfExampleAskFor', 'Workflow', '实例征求意见表', 'Wf_ExampleAskFor', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleAskFor', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleAskForImpl', '1', null, null);
INSERT INTO `metadb_entity`(`entityname`, `catalogkey`, `entityinfo`, `entitytable`, `idgenerator`, `historyflag`, `removeflag`, `idcacheflag`, `idcachesize`, `interfacename`, `classname`, `enableflag`, `inheritanceentity`, `inheritancetype`) VALUES ('WfExampleTask', 'Workflow', '实例任务表', 'Wf_ExampleTask', null, '0', '0', '0', '0', 'com.sinitek.sirm.busin.workflow.entity.IWorkflowExampleTask', 'com.sinitek.sirm.busin.workflow.entity.WorkflowExampleTaskImpl', '1', null, null);

-- ----------------------------
-- Table structure for metadb_entitycatalog
-- ----------------------------
DELETE FROM `metadb_entitycatalog`;

-- ----------------------------
-- Records of metadb_entitycatalog
-- ----------------------------
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('Commom', '公共模块', '公共模块', 'SIRM_');
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('DEMO', 'DEMO', 'DEMO', NULL);
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('FW', '应用程序框架', '应用程序框架', NULL);
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('OA', '日常工作', '日常工作', 'RT_');
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('SPRT_RIGHT', 'sprt_right', '权限模块', NULL);
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('SPRT_UM', 'SPRT_UM', '用户管理', 'UM_');
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('Workflow', '工作流', '工作流', 'WF_');
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('framework', '基础框架', '基础框架', NULL);
INSERT INTO `metadb_entitycatalog`(`catalogkey`, `catalogname`, `cataloginfo`, `tablenameprefix`) VALUES ('sprtorg', 'Spirit_ORG', '组织结构组件', 'ORG_');

-- ----------------------------
-- Table structure for metadb_idgenerator
-- ----------------------------
DELETE FROM `metadb_idgenerator`;

-- ----------------------------
-- Records of metadb_idgenerator
-- ----------------------------
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('CACHEINFO', 30, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('FUNCTIONGROUP', 100, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('FUNCTIONINFO', 100, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('HOMEPAGECFG', 100, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('MESSAGETEMPLATE', 100, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('METADB_PROPERTY', 0, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('ORGMECHANISM', 220, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('ORGOBJECT', 100, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('RIGHTDEFINE', 1000, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('RTDIRECTORY', 10, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('SEQ_ORGID', 10, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('SEQ_USERID', 10, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('SIRMGROUPSETTING', 30, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('SPRT_SYSTEM', 10, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('SPRT_WEBMODULE', 10, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('USERINFO', 1, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('USERPROPERTY', 10, 1);
INSERT INTO `metadb_idgenerator`(`entityname`, `currentvalue`, `step`) VALUES ('WFPROCESSLIST', 1000, 1);

-- ----------------------------
-- Table structure for metadb_property
-- ----------------------------
DELETE FROM `metadb_property`;

-- ----------------------------
-- Table structure for org_authorize
-- ----------------------------
DELETE FROM `org_authorize`;


-- ----------------------------
-- Table structure for org_logonlog
-- ----------------------------
DELETE FROM `org_logonlog`;



-- ----------------------------
-- Table structure for org_property
-- ----------------------------
DELETE FROM `org_property`;


-- ----------------------------
-- Table structure for org_pswhis
-- ----------------------------
DELETE FROM `org_pswhis`;


-- ----------------------------
-- Table structure for org_relation
-- ----------------------------
DELETE FROM `org_relation`;

-- ----------------------------
-- Table structure for org_relationscheme
-- ----------------------------
DELETE FROM `org_relationscheme`;

-- ----------------------------
-- Table structure for org_teamindustryrela
-- ----------------------------
DELETE FROM `org_teamindustryrela`;

-- ----------------------------
-- Table structure for org_userextendinfo
-- ----------------------------
DELETE FROM `org_userextendinfo`;


-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DELETE FROM `qrtz_blob_triggers`;

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DELETE FROM `qrtz_calendars`;

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DELETE FROM `qrtz_cron_triggers`;

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DELETE FROM `qrtz_fired_triggers`;

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DELETE FROM `qrtz_job_details`;

-- ----------------------------
-- Table structure for qrtz_job_listeners
-- ----------------------------
DELETE FROM `qrtz_job_listeners`;

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DELETE FROM `qrtz_locks`;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------
INSERT INTO `qrtz_locks`(`sched_name`, `lock_name`) VALUES ('MyQuartzScheduler','CALENDAR_ACCESS');
INSERT INTO `qrtz_locks`(`sched_name`, `lock_name`) VALUES ('MyQuartzScheduler','JOB_ACCESS');
INSERT INTO `qrtz_locks`(`sched_name`, `lock_name`) VALUES ('MyQuartzScheduler','MISFIRE_ACCESS');
INSERT INTO `qrtz_locks`(`sched_name`, `lock_name`) VALUES ('MyQuartzScheduler','STATE_ACCESS');
INSERT INTO `qrtz_locks`(`sched_name`, `lock_name`) VALUES ('MyQuartzScheduler','TRIGGER_ACCESS');

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DELETE FROM `qrtz_paused_trigger_grps`;

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DELETE FROM `qrtz_scheduler_state`;

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DELETE FROM `qrtz_simple_triggers`;

-- ----------------------------
-- Table structure for qrtz_trigger_listeners
-- ----------------------------
DELETE FROM `qrtz_trigger_listeners`;

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DELETE FROM `qrtz_triggers`;

-- ----------------------------
-- Table structure for rt_applydocauth
-- ----------------------------
DELETE FROM `rt_applydocauth`;

-- ----------------------------
-- Table structure for rt_directory
-- ----------------------------
DELETE FROM `rt_directory`;

-- ----------------------------
-- Records of rt_directory
-- ----------------------------

-- ----------------------------
-- Table structure for rt_directory_append
-- ----------------------------
DELETE FROM `rt_directory_append`;

-- ----------------------------
-- Table structure for rt_documentattachment
-- ----------------------------
DELETE FROM `rt_documentattachment`;

-- ----------------------------
-- Table structure for rt_documentauth
-- ----------------------------
DELETE FROM `rt_documentauth`;

-- ----------------------------
-- Table structure for rt_documents
-- ----------------------------
DELETE FROM `rt_documents`;


-- ----------------------------
-- Table structure for rt_holidays
-- ----------------------------
DELETE FROM `rt_holidays`;


-- ----------------------------
-- Table structure for rt_keyreader
-- ----------------------------
DELETE FROM `rt_keyreader`;


-- ----------------------------
-- Table structure for rt_receivemessage
-- ----------------------------
DELETE FROM `rt_receivemessage`;

-- ----------------------------
-- Table structure for rt_sendmessage
-- ----------------------------
DELETE FROM `rt_sendmessage`;

-- ----------------------------
-- Table structure for rt_sendmessagecontent
-- ----------------------------
DELETE FROM `rt_sendmessagecontent`;

-- ----------------------------
-- Table structure for rt_sendmessagedetail
-- ----------------------------
DELETE FROM `rt_sendmessagedetail`;


-- ----------------------------
-- Table structure for sirm_apploader
-- ----------------------------
DELETE FROM `sirm_apploader`;
-- ----------------------------
-- Table structure for sirm_attachment
-- ----------------------------
DELETE FROM `sirm_attachment`;

-- ----------------------------
-- Table structure for sirm_cacheinfo
-- ----------------------------
DELETE FROM `sirm_cacheinfo`;

-- ----------------------------
-- Records of sirm_cacheinfo
-- ----------------------------
INSERT INTO `sirm_cacheinfo`(`objid`, `name`, `entitynames`, `location`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `description`) VALUES (1, '系统菜单', 'SPRT_SYSMENU', NULL, now(), now(), 1, 'CACHEINFO', NULL);
INSERT INTO `sirm_cacheinfo`(`objid`, `name`, `entitynames`, `location`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `description`) VALUES (2, '系统配置', 'SETTING', NULL, now(), now(), 1, 'CACHEINFO', NULL);
INSERT INTO `sirm_cacheinfo`(`objid`, `name`, `entitynames`, `location`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `description`) VALUES (3, '系统功能', 'FUNCTIONINFO,FUNCTIONGROUP', NULL, now(), now(), 1, 'CACHEINFO', NULL);
INSERT INTO `sirm_cacheinfo`(`objid`, `name`, `entitynames`, `location`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `description`) VALUES (4, '组织结构', 'OrgObject,OrgRelationInfo,UserInfo,UserProperty', NULL, now(), now(), 1, 'CACHEINFO', NULL);
INSERT INTO `sirm_cacheinfo`(`objid`, `name`, `entitynames`, `location`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `description`) VALUES (5, '枚举数据', 'SIRMENUM', NULL, now(), now(), 1, 'CACHEINFO', NULL);
INSERT INTO `sirm_cacheinfo`(`objid`, `name`, `entitynames`, `location`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `description`) VALUES (6, '个人中心', 'SPRT_WEBMODULE', NULL, now(), now(), 1, 'CACHEINFO', NULL);

-- ----------------------------
-- Table structure for sirm_entitysetting
-- ----------------------------
DELETE FROM `sirm_entitysetting`;

-- ----------------------------
-- Table structure for sirm_enum
-- ----------------------------
DELETE FROM `sirm_enum`;

-- ----------------------------
-- Records of sirm_enum
-- ----------------------------

INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (1, 'COMMON', 'messagetemplatecatagory', '公共类', 1, '消息模版->模版分类->公共类', 3, now(), now(), 25, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (2, 'COMMON', 'messagetemplatecatagory', '其他', 100, '消息模版->模版分类->其他', 0, now(), now(), 122, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (3, 'COMMON', 'document-mime-types', 'md', 2, '系统可上传文件类型->md', 2, now(), now(), 3, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (4, 'COMMON', 'document-mime-types', 'rar', 3, '系统可上传文件类型->rar', 3, now(), now(), 2, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (5, 'COMMON', 'document-mime-types', 'zip', 4, '系统可上传文件类型->zip', 4, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (6, 'COMMON', 'document-mime-types', '7z', 5, '系统可上传文件类型->7z', 5, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (7, 'COMMON', 'document-mime-types', 'doc', 6, '系统可上传文件类型->doc', 6, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (8, 'COMMON', 'document-mime-types', 'docx', 7, '系统可上传文件类型->docx', 7, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (9, 'COMMON', 'document-mime-types', 'ppt', 8, '系统可上传文件类型->ppt', 8, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (10, 'COMMON', 'document-mime-types', 'pptx', 9, '系统可上传文件类型->pptx', 9, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (11, 'COMMON', 'document-mime-types', 'xls', 10, '系统可上传文件类型->xls', 10, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (12, 'COMMON', 'document-mime-types', 'xlsx', 11, '系统可上传文件类型->xlsx', 11, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (13, 'COMMON', 'document-mime-types', 'pdf', 12, '系统可上传文件类型->pdf', 12, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (14, 'COMMON', 'document-mime-types', 'jpg', 13, '系统可上传文件类型->jpg', 13, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (15, 'COMMON', 'document-mime-types', 'png', 14, '系统可上传文件类型->png', 14, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (16, 'COMMON', 'document-mime-types', 'gif', 15, '系统可上传文件类型->gif', 15, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (17, 'COMMON', 'document-mime-types', 'html', 16, '系统可上传文件类型->html', 16, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (18, 'COMMON', 'document-mime-types', 'htm', 17, '系统可上传文件类型->htm', 17, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (19, 'COMMON', 'document-mime-types', 'txt', 1, '系统可上传文件类型->txt', 1, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (20, 'COMMON', 'messagetemplatecatagory', '私有类', 120, '消息模版->模版分类->私有类', 3, now(), now(), 3, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (21, 'COMMON', 'APPROVESTATUS', '草稿', 0, '未发起流程但是已保存的实体状态', 1, now(), now(), 5, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (22, 'COMMON', 'APPROVESTATUS', '审批中', 10, '已发起流程未审批流程的关联实体状态', 2, now(), now(), 4, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (23, 'COMMON', 'APPROVESTATUS', '审批驳回', 50, '提交审批之后被驳回流程的关联实体状态', 3, now(), now(), 4, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (24, 'COMMON', 'APPROVESTATUS', '审批通过', 100, '提交审批之后审批通过流程的关联实体状态', 4, now(), now(), 4, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (25, 'COMMON', 'APPROVESTATUS', '退回修订', 120, '提交审批之后被退回流程的关联实体状态', 5, now(), now(), 3, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (26, 'COMMON', 'APPROVESTATUS', '放弃', 150, '退回修订之后修改人选择放弃修改之后流程的关联实体的状态', 6, now(), now(), 3, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (27, 'COMMON', 'APPROVESTATUS', '终止', -1, '被直接终止流程的关联实体状态', 7, now(), now(), 2, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (28, 'COMMON', 'user-workplace', '北京', 3, '工作地->北京', 0, now(), now(), 3, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (29, 'COMMON', 'user-workplace', '上海', 1, '工作地->上海', 1, now(), now(), 0, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (30, 'COMMON', 'user-workplace', '武汉', 2, '工作地->武汉', 2, now(), now(), 0, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (31, 'COMMON', 'trigger-types', '执行类', NULL, '调度类型', 0, now(), now(), 1, 'SIRMENUM', 'DEFAULT');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (32, 'COMMON', 'trigger-types', '动作', NULL, '调度类型', 0, now(), now(), 2, 'SIRMENUM', 'ACTION');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (33, 'COMMON', 'trigger-states', 'WAITING', NULL, '调度状态->等待状态', 0, now(), now(), 1, 'SIRMENUM', 'WAITING');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (34, 'COMMON', 'trigger-states', 'PAUSED', NULL, '调度状态->暂停状态', 1, now(), now(), 1, 'SIRMENUM', 'PAUSED');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (35, 'COMMON', 'trigger-states', 'ERROR', NULL, '调度状态->错误状态', 2, now(), now(), 1, 'SIRMENUM', 'ERROR');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (36, 'COMMON', 'trigger-states', 'ACQUIRED', NULL, '调度状态->正在执行状态', 3, now(), now(), 1, 'SIRMENUM', 'ACQUIRED');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (37, 'COMMON', 'trigger-states', 'BLOCKED', NULL, '调度状态->锁住状态', 4, now(), now(), 1, 'SIRMENUM', 'BLOCKED');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (1691284032896618497, 'COMMON', 'trigger-states', 'PAUSED_BLOCKED', NULL, '调度状态->暂停阻塞状态', 5, now(), now(), 1, 'SIRMENUM', 'PAUSED_BLOCKED');
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (38, 'COMMON', 'document-mime-types', 'xml', 18, '系统可上传文件类型->txt', 18, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (39, 'COMMON', 'calendar-remind-times', '日程开始时', 0, '日程动作->提醒选择', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (40, 'COMMON', 'calendar-remind-times', '15分钟前', 15, '日程动作->提醒选择', 1, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (41, 'COMMON', 'calendar-remind-times', '1小时前', 60, '日程动作->提醒选择', 2, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (42, 'COMMON', 'calendar-remind-times', '1天前', 1440, '日程动作->提醒选择', 3, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (43, 'COMMON', 'calendar-remind-times', '2天前', 2880, '日程动作->提醒选择', 4, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (44, 'COMMON', 'calendar-remind-times', '1周前', 10080, '日程动作->提醒选择', 5, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (45, 'COMMON', 'document-mime-types', 'ipa', 19, '系统可上传文件类型->ipa', 19, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (46, 'COMMON', 'document-mime-types', 'apk', 20, '系统可上传文件类型->apk', 20, now(), now(), 1, 'SIRMENUM', NULL);

INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437237778097246210, 'COMMON', 'BusinLogOperateTypes', '用户登录', 1, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437237870313213954, 'COMMON', 'BusinLogOperateTypes', '用户退出', 2, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437237935740162050, 'COMMON', 'BusinLogOperateTypes', '菜单访问', 3, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437237977683202050, 'COMMON', 'BusinLogOperateTypes', '功能权限', 4, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238071216181250, 'COMMON', 'BusinLogOperateTypes', '菜单权限', 5, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238127109476354, 'COMMON', 'BusinLogOperateTypes', '首页功能权限', 6, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238184663715841, 'COMMON', 'BusinLogOperateTypes', '用户管理', 7, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238260916162561, 'COMMON', 'BusinLogOperateTypes', '组织结构管理', 8, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238342885445633, 'COMMON', 'BusinLogOperateTypes', '功能访问', 9, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238510821183490, 'COMMON', 'BusinLogOperateTypes', '组织结构上下级管理', 10, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238605092360193, 'COMMON', 'BusinLogOperateTypes', 'IP非法访问', 11, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238697622900738, 'COMMON', 'BusinLogOperateTypes', '机构权限管理', 12, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1437238736139194370, 'COMMON', 'BusinLogOperateTypes', '请求时长管理', 13, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1484353981345042434, 'COMMON', 'BusinLogOperateTypes', '定时任务异常', 16, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1484353981345042498, 'COMMON', 'BusinLogOperateTypes', '机构默认角色同步', 15, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1484353981345042444, 'COMMON', 'BusinLogOperateTypes', '个人令牌权限', 17, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`)  VALUES(1484353981345042448, 'COMMON', 'BusinLogOperateTypes', '图标权限', 18, '', 0, now(), now(), 1, 'SIRMENUM', NULL);
INSERT INTO `sirm_enum`(`objid`, `catalog`, `type`, `name`, `value`, `description`, `sort`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `strvalue`) VALUES (1542424730731155457, 'COMMON', 'user-datasrc', '系统', NULL, '', 0, now(), now(), 1, 'SIRMENUM', '系统');

-- ----------------------------
-- Table structure for sirm_functiongroup
-- ----------------------------
DELETE FROM `sirm_functiongroup`;

-- ----------------------------
-- Table structure for sirm_functioninfo
-- ----------------------------
DELETE FROM `sirm_functioninfo`;


-- ----------------------------
-- Table structure for sirm_homepagecfg
-- ----------------------------
DELETE FROM `sirm_homepagecfg`;

-- ----------------------------
-- Records of sirm_homepagecfg
-- ----------------------------
-- ----------------------------
INSERT INTO `sirm_homepagecfg`(`objid`, `pagelayout`, `modulelayout`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `righttype`, `moduleid`, `sort`,`scheme_id`) VALUES (1, -1, '50%,330px', now(), now(), 1, 'HOMEPAGECFG', 1, 2, 1, 1);
INSERT INTO `sirm_homepagecfg`(`objid`, `pagelayout`, `modulelayout`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `righttype`, `moduleid`, `sort`,`scheme_id`) VALUES (2, -1, '50%,330px', now(), now(), 1, 'HOMEPAGECFG', 1, 1, 2, 1);
INSERT INTO `sirm_homepagecfg`(`objid`, `pagelayout`, `modulelayout`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `righttype`, `moduleid`, `sort`,`scheme_id`) VALUES (3, -1, ',', now(), now(), 0, 'HOMEPAGECFG', 1, 2, 1, 2);
INSERT INTO `sirm_homepagecfg`(`objid`, `pagelayout`, `modulelayout`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `righttype`, `moduleid`, `sort`,`scheme_id`) VALUES (4, -1, ',', now(), now(), 0, 'HOMEPAGECFG', 1, 1, 2, 2);

DELETE FROM `sirm_homepagecfg_scheme`;

INSERT INTO `sirm_homepagecfg_scheme`(`id`,`type`,`name`,`org_id`, `createtimestamp`, `updatetimestamp`, `version`) values (1, 0, '系统默认', '0', now(), now(), 1);

-- ----------------------------
-- Table structure for sirm_idgenerator
-- ----------------------------
DELETE FROM `sirm_idgenerator`;


-- ----------------------------
-- Table structure for sirm_indexqueue
-- ----------------------------
DELETE FROM `sirm_indexqueue`;

-- ----------------------------
-- Table structure for sirm_jobexecutelog
-- ----------------------------
DELETE FROM `sirm_jobexecutelog`;

-- ----------------------------
-- Table structure for sirm_job_config
-- ----------------------------
DELETE FROM `sirm_job_config`;


-- ----------------------------
-- Table structure for sirm_jobquartzdetails
-- ----------------------------
DELETE FROM `sirm_jobquartzdetails`;

-- ----------------------------
-- Table structure for sirm_jobquartzgroup
-- ----------------------------
DELETE FROM `sirm_jobquartzgroup`;

-- ----------------------------
-- Table structure for sirm_jobquartztime
-- ----------------------------
DELETE FROM `sirm_jobquartztime`;

-- ----------------------------
-- Table structure for sirm_loggerconfig
-- ----------------------------
DELETE FROM `sirm_loggerconfig`;


-- ----------------------------
-- Table structure for sirm_menufunctionrela
-- ----------------------------
DELETE FROM `sirm_menufunctionrela`;

-- ----------------------------
-- Table structure for sirm_messagereceiver
-- ----------------------------
DELETE FROM `sirm_messagereceiver`;

-- ----------------------------
-- Table structure for sirm_messagetemplate
-- ----------------------------
DELETE FROM `sirm_messagetemplate`;

-- 用户到期前提醒消息模版
INSERT INTO sirm_messagetemplate
(objid, code, name, sendmode, title, smscontent, remindcontent, forceflag, createtimestamp, updatetimestamp, version,
 entityname, catagory, remark, processtype, mobilecontent, templatetype, importantlevel, content)
VALUES(1419912304560050178, 'USER_EXPIRE_FRONT_REMIND', '用户到期前提醒', 1, '用户到期前提醒', '', '${orgName}, 您好!
为您开通的账号试用期将于3天后结束,请您尽快使用,在使用的过程中如有任何疑问,可邮件回复,或电话联系我们,希望您使用愉快!',
       1, now(), now(), 6, 'MESSAGETEMPLATE', 0, '', 0, '', 0, 3,
       '<p>${orgName}, 您好!</p><p><br></p><p>为您开通的账号试用期将于3天后结束,请您尽快使用,在使用的过程中如有任何疑问,可邮件回复,或电话联系我们,希望您使用愉快!</p>');

-- 用户找回密码的默认消息模版
INSERT INTO sirm_messagetemplate (objid,code,name,sendmode,title,smscontent,remindcontent,forceflag,createtimestamp,updatetimestamp,version,entityname,catagory,remark,processtype,mobilecontent,templatetype,importantlevel,content) VALUES
(1426050938031968257,'USER_FORGET_PWD_VERIFY','用户找回密码验证',1,'找回密码','','',1,now(),now(),1,'MESSAGETEMPLATE',0,'',0,'',0,3,'<p><span class="ql-font-helvetica ql-size-14px" style="color: rgb(49, 53, 59);">尊敬的用户 ${orgName},您好：</span></p><p><br></p><pre class="ql-syntax" spellcheck="false">&nbsp;&nbsp;您正在进行找回登录密码的重置操作，本次请求的邮件验证码是：${verificationCode}(为了保证你账号的安全性，请在5分钟内完成设置)。 本验证码5分钟内有效，请及时输入。
</pre><p><br></p><p><span class="ql-font-helvetica ql-size-14px" style="color: rgb(49, 53, 59);">&nbsp;&nbsp;为保证账号安全，请勿泄漏此验证码。</span></p><p><span class="ql-font-helvetica ql-size-14px" style="color: rgb(49, 53, 59);">&nbsp;&nbsp;如非本人操作，及时检查账号。</span></p>');

INSERT INTO sirm_messagetemplate (objid, code, name, sendmode, title, smscontent, remindcontent, forceflag, createtimestamp, updatetimestamp, version, entityname, catagory, remark, processtype, mobilecontent, templatetype, importantlevel, content)
VALUES(1438697231887765506, 'USER_RESET_PASSWORDS_REMIND', '用户重置密码通知', 1, '用户重置密码通知', '', '', 1, now(), now(), 3, 'MESSAGETEMPLATE', 0, '', 0, '', 0, 3, '<p>${orgName}, 您好!</p><p><br></p><pre class="ql-syntax" spellcheck="false">您开通的账号已重置密码为${resetPassword},在使用的过程中如有任何疑问,
可邮件回复,或电话联系我们,希望您使用愉快!
</pre>');

-- ----------------------------
-- Table structure for sirm_sendmessage
-- ----------------------------
DELETE FROM `sirm_sendmessage`;

-- ----------------------------
-- Table structure for sirm_sendmessagedetail
-- ----------------------------
DELETE FROM `sirm_sendmessagedetail`;

-- ----------------------------
-- Table structure for sirm_setting
-- ----------------------------
DELETE FROM `sirm_setting`;

-- ----------------------------
-- Records of sirm_setting
-- ----------------------------
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (1, 'COMMON', 'ROLESHOW', 'true', '是否显示角色，false=不显示 true或空=显示', 0, now(), now(), 167, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (2, 'COMMON', 'SIRM_FUNDCRMURL', ' ', '用于记录基金crm的地址。(格式http://....)', 0, now(), now(), 101, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (3, 'COMMON', 'ORGDEFAULTPOSITION', '999003042', '用户默认岗位编号66', 0, now(), now(), 104, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (4, 'ORG', 'PASSWORD', '9aQ1qd+Tsy3R9CdOPfnT5g==', '用户初始密码（用户重置密码后的默认初始密码）', 1, now(), now(), 38274, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (5, 'FRAMEWORK', 'SIRM_SHOWLANGUAGE', '1', '控制是否显示语言选择', 0, now(), now(), 99, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (6, 'ORG', 'ORGSIRM001', '2', '密码最长使用周期', 0, now(), now(), 1233, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (7, 'ORG', 'ORGSIRM002', '1', '密码失效前提前提醒天数', 0, now(), now(), 1233, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (8, 'ORG', 'ORGSIRM003', '3', '密码重复次数', 0, now(), now(), 1231, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (9, 'ORG', 'ORGSIRM004', '8', '密码长度(设置密码的长度不得小于该值)', 0, now(), now(), 1230, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (10, 'ORG', 'ORGSIRM005', '0', '密码复杂度-包含数字', 0, now(), now(), 1228, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (11, 'ORG', 'ORGSIRM006', '0', '密码复杂度-包含小写字母', 0, now(), now(), 1228, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (12, 'ORG', 'ORGSIRM007', '0', '密码复杂度-包含大写字母', 0, now(), now(), 1227, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (13, 'ORG', 'ORGSIRM008', '0', '密码复杂度-包含特殊字符', 0, now(), now(), 1226, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (14, 'ORG', 'ORGSIRM009', '0', '密码是否允许包含用户名', 0, now(), now(), 1226, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (15, 'ORG', 'ORGSIRM010', '', '密码允许输入错误次数', 0, now(), now(), 1226, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (16, 'ORG', 'ORGSIRM011', '1', '锁定周期', 0, now(), now(), 1226, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (17, 'ORG', 'ORGSIRM012', '', '账户密码输入错误次数复位时间 分钟', 0, now(), now(), 1224, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (18, 'ORG', 'ORGSIRM013', '0', '首次登录是否强制修改密码', 0, now(), now(), 1223, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (19, 'ORG', 'ORGSIRM014', '0', '重置密码后是否强制修改密码', 0, now(), now(), 1222, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (20, 'ORG', 'ORGSIRM015', '0', '密码安全策略开关', 0, now(), now(), 1222, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (21, 'ORG', 'ORGSIRM018', '', '密码复杂度必须满足多少项', 0, now(), now(), 1220, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (41, 'ROUTINE', 'FASTMENU_ID', '100000111', '快捷菜单', 0, now(), now(), 2552, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (42, 'ORG', 'roleorder', NULL, '树形菜单中角色与机构显示的顺序，1:角色在上，其他：机构在上', 0, now(), now(), 104, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (43, 'FORM', 'FORMBASEFLOW', '8', '自定义表单使用的基础工作流', 0, now(), now(), 109, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (44, 'FORM', 'TEMPLATECODES', 'qingjia', '要使用已发布的自定义表单模板', 0, now(), now(), 105, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (45, 'COMMON', 'JOBEMAIL', '', '任务提醒邮件地址\n', 0, now(), now(), 109, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (46, 'COMMON', 'TIMEINTERVAL', '', '发送消息时间间隔（单位:s，默认为10s）', 0, now(), now(), 107, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (47, 'ORG', 'EXECUTECODE', NULL, '行政上级', 0, now(), now(), 101, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (48, 'ORG', 'CANSWITCHUSER', NULL, '是否开启切换用户,1表示开启,0表示关闭', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (49, 'COMMON', 'ORGIMPORTTYPE', 'update', '导入用户时的类型, update为同名修改用户信息,insert为新增用户', 0, now(), now(), 123, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (50, 'FRAMEWORK', 'SIRM_TITLE', '应用程序框架', '系统显示标题', 0, now(), now(), 154, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (51, 'COMMON', 'ATTACHMENT_MAXSIZE', '10MB', '附件设置->附件大小限制', 0, now(), now(), 343, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (52, 'COMMON', 'MSG_SENDMODE_4', 'com.sinitek.sirm.routine.message.support.MessageRemindSender', '系统提醒发送类', 0, now(), now(), 346, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (53, 'COMMON', 'MAIL_FROMSYS', '', '系统发送邮件地址\n', 0, now(), now(), 344, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (54, 'COMMON', 'MSG_SENDMODE_2', NULL, '短消息发送类', 0, now(), now(), 340, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (55, 'COMMON', 'MSG_SENDMODE_1', 'com.sinitek.sirm.routine.message.support.MessageEmailSender', '邮件发送类', 0, now(), now(), 345, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (56, 'COMMON', 'UNIT_EMPOLYEESHOW', '', '组织结构部门下是否显示员工,1为显示,0为不显示', 0, now(), now(), 345, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (58, 'COMMON', 'ADMINUSER', '*********', '发送消息用的管理员id\n', 0, now(), now(), 344, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (59, 'COMMON', 'SMS_SUPPORTFLAG', '0', '系统中的消息是否支持短信,0为支持,1为不支持', 0, now(), now(), 348, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (60, 'COMMON', 'KICKMODE', '0', '踢人模式，1是启用，0是不启用（默认）', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (61, 'COMMON', 'ORGAUTHDEFAULTVIEW', 'role', '组织结构默认权限视图', 0, now(), now(), 343, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (62, 'COMMON', 'WATERMARK_WIDTH_HEIGHT', '300:70', '水印的密度。宽：高', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (63, 'COMMON', 'MENU_MODE', 'horizontal', '菜单模式，horizontal：横向水平菜单，vertical：左侧垂直菜单，mix：混合模式', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (64, 'COMMON', 'ADMINROLE', NULL, '管理员角色', 0, now(), now(), 340, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (65, 'COMMON', 'LOG_INDEX_MODE', '', 'ES日志索引', 0, now(), now(), 337, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (66, 'COMMON', 'WATERMARK_FONT', '14:30', '水印的字体。字体：字号：透明度。透明度范围0到255', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (67, 'COMMON', 'ATTACHMENT_ENC_FLAG', '1', '附件设置->附件是否加密。1为加密, 0为不加密', 0, now(), now(), 343, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (69, 'COMMON', 'WATERMARK_DEGREE', '22', '水印的倾斜度。0到90', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (70, 'COMMON', 'LOG_PATTERN', '[%p][%d{yyyy-MM-dd HH:mm:ss}][%C{1}:%L] - %m%n', '动态日志使用->默认日志输出格式', 0, now(), now(), 343, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (71, 'COMMON', 'INDEXHOME', '', 'ES模式下->业务日志index模式', 0, now(), now(), 341, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (72, 'COMMON', 'DOCUMENTMIMETYPES', 'txt,md,rar,zip,xlsx,xls,xml,7z,doc,docx,ppt,pptx,pdf,jpg,png,gif,html,htm,apk,ipa', '文档管理能够上传的文件类型', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (75, 'COMMON', 'WATERMARK_CONTENT', 'orgname-username', '水印的内容，可替代参数只有${orgname}、${username}、${logindate}', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (76, 'COMMON', 'WORDPARSERPATH', '', 'WORD工具路径存放位置\n', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (77, 'COMMON', 'MAIL_SMTPPORT', '', 'smtp服务器发送端口', 0, now(), now(), 345, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (78, 'COMMON', 'MAIL_SMTPPWD', '', 'smtp服务器密码', 1, now(), now(), 343, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (79, 'COMMON', 'WATERMARK_SHOW', '1', '是否展示水印。1，展示；0，不展示', 0, now(), now(), 342, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (80, 'COMMON', 'MAIL_SMTPUSER', 'admin', 'smtp服务器登录账号\n', 0, now(), now(), 344, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (81, 'ORG', 'ORGSIRM016', '', '系统管理员登录名 ', 0, now(), now(), 1676, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (82, 'COMMON', 'MAIL_SMTPSERVER', '', 'smtp服务器地址\n', 0, now(), now(), 344, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (83, 'COMMON', 'HOST_ADDRESS', 'http://*************:18090/', '系统设置->主机域名地址', 0, now(), now(), 314, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (84, 'COMMON', 'MAIL_SSL', '0', '邮件服务器设置->是否使用SSL。0为否 1为是', 0, now(), now(), 3, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (85, 'COMMON', 'ORGRESIGNDFAULTSWITCH', '0', '组织结构管理-离职用户操作开关（0：开；1：关）', 0, now(), now(), 3, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (86, 'DEMO', 'USER_EXPIRE_REMIND', '3', '1、2、3分别表示提前一天、两天、三天', 0, now(), now(), 3, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130525433532418, 'COMMON', 'USER_CHECK_LDAP_PORT', '', 'LDAP端口，默认389, 开启ssl后默认646', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130525525807106, 'COMMON', 'USER_CHECK_LDAP_DISPLAYNAME', '', 'LDAP中的用户属性中定位 员工姓名 的属性名', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130525664219137, 'COMMON', 'USER_CHECK_LDAP_PRINCIPAL', '', 'LDAP管理员账号', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130526054289409, 'COMMON', 'USER_CHECK_LDAP_BASEDN', '', 'LDAP的根，BASEDN', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130526150758401, 'COMMON', 'USER_CHECK_LDAP_FILTER', '', 'LDAP用户过滤表达式，用于唯一定位ldap中的用户，会用登录名替换掉{username}占位符的内容', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130526251421697, 'COMMON', 'USER_CHECK_CLASSNAME', '', '用户身份验证自定义实现类，用户身份验证模式 = 3时有效', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130526440165378, 'COMMON', 'USER_CHECK_LDAP_HOST', '', 'LDAP服务器地址', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130806678392834, 'COMMON', 'USER_CHECK_LDAP_SSL', '', '是否开启ssl，默认false, 不开启', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130807064268801, 'COMMON', 'USER_CHECK_LDAP_CREDENTIALS', '', 'LDAP管理员密码', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1493130807638888449, 'COMMON', 'USER_CHECK_LDAP_MEMBERDN', '', 'LDAP的账户名，会用登录名替换掉{username}占位符的内容', 0, now(), now(), 1, 'SETTING');
INSERT INTO `sirm_setting`(`objid`, `module`, `name`, `value`, `brief`, `encryptionflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES(1529403806448750593, 'COMMON', 'USER_CHECK_MODE', '0', '用户身份验证模式(0=本地（默认）1=表单验证 2=LDAP验证方式 3=自定义验证方式)', 0, now(), now(), 1, 'SETTING');


-- ----------------------------
-- Table structure for sirm_supportcenter
-- ----------------------------
DELETE FROM `sirm_supportcenter`;


-- ----------------------------
-- Table structure for sprt_businlogger
-- ----------------------------
DELETE FROM `sprt_businlogger`;

-- ----------------------------
-- Table structure for sprt_orgobject
-- ----------------------------
DELETE FROM `sprt_orgobject`;

-- ----------------------------
-- Records of sprt_orgobject
-- ----------------------------
INSERT INTO `sprt_orgobject`(`objid`, `orgname`, `objecttype`, `description`, `unittype`, `userid`, `inservice`, `orgid`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `lastobjid`, `datasrc`, `origid`, `mechanism_code`, `tenant_id`) VALUES (1, '公司', 2, '携宁科技', 'UNIT', NULL, '1', '99999', now(), now(), 68, 'ORGOBJECT', 100002261, NULL, NULL, 'root', 'root');
INSERT INTO `sprt_orgobject`(`objid`, `orgname`, `objecttype`, `description`, `unittype`, `userid`, `inservice`, `orgid`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `lastobjid`, `datasrc`, `origid`, `mechanism_code`, `tenant_id`) VALUES (2, '管理员', 1, '', NULL, '1', '1', '*********', now(), now(), 1889, 'ORGOBJECT', 100007042, NULL, NULL, NULL, 'root');
INSERT INTO `sprt_orgobject`(`objid`, `orgname`, `objecttype`, `description`, `unittype`, `userid`, `inservice`, `orgid`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `lastobjid`, `datasrc`, `origid`, `mechanism_code`, `tenant_id`) VALUES (3, '管理员', 2, '管理员', 'ROLE', NULL, '1', '9', now(), now(), 1, 'OrgObject', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sprt_orgrela
-- ----------------------------
DELETE FROM `sprt_orgrela`;

INSERT INTO `sprt_orgrela` (`objid`, `toobjectid`, `relationtype`, `fromobjectid`, `createtimestamp`, `updatetimestamp`, `version`, `tenant_id`, `sort`, `high_val`, `code_val`) VALUES (1, '99999', 'UNDERLINE', '0', SYSDATE(), SYSDATE(), 1, 'root', 1, 0, '1;');
INSERT INTO `sprt_orgrela` (`objid`, `toobjectid`, `relationtype`, `fromobjectid`, `createtimestamp`, `updatetimestamp`, `version`, `tenant_id`, `sort`, `high_val`, `code_val`) VALUES (2, '9', 'UNDERLINE', '99999', SYSDATE(), SYSDATE(), 1, 'root', 1, 1, '1;1;');
INSERT INTO `sprt_orgrela` (`objid`, `toobjectid`, `relationtype`, `fromobjectid`, `createtimestamp`, `updatetimestamp`, `version`, `tenant_id`, `sort`, `high_val`, `code_val`) VALUES (3, '*********', 'SUPERROLE', '9', SYSDATE(), SYSDATE(), 1, 'root', 1, 2, '1;1;1;');

-- ----------------------------
-- Table structure for org_userextendinfo
-- ----------------------------

INSERT INTO `org_userextendinfo` (`objid`, `email`, `sex`, `tel`, `tel2`, `job`, `mobilephone`, `userid`, `mobilephone2`, `familytelephone`, `familytelephone2`, `otherphone`, `otherphone2`, `bp`, `office`, `fax`, `fax2`, `familyfax`, `familyfax2`, `companyaddress`, `companyzip`, `familyaddress`, `familyzip`, `otheraddress`, `otherzip`, `email1`, `email2`, `homepage`, `where1`, `qq`, `msn`, `addressbook`, `introduction`, `passwordupdatetime`, `userlocktime`, `postid`, `qualifyno`, `qualifytype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `namepy`, `lzrq`, `rzrq`, `englishintroduction`, `englishname`, `tenant_id`) VALUES (1, '', 3, '', '', '', '', '1', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', -1, '', '', '', '', '', '', '', '', 0, '2022-05-13 08:08:55', '2022-05-13 08:08:55', 1, 'ORG_USEREXTENDINFO', '', '', '', '', '', 'root');

-- ----------------------------
-- Table structure for sprt_rightdef
-- ----------------------------
DELETE FROM `sprt_rightdef`;

-- ----------------------------
-- Records of sprt_rightdef
-- ----------------------------
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (1, 'SPRT_SYSMENU', '授权：SPRT_SYSMENU', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (2, 'SPRT_WEBMODULE', '授权：SPRT_WEBMODULE', NULL, 1, now(), now(), 24, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (3, 'FUNCTIONGROUP', '授权：FUNCTIONGROUP', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (4, 'FUNCTIONINFO', '授权：FUNCTIONINFO', NULL, 1, now(), now(), 36, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (5, 'RTDIRECTORY', '授权：RTDIRECTORY', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (6, 'RTDOCUMENTS', '授权：RTDOCUMENTS', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (7, 'PMINVESTMENTPLAN', '授权：PMINVESTMENTPLAN', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (8, 'BUSINESSACITON', '授权：BUSINESSACITON', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (9, 'PMBUSINESSACITON', '授权：PMBUSINESSACITON', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (10, 'PMBUSINESSACTION', '授权：PMBUSINESSACTION', NULL, 1, now(), now(), 0, 'RIGHTDEFINE');
INSERT INTO `sprt_rightdef`(`objid`, `rightdefinekey`, `rightdefinename`, `filterstring`, `rightauthtype`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`) VALUES (11, 'SPRT_BURYPOINT', '授权：SPRT_BURYPOINT', null, 1, now(), now(), 0, 'RIGHTDEFINE');

-- ----------------------------
-- Table structure for sprt_sysmenu
-- ----------------------------
DELETE FROM `sprt_sysmenu`;

-- ----------------------------
-- Records of sprt_sysmenu
-- ----------------------------
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (1, '', 0, '个人中心', '个人中心', 0, 'FRONTEND', now(), now(), 1049, 'SPRT_SYSMENU', 'icon-renyuan', '1', 'menu.personal.center');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (2, '', 0, '系统管理', '', 1, 'FRONTEND', now(), now(), 11, 'SPRT_SYSMENU', 'icon-xitongguanli_zuoce', '1', 'menu.system.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (3, NULL, 2, '运营管理', NULL, 0, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', NULL, '1', 'menu.operation.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (4, NULL, 2, '系统配置', NULL, 1, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', NULL, '1', 'menu.system.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (5, NULL, 2, '权限设置', NULL, 3, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, '1', 'menu.permission.settings');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (6, NULL, 2, '首页管理', NULL, 4, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, '1', 'menu.home.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (7, NULL, 2, '系统监控', NULL, 5, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, '1', 'menu.system.monitoring');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (8, NULL, 1, '日常工作', NULL, 0, 'FRONTEND', now(), now(), 3, 'SPRT_SYSMENU', 'menugroup', '1', 'menu.day.to.day.work');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (10, NULL, 2, '流程管理', NULL, 2, 'FRONTEND', now(), now(), 337, 'SPRT_SYSMENU', NULL, '1', 'menu.process.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (11, '/user/list', 3, '用户管理', '', 0, 'FRONTEND', now(), now(), 636, 'SPRT_SYSMENU', NULL, '2', 'menu.user.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (12, '/menu-auth', 5, '菜单权限设置', '', 1, 'FRONTEND', now(), now(), 608, 'SPRT_SYSMENU', NULL, '2', 'menu.menu.permission.setting');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (13, '/menu', 3, '菜单管理', '', 2, 'FRONTEND', now(), now(), 2, 'SPRT_SYSMENU', NULL, '2', 'menu.menu.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (14, '/org/list', 3, '组织结构管理', '', 1, 'FRONTEND', now(), now(), 1095, 'SPRT_SYSMENU', NULL, '2', 'menu.organizational.structure.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (15, '/common/setting', 4, '参数配置', '', 2, 'FRONTEND', now(), now(), 438, 'SPRT_SYSMENU', NULL, '2', 'menu.parameter.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (16, '/workflow/process/process-list', 10, '流程定义', '', 0, 'FRONTEND', now(), now(), 533, 'SPRT_SYSMENU', NULL, '2', 'menu.process.definition');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (17, '/workflow/example/manager-process', 10, '系统流程管理', '', 1, 'FRONTEND', now(), now(), 512, 'SPRT_SYSMENU', NULL, '2', 'menu.system.process.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (18, '/messagetemplate/message-template-list', 4, '消息模板', '', 1, 'FRONTEND', now(), now(), 594, 'SPRT_SYSMENU', NULL, '2', 'menu.message.template');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (19, '/common/homepage-auth', 5, '首页权限设置', '', 2, 'FRONTEND', now(), now(), 729, 'SPRT_SYSMENU', NULL, '2', 'menu.home.page.permission.settings');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (20, '/quartz/list', 7, '定时任务管理', '', 5, 'FRONTEND', now(), now(), 588, 'SPRT_SYSMENU', NULL, '2', 'menu.timed.task.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (23, '/org/authority', 7, '权限报表', '', 7, 'FRONTEND', now(), now(), 804, 'SPRT_SYSMENU', NULL, '2', 'menu.permission.report');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (24, '/workflow/process/system-parameter-list', 10, '流程参数管理', '', 3, 'FRONTEND', now(), now(), 500, 'SPRT_SYSMENU', NULL, '2', 'menu.process.parameter.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (25, '/routine/holidays', 3, '节假日管理', '', 3, 'FRONTEND', now(), now(), 582, 'SPRT_SYSMENU', NULL, '2', 'menu.holiday.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (27, '/business/business-list', 7, '业务日志查询', '', 9, 'FRONTEND', now(), now(), 486, 'SPRT_SYSMENU', NULL, '2', 'menu.business.log.query');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (28, '/org/statistical', 7, '系统使用统计', '', 10, 'FRONTEND', now(), now(), 493, 'SPRT_SYSMENU', NULL, '2', 'menu.system.usage.statistics');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (29, '/role/list', 3, '角色管理', '', 4, 'FRONTEND', now(), now(), 492, 'SPRT_SYSMENU', NULL, '2', 'menu.role.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (30, '/workflow/process/manager-agents', 10, '代理人管理', '', 4, 'FRONTEND', now(), now(), 421, 'SPRT_SYSMENU', NULL, '2', 'menu.agent.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (32, '/common/cache', 4, '缓存信息', '', 7, 'FRONTEND', now(), now(), 550, 'SPRT_SYSMENU', NULL, '2', 'menu.cache.information');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (33, '/workflow/example/manager-dealer', 7, '员工未完成任务数', '', 6, 'FRONTEND', now(), now(), 320, 'SPRT_SYSMENU', NULL, '2', 'menu.emp.uncompleted.tasks');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (34, '/workflow/process/config', 10, '流程撤回配置', '', 5, 'FRONTEND', now(), now(), 322, 'SPRT_SYSMENU', NULL, '2', 'menu.process.withdrawal.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (35, '/orgrelation/orgrelation-list', 3, '组织结构上下级管理', '', 5, 'FRONTEND', now(), now(), 395, 'SPRT_SYSMENU', NULL, '2', 'menu.org.subordinat.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (36, '/message/manage-list', 7, '系统消息查询', '', 13, 'FRONTEND', now(), now(), 381, 'SPRT_SYSMENU', NULL, '2', 'menu.system.message.query');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (37, '/orgscheme/orgscheme-list', 4, '选人方案', '', 8, 'FRONTEND', now(), now(), 379, 'SPRT_SYSMENU', NULL, '2', 'menu.selection.control.scheme');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (39, '/org/statistical/useraccess', 7, '用户模块访问', '', 16, 'FRONTEND', now(), now(), 30, 'SPRT_SYSMENU', NULL, '2', 'user.menu.access');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (40, '/webmodule/webmodule-list', 6, '首页模块管理', '', 1, 'FRONTEND', now(), now(), 2, 'SPRT_SYSMENU', NULL, 2, 'menu.home.module.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (41, '/homepage/scheme/list', 6, '首页方案管理', '', 2, 'FRONTEND', now(), now(), 2, 'SPRT_SYSMENU', NULL, '2', 'menu.homepage.template.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (42, '/log/loggerconfiglist', 4, '动态日志配置', '', 9, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', NULL, '2', 'menu.dynamic.log.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (44, '/common/support-center', 3, '支持中心', '', 6, 'FRONTEND', now(), now(), 7, 'SPRT_SYSMENU', NULL, '2', 'menu.support.center');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (45, '/message/list', 8, '我的消息', '', 0, 'FRONTEND', now(), now(), 24, 'SPRT_SYSMENU', NULL, '2', 'menu.my.message');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (46, '/workflow/example/manager-task-list', 8, '我的事宜', '', 1, 'FRONTEND', now(), now(), 20, 'SPRT_SYSMENU', NULL, '2', 'menu.my.business');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (48, '/workflow/process/common-parameter-list', 10, '流程类型参数管理', '', 6, 'FRONTEND', now(), now(), 3, 'SPRT_SYSMENU', NULL, '2', 'menu.process.type.parameter.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (49, '/common/enum', 4, '枚举配置', '', 10, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', NULL, '2', 'menu.enumeration.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (50, '/common/i18n', 4, '语言包配置', '', 3, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', NULL, '2', 'menu.language.pack.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (54, '/common/event', 4, '事件管理', '', 11, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, '2', 'menu.common.event');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (56, '/calendar/event/manager', 8, '日程管理', '', 2, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', NULL, '2', 'menu.calendar.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (57, '/common/config', 4, '系统公共配置', '', 13, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', '2', 'menu.common.config');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (58, '/common/function-list', 4,'功能配置', '', 0, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, 2, 'menu.function.configuration');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (59, '/common/function-auth-list', 5, '功能权限设置', '', 0, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, 2, 'menu.function.permission.setting');

INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (1385513899259990017, '/openapi/personal-token/list',3 , '个人令牌管理', '', 7,'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', '2', 'menu.personal.token.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`) VALUES (1388006506049441794, '/openapi/personal-token/auth', 5 , '个人令牌权限设置', '', 3, 'FRONTEND', now(), now(), 2, 'SPRT_SYSMENU', '', '2', 'menu.personal.token.permission.settings');

INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1400725913662001154, '/common/i18n-code/list', (select objid from (select objid from sprt_sysmenu where name = '系统监控' and syscode = 'FRONTEND') as temp), '国际化编码查询', '', 0, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, 2, 'menu.i18n.code');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1471044462452019202, '/workflow/example/manager-task', (select objid from (select objid from sprt_sysmenu where name = '流程管理' and syscode = 'FRONTEND') as temp), '系统任务管理', '', 2, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', '2', 'menu.system.task.management');
INSERT INTO `sprt_sysmenu`(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1446783090679746562, '/action/list', (select objid from (select objid from sprt_sysmenu where name = '系统配置' and syscode = 'FRONTEND') as temp), '动作管理', '', 14, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, 2, 'menu.common.action');

INSERT INTO `sprt_sysmenu` (`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1447475122867933186, '', 2, '移动端', '', 6, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', 1, 'menu.common.mobile');
INSERT INTO `sprt_sysmenu` (`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1436227646873800705, '/mobile/version/list', 1447475122867933186, '版本管理', '', 0, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', 2, 'menu.mobile.version');
INSERT INTO `sprt_sysmenu` (`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1447475406176391169, '/mobile/icon/list', 1447475122867933186, '图标管理', '', 1, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', 2, 'menu.mobile.icon');
INSERT INTO `sprt_sysmenu` (`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1447475518051061761, '/mobile/auth/list', 1447475122867933186, '图标权限管理', '', 2, 'FRONTEND', now(), now(), 1, 'SPRT_SYSMENU', '', 2, 'menu.mobile.iconauth');
insert into `sprt_sysmenu` (`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
values(1531875626301132801,'/common/sini-cube-info',4,'框架基本信息','',16,'FRONTEND',now(),now(),1,'SPRT_SYSMENU','',2,'menu.common.framework');
-- 添加应用管理菜单
INSERT INTO sprt_sysmenu(`objid`, `url`, `parentid`, `name`, `description`, `ord`, `syscode`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `icon`, `menu_type`, `I18N`)
VALUES (1584741906817093633, '/common/application/list', (select objid from (select objid from sprt_sysmenu where name = '运营管理' and syscode = 'FRONTEND') as temp),
'应用管理', '', 0, 'FRONTEND', now(), now(), 0, 'SPRT_SYSMENU', NULL, 2, 'menu.application.management');

-- ----------------------------
-- Table structure for sprt_webmodule
-- ----------------------------
DELETE FROM `sprt_webmodule`;

-- ----------------------------
-- Records of sprt_webmodule
-- ----------------------------
INSERT INTO `sprt_webmodule`(`objid`, `component_key`, `name`, `state`, `code`, `height`, `ord`, `syscode`, `more_url`, `description`, `configfunction`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `width`) VALUES (1, 'dashboard/DashboardMessageList', '未读消息', 1, 'icon-1', '330px', 31, 'FRONTEND', '/message/list', '未读消息', NULL, now(), now(), 0, 'SPRT_WEBMODULE', '50%');
INSERT INTO `sprt_webmodule`(`objid`, `component_key`, `name`, `state`, `code`, `height`, `ord`, `syscode`, `more_url`, `description`, `configfunction`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `width`) VALUES (2, 'dashboard/DashboardTaskList', '我的事宜', 1, '', '330px', 0, 'FRONTEND', '/workflow/example/manager-task-list', '未处理的事宜', NULL, now(), now(), 2, 'SPRT_WEBMODULE', '50%');

-- ----------------------------
-- Table structure for um_qualifyinfo
-- ----------------------------
DELETE FROM `um_qualifyinfo`;


-- ----------------------------
-- Table structure for um_userinfo
-- ----------------------------
DELETE FROM `um_userinfo`;

-- ----------------------------
-- Records of um_userinfo
-- ----------------------------
INSERT INTO `um_userinfo`(`objid`, `userid`, `username`, `password`, `lockflag`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `lastobjid`, `datasrc`, `origid`, `tenant_id`) VALUES (1, '1', 'admin', '62c8ad0a15d9d1ca38d5dee762a16e01', '0', now(), now(), 585, 'USERINFO', 3001103, '2012/11/5 11:49:29', '0', 'root');

-- ----------------------------
-- Table structure for um_userkeepsignedin
-- ----------------------------
DELETE FROM `um_userkeepsignedin`;

-- ----------------------------
-- Table structure for um_userproperty
-- ----------------------------
DELETE FROM `um_userproperty`;

-- ----------------------------
-- Records of um_userproperty
-- ----------------------------
INSERT INTO `um_userproperty`(`objid`, `userid`, `name`, `value`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (1, '1', 'inservice', '1', now(), now(), 0, 'USERPROPERTY', 'root');
INSERT INTO `um_userproperty`(`objid`, `userid`, `name`, `value`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (2, '1', 'orgid', '*********', now(), now(), 0, 'USERPROPERTY', 'root');
INSERT INTO `um_userproperty`(`objid`, `userid`, `name`, `value`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (3, '1', 'displayname', '管理员', now(), now(), 0, 'USERPROPERTY', 'root');
INSERT INTO `um_userproperty`(`objid`, `userid`, `name`, `value`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (4, '1', 'logontime', now(), now(), now(), 0, 'USERPROPERTY', 'root');
INSERT INTO `um_userproperty`(`objid`, `userid`, `name`, `value`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (5, '1', 'resetpswflag', '1', now(), now(), 0, 'USERPROPERTY', 'root');

-- ----------------------------
-- Table structure for um_userrole
-- ----------------------------
DELETE FROM `um_userrole`;

-- ----------------------------
-- Table structure for um_userscheme
-- ----------------------------
DELETE FROM `um_userscheme`;

-- ----------------------------
-- Table structure for um_userschemerela
-- ----------------------------
DELETE FROM `um_userschemerela`;

-- ----------------------------
-- Table structure for um_usersecurityquestion
-- ----------------------------
DELETE FROM `um_usersecurityquestion`;

-- ----------------------------
-- Table structure for wf_agents
-- ----------------------------
DELETE FROM `wf_agents`;

-- ----------------------------
-- Table structure for wf_approvalphrase
-- ----------------------------
DELETE FROM `wf_approvalphrase`;

-- ----------------------------
-- Table structure for wf_example
-- ----------------------------
DELETE FROM `wf_example`;

-- ----------------------------
-- Table structure for wf_exampleaskfor
-- ----------------------------
DELETE FROM `wf_exampleaskfor`;

-- ----------------------------
-- Table structure for wf_exampleaskforbatch
-- ----------------------------
DELETE FROM `wf_exampleaskforbatch`;

-- ----------------------------
-- Table structure for wf_exampleentry
-- ----------------------------
DELETE FROM `wf_exampleentry`;

-- ----------------------------
-- Table structure for wf_examplelist
-- ----------------------------
DELETE FROM `wf_examplelist`;


-- ----------------------------
-- Records of wf_examplelist
-- ----------------------------

INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (1, 0, '发起', 1, 0, 'ExampleStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (2, 1, '执行中', 1, 1, 'ExampleStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (3, 3, '终止', 1, 3, 'ExampleStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (4, 4, '撤回', 1, 4, 'ExampleStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (5, 5, '异常', 1, 5, 'ExampleStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (6, 9, '完成', 1, 9, 'ExampleStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (7, 0, '待处理', 1, 0, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (8, 1, '正在处理', 1, 1, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (9, 2, '自动完成', 1, 2, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (10, 3, '已终止', 1, 3, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (11, 4, '已撤回', 1, 4, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (12, 5, '废弃', 1, 5, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (13, 6, '被抢占', 1, 6, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (14, 7, '被跳过', 1, 7, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (15, 8, '已处理', 1, 8, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (16, 9, '被完成', 1, 9, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (17, 10, '被转移', 1, 10, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (18, 12, '已转移', 1, 12, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (19, 100, '已代理', 1, 100, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (20, 0, '无', 1, 0, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (21, 1, '通过', 1, 1, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (22, 2, '驳回', 1, 2, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (23, 3, '提交', 1, 3, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (24, 4, '放弃', 1, 4, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (25, 5, '跳过', 1, 5, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (26, -1, '正在生成', 1, -1, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (27, 0, '待处理', 1, 0, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (28, 1, '正在处理', 1, 1, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (29, 2, '自动完成', 1, 2, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (30, 3, '已终止', 1, 3, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (31, 4, '已撤回', 1, 4, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (32, 5, '废弃', 1, 5, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (33, 8, '已处理', 1, 8, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (34, 11, '异常', 1, 11, 'StepStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (35, 10, '退回修订', 1, 10, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (36, 20, '征求中', 1, 20, 'OwnerStatus', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (37, 21, '交办中', 1, 21, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');
INSERT INTO `wf_examplelist`(`objid`, `key`, `value`, `status`, `sort`, `name`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `tenant_id`) VALUES (38, 22, '已交办', 1, 22, 'StepCondition', now(), now(), 1, 'WFEXAMPLELIST', 'common');

-- ----------------------------
-- Table structure for wf_examplepara
-- ----------------------------
DELETE FROM `wf_examplepara`;

-- ----------------------------
-- Table structure for wf_examplestep
-- ----------------------------
DELETE FROM `wf_examplestep`;

-- ----------------------------
-- Table structure for wf_examplesteplink
-- ----------------------------
DELETE FROM `wf_examplesteplink`;


-- ----------------------------
-- Table structure for wf_examplestepowner
-- ----------------------------
DELETE FROM `wf_examplestepowner`;


-- ----------------------------
-- Table structure for wf_exampletask
-- ----------------------------
DELETE FROM `wf_exampletask`;

-- ----------------------------
-- Table structure for wf_flowdots
-- ----------------------------
DELETE FROM `wf_flowdots`;

-- ----------------------------
-- Table structure for wf_flownode
-- ----------------------------
DELETE FROM `wf_flownode`;


-- ----------------------------
-- Table structure for wf_flowpaths
-- ----------------------------
DELETE FROM `wf_flowpaths`;

-- ----------------------------
-- Table structure for wf_flowprops
-- ----------------------------
DELETE FROM `wf_flowprops`;

-- ----------------------------
-- Table structure for wf_process
-- ----------------------------
DELETE FROM `wf_process`;

-- ----------------------------
-- Table structure for wf_processowner
-- ----------------------------
DELETE FROM `wf_processowner`;

-- ----------------------------
-- Table structure for wf_processownerlink
-- ----------------------------
DELETE FROM `wf_processownerlink`;

-- ----------------------------
-- Table structure for wf_processpara
-- ----------------------------
DELETE FROM `wf_processpara`;

-- ----------------------------
-- Table structure for wf_processrule
-- ----------------------------
DELETE FROM `wf_processrule`;

-- ----------------------------
-- Table structure for wf_processruleinfo
-- ----------------------------
DELETE FROM `wf_processruleinfo`;

-- ----------------------------
-- Table structure for wf_processstep
-- ----------------------------
DELETE FROM `wf_processstep`;

-- ----------------------------
-- Records of wf_processstep
-- ----------------------------

-- ----------------------------
-- Table structure for wf_processstepdo
-- ----------------------------
DELETE FROM `wf_processstepdo`;

-- ----------------------------
-- Table structure for wf_processstephistory
-- ----------------------------
DELETE FROM `wf_processstephistory`;

-- ----------------------------
-- Table structure for wf_processsteplink
-- ----------------------------
DELETE FROM `wf_processsteplink`;


-- ----------------------------
-- Table structure for wf_processsteplinkdo
-- ----------------------------
DELETE FROM `wf_processsteplinkdo`;

-- ----------------------------
-- Table structure for wf_processsteplinkif
-- ----------------------------
DELETE FROM `wf_processsteplinkif`;


-- ----------------------------
-- Table structure for wf_processurl
-- ----------------------------
DELETE FROM `wf_processurl`;

INSERT INTO `wf_processurl`(`objid`, `sort`, `name`, `actionurl`, `showurl`, `type`, `status`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `viewurl`, `urltype`, `tenant_id`) VALUES (1, 1, '测试流程-审批外出申请', '/workflow/demo/wf-demo-approve', '/workflow/demo/wf-demo-detail', 99, 1, now(), now(), 28, 'WFPROCESSURL', NULL, 2, NULL);
INSERT INTO `wf_processurl`(`objid`, `sort`, `name`, `actionurl`, `showurl`, `type`, `status`, `createtimestamp`, `updatetimestamp`, `version`, `entityname`, `viewurl`, `urltype`, `tenant_id`) VALUES (2, 2, '测试流程-提交外出申请', '/workflow/demo/wf-demo-edit', '/workflow/demo/wf-demo-detail', 99, 1, now(), now(), 11, 'WFPROCESSURL', NULL, 2, NULL);


-- ----------------------------
-- Table structure for wf_processextend
-- ----------------------------

DELETE FROM `wf_processextend`;


-- ----------------------------
-- Table structure for wf_processlist
-- ----------------------------
DELETE FROM `wf_processlist`;


-- ----------------------------
-- Records of wf_processlist
-- ----------------------------
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (2, '已发布', 1, 2, 'ProcessStatus', 2, 0, '流程模板状态', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (3, '已删除', 1, 100, 'ProcessStatus', 100, 0, '流程模板状态', 0, now(), now(), 3, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (4, '未发布', 1, 1, 'StepStatus', 1, 0, '流程模板步骤状态', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (5, '已发布', 1, 2, 'StepStatus', 2, 0, '流程模板步骤状态', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (6, '已删除', 1, 4, 'StepStatus', 100, 0, '流程模板步骤状态', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (7, '开始节点', 0, 1, 'StepType', 1, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (8, '结束节点', 0, 100, 'StepType', 2, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (9, '处理节点', 1, 2, 'StepType', 3, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (10, '逻辑节点', 0, 1, 'StepType', 4, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (11, '分支节点', 0, 4, 'StepType', 5, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (12, '合并节点', 0, 5, 'StepType', 6, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (13, '提交节点', 1, 3, 'StepType', 7, 0, '流程模板节点类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (14, '抢占审批', 1, 1, 'JudgeType', 1, 0, '流程节点审批类型', 0, now(), now(), 2, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (15, '投票审批', 1, 2, 'JudgeType', 2, 0, '流程节点审批类型', 0, now(), now(), 33, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (16, '轮询审批', 0, 3, 'JudgeType', 3, 0, '流程节点审批类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (17, '最优审批', 0, 4, 'JudgeType', 4, 0, '流程节点审批类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (18, '指定特定人', 1, 1, 'SubmitOwner', 1, 0, '流程节点审批人类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (19, '流程发起人', 1, 2, 'SubmitOwner', 2, 0, '流程节点审批人类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (20, '历史处理人', 0, 3, 'SubmitOwner', 3, 0, '流程节点审批人类型', 0, now(), now(), 17, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (21, '规则表达式', 1, 100, 'LinkIfType', 1, 0, '审批通过条件判断类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (22, '审批状态', 1, 1, 'LinkIfType', 2, 0, '审批通过条件判断类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (23, '=', 1, 1, 'MathMark', 1, 0, '判断逻辑符号', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (24, '!=', 1, 2, 'MathMark', 2, 0, '判断逻辑符号', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (25, '>', 1, 3, 'MathMark', 3, 0, '判断逻辑符号', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (26, '>=', 1, 4, 'MathMark', 4, 0, '判断逻辑符号', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (27, '<', 1, 5, 'MathMark', 5, 0, '判断逻辑符号', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (28, '<=', 1, 6, 'MathMark', 6, 0, '判断逻辑符号', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (29, '通过', 1, 1, 'JudgeResult', 1, 0, '审批结果类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (30, '驳回', 1, 2, 'JudgeResult', 2, 0, '审批结果类型', 0, now(), now(), 1, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (40, '其他', 0, 3, 'JudgeResult', -1, 0, '审批结果类型', 0, now(), now(), 3, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (41, '通过人数（%）', 1, 1, 'CooJudge', 1, 0, '审批进入下一步条件的人数百分比单位', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (42, '通过人数（人）', 1, 2, 'CooJudge', 2, 0, '审批进入下一步条件的数量单位', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (43, '通过票数（%）', 1, 3, 'CooJudge', 3, 0, '审批进入下一步条件的数量单位', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (44, '通过票数（票）', 1, 4, 'CooJudge', 4, 0, '审批进入下一步条件的数量单位', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (45, '通过组数（%）', 0, 5, 'CooJudge', 5, 0, '审批进入下一步条件的数量单位', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (46, '通过组数（组）', 0, 6, 'CooJudge', 6, 0, '审批进入下一步条件的数量单位', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (47, '流程发起人', 1, 1, 'OwnerLinkOrigin', 1, 0, '目标处理的判断范围', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (48, '上步提交人', 1, 2, 'OwnerLinkOrigin', 2, 0, '目标处理的判断范围', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (52, '已发布', 1, 3, 'ProcessStatus', 3, 0, '流程模板状态', 0, now(), now(), 4, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (86, '自定义审批', 1, 100, 'JudgeType', 100, 0, '流程节点审批类型', 0, now(), now(), 0, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (106, '开始节点显示', 1, 1, 'WorkflowShow', 1, 0, '获取整个流程实例详细时 1 不展示系统自带跳过 不展示结束节点\n2 都展示', 0, now(), now(), 4, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (110, '为真', 1, 1, 'LinkIfResult', 0, 0, '自定义条件的判断条件', 0, now(), now(), 9, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (111, '为假', 1, 2, 'LinkIfResult', 1, 0, '自定义条件的判断条件', 0, now(), now(), 3, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (114, '事宜排序方式', 1, 0, 'WorkflowSort', 1, 0, '获取整个流程实例详细时1.通过流程类型，syscode，流程步骤id，任务开始时间进行降序排序。2：通过任务开始时间降序排序', 0, now(), now(), 4, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (116, '允许代理', 1, 1, 'WorkflowAgent', 1, 0, '节点对应的任务是否允许代理枚举', 0, now(), now(), 9, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (117, '禁止代理', 1, 2, 'WorkflowAgent', 2, 0, '节点对应的任务是否允许代理枚举', 0, now(), now(), 3, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (123, '等待处理', 1, 1, 'AutoSkip', 1, 0, '目标处理 满足条件之后的具体动作', 0, now(), now(), 9, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (124, '系统跳过', 1, 2, 'AutoSkip', 2, 0, '目标处理 满足条件之后的具体动作', 0, now(), now(), 2, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (125, '未发布', 1, 1, 'ProcessStatus', 1, 0, '	\n流程模板状态', 0, now(), now(), 1, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (126, '已废弃', 1, 5, 'ProcessStatus', 5, 0, '流程模板状态', 0, now(), now(), 1, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (127, '已发布', 1, 4, 'ProcessStatus', 4, 0, '流程模板状态', 0, now(), now(), 1, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (128, '退回', 1, 10, 'JudgeResult', 10, 0, '审批结果类型', 0, now(), now(), 1, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (129, '放弃', 1, 4, 'JudgeResult', 4, 0, '审批结果类型', 0, now(), now(), 7, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (130, '4', 1, 1, 'ShowWorkflowStyle', 1, 0, '流程详细展示方式,设置参数值,目前支持1和2.默认为1', 0, now(), now(), 20, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (131, '系统内置处理', 1, 1, 'UrlType', 1, 0, '页面类型', 0, now(), now(), 10, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (132, '自定义处理', 1, 2, 'UrlType', 2, 0, '页面类型', 0, now(), now(), 2, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (133, '投票等待', 1, 5, 'JudgeType', 5, 0, '流程节点审批类型', 0, now(), now(), 2, 'WF_PROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (134, '同步OA', 1, 0, 'OAFlag', 1, 0, 'com.sinitek.sirm.busin.workflow.support.OAActionImpl', 0, now(), now(), 1, 'WFPROCESSLIST', 'root');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (135, '等待', 1, 5, 'JoinType', 5, 0, '合并节点的结束由完成的分支数量决定，所有分支需要都完成', 0, now(), now(), 0, 'WFPROCESSLIST', 'root');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (136, '投票', 1, 2, 'JoinType', 2, 0, '合并节点的结束由完成的分支数量决定，所有分支不一定都完成', 0, now(), now(), 0, 'WFPROCESSLIST', 'root');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (137, '抢占', 1, 1, 'JoinType', 1, 0, '合并节点的结束由第一个完成的分支决定', 0, now(), now(), 0, 'WFPROCESSLIST', 'root');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (138, '通过百分比(%)', 1, 2, 'JoinJudge', 2, 0, '通过的分支数量所占的百分比', 0, now(), now(), 0, 'WFPROCESSLIST', 'root');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (139, '通过数量', 1, 1, 'JoinJudge', 1, 0, '通过分支的数量', 0, now(), now(), 0, 'WFPROCESSLIST', 'root');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (141, '当前节点默认配置', 1, 1, 'SpecifyOwnerRange', 1, 0, '处理节点指定下一步处理人范围', 0, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (142, '所有处理人', 1, 2, 'SpecifyOwnerRange', 2, 0, '处理节点指定下一步处理人范围 公司全体人', 0, now(), now(), 3, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (143, '方案代码', 1, 3, 'SpecifyOwnerRange', 3, 0, '处理节点指定下一步处理人范围 选人控件代码', 0, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (144, '禁止指定处理人', 1, 1, 'SpecifyOwnerType', 1, 0, '控制指定下一步处理人的枚举', 0, now(), now(), 3, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (145, '允许指定处理人', 1, 2, 'SpecifyOwnerType', 2, 0, '控制指定下一步处理人的枚举', 0, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (146, '必须指定处理人', 1, 3, 'SpecifyOwnerType', 3, 0, '控制指定下一步处理人的枚举', 0, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (147, '步骤前', 1, 1, 'CarbonCopyTiming', 1, 0, '抄送时机，步骤生成前', 0, now(), now(), 1, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (148, '步骤后', 1, 2, 'CarbonCopyTiming', 2, 0, '抄送时机，步骤执行完成后', 0, now(), now(), 1, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (149, '提交', 1, 11, 'JudgeResult', 3, 0, '审批结果类型', 0, now(), now(), 7, 'WFPROCESSLIST', 'common');
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (150, '处理人改变后执行', 1, 3, 'ChangeOwnerTask', 100150, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutapplyOwnerChangeDo', 99, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (151, '发起时执行', 1, 4, 'BusinessRecordTask', 100151, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutApplySaveAction', 99, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (152, '外出申请业务数据记录', 1, 5, 'BusinessRecordTask', 100152, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutApplyRecord', 99, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (153, '外出审批同步流程状态', 1, 6, 'SpecialTask', 100153, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutapplySynStatus', 99, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (154, '自定义条件', 1, 1, 'LinkIfType', 100154, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutapplyCondition', 99, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (155, '测试流程', 1, 1, 'ProcessType', 99, 0, '100153', 0, now(), now(), 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (156, '放弃时执行', '1', '7', 'SpecialTask', 100156, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutApplyGiveUpAction', '99', now(), now(), '2', 'WFPROCESSLIST', null);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (157, '审批驳回执行', '1', '8', 'SpecialTask', 100157, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutApplyRejectAction', '99', now(), now(), '2', 'WFPROCESSLIST', null);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (158, '退回修订执行', '1', '9', 'SpecialTask', 100158, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutApplyReviseAction', '99', now(), now(), '2', 'WFPROCESSLIST', null);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (159, '审批通过执行', '1', '10', 'SpecialTask', 100159, 0, 'com.sinitek.sirm.workflow.demo.support.DemoOutApplyPassAction', '99', now(), now(), '2', 'WFPROCESSLIST', null);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (160, '启用', 1, 1, 'SystemParamterStatus', 1, 0, '系统流程参数状态', 0, '2021-02-22 13:40:50', '2021-02-22 13:40:53', 1, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (161, '停用', 1, 2, 'SystemParamterStatus', 0, 0, '系统流程参数状态', 0, '2021-02-22 13:45:34', '2021-02-22 13:45:38', 1, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (162, '删除', 1, 3, 'SystemParamterStatus', 100, 0, '系统流程参数状态', 0, '2021-02-22 13:45:34', '2021-02-22 13:45:38', 1, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (163, '批量审批显示', 1, 0, 'BatchApproveShow', 1, 0, '1', 0, '2021-02-22 13:45:34', '2021-02-22 13:45:38', 2, 'WFPROCESSLIST', NULL);
INSERT INTO `wf_processlist`(`objid`, `value`, `status`, `sort`, `name`, `key`, `call_type`, `valueads`, `type`, `createtimestamp`, `UPDATETIMESTAMP`, `version`, `entityname`, `tenant_id`) VALUES (164, '审批历史中抄送信息显示', 1, 0, 'WorkflowHistoryExampleCcShow', 1, 0, '1', 0, now(), now(), 2, 'WFPROCESSLIST', NULL);

DELETE FROM `sirm_i18n`;

-- ----------------------------
-- Records of sirm_i18n
-- ----------------------------

INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (3, 'menu.system.management', '系统管理', '系統管理', 'System Management', now(), now(), 3);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (4, 'menu.operation.management', '运营管理', '運營管理', 'Operation Management', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (5, 'menu.system.configuration', '系统配置', '系統配置', 'System Configuration', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (6, 'menu.process.management', '流程管理', '流程管理', 'Process Management', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (7, 'menu.permission.settings', '权限设置', '權限設置', 'Permission Settings', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (8, 'menu.home.management', '首页管理', '首頁管理', 'Home Management', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (9, 'menu.system.monitoring', '系统监控', '系統監控', 'System Monitoring', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (10, 'menu.user.management', '用户管理', '用戶管理', 'User Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (11, 'menu.organizational.structure.management', '组织结构管理', '組織結構管理', 'Organizational Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (12, 'menu.menu.management', '菜单管理', '菜單管理', 'Menu Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (13, 'menu.holiday.management', '节假日管理', '節假日管理', 'Holiday Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (14, 'menu.role.management', '角色管理', '角色管理', 'Role Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (15, 'menu.org.subordinat.management', '组织结构上下级管理', '組織結構上下級管理', 'Organizational structure sub-level management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (16, 'menu.support.center', '支持中心', '支持中心', 'Support Center', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (17, 'menu.parameter.configuration', '参数配置', '參數配置', 'Parameter configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (18, 'menu.language.pack.configuration', '语言包配置', '語言包配置', 'Language Pack Configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (19, 'menu.message.template', '消息模板', '消息模板', 'Message template', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (20, 'menu.function.configuration', '功能配置', '功能配置', 'Function configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (21, 'menu.common.parameter.configuration', '公共参数配置', '公共參數配置', 'Common parameter configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (22, 'menu.password.security.policy', '密码安全策略', '密碼安全策略', 'Password Security Policy', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (23, 'menu.cache.information', '缓存信息', '緩存信息', 'Cache Information', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (24, 'menu.selection.control.scheme', '选人方案', '選人方案', 'Selection control scheme', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (25, 'menu.dynamic.log.configuration', '动态日志配置', '動態日誌配置', 'Dynamic log configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (27, 'menu.enumeration.configuration', '枚举配置', '枚舉配置', 'Enumeration configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (28, 'menu.process.definition', '流程定义', '流程定義', 'Process definition', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (29, 'menu.system.process.management', '系统流程管理', '系統流程管理', 'System Process Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (30, 'menu.process.parameter.management', '流程参数管理', '流程參數管理', 'Process parameter management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (31, 'menu.agent.management', '代理人管理', '代理人管理', 'Agent management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (32, 'menu.process.withdrawal.configuration', '流程撤回配置', '流程撤回配置', 'Process withdrawal configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (33, 'menu.process.type.parameter.management', '流程类型参数管理', '流程類型參數管理', 'Process type parameter management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (34, 'menu.menu.permission.setting', '菜单权限设置', '菜單權限設置', 'Menu permission setting', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (35, 'menu.home.page.permission.settings', '首页权限设置', '首頁權限設置', 'Home page permission settings', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (36, 'menu.function.permission.setting', '功能权限设置', '功能權限設置', 'Function permission setting', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (37, 'menu.home.module.management', '首页模块管理', '首頁模塊管理', 'Home Module Management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (38, 'menu.homepage.template.configuration', '首页方案管理', '首頁方案管理', 'Home page scheme management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (39, 'menu.timed.task.management', '定时任务管理', '定時任務管理', 'Timed task management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (40, 'menu.emp.uncompleted.tasks', '员工未完成任务数', '員工未完成任務數', 'Number of uncompleted tasks by employees', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (41, 'menu.permission.report', '权限报表', '權限報表', 'Permission report', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (42, 'menu.business.log.query', '业务日志查询', '業務日誌查詢', 'Business log query', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (43, 'menu.system.usage.statistics', '系统使用统计', '系統使用統計', 'System usage statistics', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (44, 'menu.system.message.query', '系统消息查询', '系統消息查詢', 'System message query', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (45, 'menu.personal.center', '个人中心', '個人中心', 'Personal Center', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (46, 'menu.day.to.day.work', '日常工作', '日常工作', 'Daily work', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (47, 'menu.my.message', '我的消息', '我的消息', 'My message', now(), now(), 2);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (48, 'menu.my.business', '我的事宜', '我的事宜', 'My business', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (49, 'menu.dashboard', '个人中心', '個人中心', 'Personal Center', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1401734021083435010, 'personal.center.function.configuration', '个人中心功能配置', '個人中心功能配置', 'Personal center function configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1401734021083435011, 'function.configuration', '功能配置', '功能配置', 'Function configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1401734021083435012, 'user.menu.access', '用户模块访问', '用戶模塊訪問', 'User menu access', now(), now(), 1);
-- 权限分组的语言包
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (50, 'groupsetting.menu', '菜单', '菜單', 'Menu', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (51, 'groupsetting.home', '首页', '首頁', 'Home', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (52, 'groupsetting.function', '功能', '功能', 'Function', now(), now(), 1);
-- 添加事件管理的语言包配置
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (56, 'menu.common.event', '事件管理', '事件管理', 'Event management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (57, 'menu.message.setting.config', '消息参数配置', '消息參數配置', 'Message parameter configuration', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (58, 'menu.common.mobile', '移动端', '移動端', 'Mobile', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (59, 'menu.mobile.version', '版本管理', '版本管理', 'Version management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (60, 'menu.mobile.icon', '图标管理', '圖標管理', 'Icon management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (61, 'menu.mobile.iconauth', '图标权限管理', '圖標權限管理', 'Icon permission management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (62, 'menu.calendar.management', '日程管理', '日程管理', 'Schedule management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (63, 'menu.wxwork.config', '企业微信配置', '企業微信配置', 'WxWork Config', now(), now(), 1);

INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1461055492452019636, 'menu.common.config', '系统公共配置', '系統公共配置', 'System Common Configuration', now(), now(), 1);
-- 添加个人令牌的语言包配置
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1394116261671211010, 'menu.personal.token.management', '个人令牌管理', '個人令牌管理', 'Personal token management', now(), now(), 1);
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1394167511393636354, 'menu.personal.token.permission.settings', '个人令牌权限设置', '個人令牌權限設置', 'Personal token permission settings', now(), now(), 1);
-- 添加国际化编码查询的语言包配置
INSERT INTO `sirm_i18n`(`id`, `i18n_key`, `zh_cn`, `zh_hk`, `en_us`, `createtimestamp`, `updatetimestamp`, `version`) VALUES (1400726491276382210, 'menu.i18n.code', '国际化编码查询', '國際化編碼查詢', 'International Code Query', now(), now(), 1);
-- 添加系统任务管理的语言包配置
INSERT INTO sirm_i18n(id, i18n_key, zh_cn, zh_hk, en_us, createtimestamp, updatetimestamp, version) VALUES (1391564494299336707, 'menu.system.task.management', '系统任务管理', '系統任務管理', 'System task management', now(), now(), 1);
-- 添加动作管理的语言包配置
INSERT INTO sirm_i18n(id, i18n_key, zh_cn, zh_hk, en_us, createtimestamp, updatetimestamp, version) VALUES (1391681808109801474, 'menu.common.action', '动作管理', '動作管理', 'Action management', now(), now(), 1);
INSERT INTO sirm_i18n(id, i18n_key, zh_cn, zh_hk, en_us, createtimestamp, updatetimestamp, version) VALUES (1391681808109801475, 'menu.common.authentication', '身份验证配置', '身份驗證配置', 'Authentication configuration', now(), now(), 1);
INSERT INTO sirm_i18n(id, i18n_key, zh_cn, zh_hk, en_us, createtimestamp, updatetimestamp, version) VALUES (1391681808109801476, 'menu.common.framework', '框架基本信息', '框架基本信息', 'Basic frame information', now(), now(), 1);

-- 应用管理的语言包配置
INSERT INTO sirm_i18n(id, i18n_key, zh_cn, zh_hk, en_us, createtimestamp, updatetimestamp, version) VALUES (1584743023923826689, 'menu.application.management', '应用管理', '應用管理', 'Application management', now(), now(), 1);

-- 动作表初始化数据
INSERT INTO `sirm_action` (`id`, `createtimestamp`, `updatetimestamp`, `version`, `name`, `code`, `type`, `handler`, `url`, `bind_vue_flag`) VALUES (1446783234871529474, now(), now(), 1, '发送消息', 'SEND-MESSAGE-ACTION', 1, 'com.sinitek.sirm.common.action.support.impl.SendMessageAction', '', 1);
INSERT INTO `sirm_action` (`id`, `createtimestamp`, `updatetimestamp`, `version`, `name`, `code`, `type`, `handler`, `url`, `bind_vue_flag`) VALUES (1457983832056795138, now(), now(), 1, '添加日程', 'ADD-CALENDAR-ACTION', 1, 'com.sinitek.sirm.common.action.support.impl.AddCalendarAction', '', 1);

-- 拦截表初始化数据
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (1, 'sprt_orgobject', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (2, 'sprt_orgrela', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (3, 'um_userproperty', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (4, 'um_userinfo', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (5, 'org_userextendinfo', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (6, 'sprt_businlogger', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (7, 'sirm_wx_work_config', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (8, 'wf_process', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (9, 'sirm_sendmessage', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (10, 'wf_agents', now(), now(), 1, NULL);
INSERT INTO `tenant_table_config`(`id`, `tenant_table`, `createtimestamp`, `updatetimestamp`, `version`, `description`) VALUES (11, 'sirm_wx_work_account', now(), now(), 1, NULL);

-- 机构的初始化脚本 [如果顶级机构编码要修改,请自行调整初始化脚本]
INSERT INTO tenant_mechanism (id, code, name, simple_name, status, parent_id, createtimestamp, updatetimestamp, version, locale, deleted_flag, domain_suffix, sort, high_val, code_val) VALUES(1, 'root', '携宁科技', '携宁', 1, 0, now(), now(), 1, NULL, '0', '', 1, 0, '1;');

-- 顶级机构OrgOrgobject初始化
update sprt_orgobject s set s.mechanism_code = 'root' where orgid = '99999';

-- 租户表数据进行租户初始化
update um_userinfo set tenant_id = 'root';
update sprt_orgobject set tenant_id = 'root';
update um_userproperty set tenant_id = 'root';
update sprt_orgrela set tenant_id = 'root';
update org_userextendinfo set tenant_id = 'root';

-- ----------------------------
-- Table structure for sprt_rightauth
-- ----------------------------
DELETE FROM `sprt_rightauth`;


-- 更新最大主键
UPDATE metadb_idgenerator
SET currentvalue = ( SELECT ifnull(max( objid ), 0) + 1 FROM sprt_rightauth )
WHERE
        entityname = 'RIGHTAUTH';
