INSERT INTO lc_data_model (id, version, createtimestamp, updatetimestamp, code, name, use_range, model_type, datasource, table_name, delete_mode, embedded_model, config_json, module_code, app_code) VALUES(1817764429540085761, 6, '2024-07-29 11:29:47', '2024-08-02 10:36:31', 'AUDIT_LIBRARY', '审计程序库', 1, 'COMMON', '', 'audit_library', 1, '', '{"maximumDepth":0,"unique":true,"uniquePropNames":["name"],"uniqueValidateMsg":"当前名称已存在"}', 'audit-lib', '');

INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745665, 1, '2024-08-02 10:36:31', '2024-08-02 10:36:31', 'id', 'id', 'KEY', 1, NULL, NULL, NULL, NULL, '主键', NULL, 'AUDIT_LIBRARY', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745666, 1, '2024-08-02 10:36:31', '2024-08-02 10:36:31', 'version', 'version', 'VERSION', 2, NULL, NULL, NULL, NULL, '乐观锁', NULL, 'AUDIT_LIBRARY', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745667, 1, '2024-08-02 10:36:31', '2024-08-02 10:36:31', 'createtimestamp', 'createtimestamp', 'CREATETIMESTAMP', 3, NULL, NULL, NULL, NULL, '新增时间', NULL, 'AUDIT_LIBRARY', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745668, 1, '2024-08-02 10:36:31', '2024-08-02 10:36:31', 'updatetimestamp', 'updatetimestamp', 'UPDATETIMESTAMP', 4, NULL, NULL, NULL, NULL, '更新时间', NULL, 'AUDIT_LIBRARY', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745669, 1, '2024-08-02 10:36:31', '2024-08-02 10:36:31', 'removeFlag', 'remove_flag', 'SYSTEM', 5, NULL, NULL, NULL, NULL, '删除标志', NULL, 'AUDIT_LIBRARY', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745670, 1, '2024-08-02 10:36:32', '2024-08-02 10:36:32', 'name', 'name', 'STRING', 6, NULL, NULL, NULL, NULL, '名称', NULL, 'AUDIT_LIBRARY', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577685745671, 1, '2024-08-02 10:36:32', '2024-08-02 10:36:32', 'note', 'note', 'STRING', 7, NULL, NULL, NULL, NULL, '备注', NULL, 'AUDIT_LIBRARY', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577748660225, 1, '2024-08-02 10:36:32', '2024-08-02 10:36:32', 'status', 'status', 'INTEGER', 8, NULL, NULL, NULL, NULL, '状态', NULL, 'AUDIT_LIBRARY', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577748660226, 1, '2024-08-02 10:36:32', '2024-08-02 10:36:32', 'attachments', '', 'ATTACHMENT', 9, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '相关文件', '{"association":"","customConfig":"{}","attachmentSourceName":"AUDIT_LIBRARY","attachmentSourceIdPropName":"id","attachmentType":0,"formatedName":""}', 'AUDIT_LIBRARY', '', 'FILE', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200577748660227, 1, '2024-08-02 10:36:32', '2024-08-02 10:36:32', 'groups', '', 'MODEL', 10, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '审计程序分组', '{"association":"ONE2MANY","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_LIBRARY', 'AUDIT_GROUP', 'CHILDREN_FORM', NULL, NULL);

INSERT INTO lc_data_model_prop_rela (id, version, createtimestamp, updatetimestamp, parent_name, child_name, sort, `type`, model_code, prop_name, rela_table_name, rela_value, rela_value_type) VALUES(1819200577559916546, 1, '2024-08-02 10:36:31', '2024-08-02 10:36:31', 'id', 'libId', 1, 'RELAKEY', 'AUDIT_LIBRARY', 'groups', NULL, '', 0);