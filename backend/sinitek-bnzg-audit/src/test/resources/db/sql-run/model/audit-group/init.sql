INSERT INTO lc_data_model (id, version, createtimestamp, updatetimestamp, code, name, use_range, model_type, datasource, table_name, delete_mode, embedded_model, config_json, module_code, app_code) VALUES(1817765513985773570, 7, '2024-07-29 11:34:06', '2024-08-02 10:37:23', 'AUDIT_GROUP', '审计程序分组', 1, 'TREE', '', 'audit_group', 1, '', '{"maximumDepth":4,"unique":true,"uniquePropNames":["name","libId"],"uniqueValidateMsg":"分组名称不允许重复"}', 'audit-lib', '');

INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795974103042, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'id', 'id', 'KEY', 1, NULL, NULL, NULL, NULL, '主键', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795982491649, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'version', 'version', 'VERSION', 2, NULL, NULL, NULL, NULL, '乐观锁', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795982491650, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'createtimestamp', 'createtimestamp', 'CREATETIMESTAMP', 3, NULL, NULL, NULL, NULL, '新增时间', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795982491651, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'updatetimestamp', 'updatetimestamp', 'UPDATETIMESTAMP', 4, NULL, NULL, NULL, NULL, '更新时间', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795982491652, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'parentId', 'parent_id', 'SYSTEM', 5, NULL, NULL, NULL, NULL, '父节点ID', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795982491653, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'sort', 'sort', 'SYSTEM', 6, NULL, NULL, NULL, NULL, '节点顺序', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795990880258, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'highVal', 'high_val', 'SYSTEM', 7, NULL, NULL, NULL, NULL, '节点高度', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795990880259, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'codeVal', 'code_val', 'SYSTEM', 8, NULL, NULL, NULL, NULL, '节点编码', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200795990880260, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'libId', 'lib_id', 'FOREIGN_KEY', 9, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '库id', '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_GROUP', 'AUDIT_LIBRARY', 'RELA_FORM', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200796007657473, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'name', 'name', 'TREE_NODE_NAME', 10, NULL, NULL, NULL, NULL, '树节点名称', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200796007657474, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'nameVal', 'name_val', 'SYSTEM', 11, NULL, NULL, NULL, NULL, '名称(冗余)', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200796007657475, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'removeFlag', 'remove_flag', 'SYSTEM', 12, NULL, NULL, NULL, NULL, '删除标志', NULL, 'AUDIT_GROUP', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1819200796016046081, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'procedures', '', 'MODEL', 13, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '审计程序', '{"association":"ONE2MANY","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_GROUP', 'AUDIT_PROCEDURE', 'CHILDREN_FORM', NULL, NULL);

INSERT INTO lc_data_model_prop_rela (id, version, createtimestamp, updatetimestamp, parent_name, child_name, sort, `type`, model_code, prop_name, rela_table_name, rela_value, rela_value_type) VALUES(1819200795869245442, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'libId', 'libId', 1, 'RELAKEY', 'AUDIT_GROUP', 'procedures', NULL, '', 0);
INSERT INTO lc_data_model_prop_rela (id, version, createtimestamp, updatetimestamp, parent_name, child_name, sort, `type`, model_code, prop_name, rela_table_name, rela_value, rela_value_type) VALUES(1819200795869245443, 1, '2024-08-02 10:37:24', '2024-08-02 10:37:24', 'id', 'groupId', 2, 'RELAKEY', 'AUDIT_GROUP', 'procedures', NULL, '', 0);