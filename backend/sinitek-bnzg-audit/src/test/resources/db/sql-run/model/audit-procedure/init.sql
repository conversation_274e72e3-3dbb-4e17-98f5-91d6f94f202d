INSERT INTO lc_data_model (id, version, createtimestamp, updatetimestamp, code, name, use_range, model_type, datasource, table_name, delete_mode, embedded_model, config_json, module_code, app_code) VALUES(1817765699550171138, 7, '2024-07-29 11:34:50', '2024-07-30 15:17:25', 'AUDIT_PROCEDURE', '审计程序', 1, 'COMMON', '', 'audit_procedure', 1, '', '{"maximumDepth":0}', 'audit-lib', '');

INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103276728322, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'id', 'id', 'KEY', 1, NULL, NULL, NULL, NULL, '主键', NULL, 'AUDIT_PROCEDURE', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103276728323, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'version', 'version', 'VERSION', 2, NULL, NULL, NULL, NULL, '乐观锁', NULL, 'AUDIT_PROCEDURE', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103276728324, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'createtimestamp', 'createtimestamp', 'CREATETIMESTAMP', 3, NULL, NULL, NULL, NULL, '新增时间', NULL, 'AUDIT_PROCEDURE', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103285116929, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'updatetimestamp', 'updatetimestamp', 'UPDATETIMESTAMP', 4, NULL, NULL, NULL, NULL, '更新时间', NULL, 'AUDIT_PROCEDURE', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103285116930, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'libId', 'lib_id', 'FOREIGN_KEY', 5, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '程序库id', '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_PROCEDURE', 'AUDIT_LIBRARY', 'RELA_FORM', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103289311233, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'groupId', 'group_id', 'FOREIGN_KEY', 6, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '分组id', '{"association":"ONE2ONE","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_PROCEDURE', 'AUDIT_GROUP', 'RELA_FORM', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103289311234, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'removeFlag', 'remove_flag', 'SYSTEM', 7, NULL, NULL, NULL, NULL, '删除标志', NULL, 'AUDIT_PROCEDURE', NULL, 'TEXT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103297699841, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'name', 'name', 'STRING', 8, NULL, NULL, NULL, NULL, '名称', NULL, 'AUDIT_PROCEDURE', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103297699842, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'regulationRef', 'regulation_ref', 'STRING', 9, NULL, NULL, NULL, NULL, '制度引用', NULL, 'AUDIT_PROCEDURE', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103297699843, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'interviewee', 'interviewee', 'STRING', 10, NULL, NULL, NULL, NULL, '访谈对象', NULL, 'AUDIT_PROCEDURE', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103297699844, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'checkMaterial', 'check_material', 'STRING', 11, NULL, NULL, NULL, NULL, '检查材料', NULL, 'AUDIT_PROCEDURE', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103297699845, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'auditStep', 'audit_step', 'STRING', 12, NULL, NULL, NULL, NULL, '审计步骤', NULL, 'AUDIT_PROCEDURE', NULL, 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103297699846, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'removeOrgId', 'remove_org_id', 'CUSTOM', 13, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '删除人orgId', '{"association":"","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_PROCEDURE', '', 'INPUT', NULL, NULL);
INSERT INTO lc_data_model_prop (id, version, createtimestamp, updatetimestamp, name, col_name, `type`, sort, enum_catalog, enum_type, format, validate_json, comments, config_json, model_code, rela_model_code, component_type, rela_flag, rela_table) VALUES(1818184103301894145, 1, '2024-07-30 15:17:25', '2024-07-30 15:17:25', 'removeTime', 'remove_time', 'CUSTOM', 14, '', '', '', '{"required":false,"requiredValidateMsg":"","validateStrLength":false,"strMaxLength":50,"strLengthValidateMsg":"","validateRegexp":false,"regexp":"","regexpValidateMsg":"","validateMaxNum":false,"maxNum":0,"maxNumValidateMsg":"","validateMinNum":false,"minNum":0,"minNumValidateMsg":""}', '删除时间', '{"association":"","customConfig":"{}","attachmentSourceName":"","attachmentSourceIdPropName":"","attachmentType":0,"formatedName":""}', 'AUDIT_PROCEDURE', '', 'INPUT', NULL, NULL);