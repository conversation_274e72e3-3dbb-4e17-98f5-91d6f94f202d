CREATE TABLE lc_app (
  id bigint NOT NULL COMMENT ' 主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime NOT NULL COMMENT '创建时间',
  updatetimestamp datetime NOT NULL COMMENT '修改时间',
  name varchar(100) NOT NULL COMMENT '应用名称',
  code varchar(50) NOT NULL COMMENT '应用编码',
  service_name varchar(200) NOT NULL COMMENT '服务名',
  context_path varchar(200) DEFAULT NULL COMMENT '请求前缀',
  PRIMARY KEY (id),
  UNIQUE KEY uk_mf_app_code (code),
  UNIQUE KEY uk_mf_app_name (name)
) COMMENT='应用';

CREATE TABLE lc_module (
  id bigint NOT NULL COMMENT '主键',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int DEFAULT NULL COMMENT '乐观锁',
  code varchar(150) DEFAULT NULL COMMENT '模块编码',
  name varchar(150) DEFAULT NULL COMMENT '模块名称',
  description varchar(1000) DEFAULT NULL COMMENT '描述',
  path varchar(150) DEFAULT NULL COMMENT '访问url前缀',
  app_code varchar(50) DEFAULT NULL COMMENT '应用code',
  PRIMARY KEY (id) ,
  KEY i_mf_module01 (code)
) COMMENT='模块';

CREATE TABLE lc_module_menu_rela (
  id bigint NOT NULL COMMENT '主键',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int DEFAULT NULL COMMENT '乐观锁',
  module_code varchar(50) DEFAULT NULL COMMENT '模块code',
  home_code varchar(150) DEFAULT NULL COMMENT '首页code',
  menu_id decimal(22,0) DEFAULT NULL COMMENT '菜单id',
  type tinyint(4) DEFAULT NULL COMMENT '菜单类型 1=PC 2=APP',
  app_code varchar(50) DEFAULT NULL COMMENT '应用code',
  PRIMARY KEY (id) ,
  KEY i_mf_module_menu_rela01 (module_code)
) COMMENT='模块菜单关系';

CREATE TABLE lc_form (
  id bigint NOT NULL COMMENT '主键',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int DEFAULT NULL COMMENT '乐观锁',
  name varchar(150) DEFAULT NULL COMMENT '表单名称',
  code varchar(150) DEFAULT NULL COMMENT '表单编码',
  type tinyint(4) DEFAULT NULL COMMENT '表单类型',
  description varchar(1000) DEFAULT NULL COMMENT '描述',
  page_data longtext COMMENT '表单配置数据',
  publish_status tinyint(4) DEFAULT NULL COMMENT '发布状态',
  publish_date datetime DEFAULT NULL COMMENT '发布日期',
  publish_version int DEFAULT NULL COMMENT '发布版本',
  thread_id bigint DEFAULT NULL COMMENT '线索id',
  thread_latest_flag tinyint(4) DEFAULT NULL COMMENT '标识最新生成的记录',
  latest_flag tinyint(4) DEFAULT NULL COMMENT '标识最新有效的记录',
  module_code varchar(50) DEFAULT NULL COMMENT '模块code',
  app_code varchar(50) DEFAULT NULL COMMENT '应用code',
  PRIMARY KEY (id) ,
  KEY i_mf_form01 (code)
) COMMENT='表单';

CREATE TABLE lc_form_design_history (
  id bigint NOT NULL COMMENT ' 主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime NOT NULL COMMENT '创建时间',
  updatetimestamp datetime NOT NULL COMMENT '修改时间',
  form_id bigint NOT NULL COMMENT '表单主键',
  page_data longtext COMMENT '表单配置数据',
  created_org_id varchar(30) COMMENT '创建人orgId',
  sort int(4) COMMENT '顺序',
  PRIMARY KEY (id),
  KEY i_lc_form_design_history01 (form_id)
) COMMENT='表单设计历史';

CREATE TABLE lc_data_model (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '修改时间',
  code varchar(30) DEFAULT NULL COMMENT '模型代码',
  name varchar(100) DEFAULT NULL COMMENT '模型名称',
  use_range tinyint(4) DEFAULT NULL COMMENT '使用范围',
  model_type varchar(10) DEFAULT NULL COMMENT '模型类型',
  datasource varchar(50) DEFAULT NULL COMMENT '数据源(多数据源时使用)',
  table_name varchar(50) DEFAULT NULL COMMENT '物理表名',
  delete_mode tinyint(4) DEFAULT NULL COMMENT '删除方式',
  embedded_model varchar(50) DEFAULT NULL COMMENT '内嵌模型',
  config_json varchar(1000) DEFAULT NULL COMMENT '配置信息',
  module_code varchar(50) DEFAULT NULL COMMENT '模块code',
  app_code varchar(50) DEFAULT NULL COMMENT '应用code',
  PRIMARY KEY (id),
  KEY i_mf_data_model01 (code)
) COMMENT='数据模型表';

CREATE TABLE lc_data_model_prop (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '修改时间',
  name varchar(50) DEFAULT NULL COMMENT '属性名称',
  col_name varchar(50) DEFAULT NULL COMMENT '物理字段名',
  type varchar(20) DEFAULT NULL COMMENT '类型',
  sort int(4) DEFAULT NULL COMMENT '排序',
  enum_catalog varchar(200) DEFAULT NULL COMMENT '枚举的模块名称',
  enum_type varchar(200) DEFAULT NULL COMMENT '枚举类型',
  format varchar(50) DEFAULT NULL COMMENT '时间格式',
  validate_json varchar(1000) DEFAULT NULL COMMENT '属性校验',
  comments varchar(300) DEFAULT NULL COMMENT '备注',
  config_json varchar(1000) DEFAULT NULL COMMENT '配置信息',
  model_code varchar(50) DEFAULT NULL COMMENT '模型code',
  rela_model_code varchar(50) DEFAULT NULL COMMENT '关联模型code',
  component_type varchar(50) DEFAULT NULL COMMENT '组件类型，映射的前端组件类型',
  rela_flag tinyint(4) DEFAULT NULL COMMENT '能否关联',
  rela_table varchar(200) DEFAULT NULL COMMENT '物理关系表，当组件类型是关系表时必填',
  PRIMARY KEY (id)
) COMMENT='数据模型属性表';

CREATE TABLE lc_data_model_prop_rela (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '修改时间',
  parent_name varchar(50) DEFAULT NULL COMMENT '当前模型的属性name',
  child_name varchar(50) DEFAULT NULL COMMENT '关联模型的属性name',
  sort int(4) DEFAULT NULL COMMENT '排序',
  type varchar(20) DEFAULT NULL COMMENT '关联类型',
  model_code varchar(50) DEFAULT NULL COMMENT '模型code',
  prop_name varchar(50) DEFAULT NULL COMMENT '属性字段名',
  rela_table_name varchar(100) DEFAULT NULL COMMENT '关系表表名',
  rela_value varchar(300) DEFAULT NULL COMMENT '关联值',
  rela_value_type int2 DEFAULT NULL COMMENT '关联值类型(0:字符串,1:数字)',
  PRIMARY KEY (id)
) COMMENT='数据模型属性关联表';

CREATE TABLE lc_data_model_field_mapping (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime NOT NULL COMMENT '创建时间',
  updatetimestamp datetime NOT NULL COMMENT '更新时间',
  data_model_code varchar(50) NOT NULL COMMENT '模型CODE',
  business_code varchar(50) NOT NULL COMMENT '业务代码',
  prop_name varchar(50) NOT NULL COMMENT '属性字段名',
  PRIMARY KEY (id) ,
  KEY i_mf_data_model_fm01 (data_model_code,business_code)
) COMMENT='数据模型字段映射表';

CREATE TABLE lc_data_set (
  id bigint NOT NULL COMMENT '主键',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int DEFAULT NULL COMMENT '乐观锁',
  name varchar(150) DEFAULT NULL COMMENT '数据集名称',
  source_type tinyint(4) DEFAULT NULL COMMENT '数据源类型',
  `sql` text DEFAULT NULL COMMENT 'sql',
  url varchar(1000) DEFAULT NULL COMMENT '数据请求url',
  request_type tinyint(4) DEFAULT NULL COMMENT '请求方式',
  param text COMMENT '请求参数',
  conditions text COMMENT 'sql执行条件',
  formats text COMMENT '数据格式化配置',
  code varchar(50) DEFAULT NULL COMMENT '数据集编码',
  order_rule varchar(100) DEFAULT NULL COMMENT '排序条件',
  config text COMMENT '数据集配置',
  app_code varchar(50) DEFAULT NULL COMMENT '应用code',
  PRIMARY KEY (id),
  KEY i_mf_data_set01 (code)
) COMMENT='数据集';

CREATE TABLE lc_url_proxy (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime NOT NULL COMMENT '创建时间',
  updatetimestamp datetime NOT NULL COMMENT '更新时间',
  code varchar(50) NOT NULL COMMENT '编码',
  url varchar(1000) NOT NULL COMMENT '请求地址',
  request_clazz varchar(500) DEFAULT NULL COMMENT '请求实现类',
  request_method tinyint(4) DEFAULT NULL COMMENT '请求方式',
  request_headers varchar(3000) DEFAULT NULL COMMENT '自定义请求头',
  response_clazz varchar(500) DEFAULT NULL COMMENT '返回值自定义处理类',
  response_method text COMMENT '返回值自定义处理函数',
  PRIMARY KEY (id) ,
  KEY i_mf_url_proxy01 (code)
) COMMENT='url请求代理';

CREATE TABLE lc_template (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime NOT NULL COMMENT '创建时间',
  updatetimestamp datetime NOT NULL COMMENT '更新时间',
  name varchar(150) NOT NULL COMMENT '名称',
  description varchar(300) COMMENT '描述',
  source_type tinyint(4) NOT NULL COMMENT '来源类型',
  pc_json longtext COMMENT 'PC页面模板JSON',
  mobile_json longtext COMMENT '移动页面模板JSON',
  created_org_id varchar(30) COMMENT '创建人orgId',
  sort int(4) COMMENT '顺序',
  PRIMARY KEY (id)
) COMMENT='低代码模版';

CREATE TABLE lc_data_audit (
  id bigint NOT NULL COMMENT '主键',
  version int NOT NULL COMMENT '乐观锁',
  createtimestamp datetime NOT NULL COMMENT '创建时间',
  updatetimestamp datetime NOT NULL COMMENT '更新时间',
  source_name varchar(50) DEFAULT NULL COMMENT '关联实体名称(模型code)',
  source_id bigint DEFAULT NULL COMMENT '关联实体id',
  op_orgid varchar(30) DEFAULT NULL COMMENT '操作人',
  op_type int2 DEFAULT NULL COMMENT '操作类型',
  trace_seq varchar(100) DEFAULT NULL COMMENT '追踪序列',
  op_time datetime DEFAULT NULL COMMENT '操作时间',
  old_data text DEFAULT NULL COMMENT '旧数据',
  new_data text DEFAULT NULL COMMENT '新数据',
  remark varchar(300) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (id)
) COMMENT='数据审计';