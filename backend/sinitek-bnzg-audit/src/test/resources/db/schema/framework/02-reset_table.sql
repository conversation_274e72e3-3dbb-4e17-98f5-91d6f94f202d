CREATE TABLE sirm_user_selection_history (
 `id` BIGINT NOT NULL  COMMENT '主键' ,
 `emp_id` VARCHAR(30) NOT NULL COMMENT '用户OrgId' ,
 `scene` VARCHAR(100)   COMMENT '记录场景' ,
 `selection_time` datetime(6)  COMMENT '选择时间,时间精确微妙',
 `selection_emp_id` VARCHAR(30)  COMMENT '选择用户OrgId' ,
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int NOT NULL COMMENT '乐观锁',
  PRIMARY KEY (id)
) COMMENT = '用户选择历史表';

CREATE TABLE sirm_table_view_column (
  id bigint NOT NULL COMMENT '主键',
  view_id bigint NOT NULL COMMENT '所属视图Id',
  name varchar(200) NOT NULL COMMENT '字段名',
  sort varchar(50) COMMENT '排序字段,存储前端计算的排序值',
  show_flag tinyint COMMENT '是否展示',
  parent_id bigint COMMENT '父列的id',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int NOT NULL COMMENT '乐观锁',
  PRIMARY KEY (id)
) COMMENT = '表格视图字段表';

CREATE TABLE sirm_table_view (
  id bigint NOT NULL COMMENT '主键',
  name varchar(100) NOT NULL COMMENT '视图名称',
  color varchar(9) COMMENT '视图颜色16进制',
  code varchar(300) NOT NULL COMMENT '表格的唯一标识',
  show_flag tinyint NOT NULL COMMENT '是否展示, 0=否、1=是',
  user_org_id varchar(60) NOT NULL COMMENT '用户Id,关联sprt_orgobject.orgid',
  sort int COMMENT '排序字段，从小到大',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int NOT NULL COMMENT '乐观锁',
  PRIMARY KEY (id)
) COMMENT = '表格视图表';

CREATE TABLE sirm_table_view_cond_group (
  id bigint NOT NULL COMMENT '主键',
  view_id bigint NOT NULL COMMENT '所属视图Id',
  group_relation tinyint NOT NULL COMMENT '组内关系, 1=全部、2=任何',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int NOT NULL COMMENT '乐观锁',
  PRIMARY KEY (id)
) COMMENT = '表格视图条件组表';

CREATE TABLE sirm_table_view_cond (
  id bigint NOT NULL COMMENT '主键',
  group_id bigint NOT NULL COMMENT '所属组Id',
  field_name varchar(100) NOT NULL COMMENT '字段名称',
  condition_value varchar(100) NOT NULL COMMENT '条件值',
  field_value varchar(1000) NOT NULL COMMENT '字段值',
  field_type varchar(100) COMMENT '字段类型',
  component varchar(900) COMMENT '绑定的组件信息',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int NOT NULL COMMENT '乐观锁',
  PRIMARY KEY (id)
) COMMENT = '表格视图条件表';

CREATE TABLE `sirm_application`
(
    `id`      bigint NOT NULL COMMENT '主键id',
    `app_name`    varchar(180) NOT NULL COMMENT '应用名称,唯一',
    `app_id`    varchar(60) NOT NULL COMMENT 'appId,唯一',
    `app_secret`    varchar(60) NOT NULL COMMENT 'appSecret',
    `status`  tinyint NULL DEFAULT NULL COMMENT '应用状态,1是启用 2是停用',
    `description`   varchar(600) NULL DEFAULT NULL COMMENT '应用描述',
    `createtimestamp` datetime NOT NULL COMMENT '创建时间',
    `updatetimestamp` datetime NOT NULL COMMENT '更新时间',
    `version`         int NOT NULL COMMENT '乐观锁',
    PRIMARY KEY (`id`)
) COMMENT = '应用表';

CREATE TABLE `SIRM_ACTION`  (
   `id` bigint NOT NULL COMMENT '主键',
   `createtimestamp` datetime(0) NULL COMMENT '创建时间',
   `updatetimestamp` datetime(0) NULL COMMENT '更新时间',
   `version` int NULL COMMENT '乐观锁',
   `name` varchar(100) NOT NULL COMMENT '动作名称',
   `code` varchar(100) NOT NULL COMMENT '动作唯一编码',
   `type` int(11) NULL COMMENT '动作类型：1 本地，2 远程',
   `handler` varchar(100) NULL COMMENT '动作实现类名',
   `url` varchar(600) NULL COMMENT '远程接口url，动作类型为远程时需要',
   `bind_vue_flag` tinyint NULL COMMENT '是否关联vue组件',
   PRIMARY KEY (`id`)
) COMMENT = '动作配置表';

CREATE TABLE `SIRM_ACTION_RELA`  (
    `id` bigint NOT NULL COMMENT '主键',
    `createtimestamp` datetime(0) NULL COMMENT '创建时间',
    `updatetimestamp` datetime(0) NULL COMMENT '更新时间',
    `version` int NULL COMMENT '乐观锁',
    `action_id` bigint(22) NOT NULL COMMENT '动作id，关联SIRM_ACTION.id',
    `source_id` varchar(100) NOT NULL COMMENT '绑定对象id',
    `source_name` varchar(30) NOT NULL COMMENT '绑定对象名',
    `component_data` longtext NULL COMMENT '动作绑定表单的json数据',
    `execute_expression` varchar(100) NULL COMMENT '动作执行条件',
    PRIMARY KEY (`id`)
) COMMENT = '动作绑定表';



CREATE TABLE sprt_loggerconfig(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `loggername` VARCHAR(40)    COMMENT '日志记录器名称' ,
    `loglevel` INT    COMMENT '日志级别' ,
    `packagename` VARCHAR(100)    COMMENT '包名' ,
    `loginfo` VARCHAR(200)    COMMENT '日志说明' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '日志记录器配置 ';



CREATE TABLE sirm_idgenerator(
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `currentvalue` INT    COMMENT '首页布局配置' ,
    `step` INT    COMMENT '自增基数'
) COMMENT = '主键记录表 ';


CREATE TABLE sirm_indexqueue(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `completeflag` TINYINT    COMMENT '完成标志 0=未完成;1=正在处理;2=建立索引成功;-1=建立索引失败;-2=废弃' ,
    `optype` TINYINT    COMMENT '操作类型 1=新增2=更新3=删除' ,
    `sourceentity` VARCHAR(30)    COMMENT '分类' ,
    `sourceid` BIGINT    COMMENT '文档id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '索引队列表 ';


CREATE TABLE um_userinfo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `userid` VARCHAR(30) NOT NULL   COMMENT '用户编号' ,
    `username` VARCHAR(50) NOT NULL   COMMENT '登录名' ,
    `password` VARCHAR(64) NOT NULL   COMMENT '密码' ,
    `lockflag` CHAR(5) NOT NULL   COMMENT '是否锁定 1：锁定，０：未锁定' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `lastobjid` DECIMAL(22)    COMMENT '历史objid' ,
    `datasrc` VARCHAR(100)    COMMENT '数据源' ,
    `origid` VARCHAR(30)    COMMENT '数据源objid' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    `expire_time` datetime COMMENT '用户到期时间',
    PRIMARY KEY (objid)
) COMMENT = '用户信息表 ';


CREATE TABLE um_qualifyinfo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `orgid` VARCHAR(30) NOT NULL   COMMENT '关联sprt_orgobject.orgid' ,
    `qualifytype` DECIMAL(22) NOT NULL   COMMENT '从业资格类型' ,
    `qualifyno` VARCHAR(50) NOT NULL   COMMENT '从业资格编号' ,
    `endtime` DATETIME    COMMENT '证书有效截止时间' ,
    `failuredate` DATETIME    COMMENT '证书过期时间' ,
    `issuingunit` VARCHAR(100)    COMMENT '证书颁发机构' ,
    `issuingdate` DATETIME    COMMENT '资格获取时间' ,
    `brief` VARCHAR(1000)    COMMENT '备注' ,
    `lev` DECIMAL(22)    COMMENT '资格评级' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `datasrc` VARCHAR(100)    COMMENT '数据源' ,
    `origid` VARCHAR(100)    COMMENT '数据源objid' ,
    PRIMARY KEY (objid)
) COMMENT = '从业资格表 ';


CREATE TABLE um_usersecurityquestion(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `userid` VARCHAR(30) NOT NULL   COMMENT '关联um_userinfo.userid' ,
    `question` VARCHAR(50) NOT NULL   COMMENT '问题' ,
    `answer` VARCHAR(50) NOT NULL   COMMENT '答案' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '用户安全登陆问答表 ';


CREATE TABLE um_userkeepsignedin(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `userid` VARCHAR(30) NOT NULL   COMMENT '关联um_userinfo.userid' ,
    `expirydate` DATETIME    COMMENT '过期日期' ,
    `sessionid` VARCHAR(32) NOT NULL   COMMENT '会话id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '用户保持登陆状态表 ';


CREATE TABLE um_userproperty(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `userid` VARCHAR(30) NOT NULL   COMMENT '关联um_userinfo.userid' ,
    `name` VARCHAR(20) NOT NULL   COMMENT '属性列名，包含:inservice、displayname、orgid、email、resetpswflag、sex' ,
    `value` VARCHAR(200)    COMMENT '属性列的值' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '用户属性表 ';


CREATE TABLE um_userrole(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `userid` VARCHAR(30) NOT NULL   COMMENT '关联um_userinfo.userid' ,
    `role` VARCHAR(12) NOT NULL   COMMENT '角色' ,
    `grantor` VARCHAR(12)    COMMENT '授权人' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '用户角色表 ';


CREATE TABLE um_userscheme(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `code` VARCHAR(150)    COMMENT '方案编号' ,
    `name` VARCHAR(300)    COMMENT '方案名称' ,
    `title` VARCHAR(500)    COMMENT '方案说明' ,
    `type` INT    COMMENT '方案类型：1 默认，2 本地接口，4 远程接口' ,
    `achieve` VARCHAR(300)    COMMENT '接口地址' ,
    `range` DECIMAL(22)    COMMENT '用户范围，1:限定范围，0:不限定范围' ,
    `status` DECIMAL(22)    COMMENT '用户状态，0:离职，1:在职，2:全部' ,
    `departmentids` VARCHAR(1000)    COMMENT '部门编号集合' ,
    `postids` VARCHAR(1000)    COMMENT '岗位编号集合' ,
    `roleids` VARCHAR(1000)    COMMENT '角色编号集合' ,
    `teamids` VARCHAR(1000)    COMMENT '小组编号集合' ,
    `empids` VARCHAR(1000)    COMMENT '用户编号集合' ,
    `last_use_time` datetime COMMENT '选人方案最后被使用的时间' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '选人方案表 ';


CREATE TABLE um_userschemerela(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `path` VARCHAR(500)    COMMENT '页面路径' ,
    `schemeid` DECIMAL(22)    COMMENT '方案编号，关联um_userscheme.objid' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '选人控件方案关联表 ';


CREATE TABLE openapi_personal_token(
    `id` BIGINT    COMMENT '主键' ,
    `name` VARCHAR(100) NOT NULL   COMMENT '名称' ,
    `token` VARCHAR(100) NOT NULL   COMMENT '个人令牌' ,
    `org_id` VARCHAR(30) NOT NULL   COMMENT '用户Id' ,
    `expire_date` DATETIME    COMMENT '过期时间' ,
    `version` INT    COMMENT '乐观锁' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间'
) COMMENT = 'OpenApi之个人令牌表 ';


CREATE TABLE wf_agents(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `endtime` DATETIME    COMMENT '结束时间' ,
    `typesource` VARCHAR(30)    COMMENT '代理类型' ,
    `typesourceid` DECIMAL(22)    COMMENT '类型id' ,
    `typeads` VARCHAR(200)    COMMENT '类型补充' ,
    `ownerid` VARCHAR(22)    COMMENT '被代理人id' ,
    `agentsid` VARCHAR(22)    COMMENT '代理人id' ,
    `status` DECIMAL(22)    COMMENT '1：启用;2：停用;100：删除' ,
    `starttime` DATETIME    COMMENT '开始时间' ,
    `processtype` INT    COMMENT '流程种类' ,
    `processid` DECIMAL(22)    COMMENT '流程id' ,
    `processstepid` DECIMAL(22)    COMMENT '步骤id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '代理人信息表 ';


CREATE TABLE wf_approvalphrase(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `ownerid` VARCHAR(30)    COMMENT '流程常用语所属人Id' ,
    `phraseinfo` VARCHAR(300)    COMMENT '流程常用语内容' ,
    `status` DECIMAL(22)    COMMENT '流程常用语状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '流程常用语信息表 ';


CREATE TABLE wf_entry_extend(
    `id` BIGINT NOT NULL   COMMENT '主键' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT    COMMENT '乐观锁' ,
    `entity_name` VARCHAR(30)    COMMENT '实体名称' ,
    `sys_code` VARCHAR(30)    COMMENT '流程syscode' ,
    `source_name` VARCHAR(30)    COMMENT '关联实体名' ,
    `source_id` BIGINT    COMMENT '关联实体id' ,
    `type` INT    COMMENT '类型，1：发起组件草稿信息' ,
    `value` LONGTEXT    COMMENT '数据' ,
    PRIMARY KEY (id)
) COMMENT = '实体额外流程信息表 ';


CREATE TABLE wf_example(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `status` DECIMAL(22)    COMMENT '流程实例状态 0 : 发起;1 : 执行中;3 : 终止;4 : 撤回;5 : 异常;9 : 完成' ,
    `endtime` DATETIME    COMMENT '流程实例结束时间' ,
    `starterads` VARCHAR(200)    COMMENT '发起人补充' ,
    `brief` VARCHAR(200)    COMMENT '流程实例描述' ,
    `starterid` VARCHAR(22)    COMMENT '流程发起人id' ,
    `starttime` DATETIME    COMMENT '流程开始时间' ,
    `processid` DECIMAL(22)    COMMENT '流程模板id' ,
    `processname` VARCHAR(80)    COMMENT '流程模板名称' ,
    `datafrom` VARCHAR(30)    COMMENT '数据来源' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例实体 ';


CREATE TABLE wf_processstep(
    `objid` bigint NOT NULL   COMMENT '主键' ,
    `steptypeads` VARCHAR(200)    COMMENT '步骤类型补充' ,
    `pointtypeid` INT    COMMENT '节点类型' ,
    `status` INT    COMMENT '步骤状态' ,
    `processid` DECIMAL(22)    COMMENT '流程模板id' ,
    `name` VARCHAR(100)    COMMENT '步骤名称' ,
    `steptypeid` INT    COMMENT '步骤类型id' ,
    `condition` VARCHAR(400)    COMMENT '步骤结束条件' ,
    `actionurl` VARCHAR(200)    COMMENT '处理url' ,
    `showurl` VARCHAR(200)    COMMENT '查看url' ,
    `sort` INT    COMMENT '排序' ,
    `urlmark` DECIMAL(22)    COMMENT 'url标记' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `stepcode` DECIMAL(22)    COMMENT '流程步骤标识' ,
    `agenttype` DECIMAL(22)    COMMENT '代理类型，1允许代理， 0禁止代理' ,
    `phoneshow` DECIMAL(22)    COMMENT '是否手机展示，1允许手机展示， 0禁止手机展示' ,
    `stepspecial` VARCHAR(100)    COMMENT '特殊标记' ,
    `actionurltype` INT    COMMENT '处理url类型' ,
    `urltype` INT    COMMENT 'Url类型' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    `revoketype` INT    COMMENT '是否允许撤回，1允许撤回，2禁止撤回' ,
    `resolution` VARCHAR(200)    COMMENT '出具意见' ,
    `stepbrief` VARCHAR(500)    COMMENT '步骤描述' ,
    `specifyownertype` INT    COMMENT '指定处理人类型' ,
    `ownerspecifylabeltext` VARCHAR(255)    COMMENT '指添加处理人字段显示的文字' ,
    `orgcode` VARCHAR(100)    COMMENT '选人方案代码' ,
    `associatedworkflowshow` INT    COMMENT '关联流程 0：否，1：是' ,
    `opinionshow` INT    COMMENT '意见展示' ,
    `askforshow` INT    COMMENT '征求信息展示' ,
    `jumpdealershow` INT    COMMENT '被跳过处理人展示' ,
    `unprocessedshow` INT    COMMENT '未处理节点展示' ,
    `specifyownerrange` INT    COMMENT '指定处理人范围' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板步骤实体 ';

CREATE TABLE wf_processstepdo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `taskid` BIGINT    COMMENT '自定义执行方法id' ,
    `type` INT    COMMENT '执行种类' ,
    `processid` BIGINT    COMMENT '流程模板id' ,
    `processstepid` BIGINT    COMMENT '流程模板步骤id' ,
    `status` INT    COMMENT '状态' ,
    `sort` INT    COMMENT '排序' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板步骤执行实体 ';


CREATE TABLE wf_processstephistory(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `prestepid` DECIMAL(22)    COMMENT '老流程步骤id' ,
    `preprocessid` DECIMAL(22)    COMMENT '老流程id' ,
    `newprocessid` DECIMAL(22)    COMMENT '新流程id' ,
    `newstepid` DECIMAL(22)    COMMENT '新流程步骤id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程步骤历史实体 ';


CREATE TABLE wf_processurl(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `name` VARCHAR(150)    COMMENT '名称' ,
    `actionurl` VARCHAR(200)    COMMENT '处理url' ,
    `showurl` VARCHAR(200)    COMMENT '查看url' ,
    `type` INT    COMMENT '类型' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `viewurl` VARCHAR(200)    COMMENT '查看链接' ,
    `urltype` INT    COMMENT '处理url类型' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板扩展功能实体 ';


CREATE TABLE wf_processsteplinkif(
    `objid` bigint NOT NULL   COMMENT '主键' ,
    `linkid` DECIMAL(22)    COMMENT '链接id' ,
    `processid` DECIMAL(22)    COMMENT '流程id' ,
    `iftype` DECIMAL(22)    COMMENT '条件种类 2：审批状态; >100000：自定义条件' ,
    `stepid` DECIMAL(22)    COMMENT '步骤id' ,
    `ifand` DECIMAL(22)    COMMENT '条件关系 = : 1;!= : 2;> : 3;>= : 4;< : 5;<= : 6' ,
    `ifads` VARCHAR(200)    COMMENT '条件补充 当IfType=2时：1：通过;2：驳回;-1：其他.当自定义条件时：0：为真;1：为假' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板步骤链接条件实体 ';


CREATE TABLE wf_processsteplinkdo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `linkid` DECIMAL(22)    COMMENT '链接id' ,
    `processid` DECIMAL(22)    COMMENT '流程模板id' ,
    `dotype` DECIMAL(22)    COMMENT '执行种类' ,
    `doads` VARCHAR(200)    COMMENT '执行补充' ,
    `domark` DECIMAL(22)    COMMENT '执行记号' ,
    `stepid` DECIMAL(22)    COMMENT '步骤id' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板步骤链接执行实体 ';


CREATE TABLE wf_processsteplink(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `aftstepid` DECIMAL(22)    COMMENT '后步骤' ,
    `processid` DECIMAL(22)    COMMENT '流程id' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `prestepid` DECIMAL(22)    COMMENT '前步骤' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `pathname` VARCHAR(100)    COMMENT '路径名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板步骤链接实体 ';


CREATE TABLE wf_processruleinfo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `info` VARCHAR(600)    COMMENT '规则信息' ,
    `ruletype` INT    COMMENT '规则种类 1:任务通过默认意见' ,
    `status` INT    COMMENT '规则状态' ,
    `brief` VARCHAR(200)    COMMENT '规则备注' ,
    `rulename` VARCHAR(60)    COMMENT '规则名称' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板规则信息实体 ';


CREATE TABLE wf_processrule(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `targettype` VARCHAR(30)    COMMENT '目标类型 ProcessType: 流程类型;ProcessMain: 流程模板;ProcessStep: 流程步骤' ,
    `targetid` BIGINT    COMMENT '目标id' ,
    `targetadd` VARCHAR(200)    COMMENT '目标补充' ,
    `ruleid` BIGINT    COMMENT '规则信息id' ,
    `status` INT    COMMENT '状态' ,
    `brief` VARCHAR(200)    COMMENT '处理备注' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程规则实体 ';


CREATE TABLE wf_processpara(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `typeid` DECIMAL(22)    COMMENT '参数分类' ,
    `codename` VARCHAR(30)    COMMENT '参数代号' ,
    `showname` VARCHAR(30)    COMMENT '参数名称' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `kind` DECIMAL(22)    COMMENT '参数种类' ,
    `basevalue` VARCHAR(200)    COMMENT '默认值' ,
    `columnid` DECIMAL(22)    COMMENT '参数大类  0为公用' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板参数实体 ';


CREATE TABLE wf_processownerlink(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `ownerstarter` VARCHAR(500)    COMMENT '条件范围' ,
    `ownerender` VARCHAR(500)    COMMENT '目标范围' ,
    `status` INT    COMMENT '状态' ,
    `sort` INT    COMMENT '排序' ,
    `linkroot` BIGINT    COMMENT '根类型  1：等待处理;2：系统跳过' ,
    `linkleaf` INT    COMMENT '叶类型 1：流程发起人;2：上步提交人' ,
    `stepid` BIGINT    COMMENT '步骤id' ,
    `processid` BIGINT    COMMENT '流程id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板处理人关联实体 ';


CREATE TABLE wf_processlist(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `value` VARCHAR(200)    COMMENT '枚举值' ,
    `status` DECIMAL(22)    COMMENT '枚举值' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `name` VARCHAR(30)    COMMENT '枚举名称' ,
    `key` DECIMAL(22)    COMMENT '枚举键' ,
    `call_type` INT  COMMENT '调用方式 1：远程调用，0：本地调用 默认为0',
    `valueads` VARCHAR(300)    COMMENT '枚举值补充' ,
    `type` INT    COMMENT '枚举类型 0为属于全模块' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板枚举实体 ';

CREATE TABLE wf_processowner(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `value` DECIMAL(22)    COMMENT '单位值' ,
    `ownergoto` DECIMAL(22)    COMMENT '类型指向， 0为默认处理人，-1为候选人, -3为抄送人' ,
    `ownergotoid` DECIMAL(22)    COMMENT '指向的id' ,
    `processid` DECIMAL(22)    COMMENT '所属流程id' ,
    `stepid` DECIMAL(22)    COMMENT '所属步骤id' ,
    `orgid` VARCHAR(20)    COMMENT '处理人id' ,
    `orgtype` DECIMAL(22)    COMMENT '处理人类型 1=部门;2=岗位;4=小组;8=员工' ,
    `sort` INT    COMMENT '排序' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板步骤处理人实体 ';


CREATE TABLE wf_processextend(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `key` VARCHAR(100)    COMMENT '键' ,
    `value` VARCHAR(1000)    COMMENT '值' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `examplestepid` DECIMAL(22)    COMMENT '流程实例步骤id' ,
    `exampleid` DECIMAL(22)    COMMENT '流程实例id' ,
    `processstepid` DECIMAL(22)    COMMENT '流程模板步骤id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板扩展功能实体 ';


CREATE TABLE wf_process(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `syscode` VARCHAR(30)    COMMENT '流程模板标识' ,
    `sysversion` DECIMAL(22)    COMMENT '流程模板版本' ,
    `processtype` INT    COMMENT '流程类型' ,
    `processbrief` VARCHAR(300)    COMMENT '流程描述' ,
    `status` INT    COMMENT '状态 1 : 未发布;2 : 已发布;3 : 过去发布;4 : 失效发布;5 : 废弃;100 : 已删除' ,
    `name` VARCHAR(150)    COMMENT '名称' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `processversion` DECIMAL(22)    COMMENT '流程版本' ,
    `processcode` VARCHAR(50)    COMMENT '流程Code' ,
    `specialmark` VARCHAR(200)    COMMENT '特殊模式标注 第一位标注：发起人和处理人是同一个人时的操作，1为自动通过，0或没有为无操作第二位标注：暂无' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `claimtype` DECIMAL(22)    COMMENT '应用类型' ,
    `phoneshow` DECIMAL(22)    COMMENT '手机展示 1：允许手机展示;0：禁止手机展示' ,
    `launch_show_flag` INT    COMMENT '是否展示选人控件，0不展示，1展示' ,
    `releasedate` DATETIME    COMMENT '发布日期' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程模板实体 ';


CREATE TABLE wf_flowprops(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sourcename` VARCHAR(100)    COMMENT '关联实体名' ,
    `sourceid` DECIMAL(22)    COMMENT '关联实体id' ,
    `name` VARCHAR(100)    COMMENT '参数名称' ,
    `value` VARCHAR(300)    COMMENT '参数值' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `key` VARCHAR(100)    COMMENT '参数键' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程参数 ';



CREATE TABLE wf_flowpaths(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `type` VARCHAR(30)    COMMENT '路径类型' ,
    `toid` DECIMAL(22)    COMMENT '后节点id' ,
    `fromid` DECIMAL(22)    COMMENT '前节点id' ,
    `processid` DECIMAL(22)    COMMENT '流程id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程节点路径实体 ';


CREATE TABLE wf_flownode(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `text` VARCHAR(100)    COMMENT '节点名称' ,
    `attr` VARCHAR(300)    COMMENT '节点参数(坐标，高宽)' ,
    `type` VARCHAR(30)    COMMENT '节点类型' ,
    `processid` DECIMAL(22)    COMMENT '流程id' ,
    `process_step_code` BIGINT    COMMENT '流程步骤stepcode' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程节点实体 ';



CREATE TABLE wf_flowdots(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `pathid` DECIMAL(22)    COMMENT '路径id' ,
    `pos` VARCHAR(100)    COMMENT '点的坐标' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `sort` DECIMAL(22)    COMMENT '顺序' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程路径上的点坐标实体 ';


CREATE TABLE wf_exampletask(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sourceentity` VARCHAR(30)    COMMENT '实体名' ,
    `sourceid` BIGINT    COMMENT '实体id' ,
    `dealerid` VARCHAR(32)    COMMENT '处理人id' ,
    `orginerid` VARCHAR(32)    COMMENT '发起人id' ,
    `status` INT    COMMENT '任务状态 0未处理, 8已处理' ,
    `starttime` DATETIME    COMMENT '任务开始时间' ,
    `endtime` DATETIME    COMMENT '任务结束时间' ,
    `description` VARCHAR(200)    COMMENT '任务描述' ,
    `remarks` VARCHAR(100)    COMMENT '流程分类' ,
    `sort` INT    COMMENT '排序' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `recoverable_flag` INT    COMMENT '是否可撤回标识，0不可撤回，1可撤回' ,
    `example_id` bigint comment '流程实例id',
    `example_step_id` bigint comment '流程实例步骤id',
    `process_name` varchar(100) comment '流程名称' ,
    `process_step_name` varchar(100) comment '流程步骤名称' ,
    `process_phone_show` tinyint comment '流程是否允许手机展示。0：不允许，1：允许' ,
    `process_step_phone_show` tinyint comment '流程步骤是否允许手机展示。0：不允许，1：允许' ,
    `example_status` tinyint comment '流程实例状态。0：发起，1：执行中，3：终止，4：撤回，5：异常，9：完成' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例任务（我的事宜）实体 ';

CREATE TABLE wf_examplestepowner(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `examplestepid` DECIMAL(22)    COMMENT '流程实例步骤id' ,
    `exampleid` DECIMAL(22)    COMMENT '流程实例id' ,
    `ownerid` VARCHAR(22)    COMMENT '处理人id' ,
    `preownerid` VARCHAR(22)    COMMENT '被代理人id' ,
    `operator` VARCHAR(30)    COMMENT '操作人' ,
    `status` DECIMAL(22)    COMMENT '处理人状态 0 : 待处理;1 : 正在处理;2 : 自动完成;3 : 已终止;4 : 撤回;5 : 废弃;6 : 被抢占;7 : 被跳过;8 : 已处理;9 : 被完成;10 : 被转移;12 : 已转移;100 : 已代理' ,
    `sort` INT    COMMENT '同步处理使用，排序' ,
    `messageremind` INT    COMMENT '消息提醒' ,
    `approvetime` DATETIME    COMMENT '审批时间' ,
    `approveopinion` VARCHAR(2000)    COMMENT '审批意见' ,
    `approvestatus` DECIMAL(22)    COMMENT '审批状态 0 : 无;1 : 通过;2 : 驳回;3 : 提交' ,
    `approvebrief` VARCHAR(200)    COMMENT '审批描述' ,
    `starttime` DATETIME    COMMENT '开始时间' ,
    `value` DECIMAL(22)    COMMENT '票数' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `resolution` DECIMAL(22)    COMMENT '决议' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例步骤处理人实体 ';

CREATE TABLE wf_examplesteplink(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `prestepid` DECIMAL(22)    COMMENT '前一步骤id' ,
    `aftstepid` DECIMAL(22)    COMMENT '后一步骤id' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `exampleid` DECIMAL(22)    COMMENT '所属流程实例id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例步骤链接实体 ';


CREATE TABLE wf_examplestep(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `endtime` DATETIME    COMMENT '步骤结束时间' ,
    `processstepid` DECIMAL(22)    COMMENT '流程模板步骤id' ,
    `processid` DECIMAL(22)    COMMENT '流程模板id' ,
    `exampleid` DECIMAL(22)    COMMENT '流程实例id' ,
    `sync` INT    COMMENT '是否同步处理' ,
    `status` DECIMAL(22)    COMMENT '状态 -1 : 正在生成;0 : 待处理;1 : 正在处理;2 : 自动完成;3 : 已终止;4 : 撤回;5 : 废弃;7 : 被跳过;8 : 已处理;9 : 被完成;11 : 异常' ,
    `processstepname` VARCHAR(100)    COMMENT '流程模板步骤名称' ,
    `brief` VARCHAR(500)    COMMENT '步骤描述' ,
    `stepbeigin` VARCHAR(300)    COMMENT '步骤开始条件' ,
    `stepend` VARCHAR(500)    COMMENT '步骤结束条件' ,
    `stepcondition` DECIMAL(22)    COMMENT '步骤结论 0 : 无;1 : 通过;2 : 驳回;3 : 提交;4 : 弃权;5 : 跳过' ,
    `steptype` INT    COMMENT '步骤类型 1 : 开始节点;2 : 结束节点;3 : 处理节点;4 : 逻辑节点;5 : 分支节点;6 : 合并节点;7 : 提交节点' ,
    `actionurl` VARCHAR(200)    COMMENT '步骤处理url' ,
    `showurl` VARCHAR(200)    COMMENT '步骤查看url' ,
    `urlmark` DECIMAL(22)    COMMENT 'URL标记' ,
    `starttime` DATETIME    COMMENT '步骤开始时间' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `recoverflag` DECIMAL(22)    COMMENT '撤回标识' ,
    `urltype` INT    COMMENT 'url类型' ,
    `branchstepid` DECIMAL(22)    COMMENT '分支步骤id' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例步骤实体 ';

CREATE TABLE wf_examplepara(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `type` DECIMAL(22)    COMMENT '类型 1：Integer;2：Double;3：Float;4：String;5：Map(暂时保留)' ,
    `name` VARCHAR(30)    COMMENT '参数名称' ,
    `value` VARCHAR(400)    COMMENT '参数值' ,
    `exampleid` DECIMAL(22)    COMMENT '流程实例id' ,
    `examplestepid` DECIMAL(22)    COMMENT '流程实例步骤id' ,
    `exampleownerid` DECIMAL(22)    COMMENT '流程实例处理人id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例参数实体 ';


CREATE TABLE wf_examplelist(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `key` DECIMAL(22)    COMMENT '枚举项键' ,
    `value` VARCHAR(200)    COMMENT '枚举项值' ,
    `status` DECIMAL(22)    COMMENT '状态' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `name` VARCHAR(30)    COMMENT '枚举项名' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程实例枚举实体 ';

CREATE TABLE wf_exampleentry(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `status` DECIMAL(22)    COMMENT '状态 1为正常;100为删除' ,
    `sourcename` VARCHAR(30)    COMMENT '实体名' ,
    `sourceid` DECIMAL(22)    COMMENT '实体id' ,
    `changetime` DATETIME    COMMENT '修改时间' ,
    `exampleid` DECIMAL(22)    COMMENT '所属流程实例id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '流程关联实体信息实体 ';


CREATE TABLE wf_examplecustomowner(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `exampleid` BIGINT    COMMENT '实例id,和wf_example的主键关联' ,
    `processstepid` BIGINT    COMMENT '流程步骤id，和wf_processstep的主键进行关联' ,
    `messageremind` INT    COMMENT '消息提醒 1：邮件，2为系统消息， 3：系统消息，邮件消息都提醒' ,
    `sync` INT    COMMENT '步骤处理人是否按顺序同步处理 0：否 ， 1：是' ,
    `type` INT    COMMENT '类型，1为处理人，2为抄送人' ,
    `orgid` VARCHAR(500)    COMMENT '步骤处理人ORGID' ,
    `orgtype` INT    COMMENT '组织结构类型' ,
    `sort` INT    COMMENT '排序' ,
    `status` INT    COMMENT '0 未生成处理人， 1 已生成处理人' ,
    PRIMARY KEY (objid)
) COMMENT = '流程引擎-发起组件设置的处理人信息实体 ';


CREATE TABLE wf_examplecc(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `examplestepid` BIGINT    COMMENT '步骤实例id' ,
    `starter_id` VARCHAR(30)    COMMENT '抄送发起人orgid' ,
    `ownerid` VARCHAR(33)    COMMENT '抄送人orgid' ,
    `readflag` INT    COMMENT '阅读标识' ,
    `exampleid` BIGINT    COMMENT '流程实例id' ,
    `opinion` VARCHAR(3000)    COMMENT '意见' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '抄送信息表 ';


CREATE TABLE wf_exampleassociated(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `associatedexampleid` BIGINT    COMMENT '关联实例id,和wf_example的主键关联' ,
    `processstepid` BIGINT    COMMENT '当前步骤id，和wf_processstep的主键进行关联' ,
    `exampleid` BIGINT    COMMENT '当前实例id,和wf_examlpe的主键关联' ,
    `custom_example_name` VARCHAR(150) COMMENT '自定义流程名称' ,
    PRIMARY KEY (objid)
) COMMENT = '流程引擎-关联流程信息实体 ';


CREATE TABLE wf_exampleaskforbatch(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `starttime` DATETIME    COMMENT '征求开始时间' ,
    `askforopinion` VARCHAR(1500)    COMMENT '征求备注' ,
    `exampleownerid` DECIMAL(22)    COMMENT '所属处理人id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `askforid` DECIMAL(22)    COMMENT '所属被征求id' ,
    PRIMARY KEY (objid)
) COMMENT = '征求批次实体 ';


CREATE TABLE wf_exampleaskfor(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `askerid` VARCHAR(30)    COMMENT '被征求人id' ,
    `parentid` BIGINT    COMMENT '父id' ,
    `exampleownerid` BIGINT    COMMENT '所属流程处理人id' ,
    `ownerid` VARCHAR(32)    COMMENT '征求人id' ,
    `status` INT    COMMENT '状态 0未处理, 8已处理' ,
    `starttime` DATETIME    COMMENT '征求开始时间' ,
    `approvetime` DATETIME    COMMENT '被征求人提交时间' ,
    `approvestatus` INT    COMMENT '审批状态' ,
    `opinion` VARCHAR(2000)    COMMENT '意见' ,
    `brief` VARCHAR(200)    COMMENT '描述' ,
    `type` TINYINT DEFAULT 0 NOT NULL COMMENT '任务类型：1 征求任务，2 交办任务',
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `batchid` DECIMAL(22)    COMMENT '所属征求批次id' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '被征求人信息实体 ';



CREATE TABLE wf_example_comment(
   `id` BIGINT NOT NULL   COMMENT '主键' ,
   `parent_id` BIGINT  COMMENT '回复的评论id，关联wf_example_comment.id' ,
   `example_id` BIGINT NOT NULL    COMMENT '流程实例id' ,
   `example_step_id` BIGINT NOT NULL    COMMENT '流程实例步骤id' ,
   `content` varchar(1000) NOT NULL    COMMENT '评论内容' ,
   `input_id` varchar(30) NOT NULL    COMMENT '评论人' ,
   `input_time` DATETIME NOT NULL    COMMENT '评论时间' ,
   `createtimestamp` DATETIME    COMMENT '创建时间' ,
   `updatetimestamp` DATETIME    COMMENT '更新时间' ,
   `version` INT NOT NULL   COMMENT '乐观锁' ,
   PRIMARY KEY (id)
) COMMENT = '流程实例评论';

CREATE TABLE qrtz_blob_triggers(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT '触发器名字，主键' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器所属组的名字，主键' ,
    `blob_data` LONGBLOB    COMMENT '触发器blob信息' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
) COMMENT = '以Blob类型存储的触发器 ';


CREATE TABLE qrtz_calendars(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `calendar_name` VARCHAR(200) NOT NULL   COMMENT '日历名字，主键' ,
    `calendar` LONGBLOB NOT NULL   COMMENT '日历blob数据' ,
    PRIMARY KEY (sched_name,calendar_name)
) COMMENT = '存放日历信息，quartz可配置一个日历来指定一个时间范围 ';


CREATE TABLE qrtz_cron_triggers(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT '触发器名字，主键，关联qrtz_blob_triggers.trigger_name' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器所属组的名字，主键，关联qrtz_blob_triggers.trigger_group' ,
    `cron_expression` VARCHAR(120) NOT NULL   COMMENT 'Cron表达式' ,
    `time_zone_id` VARCHAR(80)    COMMENT '时区信息' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
) COMMENT = '存储Cron Trigger，包括Cron表达式和时区信息 ';


CREATE TABLE qrtz_fired_triggers(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `entry_id` VARCHAR(95) NOT NULL   COMMENT '组标识，主键' ,
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT 'qrtz_blob_triggers.trigger_name' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '关联qrtz_blob_triggers.trigger_group' ,
    `instance_name` VARCHAR(200) NOT NULL   COMMENT '当前实例的名称' ,
    `fired_time` BIGINT NOT NULL   COMMENT '当前执行时间' ,
    `sched_time` BIGINT NOT NULL   COMMENT '定时器制定的时间' ,
    `priority` INT NOT NULL   COMMENT '权重' ,
    `state` VARCHAR(16) NOT NULL   COMMENT '状态' ,
    `job_name` VARCHAR(200)    COMMENT '作业名称' ,
    `job_group` VARCHAR(200)    COMMENT '作业组' ,
    `is_nonconcurrent` CHAR(5)    COMMENT '是否并发' ,
    `requests_recovery` CHAR(5)    COMMENT '是否要求唤醒' ,
    PRIMARY KEY (sched_name,entry_id)
) COMMENT = '存放已触发的触发器 ';


CREATE TABLE qrtz_job_details(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `job_name` VARCHAR(200) NOT NULL   COMMENT '作业名字，主键' ,
    `job_group` VARCHAR(200) NOT NULL   COMMENT '作业分组，主键' ,
    `description` VARCHAR(600)    COMMENT '描述信息' ,
    `job_class_name` VARCHAR(250) NOT NULL   COMMENT '作业的执行类' ,
    `is_durable` CHAR(5) NOT NULL   COMMENT '是否持久，把该属性设置为1，quartz会把job持久化到数据库中' ,
    `is_nonconcurrent` CHAR(5) NOT NULL   COMMENT '是否并发执行' ,
    `is_update_data` CHAR(5) NOT NULL   COMMENT '是否更新数据' ,
    `requests_recovery` CHAR(5) NOT NULL   COMMENT '是否要求唤醒' ,
    `job_data` LONGBLOB    COMMENT '持久化job对象' ,
    PRIMARY KEY (sched_name,job_name,job_group)
) COMMENT = 'JOB详细信息 ';


CREATE TABLE qrtz_job_listeners(
    `job_name` VARCHAR(200) NOT NULL   COMMENT '作业名字，主键，关联Qrtz_job_details.job_name' ,
    `job_group` VARCHAR(200) NOT NULL   COMMENT '作业分组，主键，关联Qrtz_job_details.job_group' ,
    `job_listener` VARCHAR(200) NOT NULL   COMMENT '监听器，主键' ,
    PRIMARY KEY (job_name,job_group,job_listener)
) COMMENT = 'JOB监听器 ';


CREATE TABLE qrtz_locks(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `lock_name` VARCHAR(40) NOT NULL   COMMENT '锁名，主键' ,
    PRIMARY KEY (sched_name,lock_name)
) COMMENT = '存储程序的悲观锁的信息 ';


CREATE TABLE qrtz_paused_trigger_grps(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器组名，主键，关联qrtz_blob_triggers.trigger_group' ,
    PRIMARY KEY (sched_name,trigger_group)
) COMMENT = '存放暂停掉的触发器 ';


CREATE TABLE qrtz_scheduler_state(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `instance_name` VARCHAR(200) NOT NULL   COMMENT '实例名字，主键，关联qrtz_blob_triggers.trigger_group' ,
    `last_checkin_time` BIGINT NOT NULL   COMMENT '最后的检查时间' ,
    `checkin_interval` BIGINT NOT NULL   COMMENT '检查间隔' ,
    PRIMARY KEY (sched_name,instance_name)
) COMMENT = '调度器状态 ';


CREATE TABLE qrtz_simple_triggers(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT '触发器名字，主键，关联qrtz_blob_triggers.trigger_name' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器组名，主键，关联qrtz_blob_triggers.trigger_group' ,
    `repeat_count` BIGINT NOT NULL   COMMENT '重复次数' ,
    `repeat_interval` BIGINT NOT NULL   COMMENT '重复间隔' ,
    `times_triggered` BIGINT NOT NULL   COMMENT '触发次数' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
) COMMENT = '简单的触发器 ';


CREATE TABLE qrtz_simprop_triggers(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT '触发器名字，主键，关联qrtz_blob_triggers.trigger_name' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器组名，主键，关联qrtz_blob_triggers.trigger_group' ,
    `str_prop_1` VARCHAR(512)    COMMENT 'String类型的trigger的第一个参数' ,
    `str_prop_2` VARCHAR(512)    COMMENT 'String类型的trigger的第二个参数' ,
    `str_prop_3` VARCHAR(512)    COMMENT 'String类型的trigger的第三个参数' ,
    `int_prop_1` INT    COMMENT 'int类型的trigger的第一个参数' ,
    `int_prop_2` INT    COMMENT 'int类型的trigger的第二个参数' ,
    `long_prop_1` BIGINT    COMMENT 'long类型的trigger的第一个参数' ,
    `long_prop_2` BIGINT    COMMENT 'long类型的trigger的第二个参数' ,
    `dec_prop_1` DECIMAL(13,4)    COMMENT 'decimal类型的trigger的第一个参数' ,
    `dec_prop_2` DECIMAL(13,4)    COMMENT 'decimal类型的trigger的第二个参数' ,
    `bool_prop_1` VARCHAR(1)    COMMENT 'Boolean类型的trigger的第一个参数' ,
    `bool_prop_2` VARCHAR(1)    COMMENT 'Boolean类型的trigger的第二个参数' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
) COMMENT = '存储CalendarIntervalTrigger和DailyTimeIntervalTrigger ';


CREATE TABLE qrtz_triggers(
    `sched_name` VARCHAR(120) NOT NULL   COMMENT '调度名称' ,
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT '触发器名字，主键，关联qrtz_blob_triggers.trigger_name' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器组名，主键，关联qrtz_blob_triggers.trigger_group' ,
    `job_name` VARCHAR(300) NOT NULL   COMMENT '作业名称，与QRTZ_JOB_DETAILS的job_name关联' ,
    `job_group` VARCHAR(200) NOT NULL   COMMENT '作业组，与QRTZ_JOB_DETAILS的job_group关联' ,
    `description` VARCHAR(600)    COMMENT '描述' ,
    `next_fire_time` BIGINT    COMMENT '下次执行时间' ,
    `prev_fire_time` BIGINT    COMMENT '前一次执行时间' ,
    `priority` INT    COMMENT '优先权' ,
    `trigger_state` VARCHAR(16) NOT NULL   COMMENT '触发器状态' ,
    `trigger_type` VARCHAR(8) NOT NULL   COMMENT '触发器类型' ,
    `start_time` BIGINT NOT NULL   COMMENT '开始时间' ,
    `end_time` BIGINT    COMMENT '结束时间' ,
    `calendar_name` VARCHAR(200)    COMMENT '日历名称' ,
    `misfire_instr` SMALLINT    COMMENT '失败次数' ,
    `job_data` LONGBLOB    COMMENT '作业数据' ,
    PRIMARY KEY (sched_name,trigger_name,trigger_group)
) COMMENT = '触发器 ';


CREATE TABLE qrtz_trigger_listeners(
    `trigger_name` VARCHAR(200) NOT NULL   COMMENT '触发器名字,主键，关联qrtz_blob_triggers.trigger_name' ,
    `trigger_group` VARCHAR(200) NOT NULL   COMMENT '触发器组名，主键，关联qrtz_blob_triggers.trigger_group' ,
    `trigger_listener` VARCHAR(200) NOT NULL   COMMENT '触发器监听器，主键' ,
    PRIMARY KEY (trigger_name,trigger_group,trigger_listener)
) COMMENT = '存储已配置的TriggerListener的信息 ';


CREATE TABLE sirm_job_config(
    `name` VARCHAR(100) NOT NULL  COMMENT '调度名称' ,
    `type` tinyint NOT NULL  COMMENT '动作数据来源类型：0表示执行类，1表示数据集' ,
    `action_source` VARCHAR(100) NOT NULL COMMENT '动作数据来源信息：type=0时表示类名，当type=1时表示数据集对应的code值',
    PRIMARY KEY (name)
) COMMENT = '调度动作信息表';


CREATE TABLE sirm_jobquartztime(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `timeexpression` VARCHAR(100)    COMMENT '时间表达式' ,
    `timename` VARCHAR(200)    COMMENT '时间名称' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '调度时间配置表 ';


CREATE TABLE sirm_jobquartzgroup(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `groupname` VARCHAR(200)    COMMENT '调度分组' ,
    `memo` VARCHAR(500)    COMMENT '说明' ,
    `code` VARCHAR(50)    COMMENT '分组的code' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '调度分组 ';


CREATE TABLE sirm_jobquartzdetails(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `jobid` VARCHAR(50)    COMMENT '工作编号' ,
    `quartzid` VARCHAR(100)    COMMENT '调度配置编号' ,
    `starttime` DATETIME    COMMENT '执行开始时间' ,
    `endtime` DATETIME    COMMENT '执行结束时间' ,
    `execstatus` DECIMAL(22)    COMMENT '执行状态' ,
    `resultstatus` DECIMAL(22)    COMMENT '执行结果' ,
    `auto` DECIMAL(22)    COMMENT '是否自动' ,
    `memo` LONGTEXT    COMMENT '说明' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '工作调度执行详情 ';


CREATE TABLE sirm_jobexecutelog(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(100)    COMMENT '调度名称' ,
    `status` INT    COMMENT '执行情况 1: 正在处理;2: 处理结束;3: 处理异常;4: 处理中断;' ,
    `empid` DECIMAL(22)    COMMENT '执行人' ,
    `starttime` DATETIME    COMMENT '执行开始时间' ,
    `endtime` DATETIME    COMMENT '执行结束时间' ,
    `type` INT    COMMENT '执行类型 1:自动执行, 2:手动执行' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `brief` VARCHAR(4000)    COMMENT '描述' ,
    `serverip` VARCHAR(100)    COMMENT '服务器ip' ,
    PRIMARY KEY (objid)
) COMMENT = '定时任务执行表 ';


CREATE TABLE metadb_entity(
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `catalogkey` VARCHAR(24) NOT NULL   COMMENT '实体对应目录' ,
    `entityinfo` VARCHAR(200) NOT NULL   COMMENT '实体描述信息' ,
    `entitytable` VARCHAR(100)    COMMENT '实体表名' ,
    `idgenerator` VARCHAR(100)    COMMENT '主键生成器' ,
    `historyflag` CHAR(5)    COMMENT '支持历史数据备份' ,
    `removeflag` CHAR(5)    COMMENT '支持逻辑删除' ,
    `idcacheflag` CHAR(5) NOT NULL   COMMENT '支持主键缓存' ,
    `idcachesize` INT    COMMENT '缓存大小' ,
    `interfacename` VARCHAR(100)    COMMENT '接口类' ,
    `classname` VARCHAR(100)    COMMENT '实现类' ,
    `enableflag` CHAR(5)    COMMENT '启用标识' ,
    `inheritanceentity` VARCHAR(30)    COMMENT '继承实体名' ,
    `inheritancetype` VARCHAR(30)    COMMENT '继承方式'
) COMMENT = 'METADB实体表 ';


CREATE TABLE metadb_entitycatalog(
    `catalogkey` VARCHAR(24) NOT NULL   COMMENT '实体目录key' ,
    `catalogname` VARCHAR(48) NOT NULL   COMMENT '实体目录名称' ,
    `cataloginfo` VARCHAR(200) NOT NULL   COMMENT '实体目录信息' ,
    `tablenameprefix` VARCHAR(10)    COMMENT '表名前缀'
) COMMENT = 'METADB实体目录表 ';


CREATE TABLE metadb_property(
    `id` BIGINT NOT NULL   COMMENT '主键' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `propertyname` VARCHAR(30) NOT NULL   COMMENT '属性名称' ,
    `propertydefaultvalue` VARCHAR(100)    COMMENT '属性默认值' ,
    `propertyisnotnull` CHAR(5) NOT NULL   COMMENT '是否不为空' ,
    `propertycolumnname` VARCHAR(30)    COMMENT '属性字段名' ,
    `propertyinfo` VARCHAR(200) NOT NULL   COMMENT '属性说明' ,
    `propertytype` VARCHAR(16) NOT NULL   COMMENT '属性类型' ,
    `relaentityname` VARCHAR(30)    COMMENT '关联实体名称' ,
    `relapropertyname` VARCHAR(30)    COMMENT '关联实体属性' ,
    `enumname` VARCHAR(100)    COMMENT '枚举类型名称' ,
    `propertylength` INT    COMMENT '属性字段' ,
    `propertydbtype` VARCHAR(16)    COMMENT '关联数据库类型' ,
    `propertyisunique` CHAR(5)    COMMENT '具备唯一性' ,
    PRIMARY KEY (id)
) COMMENT = 'METADB实体属性表 ';


CREATE TABLE metadb_idgenerator(
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `currentvalue` INT NOT NULL   COMMENT '当前id值' ,
    `step` INT    COMMENT 'id递增值'
) COMMENT = 'METADB主键生成表 ';


CREATE TABLE tenant_table_config(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `tenant_table` VARCHAR(60)    COMMENT '拦截表的名称' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `description` VARCHAR(255)    COMMENT '描述' ,
    PRIMARY KEY (id)
) COMMENT = '多租户拦截表 ';


CREATE TABLE tenant_mechanism(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `code` VARCHAR(60)    COMMENT '机构编码' ,
    `name` VARCHAR(100)    COMMENT '机构名称' ,
    `simple_name` VARCHAR(100)    COMMENT '机构简称' ,
    `status` DECIMAL(22)    COMMENT '机构状态,0是禁用,1是启用' ,
    `parent_id` DECIMAL(22)    COMMENT '机构父ID,顶级机构的parentId为0,其他机构在顶级机构下面' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `locale` VARCHAR(30)    COMMENT '机构默认的语言环境' ,
    `deleted_flag` CHAR(5)    COMMENT '逻辑删除标识, 删除为1, 未删除为0' ,
    `domain_suffix` VARCHAR(120)    COMMENT '域名' ,
    `sort` DECIMAL(11) NOT NULL   COMMENT '树状模型的节点顺序' ,
    `high_val` DECIMAL(11) NOT NULL   COMMENT '树状模型的节点高度' ,
    `code_val` VARCHAR(1000) NOT NULL   COMMENT '树状模型的节点编码' ,
    `expire_time` DATETIME    COMMENT '机构过期时间' ,
    PRIMARY KEY (id)
) COMMENT = '机构表 ';


CREATE TABLE tenant_mechanism_role(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(60)    COMMENT '默认角色名称' ,
    `default_status` INT    COMMENT '0表示默认,1表示不默认,默认状态下添加机构时机构会默认添加该角色' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    PRIMARY KEY (id)
) COMMENT = '机构默认角色表 ';


CREATE TABLE tenant_mechanism_role_auth(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `auth_role_id` DECIMAL(22)    COMMENT '默认角色Id' ,
    `object_key` VARCHAR(200)    COMMENT '对应的权限模块具体权限点Id' ,
    `right_define_key` VARCHAR(100)    COMMENT '权限模块分类KEY' ,
    `right_type` VARCHAR(60)    COMMENT '授权类型，ACCESS：有权限访问，其他：业务上可以自行扩展' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    PRIMARY KEY (id)
) COMMENT = '机构默认角色权限表 ';


CREATE TABLE sirm_mobile_icon(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `verification` VARCHAR(200)    COMMENT '菜单权限校验' ,
    `jumptype` VARCHAR(50)    COMMENT '跳转类型' ,
    `jumppath` VARCHAR(200)    COMMENT '跳转路径' ,
    `filename` VARCHAR(200)    COMMENT '文件名称' ,
    `sort` DECIMAL(10)    COMMENT '排序' ,
    `name` VARCHAR(300)    COMMENT '图标名称' ,
    `description` VARCHAR(500)    COMMENT '描述' ,
    `menuid` VARCHAR(100)    COMMENT '标号' ,
    `type` VARCHAR(50)    COMMENT '图标类型' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '版本号'
) COMMENT = '移动端图标表 ';


CREATE TABLE sirm_mobile_version(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `client_version` VARCHAR(100)    COMMENT '客户端版本号' ,
    `client_type` DECIMAL(10)    COMMENT '客户端类型' ,
    `remark` VARCHAR(1500)    COMMENT '备注' ,
    `latest_version_flag` TINYINT    COMMENT '是否最新版本1=是，0=否' ,
    `low_update_version` VARCHAR(10)    COMMENT '最低更新版本号' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '版本号'
) COMMENT = '移动端版本表 ';


CREATE TABLE org_userextendinfo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `email` VARCHAR(100)    COMMENT '邮箱' ,
    `sex` INT    COMMENT '性别，1:男，2:女' ,
    `tel` VARCHAR(30)    COMMENT '办公电话' ,
    `tel2` VARCHAR(30)    COMMENT '办公电话2' ,
    `job` VARCHAR(100)    COMMENT '职位' ,
    `mobilephone` VARCHAR(30)    COMMENT '手机' ,
    `userid` VARCHAR(60)    COMMENT '用户编号' ,
    `mobilephone2` VARCHAR(30)    COMMENT '手机2' ,
    `familytelephone` VARCHAR(30)    COMMENT '家庭电话1' ,
    `familytelephone2` VARCHAR(30)    COMMENT '家庭电话2' ,
    `otherphone` VARCHAR(30)    COMMENT '其他电话1' ,
    `otherphone2` VARCHAR(30)    COMMENT '其他电话2' ,
    `bp` VARCHAR(30)    COMMENT 'BP机' ,
    `office` VARCHAR(100)    COMMENT '办公室' ,
    `fax` VARCHAR(30)    COMMENT '公司传真1' ,
    `fax2` VARCHAR(30)    COMMENT '公司传真2' ,
    `familyfax` VARCHAR(30)    COMMENT '家庭传真' ,
    `familyfax2` VARCHAR(30)    COMMENT '家庭传真2' ,
    `companyaddress` VARCHAR(300)    COMMENT '公司地址' ,
    `companyzip` VARCHAR(30)    COMMENT '公司邮编' ,
    `familyaddress` VARCHAR(300)    COMMENT '家庭地址' ,
    `familyzip` VARCHAR(30)    COMMENT '家庭邮编' ,
    `otheraddress` VARCHAR(300)    COMMENT '其他地址' ,
    `otherzip` VARCHAR(30)    COMMENT '其他邮编' ,
    `email1` VARCHAR(100)    COMMENT '其他Email1' ,
    `email2` VARCHAR(100)    COMMENT '其他Email2' ,
    `homepage` VARCHAR(100)    COMMENT '主页' ,
    `where1` INT    COMMENT '工作地，1:上海，2:北京，3:深圳' ,
    `qq` VARCHAR(30)    COMMENT 'QQ' ,
    `msn` VARCHAR(30)    COMMENT 'MSN' ,
    `addressbook` VARCHAR(300)    COMMENT '通讯录' ,
    `introduction` VARCHAR(4000)    COMMENT '个人简介' ,
    `passwordupdatetime` VARCHAR(100)    COMMENT '密码修改时间' ,
    `userlocktime` VARCHAR(100)    COMMENT '用户锁定时间' ,
    `postid` VARCHAR(30)    COMMENT '岗位编号' ,
    `qualifyno` VARCHAR(100)    COMMENT '从业资格编号' ,
    `qualifytype` INT    COMMENT '从业资格类型，对应枚举类QualifyType的值' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `namepy` VARCHAR(200)    COMMENT '员工姓名拼音' ,
    `simple_name_py` varchar(200) comment '首字母拼音',
    `lzrq` VARCHAR(100)    COMMENT '离职日期' ,
    `rzrq` VARCHAR(100)    COMMENT '入职日期' ,
    `englishintroduction` VARCHAR(4000)    COMMENT '个人英文简介' ,
    `englishname` VARCHAR(100)    COMMENT '员工英文名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '用户扩展属性表 ';


CREATE TABLE org_authorize(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sourcename` VARCHAR(30)    COMMENT '授权对象实体名称' ,
    `begrantid` VARCHAR(30)    COMMENT '被授权对象Id' ,
    `alowgrant` INT    COMMENT '允许授权' ,
    `grantid` VARCHAR(30)    COMMENT '授权人Id' ,
    `sourceid` VARCHAR(30)    COMMENT '授权对象Id' ,
    `granttime` DATETIME    COMMENT '授权时间' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '部门组织结构授权表 ';


CREATE TABLE org_logonlog(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `logdate` DATETIME    COMMENT '记录时间' ,
    `failedcount` INT    COMMENT '登陆错误次数' ,
    `orgid` VARCHAR(30)    COMMENT '组织机构id,关联sprt_orgobject.orgid' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称'
) COMMENT = '登录密码输入错误次数记录表 ';



CREATE TABLE org_property(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `f1` CHAR(5)    COMMENT '是否是研究小组，1:是，0:否' ,
    `orgid` VARCHAR(30)    COMMENT '组织结构编号，关联sprt_orgobject.orgid' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称'
) COMMENT = '组织结构属性表 ';


CREATE TABLE org_pswhis(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `password` VARCHAR(64)    COMMENT '密码' ,
    `logdate` DATETIME    COMMENT '记录时间' ,
    `orgid` VARCHAR(30)    COMMENT '用户id，关联sprt_orgobject.orgid' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '密码修改历史表 ';


CREATE TABLE org_relation(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `orgid` VARCHAR(30)    COMMENT '岗位编号，关联sprt_orgobject.orgid' ,
    `schemeid` DECIMAL(22)    COMMENT '方案编号，关联org_relationscheme.objid' ,
    `parentid` DECIMAL(22)    COMMENT '父类编号' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '岗位关系表 ';


CREATE TABLE org_relationscheme(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(100)    COMMENT '方案名称' ,
    `brief` VARCHAR(400)    COMMENT '方案备注' ,
    `code` VARCHAR(100)    COMMENT '方案代码' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '岗位方案表 ';


CREATE TABLE org_teamindustryrela(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `teamid` VARCHAR(30)    COMMENT '小组编号' ,
    `industrycode` VARCHAR(50)    COMMENT '行业代码' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '行业小组关联表 ';


CREATE TABLE sprt_orgrela(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `toobjectid` VARCHAR(30) NOT NULL   COMMENT '目标节点ID,关联sprt_orgobject.orgid，一般该字段为角色、小组、岗位、部门、人员' ,
    `relationtype` VARCHAR(60) NOT NULL   COMMENT '关系类型：部门岗位、部门部门=UNDERLINE，岗位员工=SUPERVISION，岗位岗位（行政上级）=SUPEXECUTE，岗位岗位（业务上级）=SUPBUSIN，角色员工=SUPERROLE，小组员工=SUPERTEAM' ,
    `fromobjectid` VARCHAR(30) NOT NULL   COMMENT '关系起点ID：关联sprt_orgobject.ORGID，一般该字段为角色、小组、岗位、部门' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    `sort` DECIMAL(11) NOT NULL   COMMENT '树状模型的节点顺序' ,
    `high_val` DECIMAL(11) NOT NULL   COMMENT '树状模型的节点高度' ,
    `code_val` VARCHAR(1000) NOT NULL   COMMENT '树状模型的节点编码' ,
    PRIMARY KEY (objid)
) COMMENT = '组织结构图关系信息定义 ';


CREATE TABLE sprt_orgobject(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `orgname` VARCHAR(120) NOT NULL   COMMENT '组织结构名称' ,
    `objecttype` DECIMAL(22) NOT NULL   COMMENT '对象类型，员工或组织机构：角色、小组、岗位、部门' ,
    `description` VARCHAR(300)    COMMENT '描述信息' ,
    `unittype` VARCHAR(60)    COMMENT 'Unit类型：员工= NULL，角色=ROLE，小组=TEAM ，岗位=POSITION，部门=UNIT' ,
    `userid` VARCHAR(60)    COMMENT '用户ID，员工=关联um_userinfo.userid，角色、小组、岗位、部门=NULL' ,
    `inservice` CHAR(5)    COMMENT '是否在职：员工在职=1，员工离职=0，角色、小组、岗位、部门=NULL' ,
    `orgid` VARCHAR(30) NOT NULL   COMMENT '组织结构对象ID' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `lastobjid` DECIMAL(22)    COMMENT '历史objid' ,
    `datasrc` VARCHAR(100)    COMMENT '数据源' ,
    `origid` VARCHAR(30)    COMMENT '数据源objid' ,
    `mechanism_code` VARCHAR(60)    COMMENT '对应机构表中的Code, 唯一' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '组织结构对象表 ';


CREATE TABLE sprt_rightauth(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `authorgid` VARCHAR(100) NOT NULL   COMMENT '授权组织结构ID，关联sprt_orgobject.orgid' ,
    `authorgexp` VARCHAR(200)    COMMENT '授权组织结构表达式' ,
    `objectkey` VARCHAR(200)    COMMENT '对应的权限种类：菜单=SPRT_SYSMENU，首页模块权限=SPRT_WEBMODULE' ,
    `rightdefinekey` VARCHAR(100) NOT NULL   COMMENT '权限定义KEY' ,
    `rejectflag` CHAR(5) NOT NULL   COMMENT '是否拒绝，0: 授予，1: 拒绝' ,
    `righttype` VARCHAR(60) NOT NULL   COMMENT '授权类型，ACCESS：有权限访问，其他：业务上可以自行扩展' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '权限授权数据 ';


CREATE TABLE sprt_rightdef(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `rightdefinekey` VARCHAR(200) NOT NULL   COMMENT '权限定义KEY' ,
    `rightdefinename` VARCHAR(500) NOT NULL   COMMENT '权限定义名称' ,
    `filterstring` VARCHAR(200)    COMMENT '过滤条件' ,
    `rightauthtype` DECIMAL(22) NOT NULL   COMMENT '权限授权类型' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '权限定义信息 ';


CREATE TABLE sirm_functiongroup(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `parentid` BIGINT   COMMENT '父类编号' ,
    `sort` INT    COMMENT '顺序' ,
    `brief` VARCHAR(300)    COMMENT '分类说明' ,
    `name` VARCHAR(300) NOT NULL   COMMENT '分类名称' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '功能分类表 ';


CREATE TABLE sirm_functioninfo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `code` VARCHAR(100)    COMMENT '功能编号' ,
    `name` VARCHAR(300) NOT NULL   COMMENT '功能名称' ,
    `sort` INT  COMMENT '排序' ,
    `url` VARCHAR(1000)    COMMENT '菜单地址' ,
    `action` VARCHAR(100)    COMMENT '类名' ,
    `method` VARCHAR(100)    COMMENT '方法名' ,
    `brief` VARCHAR(300)    COMMENT '说明' ,
    `groupid` BIGINT    COMMENT '分组Id' ,
    `type` INT NOT NULL   COMMENT '功能类型' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `menuid` BIGINT   COMMENT '关联菜单的id' ,
    `selector` VARCHAR(100)    COMMENT '选择器属性' ,
    `routeid` BIGINT    COMMENT '路由id' ,
    `menu_route_path` VARCHAR(200)    COMMENT '关联菜单的路由',
    `page_route_path` VARCHAR(200)    COMMENT '所属路由',
    `is_page_access_control` char(5)    COMMENT '控制页面埋点标识',
    PRIMARY KEY (objid)
) COMMENT = '功能表 ';


CREATE TABLE sirm_sendmessagedetail(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sendmessageid` DECIMAL(22)    COMMENT '发送消息Id,关联sirm_sendmessage.objid' ,
    `address` VARCHAR(200)    COMMENT '消息地址,邮箱或手机号' ,
    `status` DECIMAL(22)    COMMENT '消息发送状态,1=待发送 2=发送失败 3=发送成功' ,
    `sendtime` DATETIME    COMMENT '发送完成时间' ,
    `empid` VARCHAR(30)    COMMENT '收件人编号,关联sprt_orgobject.orgid' ,
    `emp_name` VARCHAR(30)    COMMENT '收件人名称' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `receivertype` VARCHAR(30)    COMMENT '收件人类型,0:抄送 1:收件人' ,
    `reason` VARCHAR(300) COMMENT '失败原因',
    PRIMARY KEY (objid)
) COMMENT = '发送消息明细表 ';


CREATE TABLE sirm_messagereceiver(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `templateid` BIGINT    COMMENT '消息模板Id, 关联sirm_messagetemplate.objid' ,
    `type` VARCHAR(100)    COMMENT '提醒对象类型编码' ,
    `value` longtext    COMMENT '提醒对象类型对应的值：选人方案-方案编码，选人控件-组织结构id拼接,自定义提醒对象的value直接放入该值' ,
    `receiver_type` tinyint    COMMENT '接收人类型,0:抄送 1:收件人' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '消息模板接收人表 ';


CREATE TABLE sirm_messagetemplate(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `code` VARCHAR(100)    COMMENT '消息模板代码' ,
    `name` VARCHAR(300)    COMMENT '消息模板名称' ,
    `sendmode` DECIMAL(22)    COMMENT '发送方式' ,
    `title` VARCHAR(600)    COMMENT '消息标题' ,
    `smscontent` VARCHAR(1000)    COMMENT '短信消息内容' ,
    `remindcontent` VARCHAR(4000)    COMMENT '内部消息模板内容' ,
    `forceflag` DECIMAL(22)    COMMENT '配置类型' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `catagory` DECIMAL(22)    COMMENT '模板分类' ,
    `remark` VARCHAR(200)    COMMENT '消息模板说明' ,
    `processtype` INT    COMMENT '流程类型，如果要和工作流关联，设置其流程类型，0为没有关联工作流' ,
    `mobilecontent` VARCHAR(1000)    COMMENT '手机通知推送内容' ,
    `templatetype` DECIMAL(22)    COMMENT '模板类型，0为无，50为工作流' ,
    `importantlevel` DECIMAL(22)    COMMENT '消息重要度,1：紧急 3：普通(默认) 5：低' ,
    `content` LONGTEXT    COMMENT '消息模板内容' ,
    PRIMARY KEY (objid)
) COMMENT = '消息模板表 ';


CREATE TABLE sirm_sendmessage(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `status` INT    COMMENT '发送状态, 1：初始化 5：时间未到（定时发送） 10：发送中 20:失败发送 30：部分完成 50：全部完成' ,
    `title` VARCHAR(300)    COMMENT '消息标题' ,
    `type` DECIMAL(22)    COMMENT '消息类型,1=邮件 2=短信 4=内部消息' ,
    `istime` DECIMAL(22)    COMMENT '定时发送标识,1:定时 0:立即发送' ,
    `sendtime` DATETIME    COMMENT '发送时间,如果是定时发送必须填写该字段' ,
    `content` LONGTEXT    COMMENT '消息内容' ,
    `senderid` VARCHAR(30)    COMMENT '发送人Id,关联sprt_orgobject.orgid' ,
    `sender_name` VARCHAR(30)    COMMENT '发送人名称' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30)    COMMENT '实体名称' ,
    `exampleownerid` BIGINT    COMMENT '流程审批人' ,
    `templatetypeid` BIGINT    COMMENT '模板类型,(目前只有“工作流”)这个跟手机推送有关系，其他不影响' ,
    `importantlevel` INT    COMMENT '消息重要度,1：紧急 3：普通(默认) 5：低' ,
    `sourceid` BIGINT    COMMENT '来源记录id' ,
    `sourceentity` VARCHAR(30)    COMMENT '来源记录名称' ,
    `mailfrom` VARCHAR(200)    COMMENT '发送邮件地址' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    PRIMARY KEY (objid)
) COMMENT = '发送消息表 ';


CREATE TABLE rt_receivemessage(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sendmessageid` BIGINT    COMMENT '发送消息Id,关联rt_SendMessage.objid' ,
    `status` INT    COMMENT '接收消息状态,0:未读 1:已读 2:已回复' ,
    `readtime` DATETIME    COMMENT '阅读时间' ,
    `relapsemessageid` BIGINT    COMMENT '回复消息ID,关联rt_SendMessage.objid' ,
    `sendmode` INT    COMMENT '消息发送方式' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `deleteflag` INT    COMMENT '删除状态,0:未删除 1:已删除' ,
    `receivertype` INT    COMMENT '接收人类型,0:抄送 1:收件人' ,
    `receiver` VARCHAR(30)    COMMENT '接收人Id,关联SPRT_ORGOBJECT.ORGID' ,
    PRIMARY KEY (objid)
) COMMENT = '站内信息接收表 ';


CREATE TABLE rt_sendmessage(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sendtime` DATETIME    COMMENT '发送时间' ,
    `timingflag` INT    COMMENT '是否定时发送,0:立即发送 1:定时发送' ,
    `definetime` DATETIME    COMMENT '定时发送的时间' ,
    `title` VARCHAR(300)    COMMENT '消息标题' ,
    `content` LONGTEXT    COMMENT '消息内容' ,
    `receiver` VARCHAR(3000)    COMMENT '接收者,支持多部门、岗位、小组、员工,以逗号分隔' ,
    `sendmode` VARCHAR(10)    COMMENT '发送方式, 1:系统内消息 2:邮件 3:短信 4:手机客户端, 支持同时以多种方式发送消息,以分号分隔' ,
    `relapseflag` INT    COMMENT '是否需要回复,1:需要，需要回复时，当接收人阅读时自动发消息通知发送人 0:不需要' ,
    `sendstatus` INT    COMMENT '发送状态,0:待发送(主要针对定时消息) 1:已成功发送 2:部分发送失败（需查看发送明细）' ,
    `deleteflag` INT    COMMENT '删除状态,0:未删除 1:删除' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `replymessageid` BIGINT    COMMENT '回复消息的id' ,
    `editflag` INT    COMMENT '是否可编辑、转发, 0:否 1:是' ,
    `sender` VARCHAR(30)    COMMENT '消息发送人' ,
    `importantlevel` INT    COMMENT '消息重要度,1：紧急 3：普通(默认) 5：低' ,
    PRIMARY KEY (objid)
) COMMENT = '站内消息发送表 ';


CREATE TABLE rt_sendmessagecontent(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sendmessageid` BIGINT    COMMENT '消息发送ID,关联RT_SendMessage.objid' ,
    `sendmode` INT    COMMENT '消息发送方式, 1:系统内提醒 2:邮件 3:短信 4:手机客户端' ,
    `title` VARCHAR(100)    COMMENT '消息标题' ,
    `content` VARCHAR(4000)    COMMENT '消息内容' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '站内消息发送内容表 ';


CREATE TABLE rt_sendmessagedetail(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sendmessageid` BIGINT    COMMENT '消息发送id,关联RT_SendMessage.objid' ,
    `sendtime` DATETIME    COMMENT '发送时间' ,
    `sendstatus` INT    COMMENT '发送状态,1:发送成功 10:发送失败' ,
    `reason` VARCHAR(300)    COMMENT '失败原因' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `receiverid` VARCHAR(50)    COMMENT '接收人的OrgId' ,
    `receivertype` INT    COMMENT '接收人类型,0:抄送 1:收件人' ,
    `receiver` VARCHAR(30)    COMMENT '接收人' ,
    PRIMARY KEY (objid)
) COMMENT = '站内消息发送结果表 ';


CREATE TABLE sprt_sysmenu(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `url` VARCHAR(200)    COMMENT 'Url地址' ,
    `parentid` DECIMAL(22) NOT NULL   COMMENT '父菜单ID，关联sprt_sysmenu.objid' ,
    `name` VARCHAR(300) NOT NULL   COMMENT '菜单名称' ,
    `description` VARCHAR(4000)    COMMENT '描述' ,
    `ord` DECIMAL(22) NOT NULL   COMMENT '顺序' ,
    `syscode` VARCHAR(32) NOT NULL   COMMENT '系统代码：Default 、Frontend' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `icon` VARCHAR(60)    COMMENT '菜单图标' ,
    `menu_type` VARCHAR(10)    COMMENT '菜单类型,1:菜单组 2:路由菜单 3:外部菜单 4:兼容菜单' ,
    `i18n` VARCHAR(50)    COMMENT '语言配置项' ,
    PRIMARY KEY (objid)
) COMMENT = '系统菜单 ';


CREATE TABLE sprt_system(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `syscode` VARCHAR(32) NOT NULL   COMMENT '系统代码' ,
    `sysname` VARCHAR(128) NOT NULL   COMMENT '系统名称' ,
    `description` VARCHAR(4000)    COMMENT '描述' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '系统表 ';


CREATE TABLE sprt_webmodule(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `component_key` VARCHAR(300)    COMMENT '组件key' ,
    `name` VARCHAR(300) NOT NULL   COMMENT '名称' ,
    `state` DECIMAL(22) NOT NULL   COMMENT '状态，1: 激活，0: 未激活' ,
    `code` VARCHAR(100)    COMMENT '模块代码' ,
    `height` VARCHAR(32)    COMMENT '默认高度' ,
    `ord` DECIMAL(22) NOT NULL   COMMENT '顺序' ,
    `syscode` VARCHAR(32) NOT NULL   COMMENT '系统代码：Default 、Frontend' ,
    `more_url` VARCHAR(500)    COMMENT '更多url' ,
    `description` VARCHAR(4000)    COMMENT '描述' ,
    `configfunction` VARCHAR(300)    COMMENT '配置的JS函数' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `width` VARCHAR(32)    COMMENT '默认宽度' ,
    `icon` varchar(300) DEFAULT NULL COMMENT '图标',
    `hide_title` char(5) DEFAULT 0 COMMENT '是否隐藏标题: 0为不隐藏, 1为隐藏',
    `params` varchar(300) COMMENT '模块参数',
    PRIMARY KEY (objid)
) COMMENT = '网页模块 ';



CREATE TABLE sirm_homepagecfg(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `pagelayout` INT    COMMENT '枚举com.sinitek.spirit.web.webmodule.WebmoduleLayout，描述页面整体布局方式，如按列布局，3:7' ,
    `modulelayout` VARCHAR(200)    COMMENT '描述每个列放置的模块及模块的顺序，格式如下：1_2,3;2_4,5,6表示第1列按顺序放置模块1，2，3；' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `righttype` INT    COMMENT '配置类型，1为展示，2为删除' ,
    `moduleid` BIGINT    COMMENT '模块id' ,
    `sort` INT    COMMENT '排序' ,
    `offset_grid_x` INT    COMMENT '距左侧偏移栅格值' ,
    `offset_grid_y` INT    COMMENT '距上侧偏移栅格值' ,
    `grid_width` INT    COMMENT '宽度栅格值' ,
    `grid_height` INT    COMMENT '高度栅格值' ,
    `scheme_id` BIGINT NOT NULL   COMMENT '方案id，关联sirm_homepagecfg_scheme.id' ,
    PRIMARY KEY (objid)
) COMMENT = '首页布局配置 ';



CREATE TABLE sirm_homepagecfg_default(
   `id` BIGINT NOT NULL   COMMENT '主键' ,
   `emp_id` varchar(30) NOT NULL    COMMENT '用户orgid' ,
   `scheme_id` BIGINT NOT NULL   COMMENT '方案id，关联sirm_homepagecfg_scheme.id' ,
   `createtimestamp` DATETIME    COMMENT '创建时间' ,
   `updatetimestamp` DATETIME    COMMENT '更新时间' ,
   `version` INT NOT NULL   COMMENT '乐观锁' ,
   PRIMARY KEY (id)
) COMMENT = '首页默认配置表';



CREATE TABLE sirm_homepagecfg_scheme(
    `id` BIGINT NOT NULL   COMMENT '主键' ,
    `type` tinyint NOT NULL    COMMENT '方案类型:0-系统默认方案，1-角色方案，2-自定义方案' ,
    `name` varchar(100) COMMENT '方案名称' ,
    `org_id` varchar(4000) NOT NULL    COMMENT '与方案类型挂钩，默认方案-0，角色方案-角色orgid，自定义方案-用户orgid' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    PRIMARY KEY (id)
) COMMENT = '首页配置方案表';



CREATE TABLE sirm_loggerconfig(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(600) NOT NULL   COMMENT '日志配置名称' ,
    `modulename` VARCHAR(100) NOT NULL   COMMENT '模块名称' ,
    `printtype` DECIMAL(22) NOT NULL   COMMENT '打印类型' ,
    `filename` VARCHAR(300)    COMMENT '文件名称' ,
    `loglevel` VARCHAR(10) NOT NULL   COMMENT '日志级别' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    PRIMARY KEY (objid)
) COMMENT = '日志配置信息表 ';


CREATE TABLE sprt_businlogger(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `userid` VARCHAR(30) NOT NULL   COMMENT '用户编号' ,
    `operatetype` DECIMAL(22)    COMMENT '操作类型' ,
    `description` VARCHAR(4000)    COMMENT '操作描述' ,
    `starttime` DATETIME    COMMENT '操作开始时间' ,
    `endtime` DATETIME    COMMENT '操作结束时间' ,
    `url` VARCHAR(256)    COMMENT '操作URL' ,
    `menuid` DECIMAL(22)    COMMENT '菜单ID，关联sprt_sysmenu.objid' ,
    `physicaladdress` VARCHAR(128)    COMMENT '用户物理地址' ,
    `ipaddress` VARCHAR(32)    COMMENT '用户IP地址' ,
    `modulename` VARCHAR(32)    COMMENT '模块名' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `tenant_id` VARCHAR(60)    COMMENT '多租户id' ,
    `server_ip` VARCHAR(60) COMMENT '服务器IP',
    `terminal` VARCHAR(60) COMMENT '用户终端类型',
    `operating_system` VARCHAR(60) COMMENT '客户端操作系统',
    `app_name` VARCHAR(100) COMMENT '日志所属服务,预留字段',
    `big_description` longtext COMMENT '详细描述',
    PRIMARY KEY (objid)
) COMMENT = '业务日志 ';


CREATE TABLE sirm_i18n(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `i18n_key` VARCHAR(50)    COMMENT '语言包名称' ,
    `zh_cn` VARCHAR(300)    COMMENT '简体中文' ,
    `zh_hk` VARCHAR(300)    COMMENT '繁体中文(香港)' ,
    `en_us` VARCHAR(300)    COMMENT '英文' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT    COMMENT '乐观锁' ,
    PRIMARY KEY (id)
) COMMENT = '国际化之语言包管理表 ';


CREATE TABLE sirm_apploader(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(60)    COMMENT '加载项名称中文' ,
    `code` VARCHAR(30)    COMMENT '加载项标识英文' ,
    `brief` VARCHAR(200)    COMMENT '加载项描述' ,
    `classfullname` VARCHAR(150)    COMMENT '加载项类名，实现IAppLoader接口的全类名' ,
    `priority` INT    COMMENT '加载顺序。数值越小越先加载' ,
    `enabled` CHAR(5)    COMMENT '有效表示' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = 'APP启动加载项表 ';


CREATE TABLE sirm_attachment(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sourceentity` VARCHAR(50)    COMMENT '所属实体名称' ,
    `type` INT    COMMENT '附件类型' ,
    `sourceid` BIGINT    COMMENT '所属业务实例ID' ,
    `name` VARCHAR(1000)    COMMENT '附件名称' ,
    `contentsize` BIGINT    COMMENT '附件大小' ,
    `pagecount` INT    COMMENT '页数' ,
    `convertstatus` INT    COMMENT '转换标识' ,
    `digest` VARCHAR(100)    COMMENT '附件摘要' ,
    `storetype` INT    COMMENT '存储方式' ,
    `storepath` VARCHAR(600)    COMMENT '文档存储路径' ,
    `filetype` VARCHAR(10)    COMMENT '文件类型' ,
    `content` LONGBLOB    COMMENT '附件内容' ,
    `convertresult` INT    COMMENT '转换结果' ,
    `sendflag` INT    COMMENT '转换标识' ,
    `convertflag` INT    COMMENT '转换标识' ,
    `convertid` BIGINT    COMMENT '转换附件Id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `ownerid` VARCHAR(30)    COMMENT '附件上传人id' ,
    `brief` VARCHAR(200)    COMMENT '备注' ,
    `storekey` VARCHAR(300)    COMMENT '存储key' ,
    `enckey` VARCHAR(128)    COMMENT '加密秘钥' ,
    `enc_algorithm` varchar(30) COMMENT '加密所用算法,AES/SM4/空为AES',
    `deleted_flag` tinyINT DEFAULT 0 COMMENT '逻辑删除标识, 删除为1, 未删除为0',
    `content_id` BIGINT COMMENT '附件归档内容id',
    PRIMARY KEY (objid)
) COMMENT = '附件信息表 ';


CREATE TABLE `sirm_attachment_content`  (
  `id` bigint NOT NULL COMMENT '主键id',
  `type` int NULL DEFAULT NULL COMMENT '类型',
  `name` varchar(1000)  NULL DEFAULT NULL COMMENT '附件名称',
  `contentsize` bigint NULL DEFAULT NULL COMMENT '附件大小',
  `storetype` INT    COMMENT '存储方式' ,
  `storepath` varchar(600)  NULL DEFAULT NULL COMMENT '附件存放路径',
  `filetype` varchar(10)  NULL DEFAULT NULL COMMENT '文件类型',
  `content` longblob NULL COMMENT '附件内容',
  `enckey` varchar(128)  NULL DEFAULT NULL COMMENT '加密秘钥',
  `enc_algorithm` varchar(30)  NULL DEFAULT NULL COMMENT '加密所用算法,AES/SM4/空为AES',
  `ref_count` int NULL DEFAULT NULL COMMENT '引用次数',
  `md5` varchar(100)  NULL DEFAULT NULL COMMENT '验证标识',
  `createtimestamp` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetimestamp` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `version` int NULL DEFAULT NULL COMMENT '乐观锁',
  PRIMARY KEY (`id`) 
)COMMENT = '附件内容表 ';

CREATE TABLE sirm_cacheinfo(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(30)    COMMENT '缓存名称' ,
    `entitynames` VARCHAR(100)    COMMENT '缓存实体信息' ,
    `location` VARCHAR(100)    COMMENT '缓存刷新地址信息' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `description` VARCHAR(1000)    COMMENT '描述' ,
    PRIMARY KEY (objid)
) COMMENT = '缓存信息 ';


CREATE TABLE sirm_entitysetting(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sourceentity` VARCHAR(24)    COMMENT '实体名称' ,
    `sourceid` DECIMAL(22)    COMMENT '实体对应的记录Id' ,
    `name` VARCHAR(30)    COMMENT '配置名称' ,
    `value` VARCHAR(1000)    COMMENT '配置值' ,
    `brief` VARCHAR(200)    COMMENT '配置简介' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '实体配置 ';



CREATE TABLE sirm_calendar_event(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `type` VARCHAR(30) NOT NULL    COMMENT '日程类型' ,
    `calendar_type` INTEGER    COMMENT '日历类型' ,
    `start_time` DATETIME NOT NULL   COMMENT '日程开始时间' ,
    `end_time` DATETIME NOT NULL   COMMENT '日程结束时间' ,
    `input_id` VARCHAR(30) COMMENT '录入人ID' ,
    `title` VARCHAR(200)  NOT NULL   COMMENT '标题' ,
    `source_name` VARCHAR(30)  NOT NULL  COMMENT '日程来源对象' ,
    `source_id` BIGINT NOT NULL   COMMENT '日程来源ID' ,
    `emp_id` VARCHAR(30)  COMMENT '人员ID' ,
    `content` TEXT  COMMENT '日程说明' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `url` VARCHAR(200)    COMMENT 'url' ,
    `thread_id` BIGINT   COMMENT '线索ID' ,
    `owner_name` VARCHAR(30)    COMMENT '日程所有者名称' ,
    `owner_id` BIGINT    COMMENT '日程所有者ID' ,
    `remind_flag` tinyint NOT NULL COMMENT '日程提醒,0表示不提醒,1表示提醒' ,
    `remind_type` int  COMMENT '提醒方式' ,
    `all_day` tinyint COMMENT '是否为全天' ,
    PRIMARY KEY (id)
) COMMENT = '日程表 ';



CREATE TABLE sirm_calendar_event_remind(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `calendar_event_id` BIGINT(20) NOT NULL   COMMENT '日程id' ,
    `remind_time` int NOT NULL  COMMENT '提前XX分钟提醒' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    PRIMARY KEY (id)
) COMMENT = '日程提醒表 ';



CREATE TABLE sirm_calendar_event_sync(
 `id` bigint NOT NULL   COMMENT '主键' ,
 `calendar_event_id` BIGINT NOT NULL    COMMENT '日程id' ,
 `type` INTEGER NOT NULL COMMENT '软件类型' ,
 `schedule_id` VARCHAR(100) NOT NULL    COMMENT '软件日程id' ,
 `createtimestamp` DATETIME    COMMENT '创建时间' ,
 `updatetimestamp` DATETIME    COMMENT '更新时间' ,
 `version` INT NOT NULL   COMMENT '乐观锁' ,
 PRIMARY KEY (id)
) COMMENT = '日程同步表 ';



CREATE TABLE sirm_enum(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `catalog` VARCHAR(200)    COMMENT '实体配置' ,
    `type` VARCHAR(200)    COMMENT '枚举类型' ,
    `name` VARCHAR(600)    COMMENT '枚举项显示名称' ,
    `value` DECIMAL(22)    COMMENT '枚举项值' ,
    `description` VARCHAR(500)    COMMENT '枚举项描述' ,
    `sort` DECIMAL(22)    COMMENT '序号' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `strvalue` VARCHAR(600)    COMMENT '枚举项值' ,
    PRIMARY KEY (objid)
) COMMENT = '枚举 ';


CREATE TABLE sirm_groupsetting(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `name` VARCHAR(30)    COMMENT '参数名称' ,
    `catalogcode` VARCHAR(30)    COMMENT '参数组编码' ,
    `url` VARCHAR(100)    COMMENT 'url地址' ,
    `sort` DECIMAL(22)    COMMENT '顺序' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `i18n` VARCHAR(50)    COMMENT '语言配置项' ,
    PRIMARY KEY (objid)
) COMMENT = '参数组配置 ';


CREATE TABLE sirm_menufunctionrela(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `functionid` BIGINT NOT NULL   COMMENT '功能记录Id' ,
    `menuid` BIGINT NOT NULL   COMMENT '菜单记录Id' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '菜单功能对应关系 ';






CREATE TABLE SIRM_REMIND_EVENT (
  `ID` BIGINT NOT NULL COMMENT '主键id',
  `NAME` longtext COMMENT '事件名称',
  `TYPE` VARCHAR(100) NOT NULL COMMENT '事件类型：1 本地接口，2 远程接口',
  `ACHIEVE` longtext COMMENT '事件实现',
  `createtimestamp` DATETIME    COMMENT '创建时间' ,
  `updatetimestamp` DATETIME    COMMENT '更新时间' ,
  `version` INT NOT NULL   COMMENT '乐观锁' ,
  PRIMARY KEY (ID)
) COMMENT = '提醒事件表';

CREATE TABLE SIRM_REMIND_TRIGGER_HISTORY (
  `ID` BIGINT NOT NULL COMMENT '主键id',
  `REMIND_ID` BIGINT NOT NULL COMMENT '关联提醒表的主键id',
  `TRIGGER_TIME` DATETIME NOT NULL COMMENT '触发时间',
  `END_TIME` DATETIME COMMENT '结束时间',
  `createtimestamp` DATETIME    COMMENT '创建时间' ,
  `updatetimestamp` DATETIME    COMMENT '更新时间' ,
  `version` INT NOT NULL   COMMENT '乐观锁' ,
  PRIMARY KEY (ID)
) COMMENT = '提醒触发历史表';

CREATE TABLE sirm_supportcenter(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `range` VARCHAR(150)    COMMENT '支持范围' ,
    `name` VARCHAR(90)    COMMENT '姓名' ,
    `tel` VARCHAR(50)    COMMENT '联系电话' ,
    `email` VARCHAR(300)    COMMENT '邮箱' ,
    `msn` VARCHAR(100)    COMMENT 'MSN' ,
    `qq` VARCHAR(100)    COMMENT 'QQ' ,
    `sort` DECIMAL(22)    COMMENT '排序' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '支持中心表 ';


CREATE TABLE sirm_setting(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `module` VARCHAR(100)    COMMENT '模块名称，模块名称（英文） 建议全大写' ,
    `name` VARCHAR(100)    COMMENT '参数名称，在一个MODULE内，NAME是唯一的。建议全大写' ,
    `value` VARCHAR(1800)    COMMENT '参数值' ,
    `brief` VARCHAR(600)    COMMENT '参数说明，0：不加密，1：加密' ,
    `encryptionflag` DECIMAL(22)    COMMENT '是否加密' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '参数配置表 ';



CREATE TABLE rt_holidays(
    `id` bigint NOT NULL   COMMENT '主键' ,
    `inputId` VARCHAR(30)    COMMENT '节假日保存人' ,
    `year` INT(10)    COMMENT '年份' ,
    `holiday_date` DATE    COMMENT '时间' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    PRIMARY KEY (id)
) COMMENT = '节假日管理表 ';




CREATE TABLE rt_applydocauth(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `documentid` BIGINT    COMMENT '申请权限的文档id' ,
    `orgid` VARCHAR(30)    COMMENT '申请权限的人id' ,
    `status` INT    COMMENT '审批状态，0：审批中，1：审批通过，2：审批驳回' ,
    `starttime` DATETIME    COMMENT '申请权限开始时间' ,
    `endtime` DATETIME    COMMENT '申请权限截止时间' ,
    `applytime` DATETIME    COMMENT '申请时间' ,
    `applyreason` VARCHAR(1024)    COMMENT '申请理由' ,
    `exampleid` BIGINT    COMMENT '流程实例id（备用）' ,
    `applyauth` VARCHAR(60)    COMMENT '申请的权限，如果有多个就用逗号隔开' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '存储权限申请信息表 ';


CREATE TABLE rt_directory(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sort` INT    COMMENT '排序' ,
    `directoryer` VARCHAR(60)    COMMENT '目录新建人，关联SPRT_ORGOBJECT.ORGID' ,
    `directoryname` VARCHAR(200)    COMMENT '目录名称' ,
    `parentid` BIGINT    COMMENT '上级目录' ,
    `directorycontent` VARCHAR(600)    COMMENT '目录内容' ,
    `directorytype` INT    COMMENT '目录类型，1：公司 2：个人' ,
    `status` INT    COMMENT '文档状态，1：启用 0：禁用' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '文档目录表 ';


CREATE TABLE rt_directory_append(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `issearch` INT    COMMENT '是否允许检索，1是允许，0是不允许' ,
    `allowapplyautor` INT    COMMENT '是否允许申请权限，1是允许，0是不允许' ,
    `allowsubapplyauthor` INT    COMMENT '允许子级设置权限，1是允许，0是不允许' ,
    `workflowid` BIGINT    COMMENT '申请权限工作流id，只有Allowapplyaytor为1时有效' ,
    `directoryid` BIGINT    COMMENT '目录id,关联RT_DIRECTORY.OBJID' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '目录的附加信息表 ';


CREATE TABLE rt_documentattachment(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `attachmenter` VARCHAR(60)    COMMENT '附件上传人,关联SPRT_ORGOBJECT.ORGID' ,
    `attachmentname` VARCHAR(100)    COMMENT '附件名称' ,
    `documentid` BIGINT    COMMENT '文档编号,关联RT_DOCUMENTS.OBJID' ,
    `createtime` DATETIME    COMMENT '上传时间' ,
    `documentversion` VARCHAR(50)    COMMENT '版本号' ,
    `attachmentid` BIGINT    COMMENT '附件编号' ,
    `attachmentername` VARCHAR(100)    COMMENT '上传人姓名' ,
    `groupid` BIGINT    COMMENT '分类号,以第一封附件的Id为分类编号' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `remarks` VARCHAR(3200)    COMMENT '附件描述' ,
    PRIMARY KEY (objid)
) COMMENT = '文档附件版本表 ';


CREATE TABLE rt_documentauth(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `sharinger` VARCHAR(60)    COMMENT '共享对象' ,
    `orgid` VARCHAR(60)    COMMENT '创建人,关联SPRT_ORGOBJECT.ORGID' ,
    `authid` BIGINT    COMMENT '权限编号,关联SPRT_RIGHTAUTH.OBJID' ,
    `begintime` DATETIME    COMMENT '开始时间' ,
    `endtime` DATETIME    COMMENT '结束时间' ,
    `authentity` VARCHAR(30)    COMMENT '实体名称,文档实体:RTDOCUMENTS 目录实体:RTDIRECTORY' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    `fromentity` VARCHAR(60)    COMMENT '自动授权的来源实体，默认为null' ,
    `fromobjid` BIGINT    COMMENT '自动授权的来源id,默认为null' ,
    PRIMARY KEY (objid)
) COMMENT = '文档管理权限表 ';


CREATE TABLE rt_documents(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `directoryid` BIGINT NOT NULL   COMMENT '所在目录id,关联RT_DIRECTORY表中objid' ,
    `documenter` VARCHAR(60) NOT NULL   COMMENT '文档上传人,关联SPRT_ORGOBJECT.ORGID' ,
    `documentname` VARCHAR(200)    COMMENT '文档名称' ,
    `documentcontent` VARCHAR(200)    COMMENT '文档内容' ,
    `status` INT    COMMENT '文档状态,1：启用 0：禁用' ,
    `createtime` DATETIME    COMMENT '上传时间' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '文档信息表 ';


CREATE TABLE rt_keyreader(
    `objid` BIGINT NOT NULL   COMMENT '主键' ,
    `authentity` VARCHAR(30)    COMMENT '类型' ,
    `reply` VARCHAR(3200)    COMMENT '回复' ,
    `readerid` VARCHAR(60)    COMMENT '阅读人Id' ,
    `authid` BIGINT    COMMENT '权限编号' ,
    `orgid` VARCHAR(60)    COMMENT '创建人' ,
    `createtimestamp` DATETIME    COMMENT '创建时间' ,
    `updatetimestamp` DATETIME    COMMENT '更新时间' ,
    `version` INT NOT NULL   COMMENT '乐观锁' ,
    `entityname` VARCHAR(30) NOT NULL   COMMENT '实体名称' ,
    PRIMARY KEY (objid)
) COMMENT = '重点阅读人表 ';



CREATE TABLE `sirm_remind_config`
(
    `id`                     bigint NOT NULL COMMENT '主键id',
    `name`                   varchar(100) NOT NULL COMMENT '提醒名称',
    `start_time`             datetime     NOT NULL COMMENT '开始时间',
    `end_time`               datetime NULL COMMENT '结束时间',
    `repeat_flag`            tinyint NULL DEFAULT 0 COMMENT '是否重复，0：不重复（默认），1：重复',
    `repeat_type`            varchar(10) NULL COMMENT '重复类型，DAY：天，WEEK：周，MONTH：月，SEASON：季，YEAR：年',
    `repeat_rate`            int NULL COMMENT '重复频率，单位为重复类型',
    `repeat_times`           int NULL COMMENT '重复次数，0:不限制次数',
    `holiday_strategy`       int NULL DEFAULT 0 COMMENT '节假日策略，0：正常触发（默认），1：跳过，2：提前，3：顺延',
    `regex`                  varchar(300) NULL COMMENT '条件表达式',
    `next_trigger_time`      datetime NULL COMMENT '下一次触发时间（经过了节假日策略偏移）',
    `next_plan_trigger_time` datetime     DEFAULT NULL COMMENT '下一次预计触发时间（没有经过节假日策略偏移）',
    `trigger_times`          int NULL COMMENT '已触发次数',
    `source_id`              varchar(100) NULL DEFAULT 0 COMMENT '关联的业务对象id',
    `source_name`            varchar(100) NULL COMMENT '关联的业务对象名',
    `createtimestamp`        datetime     NOT NULL COMMENT '创建时间',
    `updatetimestamp`        datetime     NOT NULL COMMENT '更新时间',
    `version`                int NOT NULL COMMENT '乐观锁',
    `tenant_id`              varchar(128) DEFAULT NULL,
    PRIMARY KEY (`id`)
) COMMENT = '提醒配置表';


-- 添加提醒额外配置表
CREATE TABLE `sirm_remind_extra_config`
(
    `id`              bigint NOT NULL COMMENT '主键id',
    `remind_id`       bigint NOT NULL COMMENT '关联提醒表的主键id',
    `title`           varchar(100) NOT NULL COMMENT '提醒标题',
    `content`         longtext     NOT NULL COMMENT '提醒内容',
    `type`            int          NOT NULL COMMENT '提醒方式',
    `receiver`        bigint NOT NULL COMMENT '提醒对象',
    `createtimestamp` datetime     NOT NULL COMMENT '创建时间',
    `updatetimestamp` datetime     NOT NULL COMMENT '更新时间',
    `version`         int NOT NULL COMMENT '乐观锁',
    PRIMARY KEY (`id`)
) COMMENT = '提醒额外配置表';


-- 添加提醒配置关联表
CREATE TABLE `sirm_remind_rela`
(
    `id`              bigint NOT NULL COMMENT '主键id',
    `remind_id`       BIGINT       NOT NULL COMMENT '提醒id',
    `source_id`       BIGINT       NOT NULL COMMENT '关联实体id',
    `source_name`     varchar(100) NOT NULL COMMENT '关联实体名',
    `createtimestamp` datetime     NOT NULL COMMENT '创建时间',
    `updatetimestamp` datetime     NOT NULL COMMENT '更新时间',
    `version`         int NOT NULL COMMENT '乐观锁',
    PRIMARY KEY (`id`)
) COMMENT = '提醒关联信息表';



CREATE TABLE sirm_wx_work_config(
 `id` bigint NOT NULL   COMMENT '主键' ,
 `corp_id` VARCHAR(100) NOT NULL    COMMENT '企业id' ,
 `tenant_id` VARCHAR(60) COMMENT '多租户id' ,
 `createtimestamp` DATETIME    COMMENT '创建时间' ,
 `updatetimestamp` DATETIME    COMMENT '更新时间' ,
 `version` INT NOT NULL   COMMENT '乐观锁' ,
 PRIMARY KEY (id)
) COMMENT = '企业微信配置表 ';


CREATE TABLE sirm_wx_work_app_config(
 `id` bigint NOT NULL   COMMENT '主键' ,
 `config_id` BIGINT COMMENT '企业数据id' ,
 `code` VARCHAR(100) COMMENT '应用名称编码' ,
 `app_id` VARCHAR(100) NOT NULL   COMMENT '应用id' ,
 `secret` VARCHAR(100) NOT NULL   COMMENT '应用secret' ,
 `createtimestamp` DATETIME    COMMENT '创建时间' ,
 `updatetimestamp` DATETIME    COMMENT '更新时间' ,
 `version` INT NOT NULL   COMMENT '乐观锁' ,
 PRIMARY KEY (id)
) COMMENT = '企业微信应用配置表 ';

CREATE TABLE sirm_wx_work_account(
 `id` bigint NOT NULL   COMMENT '主键' ,
 `org_id` VARCHAR(30) NOT NULL    COMMENT '员工id' ,
 `wxwork_id` VARCHAR(50) NOT NULL    COMMENT '员工企业微信账号' ,
 `tenant_id` VARCHAR(60) COMMENT '多租户id' ,
 `createtimestamp` DATETIME    COMMENT '创建时间' ,
 `updatetimestamp` DATETIME    COMMENT '更新时间' ,
 `version` INT NOT NULL   COMMENT '乐观锁' ,
 PRIMARY KEY (id)
) COMMENT = '企业微信映射关系表 ';

CREATE TABLE sirm_shortcut_menu (
  id bigint NOT NULL COMMENT '主键',
  menu_id bigint NOT NULL COMMENT '菜单id，对应sprt_sysmenu.objid',
  emp_id varchar(30) NOT NULL COMMENT '用户Id,关联sprt_orgobject.orgid',
  sort int NOT NULL COMMENT '菜单排序，从小到大',
  createtimestamp datetime DEFAULT NULL COMMENT '创建时间',
  updatetimestamp datetime DEFAULT NULL COMMENT '更新时间',
  version int NOT NULL COMMENT '乐观锁',
  PRIMARY KEY (id)
) COMMENT = '快捷菜单表';

-- 给表增加索引
-- ALTER TABLE sirm_enum ADD UNIQUE uk_sirm_enum_ca_ty_name(catalog,type,name);
ALTER TABLE sprt_rightauth ADD INDEX idx_rightauth_orgid(authorgid);
ALTER TABLE sprt_rightauth ADD INDEX idx_rightauth_objectkey(objectkey);

ALTER TABLE sirm_idgenerator ADD UNIQUE uk_idgenerator_entityname(entityname);

ALTER TABLE um_userinfo ADD UNIQUE uk_userinfo_userid(userid);
ALTER TABLE um_userinfo ADD UNIQUE uk_userinfo_username(username);
ALTER TABLE um_userproperty ADD UNIQUE uk_userproperty_userid_name(userid,name);

ALTER TABLE WF_PROCESSSTEP ADD INDEX idx_wf_ps_processid(processid);
ALTER TABLE WF_PROCESSLIST ADD INDEX idx_wf_pl_k_n(`key`, name);
ALTER TABLE WF_FLOWPROPS ADD INDEX idx_wf_flowprops_si_se(sourceid,sourcename);
ALTER TABLE WF_FLOWNODE ADD INDEX idx_wf_flownode_processid(processid);
ALTER TABLE Wf_ExampleTask ADD INDEX idx_wf_task_s_s(sourceid, sourceentity);
ALTER TABLE Wf_ExampleTask ADD INDEX idx_wf_task_se(sourceentity);
ALTER TABLE WF_EXAMPLESTEPOWNER ADD INDEX idx_wf_eso_eid_stepid(exampleid,examplestepid);
ALTER TABLE WF_EXAMPLESTEP ADD INDEX idx_wf_es_exampleid(exampleid);
ALTER TABLE WF_EXAMPLELIST ADD INDEX idx_wf_el_k_n(`key`, name);

ALTER TABLE qrtz_fired_triggers ADD INDEX idx_qrtz_ft_trig_inst_name(sched_name,instance_name);
ALTER TABLE qrtz_fired_triggers ADD INDEX idx_qrtz_ft_inst_job_req_rcvry(sched_name,instance_name,requests_recovery);
ALTER TABLE qrtz_fired_triggers ADD INDEX idx_qrtz_ft_j_g(sched_name,job_name,job_group);
ALTER TABLE qrtz_fired_triggers ADD INDEX idx_qrtz_ft_jg(sched_name,job_group);
ALTER TABLE qrtz_fired_triggers ADD INDEX idx_qrtz_ft_t_g(sched_name,trigger_name,trigger_group);
ALTER TABLE qrtz_fired_triggers ADD INDEX qrtz_fired_triggers(sched_name,trigger_group);
ALTER TABLE qrtz_job_details ADD INDEX idx_qrtz_j_req_recovery(sched_name,requests_recovery);
ALTER TABLE qrtz_job_details ADD INDEX idx_qrtz_j_grp(sched_name,job_group);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_j(sched_name,job_name,job_group);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_jg(sched_name,job_group);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_c(sched_name,calendar_name);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_g(sched_name,trigger_group);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_state(sched_name,trigger_state);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_n_state(sched_name,trigger_name,trigger_group,trigger_state);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_n_g_state(sched_name,trigger_group,trigger_state);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_next_fire_time(sched_name,next_fire_time);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_nft_st(sched_name,trigger_state,next_fire_time);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_nft_misfire(sched_name,misfire_instr,next_fire_time);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_nft_st_misfire(sched_name,misfire_instr,next_fire_time,trigger_state);
ALTER TABLE qrtz_triggers ADD INDEX idx_qrtz_t_nft_st_misfire_grp(sched_name,misfire_instr,next_fire_time,trigger_group,trigger_state);

ALTER TABLE metadb_entitycatalog ADD UNIQUE uk_entitycatalog_catalogkey(catalogkey);
ALTER TABLE metadb_idgenerator ADD UNIQUE uk_metadb_idgenerator_en(entityname);

ALTER TABLE tenant_table_config ADD UNIQUE uk_tenant_table(tenant_table);
ALTER TABLE tenant_mechanism ADD UNIQUE uk_mechanism_code(code);
ALTER TABLE tenant_mechanism ADD UNIQUE uk_mechanism_name(name);

ALTER TABLE org_userextendinfo ADD UNIQUE uk_userextendinfo_userid(userid);
ALTER TABLE org_userextendinfo ADD UNIQUE uk_userextendinfo_email(email);
ALTER TABLE org_logonlog ADD UNIQUE uk_logonlog_objid(objid);
ALTER TABLE org_logonlog ADD INDEX logonlog_orgid(orgid);
ALTER TABLE org_property ADD UNIQUE uk_org_property_objid(objid);
ALTER TABLE org_relationscheme ADD UNIQUE uk_relationscheme_code(code);
ALTER TABLE sprt_orgrela ADD UNIQUE uk_orgrela_to_fr_re(toobjectid,fromobjectid,relationtype);
ALTER TABLE sprt_orgobject ADD UNIQUE uk_orgobject_orgid(orgid);
ALTER TABLE sprt_orgobject ADD UNIQUE uk_orgobject_userid(userid);

ALTER TABLE sirm_application ADD UNIQUE uk_application_app_name(app_name);
ALTER TABLE sirm_application ADD UNIQUE uk_application_app_id(app_id);
ALTER TABLE sirm_i18n ADD UNIQUE uk_i18n_key(i18n_key);
ALTER TABLE sirm_functioninfo ADD UNIQUE uk_functioninfo_code(code);
ALTER TABLE sirm_messagetemplate ADD UNIQUE uk_messagetemplate_code(code);
ALTER TABLE sirm_apploader ADD UNIQUE uk_apploader_code(code);
ALTER TABLE sirm_attachment_content ADD UNIQUE uk_attachment_content_md5(md5);
ALTER TABLE sirm_setting ADD UNIQUE uk_setting_module_name(module,name);
