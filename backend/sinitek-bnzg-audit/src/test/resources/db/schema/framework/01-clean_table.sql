drop table if exists  sirm_attachment_content;
drop table if exists  SIRM_SERVICE_API;
drop table if exists  SIRM_SERVICE_CLIENT;
drop table if exists  SIRM_SERVICE_CLIENT_AUTH;
drop table if exists  SIRM_ACTION;
drop table if exists  SIRM_ACTION_RELA;
drop table if exists  sprt_loggerconfig;
drop table if exists  sirm_idgenerator;
drop table if exists  sirm_indexqueue;
drop table if exists  um_userinfo;
drop table if exists  um_qualifyinfo;
drop table if exists  um_usersecurityquestion;
drop table if exists  um_userkeepsignedin;
drop table if exists  um_userproperty;
drop table if exists  um_userrole;
drop table if exists  um_userscheme;
drop table if exists  um_userschemerela;
drop table if exists  openapi_personal_token;
drop table if exists  wf_agents;
drop table if exists  wf_approvalphrase;
drop table if exists  wf_entry_extend;
drop table if exists  wf_example;
drop table if exists  wf_processstep;
drop table if exists  wf_processstepdo;
drop table if exists  wf_processstephistory;
drop table if exists  wf_processurl;
drop table if exists  wf_processsteplinkif;
drop table if exists  wf_processsteplinkdo;
drop table if exists  wf_processsteplink;
drop table if exists  wf_processruleinfo;
drop table if exists  wf_processrule;
drop table if exists  wf_processpara;
drop table if exists  wf_processownerlink;
drop table if exists  wf_processlist;
drop table if exists  wf_processowner;
drop table if exists  wf_processextend;
drop table if exists  wf_process;
drop table if exists  wf_flowprops;
drop table if exists  wf_flowpaths;
drop table if exists  wf_flownode;
drop table if exists  wf_flowdots;
drop table if exists  wf_exampletask;
drop table if exists  wf_examplestepowner;
drop table if exists  wf_examplesteplink;
drop table if exists  wf_examplestep;
drop table if exists  wf_examplepara;
drop table if exists  wf_examplelist;
drop table if exists  wf_exampleentry;
drop table if exists  wf_examplecustomowner;
drop table if exists  wf_examplecc;
drop table if exists  wf_exampleassociated;
drop table if exists  wf_exampleaskforbatch;
drop table if exists  wf_exampleaskfor;
drop table if exists  demo_out_apply;
drop table if exists  qrtz_blob_triggers;
drop table if exists  qrtz_calendars;
drop table if exists  qrtz_cron_triggers;
drop table if exists  qrtz_fired_triggers;
drop table if exists  qrtz_job_details;
drop table if exists  qrtz_job_listeners;
drop table if exists  qrtz_locks;
drop table if exists  qrtz_paused_trigger_grps;
drop table if exists  qrtz_scheduler_state;
drop table if exists  qrtz_simple_triggers;
drop table if exists  qrtz_simprop_triggers;
drop table if exists  qrtz_triggers;
drop table if exists  qrtz_trigger_listeners;
drop table if exists  sirm_jobquartztime;
drop table if exists  sirm_jobquartzgroup;
drop table if exists  sirm_jobquartzdetails;
drop table if exists  sirm_jobexecutelog;
drop table if exists  sirm_job_config;
drop table if exists  metadb_entity;
drop table if exists  metadb_entitycatalog;
drop table if exists  metadb_property;
drop table if exists  metadb_idgenerator;
drop table if exists  tenant_table_config;
drop table if exists  tenant_mechanism;
drop table if exists  tenant_mechanism_role;
drop table if exists  tenant_mechanism_role_auth;
drop table if exists  sirm_mobile_icon;
drop table if exists  sirm_mobile_version;
drop table if exists  org_userextendinfo;
drop table if exists  org_authorize;
drop table if exists  org_logonlog;
drop table if exists  org_property;
drop table if exists  org_pswhis;
drop table if exists  org_relation;
drop table if exists  org_relationscheme;
drop table if exists  org_teamindustryrela;
drop table if exists  sprt_orgrela;
drop table if exists  sprt_orgobject;
drop table if exists  sprt_rightauth;
drop table if exists  sprt_rightdef;
drop table if exists  sirm_functiongroup;
drop table if exists  sirm_functioninfo;
drop table if exists  sirm_sendmessagedetail;
drop table if exists  sirm_messagereceiver;
drop table if exists  sirm_messagetemplate;
drop table if exists  sirm_sendmessage;
drop table if exists  rt_receivemessage;
drop table if exists  rt_sendmessage;
drop table if exists  rt_sendmessagecontent;
drop table if exists  rt_sendmessagedetail;
drop table if exists  sprt_sysmenu;
drop table if exists  sprt_system;
drop table if exists  sprt_webmodule;
drop table if exists  sirm_homepagecfg;
drop table if exists  sirm_loggerconfig;
drop table if exists  sprt_businlogger;
drop table if exists  sirm_i18n;
drop table if exists  sirm_apploader;
drop table if exists  sirm_attachment;
drop table if exists  sirm_cacheinfo;
drop table if exists  sirm_entitysetting;
drop table if exists  sirm_enum;
drop table if exists  sirm_groupsetting;
drop table if exists  sirm_menufunctionrela;
drop table if exists  sirm_supportcenter;
drop table if exists  sirm_setting;
drop table if exists  rt_holidays;
drop table if exists  rt_holiday_scheme;
drop table if exists  sirm_burypoint;
drop table if exists  sirm_burypoint_url;
drop table if exists  rt_applydocauth;
drop table if exists  rt_directory;
drop table if exists  rt_directory_append;
drop table if exists  rt_documentattachment;
drop table if exists  rt_documentauth;
drop table if exists  rt_documents;
drop table if exists  rt_keyreader;
drop table if exists  sirm_calendar_event;
drop table if exists  sirm_calendar_event_remind;
drop table if exists  sirm_remind_config;
drop table if exists  sirm_remind_extra_config;
drop table if exists  sirm_remind_rela;
drop table if exists  sirm_calendar_event_sync;
drop table if exists  sirm_wx_work_config;
drop table if exists  sirm_wx_work_app_config;
drop table if exists  sirm_wx_work_account;
drop table if exists  sirm_application;