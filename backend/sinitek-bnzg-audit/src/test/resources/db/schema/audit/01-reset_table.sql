CREATE TABLE audit_library (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  remove_flag int2 COMMENT '逻辑删除',
  name varchar(150) COMMENT '名称',
  remark varchar(1500) COMMENT '备注',
  status int2 COMMENT '状态',
  PRIMARY KEY (id)
) COMMENT='审计程序库';

CREATE TABLE audit_group (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  parent_id int8 COMMENT '父节点id',
  sort int4 COMMENT '顺序',
  high_val int4 COMMENT '节点高度',
  code_val varchar(1000) COMMENT '节点编码',
  lib_id int8 COMMENT '库id',
  name varchar(150) COMMENT '名称',
  name_val varchar(1500) COMMENT '名称冗余',
  remove_flag int2 COMMENT '逻辑删除',
  PRIMARY KEY (id)
) COMMENT='审计程序分组';

create index i_audit_group_01 on audit_group(lib_id);
create index i_audit_group_02 on audit_group(parent_id);
-- create index uk_audit_group_01 on audit_group(code_val);

CREATE TABLE audit_procedure (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  lib_id int8 COMMENT '程序库id',
  group_id int8 COMMENT '分组id',
  remove_flag int2 COMMENT '逻辑删除',
  name varchar(150) COMMENT '名称',
  regulation_ref varchar(3000) COMMENT '制度引用',
  interviewee varchar(3000) COMMENT '访谈对象',
  check_material varchar(3000) COMMENT '检查材料',
  audit_step varchar(3000) COMMENT '审计步骤',
  remove_org_id varchar(30) COMMENT '删除人orgId',
  remove_time datetime COMMENT '删除时间',
  PRIMARY KEY (id)
) COMMENT='审计程序';

create index i_audit_procedure_01 on audit_procedure(group_id);
create index i_audit_procedure_02 on audit_procedure(lib_id);

CREATE TABLE bnzg_value_change_log (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  source_id int8 COMMENT '关联实体id',
  source_name varchar(50) COMMENT '关联实体名',
  old_value int4 COMMENT '旧值',
  new_value int4 COMMENT '新值',
  operator_id varchar(30) COMMENT '操作人',
  op_time datetime COMMENT '操作时间',
  remark varchar(300) COMMENT '备注',
  PRIMARY KEY (id)
) COMMENT='值变动日志';

create index i_bnzg_value_change_log_01 on bnzg_value_change_log(source_id,source_name);

CREATE TABLE audit_plan (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  name varchar(150) COMMENT '名称',
  year int4 COMMENT '审计年份',
  status int2 COMMENT '状态',
  start_date date COMMENT '开始日期',
  end_date date COMMENT '结束日期',
  update_org_id varchar(30) COMMENT '更新人',
  remark varchar(1500) COMMENT '备注',
  PRIMARY KEY (id)
) COMMENT='审计计划';

CREATE TABLE audit_project (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  plan_id int8 COMMENT '计划id',
  code varchar(150) COMMENT '编码',
  name varchar(150) COMMENT '名称',
  start_date date COMMENT '开始日期',
  end_date date COMMENT '结束日期',
  project_phase int2 COMMENT '项目进度',
  startup_time datetime COMMENT '启动时间',
  remark varchar(1500) COMMENT '备注',
  PRIMARY KEY (id)
) COMMENT='审计项目';

create index i_audit_project_01 on audit_project(plan_id);

CREATE TABLE audit_project_procedure (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  project_id int8 COMMENT '项目id',
  procedure_id int8 COMMENT '审计程序id',
  PRIMARY KEY (id)
) COMMENT='项目程序';

create index i_audit_project_procedure_01 on audit_project_procedure(project_id);

CREATE TABLE audit_project_member (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  project_id int8 COMMENT '项目id',
  org_id varchar(30) COMMENT '人员orgId',
  remark varchar(1500) COMMENT '备注',
  role int2 COMMENT '角色',
  PRIMARY KEY (id)
) COMMENT='项目成员';

create index i_audit_project_member_01 on audit_project_member(project_id);

CREATE TABLE audit_pp_auth (
  id bigint NOT NULL COMMENT '主键',
  version int4 COMMENT '乐观锁',
  createtimestamp datetime COMMENT '创建时间',
  updatetimestamp datetime COMMENT '修改时间',
  project_id int8 COMMENT '计划id',
  procedure_id int8 COMMENT '审计程序id',
  org_id varchar(30) COMMENT '人员orgId',
  role int2 COMMENT '角色',
  PRIMARY KEY (id)
) COMMENT='项目程序权限';

create index i_audit_pp_auth_01 on audit_pp_auth(project_id,procedure_id,org_id);

