spring:
  h2:
    console:
      enable: true
  datasource:
    driver-class-name: org.h2.Driver
    schema:
      - classpath:db/schema/framework/*.sql
      - classpath:db/schema/lc/*.sql
      - classpath:db/schema/audit/*.sql
    data:
      - classpath:db/data/framework/*.sql
      - classpath:db/data/audit/*.sql
    url: jdbc:h2:mem:test;MODE=MYSQL;DB_CLOSE_ON_EXIT=TRUE;
    username: root
    password: test
    druid:
      validationQuery: SELECT 1
      testWhileIdle: true