package com.sinitek.sinicube.bnzg.audit;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import com.alibaba.excel.EasyExcel;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectExcelImportWrapperDTO;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectRiskExcelParseDTO;
import com.sinitek.bnzg.audit.project.excelimport.service.IAuditProjectExcelImportService;
import com.sinitek.sinicube.bnzg.SirmApplication;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2024-12-25 11:05
 */
@Slf4j
@ActiveProfiles({"local"})
@SpringBootTest(classes = SirmApplication.class)
public class ParseProjectExcelTest {

    @Autowired
    private IAuditProjectExcelImportService auditProjectExcelImportService;

    private String readFileName = "project_read.xlsx";

    private String readFilePath = "D:\\temp\\" + readFileName;

    private String writeFileName = "project_write.xlsx";

    private String writeFilePath = "D:\\temp\\" + writeFileName;

    @Test
    @Disabled
    public void testParseProcedureExcel() {
        ProjectExcelImportWrapperDTO info = this.auditProjectExcelImportService.readProjectImportExcel(
            readFileName, readFilePath);
        List<ProjectRiskExcelParseDTO> cachedDataList = info.getRisks();
        System.out.println("文件名: " + info.getFileName());
        System.out.println("共读取 " + cachedDataList.size() + " 条");
        for (ProjectRiskExcelParseDTO data : cachedDataList) {
            log.info("{}", JsonUtil.toJsonString(data));
        }
        EasyExcel.write(writeFilePath, ProjectRiskExcelParseDTO.class).sheet("解析后数据")
            .doWrite(cachedDataList);
        assertThat(true, is(true));
    }

}
