package com.sinitek.sinicube.bnzg.audit.support;

import static com.sinitek.bnzg.audit.lib.excelimport.constant.AuditLibraryExcelImportMessageCodeConstant.DATA_EXCEPTION;

import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.sinitek.sinicube.bnzg.audit.dto.ProjectRiskExcelProcessDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.LinkedList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-12-25 15:59
 */
@Slf4j
@NoArgsConstructor
public class ProjectRiskExcelPreImportListener implements ReadListener<ProjectRiskExcelProcessDTO> {

    // 默认缓存数据大小
    private static final int DEFAULT_CACHE_DATA_SIZE = 1000;

    private static final Pattern REPLACE_PATTERN = Pattern.compile("^(\n){2,}");

    // 数据
    private List<ProjectRiskExcelProcessDTO> list = new LinkedList<>();

    @Override
    public void invoke(ProjectRiskExcelProcessDTO data, AnalysisContext context) {
        if (log.isDebugEnabled()) {
            log.debug("解析到一条数据:{}", JsonUtil.toJsonString(data));
        }

        this.cleanHtmlTag(data, ProjectRiskExcelProcessDTO::getProblemDescription,
            data::setProblemDescription);
        this.cleanHtmlTag(data, ProjectRiskExcelProcessDTO::getManagementSuggestion,
            data::setManagementSuggestion);
        this.cleanHtmlTag(data, ProjectRiskExcelProcessDTO::getManagementResponse,
            data::setManagementResponse);

        list.add(data);
    }

    private void cleanHtmlTag(ProjectRiskExcelProcessDTO data,
        Function<ProjectRiskExcelProcessDTO, String> func, Consumer<String> consumer) {
        String value = func.apply(data);
        if (StringUtils.isNotBlank(value)) {
            String result = HtmlUtil.cleanHtmlTag(value.trim())
                .replaceAll("^(\n*)|(\n*)$", "")
                .replaceAll("&nbsp;", "");
//                .replaceAll("(\n)+", "\n");
            consumer.accept(result);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ReadSheetHolder readSheetHolder = context.readSheetHolder();
        ReadSheet readSheet = readSheetHolder.getReadSheet();
        String sheetName = readSheet.getSheetName();
        Integer sheetNo = readSheet.getSheetNo();
        log.info("sheetName: {},sheetNo: {} 解析完成", sheetName, sheetNo);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            ReadSheetHolder readSheetHolder = context.readSheetHolder();
            ReadSheet readSheet = readSheetHolder.getReadSheet();
            String sheetName = readSheet.getSheetName();
            Integer sheetNo = readSheet.getSheetNo();

            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            Integer rowIndex = excelDataConvertException.getRowIndex();
            Integer columnIndex = excelDataConvertException.getColumnIndex();
            String errMsg = String.format("第[%s]个名为[%s]sheet中第[%s]行，第[%s]列解析异常",
                sheetNo + 1, sheetName, rowIndex + 1, columnIndex + 1);
            log.error("sheetName: {},sheetNo: {},第{}行，第{}列解析异常", sheetName, sheetNo + 1,
                rowIndex + 1, columnIndex + 1);
            throw new BussinessException(DATA_EXCEPTION, errMsg);
        } else {
            throw exception;
        }
    }

    public List<ProjectRiskExcelProcessDTO> getAllData() {
        return list;
    }
}
