package com.sinitek.sinicube.bnzg.audit;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import cn.hutool.core.collection.CollUtil;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.sinitek.bnzg.audit.doc.dto.DocRenderParamDTO;
import com.sinitek.bnzg.audit.doc.util.AsposeUtil;
import com.sinitek.bnzg.audit.doc.util.WordUtil;
import com.sinitek.bnzg.audit.risk.dto.AuditReportTemplateSourceDataDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditReportTemplateSourceDataDTO.AuditGroupMemberItem;
import com.sinitek.bnzg.audit.risk.dto.AuditReportTemplateSourceDataDTO.RiskItem;
import com.sinitek.sinicube.bnzg.SirmApplication;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.io.File;
import java.text.ParseException;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 09/12/2024 10:37
 */
@Slf4j
@ActiveProfiles({"unit-test"})
@SpringBootTest(classes = SirmApplication.class)
public class AuditReportGenerateTest {

    private Date getDate(String dateStr) throws ParseException {
        return DateUtils.parseDateStrictly(dateStr, GlobalConstant.TIME_FORMAT_TEN);
    }

    @Test
    void test() throws Exception {
        File template = new File(
            "D:\\temp\\report_template.docx");
        DocRenderParamDTO param = new DocRenderParamDTO();
        param.setFileName("generate");
        param.setFileSuffix("docx");

        List<RiskItem> risks = new LinkedList<>();
        for (int i = 0; i < 50; i++) {
            risks.add(RiskItem.builder().index(i).name("风险点" + i)
                .level(1).levelName("高" + i)
                .audtiFinding("审计发现" + i).managementSuggestion("管理建议" + i)
                .managementResperName("管理责任人姓名" + i)
                .managementReply("管理答复" + i)
                .expendRectifyDate(getDate("2024-11-11"))
                .expendRectifyDateStr("2024-11-1" + i)
                .build());
        }

        AuditReportTemplateSourceDataDTO data = AuditReportTemplateSourceDataDTO.builder()
            .projectName("项目名称")
            .signDate(getDate("2024-09-13"))
            .signDateStr("2024-09-13")
            .auditPeriodStartDate(getDate("2024-09-14"))
            .auditPeriodStartDateStr("2024-09-14")
            .auditPeriodEndDate(getDate("2024-09-23"))
            .auditPeriodEndDateStr("2024-09-23")
            .mainAuditerId("1")
            .mainAuditerName("主审")
            .auditGroupMemberNames("复审1,复审2")
            .auditGroupMembers(CollUtil.toList(
                AuditGroupMemberItem.builder().orgId("2").name("复审1").build(),
                AuditGroupMemberItem.builder().orgId("3").name("复审2").build()))
            .risks(risks)
            .build();

        param.setData(JsonUtil.toMap(data));
        param.setConfigureBuilderConsumer((configureBuilder -> {
            configureBuilder.bind("risks", new LoopRowTableRenderPolicy());
            configureBuilder.bind("auditGroupMembers", new LoopRowTableRenderPolicy());
        }));

        File file = WordUtil.generateWord4File(template, param);

        if (Objects.nonNull(file)) {
            AsposeUtil.updateDocToc(file, new File("D:\\temp\\generate.docx"));
        }

        assertThat(true, is(true));
    }
}
