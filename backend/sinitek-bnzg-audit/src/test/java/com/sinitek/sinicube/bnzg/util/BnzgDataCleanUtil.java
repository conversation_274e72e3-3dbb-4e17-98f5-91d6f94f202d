package com.sinitek.sinicube.bnzg.util;

import com.sinitek.sinicube.bnzg.SirmApplication;
import com.sinitek.sinicube.sirm.lowcode.test.tool.LcBeanUnitTest;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 07/29/2024 18:03
 */
@ActiveProfiles({"unit-test"})
@SpringBootTest(
    classes = {SirmApplication.class}
)
@AutoConfigureMockMvc
public class BnzgDataCleanUtil extends LcBeanUnitTest {

    @Autowired
    protected Environment environment;

    @Override
    protected void cleanAllExampleData() throws SQLException, IOException {
        String[] activeProfiles = this.environment.getActiveProfiles();
        if (Arrays.stream(activeProfiles).allMatch((item) -> item.equals("unit-test"))) {
            this.runSqlScript("db/sql-run/delete_all_example_data.sql");
            this.runSqlScript("db/sql-run/delete_all_lowcode_table.sql");
            this.runSqlScript("db/sql-run/delete_all_audit_table.sql");
        } else {
            throw new IllegalStateException("当前不是单测环境,无法执行删除实例数据的SQL");
        }
    }

    protected void initModel() throws SQLException, IOException {
        this.runSqlScript("db/sql-run/model/audit-group/init.sql");
        this.runSqlScript("db/sql-run/model/audit-library/init.sql");
        this.runSqlScript("db/sql-run/model/audit-procedure/init.sql");
    }

}
