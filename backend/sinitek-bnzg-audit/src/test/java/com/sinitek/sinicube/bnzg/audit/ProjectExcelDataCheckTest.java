package com.sinitek.sinicube.bnzg.audit;

import static com.sinitek.bnzg.audit.lib.excelimport.constant.AuditLibraryExcelImportMessageCodeConstant.READ_FAIL;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportConstant;
import com.sinitek.sinicube.bnzg.audit.dto.ProjectRiskExcelProcessDTO;
import com.sinitek.sinicube.bnzg.audit.support.ProjectRiskExcelPreImportListener;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024-12-25 11:05
 */
@Slf4j
public class ProjectExcelDataCheckTest {

    private String readFileName1 = "project_read.xlsx";

    private String readFilePath1 = "D:\\temp\\" + readFileName1;

    private String readFileName2 = "project_read_2.xlsx";

    private String readFilePath2 = "D:\\temp\\" + readFileName2;

    private static List<String> NOT_CHECK_HTML_FRAGMENTS = new ArrayList<>(35);

    static {
        NOT_CHECK_HTML_FRAGMENTS.add("1573");
        NOT_CHECK_HTML_FRAGMENTS.add("1579");
        NOT_CHECK_HTML_FRAGMENTS.add("1581");
        NOT_CHECK_HTML_FRAGMENTS.add("2160");
        NOT_CHECK_HTML_FRAGMENTS.add("2163");
        NOT_CHECK_HTML_FRAGMENTS.add("2194");
        NOT_CHECK_HTML_FRAGMENTS.add("2203");
        NOT_CHECK_HTML_FRAGMENTS.add("2914");
        NOT_CHECK_HTML_FRAGMENTS.add("2915");
        NOT_CHECK_HTML_FRAGMENTS.add("2916");
        NOT_CHECK_HTML_FRAGMENTS.add("2917");
        NOT_CHECK_HTML_FRAGMENTS.add("2921");
        NOT_CHECK_HTML_FRAGMENTS.add("2945");
        NOT_CHECK_HTML_FRAGMENTS.add("2953");
        NOT_CHECK_HTML_FRAGMENTS.add("2955");
        NOT_CHECK_HTML_FRAGMENTS.add("2956");
        NOT_CHECK_HTML_FRAGMENTS.add("2965");
        NOT_CHECK_HTML_FRAGMENTS.add("2969");
        NOT_CHECK_HTML_FRAGMENTS.add("2971");
        NOT_CHECK_HTML_FRAGMENTS.add("2977");
        NOT_CHECK_HTML_FRAGMENTS.add("2979");
        NOT_CHECK_HTML_FRAGMENTS.add("2982");
        NOT_CHECK_HTML_FRAGMENTS.add("2983");
        NOT_CHECK_HTML_FRAGMENTS.add("2984");
        NOT_CHECK_HTML_FRAGMENTS.add("2985");
        NOT_CHECK_HTML_FRAGMENTS.add("2986");
        NOT_CHECK_HTML_FRAGMENTS.add("2991");
        NOT_CHECK_HTML_FRAGMENTS.add("2993");
        NOT_CHECK_HTML_FRAGMENTS.add("3000");
        NOT_CHECK_HTML_FRAGMENTS.add("3001");
        NOT_CHECK_HTML_FRAGMENTS.add("3005");
        NOT_CHECK_HTML_FRAGMENTS.add("3006");
        NOT_CHECK_HTML_FRAGMENTS.add("3010");
        NOT_CHECK_HTML_FRAGMENTS.add("3011");
        NOT_CHECK_HTML_FRAGMENTS.add("3012");
        NOT_CHECK_HTML_FRAGMENTS.add("3013");
        NOT_CHECK_HTML_FRAGMENTS.add("3014");
        NOT_CHECK_HTML_FRAGMENTS.add("3015");
        NOT_CHECK_HTML_FRAGMENTS.add("3016");
        NOT_CHECK_HTML_FRAGMENTS.add("3017");
        NOT_CHECK_HTML_FRAGMENTS.add("3018");
        NOT_CHECK_HTML_FRAGMENTS.add("3019");
        NOT_CHECK_HTML_FRAGMENTS.add("3020");
        NOT_CHECK_HTML_FRAGMENTS.add("3021");
        NOT_CHECK_HTML_FRAGMENTS.add("3022");
        NOT_CHECK_HTML_FRAGMENTS.add("3023");
        NOT_CHECK_HTML_FRAGMENTS.add("3024");
        NOT_CHECK_HTML_FRAGMENTS.add("3025");
    }


    @Test
    @Disabled
    public void testParseProcedureExcel() {
        boolean isCheckHtmlFragments = true;

        Map<String, ProjectRiskExcelProcessDTO> oldDataMap = this.toMap(
            this.readData(readFilePath1));

        Map<String, ProjectRiskExcelProcessDTO> newDataMap = this.toMap(
            this.readData(readFilePath2));

        oldDataMap.forEach((serialno, oldData) -> {
            ProjectRiskExcelProcessDTO newData = newDataMap.get(serialno);

            System.out.println("对比serialno: " + serialno);
            assertThat(newData, notNullValue());

            // 项目ID
            assertStrEqualTo(oldData.getProjectId(), newData.getProjectId());

            // 项目编码
            assertStrEqualTo(oldData.getProjectCode(), newData.getProjectCode());

            // 项目名称
            assertStrEqualTo(oldData.getProjectName(), newData.getProjectName());

            // 审计发现标题(名称)
            assertStrEqualTo(oldData.getAuditFindingTitle(), newData.getAuditFindingTitle());

            // 审计发现类型
            assertStrEqualTo(oldData.getAuditFindingType(), newData.getAuditFindingType());

            // 一级分类
            assertStrEqualTo(oldData.getFirstLevelClassification(),
                newData.getFirstLevelClassification());

            // 二级分类
            assertStrEqualTo(oldData.getSecondLevelClassification(),
                newData.getSecondLevelClassification());

            // 风险级别
            assertStrEqualTo(oldData.getRiskLevel(), newData.getRiskLevel());

            // 整改实施计划日期
            assertStrEqualTo(oldData.getRectificationPlanDate(),
                newData.getRectificationPlanDate());

            // 整改实施实际日期
            assertStrEqualTo(oldData.getRectificationActualDate(),
                newData.getRectificationActualDate());

            if (isCheckHtmlFragments) {

                // 部分 serialno 手动修订过数据不需要检查
                if (!NOT_CHECK_HTML_FRAGMENTS.contains(serialno)) {
                    // 问题描述
                    assertStrEqualTo(oldData.getProblemDescription(),
                        newData.getProblemDescription());

                    // 管理建议
                    assertStrEqualTo(oldData.getManagementSuggestion(),
                        newData.getManagementSuggestion());

                    // 管理答复
                    assertStrEqualTo(oldData.getManagementResponse(),
                        newData.getManagementResponse());
                }
            }
        });
    }

    private void assertStrEqualTo(String a, String b) {
        assertThat(StringUtils.trim(a),
            equalTo(StringUtils.trim(b)));
    }

    private Map<String, ProjectRiskExcelProcessDTO> toMap(List<ProjectRiskExcelProcessDTO> data) {
        return data.stream()
            .collect(Collectors.toMap(ProjectRiskExcelProcessDTO::getSerialno, v -> v));
    }

    private List<ProjectRiskExcelProcessDTO> readData(String filePath) {
        ProjectRiskExcelPreImportListener listener = new ProjectRiskExcelPreImportListener();
        ExcelReaderBuilder readerBuilder = EasyExcel.read(filePath,
            ProjectRiskExcelProcessDTO.class,
            listener);
        try (ExcelReader reader = readerBuilder.headRowNumber(
                AuditProjectExcelImportConstant.HEADER_ROW_NUMBER).extraRead(CellExtraTypeEnum.MERGE)
            .build()) {
            List<ReadSheet> readSheets = reader.excelExecutor().sheetList();

            ReadSheet readSheet1 = readSheets.get(0);
            log.info("sheetName: {}, sheetNo: {} 需要读取", readSheet1.getSheetName(),
                readSheet1.getSheetNo());

            reader.read(readSheet1);

            return listener.getAllData();
        } catch (Exception e) {
            log.error("读取审计项目风险点 excel文件 {} 失败,{}", filePath, e.getMessage(), e);
            if (e instanceof BussinessException) {
                throw e;
            } else {
                throw new BussinessException(READ_FAIL);
            }
        }
    }
}
