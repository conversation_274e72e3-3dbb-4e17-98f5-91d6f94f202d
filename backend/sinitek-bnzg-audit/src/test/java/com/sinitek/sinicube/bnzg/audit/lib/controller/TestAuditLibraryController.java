package com.sinitek.sinicube.bnzg.audit.lib.controller;

import com.jayway.jsonpath.JsonPath;
import com.sinitek.bnzg.audit.lib.constant.AuditLibraryModelConstant;
import com.sinitek.bnzg.audit.lib.constant.AuditLibraryStatusConstant;
import com.sinitek.bnzg.audit.lib.dao.AuditGroupDAO;
import com.sinitek.bnzg.audit.lib.dao.AuditLibraryDAO;
import com.sinitek.bnzg.audit.lib.dto.AuditLibraryDTO;
import com.sinitek.bnzg.audit.lib.entity.AuditGroup;
import com.sinitek.bnzg.audit.lib.entity.AuditLibrary;
import com.sinitek.bnzg.audit.lib.log.status.dao.AuditLibStatusLogDAO;
import com.sinitek.bnzg.audit.lib.log.status.entity.AuditLibStatusLog;
import com.sinitek.sinicube.bnzg.util.BnzgControllerUnitTestUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.lowcode.common.model.constant.LcModelConstant;
import com.sinitek.sirm.lowcode.model.dto.LcModelSaveOrUpdateOpParamDTO;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;

/**
 * <AUTHOR>
 * @date 07/25/2024 09:28
 */
@Slf4j
@ActiveProfiles("unit-test")
class TestAuditLibraryController extends BnzgControllerUnitTestUtil {

    @Autowired
    private AuditLibraryDAO dao;

    @Autowired
    private AuditGroupDAO groupDAO;

    @Autowired
    private AuditLibStatusLogDAO logDAO;

    @BeforeEach
    public void before() throws SQLException, IOException {
        this.after();
        this.initSqlRrunner();
        this.initModel();
    }

    @AfterEach
    public void after() throws SQLException, IOException {
        this.cleanAllExampleData();
    }

    @Test
    void testAuditLibSave() throws Exception {
        LcModelSaveOrUpdateOpParamDTO<AuditLibraryDTO> param = new LcModelSaveOrUpdateOpParamDTO<>();
        param.setModelCode(AuditLibraryModelConstant.MODEL_CODE);

        AuditLibraryDTO data = new AuditLibraryDTO();
        param.setData(data);

        data.setName("测试新增程序库");
        data.setRemark("这是备注");

        RequestBuilder request = this.post("/frontend/api/lowcode/model/dynamic/save",
            JsonUtil.toJsonString(param));
        MvcResult mvcResult = mockMvc.perform(request).andReturn();

        MockHttpServletResponse response = mvcResult.getResponse();
        String contentAsString = response.getContentAsString(StandardCharsets.UTF_8);

        Long id = Long.valueOf(JsonPath.read(contentAsString, "$.data"));

        AuditLibrary lib = this.dao.getById(id);
        this.shouldBeEquals(lib.getName(), "测试新增程序库");
        this.shouldBeEquals(lib.getRemark(), "这是备注");
        this.shouldBeEquals(lib.getStatus(), AuditLibraryStatusConstant.ENABLED);
        this.shouldBeEquals(lib.getRemoveFlag(), LcModelConstant.EXISTS_FLAG);

        List<AuditGroup> list = this.groupDAO.findAllExistsNodeByLibId(id);
        this.shouldHasSize(list, 1);
        AuditGroup group = list.get(0);
        this.shouldBeEquals(group.getName(), "测试新增程序库");
        this.shouldBeEquals(group.getParentId(), 0L);
        this.shouldBeEquals(group.getHighVal(), 0);

        List<AuditLibStatusLog> list2 = this.logDAO.findByLibId(id);
        this.shouldHasSize(list2, 1);
        AuditLibStatusLog item = list2.get(0);
        this.shouldBeEquals(item.getOldValue(), null);
        this.shouldBeEquals(item.getNewValue(), AuditLibraryStatusConstant.ENABLED);
    }

}
