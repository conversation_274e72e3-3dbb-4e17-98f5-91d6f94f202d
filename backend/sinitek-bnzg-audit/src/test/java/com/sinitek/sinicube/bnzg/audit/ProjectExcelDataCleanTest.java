package com.sinitek.sinicube.bnzg.audit;

import static com.sinitek.bnzg.audit.lib.excelimport.constant.AuditLibraryExcelImportMessageCodeConstant.READ_FAIL;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportConstant;
import com.sinitek.sinicube.bnzg.audit.dto.ProjectRiskExcelProcessDTO;
import com.sinitek.sinicube.bnzg.audit.support.ProjectRiskExcelPreImportListener;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2024-12-25 11:05
 */
@Slf4j
public class ProjectExcelDataCleanTest {

    private String readFileName = "project_read.xlsx";

    private String readFilePath = "D:\\temp\\" + readFileName;

    private String writeFileName = "project_write.xlsx";

    private String writeFilePath = "D:\\temp\\" + writeFileName;

    @Test
    @Disabled
    public void testParseProcedureExcel() {
        String filePath = readFilePath;

        ProjectRiskExcelPreImportListener listener = new ProjectRiskExcelPreImportListener();
        ExcelReaderBuilder readerBuilder = EasyExcel.read(readFilePath,
            ProjectRiskExcelProcessDTO.class,
            listener);
        try (ExcelReader reader = readerBuilder.headRowNumber(
                AuditProjectExcelImportConstant.HEADER_ROW_NUMBER).extraRead(CellExtraTypeEnum.MERGE)
            .build()) {
            List<ReadSheet> readSheets = reader.excelExecutor().sheetList();

            ReadSheet readSheet1 = readSheets.get(0);
            log.info("sheetName: {}, sheetNo: {} 需要读取", readSheet1.getSheetName(),
                readSheet1.getSheetNo());

            reader.read(readSheet1);

            List<ProjectRiskExcelProcessDTO> allData = listener.getAllData();

            System.out.println("共读取 " + allData.size() + " 条");
            for (ProjectRiskExcelProcessDTO data : allData) {
                log.info("{}", JsonUtil.toJsonString(data));
            }
            EasyExcel.write(writeFilePath, ProjectRiskExcelProcessDTO.class).sheet("解析后数据")
                .doWrite(allData);
            assertThat(true, is(true));
        } catch (Exception e) {
            log.error("读取审计项目风险点 excel文件 {} 失败,{}", filePath, e.getMessage(), e);
            if (e instanceof BussinessException) {
                throw e;
            } else {
                throw new BussinessException(READ_FAIL);
            }
        }
    }

}
