package com.sinitek.sinicube.bnzg.audit;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import com.sinitek.bnzg.audit.lib.excelimport.dto.ProcedureExcelImportWrapperDTO;
import com.sinitek.bnzg.audit.lib.excelimport.dto.ProcedureExcelParseDTO;
import com.sinitek.bnzg.audit.lib.excelimport.service.IAuditLibExcelImportService;
import com.sinitek.sinicube.bnzg.SirmApplication;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2024-12-25 11:05
 */
@Slf4j
@ActiveProfiles({"local"})
@SpringBootTest(classes = SirmApplication.class)
public class ParseProcedureExcelTest {

    @Autowired
    private IAuditLibExcelImportService procedureExcelImportService;

    private String fileName = "2024年审计程序库1.xlsx";

    private String filePath = "D:\\temp\\" + fileName;

    @Test
    @Disabled
    public void testParseProcedureExcel() {
        String fileName = "2024年审计程序库1.xlsx";
        String filePath = "D:\\temp\\" + fileName;
        ProcedureExcelImportWrapperDTO info = this.procedureExcelImportService.readProcedureImportExcel(
            fileName, filePath);
        List<ProcedureExcelParseDTO> cachedDataList = info.getProcedures();
        System.out.println("文件名: " + info.getFileName());
        System.out.println("审计程序库: " + info.getLibName());
        System.out.println("审计程序库审计年份: " + info.getLibYear());
        System.out.println("共读取 " + cachedDataList.size() + " 条");
        for (ProcedureExcelParseDTO data : cachedDataList) {
            log.info("{}", JsonUtil.toJsonString(data));
        }
        assertThat(true, is(true));
    }

}
