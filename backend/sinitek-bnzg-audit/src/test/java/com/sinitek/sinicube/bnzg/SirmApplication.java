package com.sinitek.sinicube.bnzg;

import com.sinitek.cloud.base.support.ActionTrackInterceptor;
import java.util.Collections;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@ComponentScan(value = {"com.sinitek"})
@EnableFeignClients(basePackages = {"com.sinitek"})
@PropertySource(value = "classpath:sinicube.properties")
@MapperScan("com.sinitek.**.mapper")
public class SirmApplication {

    public static void main(String[] args) {
        SpringApplication.run(SirmApplication.class, args);
    }

    @Autowired
    private ActionTrackInterceptor actionTrackInterceptor;

    @Bean("restTemplate")
    @LoadBalanced
    public RestTemplate getRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(actionTrackInterceptor));
        return restTemplate;
    }

}
