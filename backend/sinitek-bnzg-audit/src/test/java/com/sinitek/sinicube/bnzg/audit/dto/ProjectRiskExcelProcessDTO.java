package com.sinitek.sinicube.bnzg.audit.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024-12-31 10:10
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目风险点excel解析dto")
public class ProjectRiskExcelProcessDTO {

    @ExcelProperty(value = "serialno")
    @ApiModelProperty("serialno")
    private String serialno;

    @ExcelProperty(value = "项目ID")
    @ApiModelProperty("项目ID")
    private String projectId;

    @ExcelProperty(value = "项目编码")
    @ApiModelProperty("项目编码")
    private String projectCode;

    @ExcelProperty(value = "项目名称")
    @ApiModelProperty("项目名称")
    private String projectName;
    
    @ExcelProperty(value = "审计发现标题")
    @ApiModelProperty("审计发现标题(名称)")
    private String auditFindingTitle;

    @ExcelProperty(value = "审计发现类型")
    @ApiModelProperty("审计发现类型(类型)")
    private String auditFindingType;

    @ExcelProperty(value = "一级分类")
    @ApiModelProperty("一级分类")
    private String firstLevelClassification;

    @ExcelProperty(value = "二级分类")
    @ApiModelProperty("二级分类")
    private String secondLevelClassification;

    @ExcelProperty(value = "问题描述")
    @ApiModelProperty("问题描述(描述)")
    private String problemDescription;

    @ExcelProperty(value = "风险级别")
    @ApiModelProperty("风险级别(风险级别)")
    private String riskLevel;

    @ExcelProperty(value = "管理建议")
    @ApiModelProperty("管理建议(审计建议)")
    private String managementSuggestion;

    @ExcelProperty(value = "管理答复")
    @ApiModelProperty("管理答复(责任部门意见反馈)")
    private String managementResponse;

    @ExcelProperty(value = "整改实施计划日期")
    @ApiModelProperty("整改实施计划日期(要求整改日期)")
    private String rectificationPlanDate;

    @ExcelProperty(value = "整改实施实际日期")
    @ApiModelProperty("整改实施实际日期(实际整改日期)")
    private String rectificationActualDate;

}
