package com.sinitek.sinicube.bnzg.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.sinitek.sinicube.bnzg.SirmApplication;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.StreamUtils;

/**
 * <AUTHOR>
 * @date 07/29/2024 18:06
 */
@Slf4j
@ActiveProfiles("unit-test")
@SpringBootTest(classes = SirmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class BnzgControllerUnitTestUtil extends BnzgDataCleanUtil {

    @Autowired
    protected MockMvc mockMvc;

    protected RequestBuilder postForJsonFile(String url, String jsonFile) {
        String json = readJsonFile(jsonFile);
        return post(url, json);
    }

    protected RequestBuilder post(String url, String json) {
        return MockMvcRequestBuilders
            .post(url)
            .contentType(MediaType.APPLICATION_JSON)
            .content(json);
    }

    protected RequestBuilder post(String url, Map<String, Object> urlParam)
        throws InvocationTargetException, IllegalAccessException {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders
            .post(url)
            .contentType(MediaType.APPLICATION_JSON)
            .headers(new HttpHeaders());
        appendUrlParam(builder, urlParam);
        return builder;
    }

    protected RequestBuilder postXWWWFormUrlEncoded(String url, Object param)
        throws InvocationTargetException, IllegalAccessException {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders
            .post(url)
            .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
            .headers(new HttpHeaders());
        appendUrlParam(builder, param);
        return builder;
    }

    protected RequestBuilder get(String url, Object param)
        throws InvocationTargetException, IllegalAccessException {
        return get(url, param, null);
    }

    protected RequestBuilder get(String url, Object param, Map<String, String> headers)
        throws InvocationTargetException, IllegalAccessException {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders
            .get(url)
            .contentType(MediaType.APPLICATION_JSON);

        if (CollUtil.isNotEmpty(headers)) {
            headers.forEach(builder::header);
        }

        appendUrlParam(builder, param);

        return builder;
    }


    protected <T> T getRequestResult(MvcResult mvcResult, Class<T> clazz)
        throws UnsupportedEncodingException {
        MockHttpServletResponse response = mvcResult.getResponse();
        String contentString = response.getContentAsString();
        Map<String, Object> result = JsonUtil.toMap(contentString);
        return JsonUtil.toJavaObject(MapUtils.getString(result, "data"), clazz);
    }

    protected MvcResult checkSuccessResponse(ResultActions perform) throws Exception {
        return perform.andDo(item -> {
                String contentAsString = item.getResponse().getContentAsString(StandardCharsets.UTF_8);
                log.info("请求结果: {}", contentAsString);
            }).andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.resultcode",
                Matchers.is(RequestResult.SUCCESS_CODE)))
            .andReturn();
    }

    protected <T> MvcResult checkUnSuccessResponse(ResultActions perform, String resultCode,
        String message, Matcher<T> dataMatcher) throws Exception {
        return perform.andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.resultcode", Matchers.is(resultCode)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.message", Matchers.is(message)))
            .andExpect(MockMvcResultMatchers.jsonPath("$.data", dataMatcher))
            .andReturn();
    }

    protected File getExportFile(MvcResult mvcResult, String filePrefix, String fileSuffix)
        throws IOException {
        File tempFile = File.createTempFile(filePrefix, fileSuffix);
        tempFile.deleteOnExit();

        try (FileOutputStream fout = new FileOutputStream(tempFile)) {
            ByteArrayInputStream bin = new ByteArrayInputStream(
                mvcResult.getResponse().getContentAsByteArray());
            StreamUtils.copy(bin, fout);
        }
        log.info("导出文件位置: {}", tempFile.getAbsolutePath());
        return tempFile;
    }

    protected void appendUrlParam(MockHttpServletRequestBuilder builder, Object param)
        throws InvocationTargetException, IllegalAccessException {
        if (Objects.nonNull(param)) {
            if (param instanceof Map) {
                Map<String, Object> paramMap = (Map) param;
                Iterator<Entry<String, Object>> iterator = paramMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Entry<String, Object> next = iterator.next();
                    Object value = next.getValue();
                    String key = next.getKey();
                    setParam(builder, key, value);
                }
            } else {
                BeanWrapperImpl beanWrapper = new BeanWrapperImpl(param);
                PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();
                for (PropertyDescriptor property : propertyDescriptors) {
                    Method readMethod = property.getReadMethod();
                    if (Objects.nonNull(readMethod)) {
                        Object value = readMethod.invoke(param);
                        setParam(builder, property.getName(), value);
                    }
                }
            }
        }
    }

    private void setParam(MockHttpServletRequestBuilder builder, String name, Object value) {
        if (Objects.nonNull(value)) {
            if (ArrayUtil.isArray(value)) {
                String[] arrays = new String[((Object[]) value).length];
                int i = 0;
                for (Object o : ((Object[]) value)) {
                    arrays[i++] = o.toString();
                }
                builder.param(name, arrays);
            } else if (value instanceof Collection) {
                String[] arrays = new String[((Collection<?>) value).size()];
                Iterator<Object> iterator = ((Collection<Object>) value).stream().iterator();
                int i = 0;
                while (iterator.hasNext()) {
                    Object next = iterator.next();
                    arrays[i++] = next.toString();
                }
                builder.param(name, arrays);
            } else {
                builder.param(name, value.toString());
            }
        } else {
            builder.param(name, null);
        }
    }

}
