#com.sinitek.sirm.bnzg 99
#通用 9901
#审计系统 9902
#程序库管理 9902001
#审计计划管理 9902002
#审计项目管理 9902003
#审计项目管理 9902004
#审计项目审计程序管理 9902004001
#文档类型管理 9902005
#文档类型方案管理 9902006
#文档管理 9902007
#文档上传表格 9902008
#文档生成 99020080001
#风险点 9902009
#风险点审批 9902010
#风险点查询统计 9902011
#程序库管理 9902001
9902001001=启用审计程序库时,传入数据不能为空
9902001002=关闭审计程序库时,传入数据不能为空
9902001003=复制审计程序库时,被复制数据不存在
9902001004=分组[{0}]下含有审计程序,无法删除
9902001005=当前审计程序所在程序库已被关闭,无法删除该审计程序
9902001006=当前审计程序所在程序库不存在,无法继续
9902001007=复制审计程序库时,当前名称[{0}]已存在
9902001008=无法将当前分组移动至[{0}]的同级位置
9902001009=存在被项目引用的审计程序库,无法删除
9902001010=被删除的审计程序存在风险点,无法删除
9902001011=被删除的审计程序在所在项目中不为未审计或草稿状态,无法删除
# 审计程序excel导入 9902001012
9902001012001=解析excel后,需要导入的sheet数据为空
9902001012002=数据异常: {0}
9902001012003=读取excel文件失败
9902001012004=excel文件中sheet名称{0}重复
9902001012005=系统中不存在{0}的部门
9902001012006=excel文件中sheet名称[{0}]不符合[x-部门名]的格式
9902001012007=文件名[{0}]格式不正确，请检查文件名前4位是否为年份数字
9902001012008=审计程序库保存失败: {0}
9902001012009=审计程序分组[{0}]保存失败: {1}
9902001012010=审计程序[{0}]保存失败: {1}
9902001012011=上传文件为空
9902001012012=只支持上传单个excel文件
9902001012013=根据模块[{0}]没有找到对应的sheet
9902001012014=根据风险点[{0}]没有找到对应的模块
9902001012015=根据模块[{0}]没有找到对应的sheet[{1}]的id
9902001012016=根据风险点[{0}]没有找到对应的模块[{1}]的id
9902001012017=根据细化风险点[{0}]没有找到对应的分组数据
#审计计划管理 9902002
9902002001=被复制的审计计划不存在
9902002002=当前审计计划已完成,无法更新为进行中状态
9902002003=当前审计计划未开始,无法更新为已完成状态
9902002004=存在进行中或者已完成的计划,无法删除
9902002005=复制审计计划时,当前名称[{0}]已存在
#审计项目管理 9902003
9902003001=当前项目不存在
9902003002=当前项目进度为[{0}]无法启动
9902003003=存在阶段已启动的项目,无法删除
9902003004=保存项目审计程序时,没有新数据保存
9902003005=[{0}]已经是项目负责人,无法保存新角色
9902003006=该项目下,被删除的审计程序存在风险点,无法删除
9902003007=当前审计项目未配置项目负责人,无法保存审计程序
9902003008=当前登录人不是当前审计项目的负责人,无法保存审计程序
9902003009=当前审计项目未配置项目负责人,无法保存项目成员
9902003010=当前登录人不是当前审计项目的负责人,无法保存项目成员
9902003011=当前审计项目未配置项目负责人,无法保存项目成员权限
9902003012=当前登录人不是当前审计项目的负责人,无法保存项目成员权限
9902003013=当前审计项目未配置相关角色,无法查看项目数据
9902003014=当前登录人不属于当前项目的相关角色人员,无法查看项目数据
9902003015=当前审计项目项目进度为[{0}],不可再编辑
9902003016=当前审计项目项目进度为[{0}],无法设置团队成员
9902003017=当前审计项目项目进度为[{0}],无法设置审计程序
9902003018=当前审计项目项目进度为[{0}],无法删除审计程序
9902003019=实际完成日期不能超过要求完成整改日期
9902003020=目前状态已经是整改完成，无法再次进行整改反馈操作
9902003021=目前状态已经是整改完成，无法再次进行整改延期操作
9902003022=目前状态已经是整改完成，无法再次保存整改联系人
9902003023=目前状态已经是整改延期，无法再次进行整改反馈操作
9902003024=数据不存在无法变更
9902003025=仅状态为审批通过,不通过才能变更
9902003026=项目审计程序数据为空,无法保存
9902003027=项目审计程序为审批中状态,无法保存
9902003028=延期日期与预计整改完成日期不能相等
9902003029=当前审计程序存在风险点无法变更
9902003030=当前项目进度为[{0}]无法重开
9902003031=当前项目审计程序非最新数据,无法变更
9902003032=当前数据不存在,无法取消变更
9902003033=当前项目审计程序非最新数据,无法取消变更
9902003034=当前项目审计程序非草稿状态,无法取消变更
9902003035=当前项目审计程序不存在变更前数据,无法取消变更
9902003036=当前项目审计程序不存在需要提交的数据
9902003037=当前项目进度为[{0}]无法关闭
9902003038=当前审计项目未配置项目负责人,无法中止项目
9902003039=当前登录人不是当前审计项目的负责人,无法中止项目
9902003040=审计项目不存在,无法操作
9902003041=审计项目已中止,无法操作
9902003042=项目审计程序不存在,无法操作
9902003043=当前项目进度为[{0}]无法中止
9902003044=当前数据非最新,无法{0}
#审计项目管理 9902004
9902004001=当前项目不存在审计阶段
9902004002=无法获取阶段步骤实例数据
9902004003=当前阶段步骤状态为[{0}]无法更新为完成状态
9902004004=当前项目已存在阶段实例
9902004005=当前项目不存在阶段实例
9902004006=产品[{0}]缺少对应的阶段实例
9902004007=产品[{0}]缺少对应的步骤数据
#审计项目审计程序管理 9902004001
9902004001001=待审批审计程序数据为空,无法提交
9902004001002=审计程序存在风险点,无法提交
9902004001003=当前项目缺少除[{0}]之外的审批人,无法提交项目审计程序审批
9902004001004=当前审计项目未配置项目审批人员,无法审批
9902004001005=当前登录人不是当前审计项目的审批人员,无法审批
9902004001006=审计程序[{0}]为未审计数据,无法提交
9902004001007=审计程序[{0}]非草稿状态且不存在风险点,无法提交
9902004001008=审计程序[{0}]非草稿状态且没有待审批的风险点,无法提交
9902004001009=审计程序[{0}]缺少审计人数据,无法提交
9902004001010=审计程序[{0}]为审批中状态,无法提交
9902004001011=审计程序[{0}]为审批不通过状态,无法提交
9902004001012=审计程序不存在,无法创建审批任务
9902004001013=审计程序[{0}]对应审计项目不存在,无法创建审批任务
9902004001014=审计程序[{0}]对应审计计划不存在,无法创建审批任务
9902004001015=审批数据为空,保存失败
#文档类型管理 9902005
9902005001=名称[{0}]已存在
9902005002=类型值[{0}]已存在
9902005003=顺序[{0}]已存在
9902005004=文档保存时,附件复制失败
#文档类型方案管理 9902006
#文档管理 9902007
9902007001=上传的附件不能为空
9902007002=文档数据不存在
9902007003=当前文档非最新有效版本,无法更新
9902007004=被下载的文档数据不存在
9902007005=文档批量下载失败
9902007006=存在文档数据非最新有效无法被删除
9902007007=删除文档时,传入数据为空
9902007008=删除文档时,文档数据不存在
9902007009=当前审计项目项目进度为[{0}]状态,无法删除文档
9902007010=当前审计项目项目进度为[{0}]状态,无法再新增文档
9902007011=当前审计项目项目进度为[{0}]状态,无法再更新文档
9902007012=当前审计项目项目进度为[{0}]状态,无法再编辑文档
9902007013=当前文档类型[{0}]模板不存在，无法生成文档
9902007014=当前文档类型模板存在[{0}]个
9902007015=当前项目[{0}]生成审计报告失败
9902007016001=获取本地文件失败
9902007016002=解压文件失败
9902007016003=不存在名为[{0}]的审计计划
9902007016004=[{0}]下不存在名为[{1}]的审计项目
9902007016005=[{0}]下不存在名为[{1}]的项目审计程序/文档类型
9902007016006=[{0}]下不存在名为[{1}]的风险点/文档类型
9902007016007=[{0}]下不存在名为[{1}]的文档类型
9902007016008=[{0}]文件大小为0,无法上传
9902007016009=项目下审计程序名称重复无法导入
#文档上传表格 9902008
9902008001=文档数据不能为空
9902008002=该产品不存在最新有效的文档数据
9902008003=文档确认时,对应文档数据不可为空
9902008004=文档确认时,文档类型为[{0}]的数据非最新,请刷新页面后重试
9902008005=当前节点已上传过该文档类型的数据,只允许编辑,无法重复上传
#文档生成 99020080001
99020080001=更新文档目录失败
99020080002=加载系统字体目录失败
#文档同步 9902008006
9902008006001=文档同步时,审计项目不存在
#风险点 9902009
com.sinitek.bnzg.audit.risk.name_can_not_exceed=名称不能超过100个字符
com.sinitek.bnzg.audit.risk.description_can_not_exceed=描述不能超过500个字符
com.sinitek.bnzg.audit.risk.sys_basis_can_not_exceed=制度依据不能超过500个字符
com.sinitek.bnzg.audit.risk.innercategory_can_not_exceed=内部分类不能超过10个字符
com.sinitek.bnzg.audit.risk.auditsuggestion_can_not_exceed=审计建议不能超过500个字符
com.sinitek.bnzg.audit.risk.respdeptsuggestion_can_not_exceed=责任部门建议不能超过500个字符
com.sinitek.bnzg.audit.risk.rectifyfeedback_can_not_exceed=整改反馈不能超过500个字符
9902009001=风险点[{0}]不是草稿状态,无法删除
9902009002=风险点不存在
9902009003=当前风险点不为草稿状态,无法编辑
9902009004=当前风险点不为审批通过,审批不通过状态,无法变更
9902009005=当前风险点不为最新数据,无法变更
9902009006=当前风险点未审批通过,无法保存审计建议
9902009007=当前风险点不为草稿状态,无法取消变更
9902009008=当前风险点不为最新数据,无法取消变更
9902009009=当前风险点不存在变更前数据,无法取消变更
#风险点审批 9902010
9902010001=当前项目不存在审批人员,无法提交
9902010002=风险点数据为空,无法提交
9902010003=只允许提交草稿状态的数据
9902010004=存在未审批的数据无法提交
9902010005=数据不存在无法提交
9902010006=当前数据已提交
9902010007=被审批的数据不存在
9902010008=当前数据已提交,无法再次审批
9902010009=只允许提交最新的数据
9902010010=被审批数据所属审计项目不存在
9902010011=当前审计项目未配置项目审批人员,无法审批风险点
9902010012=当前登录人不是当前审计项目的审批人员,无法审批风险点
9902010013=当前审计项目未配置项目审批人员,无法提交风险点审批
9902010014=当前登录人不是当前审计项目的审批人员,无法提交风险点审批
9902010015=当前项目缺少除[{0}]之外的审批人,无法提交风险点审批
9902010016=无法提交有风险点的审计程序
9902010017=审计程序[{0}]未审批,无法提交
9902010018=审计程序[{0}]下风险点[{1}]未审批,无法提交
9902010019=当前数据已中止,无法再次审批
# 项目风险点excel导入 9902010019001
9902010019001=excel中serialno为[{0}]的项目编码为空
9902010019002=excel中serialno为[{0}]的项目编码[{1}]前4位不为年份数字
9902010019003=excel中serialno为[{0}]的审计发现类型[{1}]无法匹配
9902010019004=excel中serialno为[{0}]的风险级别[{1}]无法匹配
9902010019005=excel中serialno为[{0}]的一级分类[{1}]无法匹配
9902010019006=枚举[{0},{1}]枚举名称非唯一,数据处理异常
9902010019007=待保存数据为空,无法导入
9902001019008=读取excel文件失败
9902001019009=审计项目[{0}]找不到匹配的审计计划[{1}]
9902001019010=导入数据时,默认项目启动人不能为空
9902001019011=导入数据时,默认项目所有人不能为空
9902001019012=导入数据时,默认项目成员不能为空
9902001019013=导入数据时,默认审计程序库不能为空
9902001019014=导入数据时,默认审计程序库下审计程序不能为空
9902001019015=导入数据时,项目[{0}]下审计程序[{1}]不存在对应项目审计程序数据
9902001019016=导入数据时,无法获取项目[{0}]对应实例数据
#风险点查询统计 9902011
9902011001=传入的内部分类调整对比数据为空
9902011002=过滤传入的内部分类后没有找到可更新的内部分类
9902011003=没有通过当前审计年度找到风险点
#风险点问责管理 9902012
9902012001=当前风险点已存在问责管理数据,请前往问责结果管理查看
9902012002=当前风险点未审批通过，无法添加问责管理数据
9902012003=当前风险点责任部门意见不同意，无法添加问责管理数据
9902012004=未选择风险点，无法添加问责管理数据
