package com.sinitek.bnzg.audit.risk.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryResultDTO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryResultPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date：2024/11/12 9:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskHistoryUtil {

    public static AuditRiskHistoryParamPO makeSearchParamDTO2PO(AuditRiskHistoryParamDTO dto) {
        Long riskId = dto.getRiskId();
        String description = dto.getDescription();
        List<Long> procedureIds = dto.getProcedureIds();
        List<String> respDeptIds = dto.getRespDeptIds();
        List<String> respManIds = dto.getRespManIds();
        List<Integer> firstCatalog = dto.getFirstCatalog();
        List<String> innerCategory = dto.getInnerCategory();
        String riskName = dto.getRiskName();

        AuditRiskHistoryParamPO po = new AuditRiskHistoryParamPO();

        if (CollUtil.isNotEmpty(procedureIds)) {
            po.setProcedureIds(procedureIds);
        }
        if (CollUtil.isNotEmpty(respDeptIds)) {
            po.setRespDeptIds(respDeptIds);
        }
        if (CollUtil.isNotEmpty(respManIds)) {
            po.setRespManIds(respManIds);
        }
        if (ObjectUtil.isNotEmpty(riskId)) {
            po.setRiskId(riskId);
        }
        List<String> descriptions = CommonStringUtil.toSearchStrList(description);
        if (CollUtil.isNotEmpty(descriptions)) {
            po.setDescriptions(descriptions);
        }
        List<String> riskNames = CommonStringUtil.toSearchStrList(riskName);
        if (ObjectUtil.isNotEmpty(riskNames)) {
            po.setRiskNames(riskNames);
        }
        if (CollUtil.isNotEmpty(firstCatalog)) {
            po.setFirstCatalogs(firstCatalog);
        }
        if (CollUtil.isNotEmpty(innerCategory)) {
            po.setInnerCategorys(innerCategory);
        }

        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());

        return po;
    }

    public static AuditRiskHistoryResultDTO makeResultPO2DTO(AuditRiskHistoryResultPO po) {
        return LcConvertUtil.convert(po, AuditRiskHistoryResultDTO::new);
    }

}
