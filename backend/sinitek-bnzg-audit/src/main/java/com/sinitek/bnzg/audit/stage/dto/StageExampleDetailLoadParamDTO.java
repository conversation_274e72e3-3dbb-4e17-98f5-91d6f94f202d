package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@Data
@ApiModel("阶段实例详情查询参数")
public class StageExampleDetailLoadParamDTO {

    @NotNull(message = "审计项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

}
