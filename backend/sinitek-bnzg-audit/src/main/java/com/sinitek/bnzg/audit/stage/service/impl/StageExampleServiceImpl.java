package com.sinitek.bnzg.audit.stage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.bnzg.audit.stage.constant.StageStatusConstant;
import com.sinitek.bnzg.audit.stage.dao.StageExampleDAO;
import com.sinitek.bnzg.audit.stage.dto.BatchGenerateStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.entity.StageExample;
import com.sinitek.bnzg.audit.stage.log.status.stage.util.StageExampleStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.stage.service.IStageExampleService;
import com.sinitek.bnzg.audit.stage.util.StageExampleConvertUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 阶段实例 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Slf4j
@Service
public class StageExampleServiceImpl implements IStageExampleService {

    @Autowired
    private StageExampleDAO dao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateStageExample(GenerateStageExampleParamDTO param) {
        Long projectId = param.getProjectId();
        String opOrgId = param.getOpOrgId();
        Date opTime = param.getOpTime();
        Boolean publishEventFlag = param.getPublishEventFlag();

        int newStatus = StageStatusConstant.READY;

        StageExample example = new StageExample();
        example.setProjectId(projectId);
        example.setStatus(newStatus);
        this.dao.save(example);
        Long id = example.getId();

        if (Objects.equals(Boolean.TRUE, publishEventFlag)) {
            StageExampleStatusChangeEventPublishUtil.publishEvent(
                RecordChangeLogAddParamDTO.builder()
                    .foreignKey(id)
                    .oldValue(null)
                    .newValue(newStatus)
                    .operatorId(opOrgId)
                    .opTime(opTime)
                    .remark("阶段实例生成")
                    .build());
        } else {
            log.info("当前无需抛出阶段实例[{}]生成事件", id);
        }

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StageExampleDTO> batchGenerateStageExamples(
        BatchGenerateStageExampleParamDTO param) {
        List<Long> projectIds = param.getProjectIds();
        String opOrgId = param.getOperatorId();
        Date opTime = param.getOpTime();
        Integer stageExampleStatus = param.getStageExampleStatus();
        Boolean publishEventFlag = param.getPublishEventFlag();

        if (CollUtil.isNotEmpty(projectIds)) {
            List<StageExample> needSaveExamples = projectIds.stream().map(projectId -> {
                StageExample example = new StageExample();
                example.setProjectId(projectId);
                example.setStatus(stageExampleStatus);
                return example;
            }).collect(Collectors.toList());

            this.dao.saveBatch(needSaveExamples);

            Map<Long, Integer> idAndStatusMap = needSaveExamples.stream()
                .collect(Collectors.toMap(StageExample::getId, StageExample::getStatus));

            if (Objects.equals(Boolean.TRUE, publishEventFlag)) {
                StageExampleStatusChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                        .foreignKeys(idAndStatusMap.keySet())
                        .oldValueMap(Collections.emptyMap())
                        .newValueMap(idAndStatusMap)
                        .operatorId(opOrgId)
                        .opTime(opTime)
                        .remark("阶段实例批量生成")
                        .build());
            } else {
                log.info("当前无需抛出阶段实例[{}]生成事件", projectIds);
            }

            return needSaveExamples.stream().map(StageExampleConvertUtil::makeEntity2DTO)
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<StageExampleDTO> findByProjectIds(Collection<Long> projectIds) {
        if (CollUtil.isNotEmpty(projectIds)) {
            LambdaQueryWrapper<StageExample> queryWrapper = Wrappers.lambdaQuery(
                StageExample.class);
            queryWrapper.in(StageExample::getProjectId, projectIds);
            List<StageExample> stageExamples = this.dao.list(queryWrapper);
            if (CollUtil.isNotEmpty(stageExamples)) {
                return stageExamples.stream().map(StageExampleConvertUtil::makeEntity2DTO)
                    .collect(Collectors.toList());
            } else {
                log.warn("根据阶段实例项目id[{}]获取到阶段实例数据为空", projectIds);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public StageExampleDTO getStageExampleById(Long id) {
        StageExample stageExample = this.dao.getById(id);
        return StageExampleConvertUtil.makeEntity2DTO(stageExample);
    }

}