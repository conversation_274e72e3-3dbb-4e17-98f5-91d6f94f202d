package com.sinitek.bnzg.audit.stage.enumation;

import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:15
 */
@Slf4j
@Getter
public enum StageStepEnum {
    AUDIT_PREPARING("审计准备", StageStepConstant.AUDIT_PREPARING),
    AUDIT_EXECUTION("审计实施", StageStepConstant.AUDIT_EXECUTION),
    AUDIT_REPORT("审计报告", StageStepConstant.AUDIT_REPORT),
    RECTIFICATION_TRACKING("整改跟踪", StageStepConstant.RECTIFICATION_TRACKING);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    StageStepEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static Map<Integer, String> getValueAndNameMap() {
        return Arrays.stream(values()).collect(
            Collectors.toMap(StageStepEnum::getValue, StageStepEnum::getName));
    }
}
