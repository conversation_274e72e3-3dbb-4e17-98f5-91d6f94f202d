package com.sinitek.bnzg.audit.project.procedure.approve.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpRiskApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditPpApproveResultSearchResultFormat implements
    ITableResultFormat<PpApproveResultSearchResultDTO> {

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IRectifyContactService rectifyContactService;

    @Override
    public List<PpApproveResultSearchResultDTO> format(
        List<PpApproveResultSearchResultDTO> data) {
        List<String> orgIds = new LinkedList<>();
        List<Long> procedureIds = new LinkedList<>();

        data.forEach(item -> {
            orgIds.add(item.getAuditorId());
            orgIds.add(item.getOperatorId());

            List<PpRiskApproveResultSearchResultDTO> risks = item.getChildren();
            if (CollUtil.isNotEmpty(risks)) {
                risks.forEach(risk -> {
                    orgIds.add(risk.getAuditorId());
                    orgIds.add(risk.getOperatorId());
                });
            }

            procedureIds.add(item.getProcedureId());
        });
        // key: orgId
        // value: 名称
        Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
            orgIds.stream().distinct().collect(Collectors.toList()));

        Map<Long, String> procedureIdAndNameMap = this.getProcedureMap(
            procedureIds.stream().distinct().collect(Collectors.toList()));

        // key: 类型值字符串
        // value: 名称
        // 风险类型
        Map<String, String> riskTypeMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_TYPE);
        // 风险级别
        Map<String, String> riskLevelMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_LEVEL);
        // 一级分类
        Map<String, String> firstCatalogMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.FIRST_CATALOG);
        // key: 类型值字符串
        // value: 名称
        // 审批结果
        Map<String, String> approveResultMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_APPROVE_RESULT);

        data.forEach(item -> {
            item.setName(MapUtils.getString(procedureIdAndNameMap, item.getProcedureId()));

            item.setApproveResultName(
                MapUtils.getString(approveResultMap, String.valueOf(item.getApproveResult())));

            item.setAuditorName(MapUtils.getString(orgIdAndNameMap, item.getAuditorId()));
            item.setOperatorName(MapUtils.getString(orgIdAndNameMap, item.getOperatorId()));

            List<PpRiskApproveResultSearchResultDTO> risks = item.getChildren();
            if (CollUtil.isNotEmpty(risks)) {
                risks.forEach(risk -> {
                    risk.setTypeName(
                        MapUtils.getString(riskTypeMap, String.valueOf(risk.getType())));
                    risk.setLevelName(
                        MapUtils.getString(riskLevelMap, String.valueOf(risk.getLevel())));
                    risk.setApproveResultName(
                        MapUtils.getString(approveResultMap,
                            String.valueOf(risk.getApproveResult())));
                    risk.setFirstCatalogName(MapUtils.getString(firstCatalogMap,
                        String.valueOf(risk.getFirstCatalog())));

                    risk.setAuditorName(MapUtils.getString(orgIdAndNameMap, risk.getAuditorId()));
                    risk.setOperatorName(MapUtils.getString(orgIdAndNameMap, risk.getOperatorId()));
                });
            }

        });

        return data;
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<Long, String> getProcedureMap(Collection<Long> ids) {
        List<AuditProcedureBaseInfoDTO> projects = this.procedureService.findExistsBaseInfoIds(
            ids);
        if (CollUtil.isNotEmpty(projects)) {
            return projects.stream().collect(
                Collectors.toMap(AuditProcedureBaseInfoDTO::getId,
                    AuditProcedureBaseInfoDTO::getName));
        } else {
            return Collections.emptyMap();
        }
    }
}
