package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(value = "风险点审批-创建参数DTO")
public class RiskApproveResultCreateParamDTO {

    @ApiModelProperty("审批id")
    private Long approveId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("审计程序id")
    private List<Long> riskIds;

    @ApiModelProperty("风险点id/审计程序idMap")
    private Map<Long, Long> riskIdAndProcedureIdMap;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
