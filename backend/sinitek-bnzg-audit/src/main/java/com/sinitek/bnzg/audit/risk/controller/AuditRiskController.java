package com.sinitek.bnzg.audit.risk.controller;

import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskResultDTO;
import com.sinitek.bnzg.audit.risk.dto.AdjustInnerCategoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryResultDTO;
import com.sinitek.bnzg.audit.risk.dto.InnerCategoryListParamDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.risk.support.AuditRiskSearchResultFormat;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目风险点 Controller
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@RestController
@RequestMapping("/frontend/api/audit/project/risk")
@Api(value = "/frontend/api/audit/project/risk", tags = "审计系统-项目风险点")
public class AuditRiskController {

    @Autowired
    private IAuditRiskService riskService;

    @Autowired
    private AuditRiskSearchResultFormat<AuditRiskDetailDTO> format;

    @ApiOperation(value = "查询项目风险点分页列表")
    @GetMapping("/detail")
    public RequestResult<AuditRiskDetailDTO> loadDetail(@Param("id") Long id) {
        AuditRiskDetailDTO dto = this.riskService.loadDetail(id);
        if (Objects.nonNull(dto)) {
            this.format.format(Collections.singletonList(dto));
        }
        return new RequestResult<>(dto);
    }

    @ApiOperation(value = "查询风险点内部分类")
    @GetMapping("/inner-category/list")
    public RequestResult<List<String>> listInnerCategory() {
        return new RequestResult<>(this.riskService.findInnerCategory());
    }

    @ApiOperation(value = "根据审计年份查询风险点内部分类")
    @PostMapping("/inner-category/list-by-year")
    public RequestResult<List<String>> findInnerCategoryByYear(
        @RequestBody @Valid InnerCategoryListParamDTO param) {
        return new RequestResult<>(this.riskService.findInnerCategoryByYear(param));
    }

    @ApiOperation(value = "风险分类调整")
    @PostMapping("/inner-category/update-batch")
    public RequestResult<Void> updateBatchInnerCategory(
        @RequestBody @Valid AdjustInnerCategoryParamDTO param) {
        this.riskService.updateBatchInnerCategory(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "查询风险的历史，是否重复发生")
    @PostMapping("/history-list")
    public RequestResult<List<AuditRiskHistoryResultDTO>> findRiskHistory(
        @RequestBody @Valid AuditRiskHistoryParamDTO param) {
        return new RequestResult<>(this.riskService.findRiskHistory(param));
    }

    @ApiOperation(value = "查询所有的风险点-问责管理查询")
    @PostMapping(path = "/find-audit-risk")
    public RequestResult<List<FindAllAuditRiskResultDTO>> findAllauditRisk(
        @RequestBody @Valid FindAllAuditRiskParamDTO param) {
        return new RequestResult<>(this.riskService.findAllauditRisk(param));
    }

    @ApiOperation(value = "查询风险点相关数据-问责管理新增")
    @GetMapping("/get-risk-by-id")
    public RequestResult<AuditRiskDetailDTO> getRiskById(@Param("id") Long id) {
        AuditRiskDetailDTO dto = this.riskService.getRiskByIdWithAcCheck(id);
        if (Objects.nonNull(dto)) {
            this.format.format(Collections.singletonList(dto));
        }
        return new RequestResult<>(dto);
    }
}
