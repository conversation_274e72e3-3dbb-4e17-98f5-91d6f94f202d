package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("生成阶段步骤实例参数")
public class BatchGenerateStageStepExampleParamDTO {

    @ApiModelProperty("项目id集合")
    private List<Long> projectIds;

    /**
     * key:项目id,value:阶段步骤实例
     */
    @ApiModelProperty("项目步骤信息map")
    private Map<Long, List<StageStepDefInfoDTO>> projectIdAndStepsMap;

    /**
     * key:项目id,value:阶段实例id
     */
    @ApiModelProperty("项目阶段实例map")
    private Map<Long, Long> projectIdAndstageExampleIdMap;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

    @ApiModelProperty("是否抛出事件")
    private Boolean publishEventFlag;

}
