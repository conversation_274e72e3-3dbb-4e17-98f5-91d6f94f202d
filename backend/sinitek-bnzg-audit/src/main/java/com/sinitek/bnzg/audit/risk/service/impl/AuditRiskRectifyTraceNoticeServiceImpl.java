package com.sinitek.bnzg.audit.risk.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRectifyTraceNoticeService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.risk.support.AuditRiskSearchResultFormat;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.bnzg.common.service.IBnzgOrgService;
import com.sinitek.sirm.calendar.constant.CalendarEventConstant;
import com.sinitek.sirm.calendar.dto.CalendarEventDTO;
import com.sinitek.sirm.calendar.dto.CalendarEventQueryDTO;
import com.sinitek.sirm.calendar.service.ICalendarEventService;
import com.sinitek.sirm.common.message.constant.MessageConstant;
import com.sinitek.sirm.common.message.service.ISirmMessageService;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.common.utils.StringUtil;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.lowcode.common.model.constant.LcModelConstant;
import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.remind.enumerate.HolidayStrategyEnum;
import com.sinitek.sirm.remind.enumerate.RepeatTypeEnum;
import com.sinitek.sirm.remind.support.ISirmRemindSupportService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.mail.Message.RecipientType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目风险点 整改跟踪提醒 接口
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@Slf4j
@Service
public class AuditRiskRectifyTraceNoticeServiceImpl implements IAuditRiskRectifyTraceNoticeService {

    public static final int MINUTE_DIVISOR = 60;

    public static final int MILLI_SECOND_DIVISOR = 1000;

    private static final int DEFAULT_MAP_INITIAL_CAPACITY_PLUS_VALUE = 2;

    @Autowired
    private IAuditRiskService riskService;

    @Autowired
    private ISirmMessageService sirmMessageService;

    @Autowired
    private ISirmRemindSupportService sirmRemindSupportService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IBnzgOrgService bnzgOrgService;

    @Autowired
    private ICalendarEventService calendarEventService;

    @Autowired
    private AuditRiskSearchResultFormat<AuditRiskDetailDTO> format;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRectifyChangeNotice(Long riskId, String operatorId) {
        AuditRiskDetailDTO risk = this.getFormatedData(riskId);

        Integer threadLatestFlag = risk.getThreadLatestFlag();
        // 预计整改完成时间
        Date reqRectifyDate = risk.getReqRectifyDate();
        // 整改状态
        Integer rectifyState = risk.getRectifyState();

        // 删除未发送的信息
        this.doDeleteRectifyChangeCalendar(riskId, operatorId);

        if (Objects.equals(LcModelConstant.NOT_THREAD_LATEST_FLAG, threadLatestFlag)) {
            log.warn("风险点[{}]非最新数据,不再继续创建提醒,已创建的提醒如果未执行将会被删除",
                riskId);
            return;
        }

        if (Objects.isNull(reqRectifyDate)) {
            log.warn("风险点[{}]预计整改完成时间为空,无法创建提醒", riskId);
            return;
        }

        if (!Objects.equals(rectifyState, AuditRiskRectifyStateConstant.IN_RECTIFICATION)) {
            log.warn("风险点[{}]整改状态为[{}],无需创建提醒", JsonUtil.toJsonString(risk),
                rectifyState);
            return;
        }

        String riskRectifyTime = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_CHANGE_TIME, "09:00");

        String riskRectifyNoticeTitle = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_CHANGE_TITLE);
        if (StringUtils.isBlank(riskRectifyNoticeTitle)) {
            log.warn("系统配置 风险点整改状态变动提醒标题 [{},{}]为空,无法创建提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_RECTIFY_CHANGE_TITLE);
            return;
        }

        String riskRectifyNoticeTemplate = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_CHANGE_TEMPLATE);
        if (StringUtils.isBlank(riskRectifyNoticeTemplate)) {
            log.warn("系统配置 风险点整改状态变动提醒模板 [{},{}]为空,无法创建提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_RECTIFY_CHANGE_TEMPLATE);
            return;
        }

        log.info("系统参数 风险点整改状态变动提醒时间 [module:{},name:{}]配置为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_CHANGE_TIME, riskRectifyTime);
        log.info("系统参数 风险点整改状态变动提醒标题 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_CHANGE_TITLE, riskRectifyNoticeTitle);
        log.info("系统参数 风险点整改状态变动提醒模板 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_CHANGE_TEMPLATE, riskRectifyNoticeTemplate);

        String reqRectifyDateYYYYMMDDStr = DateUtil.format(reqRectifyDate,
            GlobalConstant.TIME_FORMAT_TEN);
        String reqRectifyDateTimeStr = String.format("%s %s:00", reqRectifyDateYYYYMMDDStr,
            riskRectifyTime);

        Date reqRectifyCalDateTime = DateUtil.parse(reqRectifyDateTimeStr,
            GlobalConstant.TIME_FORMAT_THIRTEEN);

        List<Integer> remindTimeList = new LinkedList<>();
        // 为了生成提醒，这里设置1分钟时间间隔
        remindTimeList.add(1);

        List<Date> sendTimes = new LinkedList<>();
        Date sendTime = DateUtils.addMinutes(reqRectifyCalDateTime, 1);

        if (sendTime.before(new Date())) {
            log.warn(
                "风险点[{}]预计整改完成时间为[{}],整改变动提醒提醒计算时间为[{}],发送时间为[{}],发送时间为过去的时间,不再继续发送",
                riskId,
                reqRectifyDateYYYYMMDDStr, reqRectifyDateTimeStr,
                DateUtil.format(sendTime, GlobalConstant.TIME_FORMAT_THIRTEEN));
            return;
        }

        sendTimes.add(sendTime);

        if (log.isInfoEnabled()) {
            log.info(
                "风险点[{}]预计整改完成时间为[{}],整改变动提醒提醒计算时间为[{}],发送时间为[{}]",
                riskId,
                reqRectifyDateYYYYMMDDStr, reqRectifyDateTimeStr,
                DateUtil.format(sendTime, GlobalConstant.TIME_FORMAT_THIRTEEN));
        }

        List<String> orgIds = new LinkedList<>();

        // 审计人
        String auditorId = risk.getAuditorId();
        orgIds.add(auditorId);
        // 整改联系人
        List<String> rectifyContacts = risk.getRectifyContactIds();
        if (CollUtil.isNotEmpty(rectifyContacts)) {
            orgIds.addAll(rectifyContacts);
        } else {
            rectifyContacts = Collections.emptyList();
        }

        EmployeeSearchDTO params = new EmployeeSearchDTO();
        params.setPageSize(orgIds.size());
        params.setEmpIds(orgIds.stream().distinct().collect(Collectors.joining(",")));

        List<Employee> employee = this.orgService.findAllEmployees(params);

        // key: orgId;value: 人员数据
        Map<String, Employee> orgIdAndEmployeeMap = employee.stream()
            .collect(Collectors.toMap(Employee::getId, v -> v));

        CalendarEventDTO param = new CalendarEventDTO();

        // 日程类型
        param.setType(AuditRiskConstant.RISK_RECTIFY_CHANGE_CALENDAR_EVENT);

        // 是否勾选全天
        param.setAllDay(CommonBooleanEnum.TRUE.getValue());
        // 日程开始时间
        param.setStartTime(reqRectifyDate);
        // 日程结束时间
        param.setEndTime(reqRectifyDate);

        // 录入人ID inputId
        param.setInputId(operatorId);
        Map<String, Object> replaceVariableCtx = JsonUtil.toMap(risk);
        // 日程主题 title
        param.setTitle(StringUtil.replaceVariables(riskRectifyNoticeTitle,
            replaceVariableCtx));

        // 日程所有者名称 ownerName
        param.setOwnerId(riskId);
        // 日程所有者ID ownerId
        param.setOwnerName(AuditRiskConstant.AUDIT_RISK_EXPECTED_FINISH_CALENDAR);
        // 日程来源ID sourceId
        param.setSourceId(riskId);
        // 日程来源对象 sourceName
        param.setSourceName(AuditRiskConstant.AUDIT_RISK_EXPECTED_FINISH_CALENDAR);

        // 人员ID empId;
        param.setEmpId(null);
        // 内容 content;
        String content = StringUtil.replaceVariables(riskRectifyNoticeTemplate,
            replaceVariableCtx);
        param.setContent(content);
        // url url
        param.setUrl(null);

        // 是否提醒 remindFlag
        param.setRemindFlag(CommonBooleanEnum.TRUE.getValue());
        // 提醒时间 remindTime
        Integer[] remindType = new Integer[remindTimeList.size()];
        remindTimeList.toArray(remindType);
        param.setRemindTime(remindType);
        // 提醒方式 remindType
        param.setRemindType(MessageConstant.SENDMODE_EMAIL);
        // 发送时间 sendTimes
        param.setSendTimes(sendTimes);
        // 接收人 reminders;
        List<MessageReceiverTemplateDTO> reminders = new LinkedList<>();

        // 发送给审计人
        if (Objects.nonNull(auditorId)) {
            List<MessageReceiverTemplateDTO> auditors = this.makeEmployee2EmailReceiver(
                Collections.singletonList(auditorId), orgIdAndEmployeeMap, RecipientType.TO);

            if (CollUtil.isNotEmpty(auditors)) {
                reminders.addAll(auditors);
                log.info("添加风险点[{}]审计人 {} 作为发送人", riskId, auditors);
            } else {
                log.warn("添加风险点[{}]审计人 {} 作为发送人为空", riskId, auditorId);
            }
        } else {
            log.warn(
                "风险点[{}]审计人[{}]无法转为 MessageReceiverTemplateDTO,该风险点审计人无法接收整改提醒邮件",
                riskId, auditorId);
        }

        if (CollUtil.isEmpty(reminders)) {
            log.error("风险点[{}]整改状态变动邮件提醒to接收人为空,无法发送提醒", riskId);
            return;
        }

        // 抄送人员设置
        // 审计部门
        String auditDeptOrgId = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.AUDIT_DEPT_ORGID);
        // 审计部门负责人
        List<Employee> auditDeptRespMans = this.bnzgOrgService.findDepartmentChief(
            auditDeptOrgId);

        // key: orgId;value: 人员数据
        Map<String, Employee> needCcEmployeeMap = new HashMap<>(
            orgIdAndEmployeeMap.size() + DEFAULT_MAP_INITIAL_CAPACITY_PLUS_VALUE);
        List<String> needCcEmpOrgId = new LinkedList<>();

        needCcEmployeeMap.putAll(orgIdAndEmployeeMap);

        if (CollUtil.isEmpty(auditDeptRespMans)) {
            log.warn("发送风险点[{}]整改提醒时,审计部门[{}]对应部门负责人为空", riskId,
                auditDeptOrgId);
        } else {
            needCcEmployeeMap.putAll(auditDeptRespMans.stream()
                .collect(Collectors.toMap(Employee::getId, v -> v)));
            needCcEmpOrgId.addAll(auditDeptRespMans.stream().map(Employee::getId).collect(
                Collectors.toList()));
        }

        List<String> finalRectifyContacts = rectifyContacts;
        List<String> filterCcOrgIds = needCcEmpOrgId.stream().distinct()
            .filter(ccOrgId -> !finalRectifyContacts.contains(ccOrgId))
            .collect(Collectors.toList());

        List<MessageReceiverTemplateDTO> needCcEmployees = this.makeEmployee2EmailReceiver(
            filterCcOrgIds, needCcEmployeeMap, RecipientType.CC);
        if (CollUtil.isNotEmpty(needCcEmployees)) {
            reminders.addAll(needCcEmployees);
            log.info("添加 {} 作为风险点[{}]整改提醒邮件抄送人", needCcEmployees, riskId);
        } else {
            log.warn(
                "风险点[{}]审计人orgId[{}]生成 MessageReceiverTemplateDTO 失败,整改提醒邮件无法抄送给审计人",
                riskId, auditorId);
        }

        param.setReminders(reminders);
        this.calendarEventService.saveOrUpdateCalendarEvent(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRectifyChangeCalendar(Long riskId, String operatorId) {
        this.doDeleteRectifyChangeCalendar(riskId, operatorId);
    }

    private void doDeleteRectifyChangeCalendar(Long riskId, String operatorId) {
        log.info("操作人[{}]删除风险点[{}]对应的整改状态变动当天提醒日程", operatorId, riskId);
        this.calendarEventService.deleteCalendarEventByOwnerIdAndOwnerName(riskId,
            AuditRiskConstant.AUDIT_RISK_EXPECTED_FINISH_CALENDAR);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRectifyChangeNotice(Long riskId, String operatorId) {
        // 风险点整改状态变动当天提醒
        // 删除未发送的信息
        List<CalendarEventDTO> calendarEventList = this.findCalendarEvent(riskId,
            AuditRiskConstant.AUDIT_RISK_EXPECTED_FINISH_CALENDAR);

        if (CollUtil.isNotEmpty(calendarEventList)) {
            log.info("操作人[{}]取消风险点[{}]整改状态变动当天提醒[{}]条", riskId, operatorId,
                calendarEventList.size());
            calendarEventList.forEach(calendarEvent -> {
                Long calendarId = calendarEvent.getId();
                this.sirmMessageService.deleteUnSendTimingMessageBySourceIdAndSourceEntity(
                    calendarId,
                    CalendarEventConstant.REMIND_SOURCE_NAME);
            });
        } else {
            log.info("操作人[{}]取消风险点[{}]整改状态变动当天提醒[{},{}]查询到数据为空,无需提醒",
                riskId,
                operatorId,
                riskId,
                AuditRiskConstant.AUDIT_RISK_EXPECTED_FINISH_CALENDAR);
        }
    }

    private AuditRiskDetailDTO getFormatedData(Long riskId) {
        AuditRiskDetailDTO risk = this.riskService.loadDetail(riskId);
        this.format.format(Collections.singletonList(risk));
        return risk;
    }

    /**
     * 保存整改跟踪提醒
     *
     * 调用节点:
     * 1.预计完成时间修改后
     * 2.整改联系人修改后
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRectifyTraceNotice(Long riskId, String operatorId) {
        AuditRiskDetailDTO risk = this.getFormatedData(riskId);

        Integer threadLatestFlag = risk.getThreadLatestFlag();
        // 预计整改完成时间
        Date reqRectifyDate = risk.getReqRectifyDate();
        // 整改状态
        Integer rectifyState = risk.getRectifyState();

        // 先删除后插入
        this.deleteRectifyTraceCalendar(riskId, operatorId);

        if (Objects.equals(LcModelConstant.NOT_THREAD_LATEST_FLAG, threadLatestFlag)) {
            log.warn("风险点[{}]非最新数据,不再继续创建提醒,已创建的提醒如果未执行将会被删除",
                riskId);
            return;
        }

        if (Objects.isNull(reqRectifyDate)) {
            log.warn("风险点[{}]预计整改完成时间为空,无法创建提醒", riskId);
            return;
        }

        if (!Objects.equals(rectifyState, AuditRiskRectifyStateConstant.IN_RECTIFICATION)
            && !Objects.equals(rectifyState, AuditRiskRectifyStateConstant.OVERDUE_UNRECTIFIED)) {
            log.warn("风险点[{}]整改状态为[{}],无需创建提醒", risk, rectifyState);
            return;
        }

        String rectifyFrrequency = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_FREQUENCY);

        String riskRectifyTime = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_TIME, "09:00");

        Integer riskRectifyHolidayStrategy = SettingUtils.getIntegerValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_HOLIDAY_STRATEGY);
        HolidayStrategyEnum holidayStrategyEnum = HolidayStrategyEnum.getEnum(
            riskRectifyHolidayStrategy);

        HolidayStrategyEnum finalHolidayStrategyEnum;
        if (Objects.isNull(holidayStrategyEnum)) {
            finalHolidayStrategyEnum = HolidayStrategyEnum.NOMMON;
        } else {
            finalHolidayStrategyEnum = holidayStrategyEnum;
        }

        if (StringUtils.isBlank(rectifyFrrequency)) {
            log.warn("系统配置 风险点提醒频率 [{},{}]为空,无法创建提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_RECTIFY_FREQUENCY);
            return;
        }

        String riskRectifyNoticeTitle = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_NOTICE_TITLE);
        if (StringUtils.isBlank(riskRectifyNoticeTitle)) {
            log.warn("系统配置 风险点整改提醒标题 [{},{}]为空,无法创建提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_RECTIFY_NOTICE_TITLE);
            return;
        }

        String riskRectifyNoticeTemplate = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_NOTICE_TEMPLATE);
        if (StringUtils.isBlank(riskRectifyNoticeTemplate)) {
            log.warn("系统配置 风险点整改提醒模板 [{},{}]为空,无法创建提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_RECTIFY_NOTICE_TEMPLATE);
            return;
        }

        log.info("系统参数 整改跟踪提醒频率 [module:{},name:{}]配置为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_FREQUENCY, rectifyFrrequency);
        log.info("系统参数 风险点整改提醒时间 [module:{},name:{}]配置为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_TIME, riskRectifyTime);
        log.info("系统参数 风险点整改提醒节假日策略 [module:{},name:{}]配置值对应枚举为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_HOLIDAY_STRATEGY, holidayStrategyEnum);
        log.info("系统参数 风险点整改提醒标题 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_NOTICE_TITLE, riskRectifyNoticeTitle);
        log.info("系统参数 风险点整改提醒模板 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_RECTIFY_NOTICE_TEMPLATE, riskRectifyNoticeTemplate);

        String reqRectifyDateYYYYMMDDStr = DateUtil.format(reqRectifyDate,
            GlobalConstant.TIME_FORMAT_TEN);
        String reqRectifyDateTimeStr = String.format("%s %s:00", reqRectifyDateYYYYMMDDStr,
            riskRectifyTime);
        log.info("风险点[{}]预计整改完成时间为[{}],提醒计算时间为[{}]", riskId,
            reqRectifyDateYYYYMMDDStr, reqRectifyDateTimeStr);

        Date reqRectifyCalDateTime = DateUtil.parse(reqRectifyDateTimeStr,
            GlobalConstant.TIME_FORMAT_THIRTEEN);

        List<TriggerrDateTime> remindDateList = new LinkedList<>();
        List<Integer> remindTimeList = new LinkedList<>();
        List<Date> sendTimes = new LinkedList<>();

        Arrays.stream(rectifyFrrequency.split("[,， ]"))
            .forEach(frequency -> {
                int frequencyInt = Integer.parseInt(frequency);
                if (frequencyInt > 0) {
                    Date realTriggerDateTime = this.sirmRemindSupportService.calculateNextTime(
                        reqRectifyCalDateTime, -frequencyInt,
                        RepeatTypeEnum.DAY, finalHolidayStrategyEnum);

                    long minutes =
                        (reqRectifyCalDateTime.getTime() - realTriggerDateTime.getTime())
                            / MINUTE_DIVISOR
                            / MILLI_SECOND_DIVISOR;

                    TriggerrDateTime triggerrDateTime = new TriggerrDateTime();
                    triggerrDateTime.setDayNum(frequencyInt);

                    int minutesIntValue = (int) minutes;
                    if (!Objects.equals((long) minutesIntValue, minutes)) {
                        log.warn(
                            "风险点[{}]计算整改延期提醒时间[{}],分钟数转换后出现溢出 long: {}, int: {},影响日程中分钟数的保存",
                            riskId, frequency, minutes, minutesIntValue);
                    }
                    triggerrDateTime.setMinutes(minutesIntValue);

                    triggerrDateTime.setTriggerDateTime(realTriggerDateTime);

                    log.info(
                        "配置的提醒频率[{}]对应分钟数[{}],节假日处理策略为[{}]计算出发送时间[{}]",
                        frequency, minutes, finalHolidayStrategyEnum,
                        DateUtil.format(realTriggerDateTime, GlobalConstant.TIME_FORMAT_THIRTEEN));

                    remindTimeList.add(minutesIntValue);
                    if (realTriggerDateTime.before(new Date())) {
                        log.warn(
                            "风险点[{}]预计整改完成时间为[{}],整改反馈提醒提醒发送时间为[{}],发送时间为过去的时间,不再继续发送",
                            riskId,
                            reqRectifyDateYYYYMMDDStr,
                            DateUtil.format(realTriggerDateTime,
                                GlobalConstant.TIME_FORMAT_THIRTEEN));
                    } else {
                        sendTimes.add(realTriggerDateTime);
                        remindDateList.add(triggerrDateTime);
                    }
                } else {
                    log.warn("配置的提醒频率[{}]转为Int后小于0,无法继续计算发送时间", frequency);
                }
            });

        if (CollUtil.isEmpty(remindDateList)) {
            log.info("风险点[{}]计算出提醒时间为空,不再继续生成提醒邮件", riskId);
            return;
        }

        List<String> orgIds = new LinkedList<>();

        // 审计人
        String auditorId = risk.getAuditorId();
        orgIds.add(auditorId);
        // 整改联系人
        List<String> rectifyContacts = risk.getRectifyContactIds();
        if (CollUtil.isNotEmpty(rectifyContacts)) {
            orgIds.addAll(rectifyContacts);
        }

        EmployeeSearchDTO params = new EmployeeSearchDTO();
        params.setPageSize(orgIds.size());
        params.setEmpIds(orgIds.stream().distinct().collect(Collectors.joining(",")));

        List<Employee> employee = this.orgService.findAllEmployees(params);

        // key: orgId;value: 人员数据
        Map<String, Employee> orgIdAndEmployeeMap = employee.stream()
            .collect(Collectors.toMap(Employee::getId, v -> v));

        CalendarEventDTO param = new CalendarEventDTO();

        // 日程类型
        param.setType(AuditRiskConstant.RISK_RECTIFY_NOTICE_CALENDAR_EVENT);

        // 是否勾选全天
        param.setAllDay(CommonBooleanEnum.TRUE.getValue());
        // 日程开始时间
        param.setStartTime(reqRectifyDate);
        // 日程结束时间
        param.setEndTime(reqRectifyDate);

        // 录入人ID inputId
        param.setInputId(operatorId);
        // 日程主题 title
        Map<String, Object> replaceVariableCtx = JsonUtil.toMap(risk);
        param.setTitle(StringUtil.replaceVariables(riskRectifyNoticeTitle,
            replaceVariableCtx));

        // 日程所有者名称 ownerName
        param.setOwnerId(riskId);
        // 日程所有者ID ownerId
        param.setOwnerName(AuditRiskConstant.DEFAULT_SOURCE_NAME);
        // 日程来源ID sourceId
        param.setSourceId(riskId);
        // 日程来源对象 sourceName
        param.setSourceName(AuditRiskConstant.DEFAULT_SOURCE_NAME);

        // 人员ID empId;
        param.setEmpId(null);
        // 内容 content;
        String content = StringUtil.replaceVariables(riskRectifyNoticeTemplate,
            replaceVariableCtx);
        param.setContent(content);
        // url url
        param.setUrl(null);

        // 是否提醒 remindFlag
        param.setRemindFlag(CommonBooleanEnum.TRUE.getValue());
        // 提醒时间 remindTime
        Integer[] remindType = new Integer[remindTimeList.size()];
        remindTimeList.toArray(remindType);
        param.setRemindTime(remindType);
        // 提醒方式 remindType
        param.setRemindType(MessageConstant.SENDMODE_EMAIL);
        // 发送时间 sendTimes
        param.setSendTimes(sendTimes);
        // 接收人 reminders;
        List<MessageReceiverTemplateDTO> reminders = new LinkedList<>();

        // 发送给整改联系人
        if (CollUtil.isNotEmpty(rectifyContacts)) {
            List<MessageReceiverTemplateDTO> contactReceivers = this.makeEmployee2EmailReceiver(
                rectifyContacts, orgIdAndEmployeeMap, RecipientType.TO);

            if (CollUtil.isNotEmpty(contactReceivers)) {
                reminders.addAll(contactReceivers);
                log.info("添加风险点[{}]整改联系人 {}, {} 作为发送人", riskId, rectifyContacts,
                    contactReceivers);
            } else {
                log.warn("添加风险点[{}]整改联系人 {} 作为发送人为空", riskId, rectifyContacts);
            }
        } else {
            log.warn(
                "风险点[{}]整改联系人[{}]无法转为 MessageReceiverTemplateDTO,该整改联系人无法接收整改提醒邮件",
                riskId, rectifyContacts);
        }

        if (CollUtil.isEmpty(reminders)) {
            log.error("风险点[{}]整改提醒to接收人为空,无法发送提醒", riskId);
            return;
        }

        // 抄送人员设置
        // 审计部门
        String auditDeptOrgId = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.AUDIT_DEPT_ORGID);
        // 审计部门负责人
        List<Employee> auditDeptRespMans = this.bnzgOrgService.findDepartmentChief(
            auditDeptOrgId);
        // 责任部门orgId
        List<String> respDeptIds = risk.getRespDeptId();
        List<Employee> respDeptMans = new LinkedList<>();
        for (String respDeptId : respDeptIds) {
            List<Employee> respDeptMansInner = this.bnzgOrgService.findDepartmentChief(respDeptId);
            if (CollUtil.isNotEmpty(respDeptMansInner)) {
                respDeptMans.addAll(respDeptMansInner);
            }
        }
        // key: orgId;value: 人员数据
        Map<String, Employee> needCcEmployeeMap = new HashMap<>(
            orgIdAndEmployeeMap.size() + DEFAULT_MAP_INITIAL_CAPACITY_PLUS_VALUE);
        List<String> needCcEmpOrgId = new LinkedList<>();
        needCcEmpOrgId.add(auditorId);

        needCcEmployeeMap.putAll(orgIdAndEmployeeMap);

        if (CollUtil.isEmpty(auditDeptRespMans)) {
            log.warn("发送风险点[{}]整改提醒时,审计部门[{}]对应部门负责人为空", riskId,
                auditDeptOrgId);
        } else {
            needCcEmployeeMap.putAll(auditDeptRespMans.stream()
                .collect(Collectors.toMap(Employee::getId, v -> v)));
            needCcEmpOrgId.addAll(auditDeptRespMans.stream().map(Employee::getId).collect(
                Collectors.toList()));
        }

        if (CollUtil.isEmpty(respDeptMans)) {
            log.warn("发送风险点[{}]整改提醒时,责任部门[{}]对应部门负责人为空", riskId,
                respDeptIds);
        } else {
            respDeptMans.forEach(item -> {
                needCcEmployeeMap.put(item.getId(), item);
            });
            needCcEmpOrgId.addAll(respDeptMans.stream().map(Employee::getId).collect(
                Collectors.toList()));
        }

        List<String> filterCcOrgIds = needCcEmpOrgId.stream().distinct()
            .filter(ccOrgId -> !rectifyContacts.contains(ccOrgId)).collect(Collectors.toList());

        List<MessageReceiverTemplateDTO> needCcEmployees = this.makeEmployee2EmailReceiver(
            filterCcOrgIds, needCcEmployeeMap, RecipientType.CC);
        if (CollUtil.isNotEmpty(needCcEmployees)) {
            reminders.addAll(needCcEmployees);
            log.info("添加 {} 作为风险点[{}]整改提醒邮件抄送人", needCcEmployees, riskId);
        } else {
            log.warn(
                "风险点[{}]审计人orgId[{}]生成 MessageReceiverTemplateDTO 失败,整改提醒邮件无法抄送给审计人",
                riskId, auditorId);
        }

        param.setReminders(reminders);
        this.calendarEventService.saveOrUpdateCalendarEvent(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRectifyTraceCalendar(Long riskId, String operatorId) {
        this.doDeleteRectifyTraceCalendar(riskId, operatorId);
    }

    private void doDeleteRectifyTraceCalendar(Long riskId, String operatorId) {
        log.info("操作人[{}]删除风险点[{}]对应的整改跟踪提醒日程", operatorId, riskId);
        this.calendarEventService.deleteCalendarEventByOwnerIdAndOwnerName(riskId,
            AuditRiskConstant.DEFAULT_SOURCE_NAME);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRectifyTraceNotice(Long riskId, String operatorId) {
        // 风险点整改完成要取消提醒
        // 删除未发送的信息
        List<CalendarEventDTO> calendarEventList = this.findCalendarEvent(riskId,
            AuditRiskConstant.DEFAULT_SOURCE_NAME);

        if (CollUtil.isNotEmpty(calendarEventList)) {
            log.info("操作人[{}]取消风险点[{}]整改提醒[{}]条", riskId, operatorId,
                calendarEventList.size());
            calendarEventList.forEach(calendarEvent -> {
                Long calendarId = calendarEvent.getId();
                this.sirmMessageService.deleteUnSendTimingMessageBySourceIdAndSourceEntity(
                    calendarId,
                    CalendarEventConstant.REMIND_SOURCE_NAME);
            });
        } else {
            log.info("操作人[{}]取消风险点[{}]整改提醒时根据[{},{}]查询到数据为空,无需提醒", riskId,
                operatorId,
                riskId,
                AuditRiskConstant.DEFAULT_SOURCE_NAME);
        }
    }

    private List<CalendarEventDTO> findCalendarEvent(Long ownerId, String ownerName) {
        CalendarEventQueryDTO queryDTO = new CalendarEventQueryDTO();
        queryDTO.setOwnerId(ownerId);
        queryDTO.setOwnerName(ownerName);
        return this.calendarEventService.findCalendarEvent(queryDTO);
    }

    /**
     * @param type 'to':邮件收件人，'cc':邮件抄送人
     */
    private List<MessageReceiverTemplateDTO> makeEmployee2EmailReceiver(
        List<String> needCcEmpOrgIdList,
        Map<String, Employee> orgIdAndEmployeeMap,
        RecipientType type) {

        if (CollUtil.isNotEmpty(needCcEmpOrgIdList)) {
            return needCcEmpOrgIdList.stream().distinct().map(orgId -> {
                Employee employee = orgIdAndEmployeeMap.get(orgId);
                if (Objects.isNull(employee)) {
                    log.warn("根据orgId[{}]无法获取 Employee 对象", orgId);
                    return null;
                }

                String auditorId = employee.getId();
                String empName = employee.getEmpName();
                String userName = employee.getUserName();

                Optional<String> possiableEmail = CollUtil.toList(employee.getEmail(),
                    employee.getEmail1(),
                    employee.getEmail2()).stream().filter(StringUtils::isNotBlank).findFirst();
                if (possiableEmail.isPresent()) {
                    String email = possiableEmail.get();

                    MessageReceiverTemplateDTO auditorMessageReceiver = new MessageReceiverTemplateDTO();
                    auditorMessageReceiver.setEmpId(auditorId);
                    auditorMessageReceiver.setEmpName(employee.getEmpName());
                    auditorMessageReceiver.setUserName(employee.getUserName());
                    auditorMessageReceiver.setEmail(email);
                    auditorMessageReceiver.setReceiveType(type.toString());

                    return auditorMessageReceiver;
                } else {
                    log.warn(
                        "用户[{},{},{}]不存在邮箱地址,无法生成 MessageReceiverTemplateDTO 对象",
                        auditorId, empName, userName);
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } else {
            log.info("当前邮件发送类型为[{}],传入数据为空,无法生成邮件接收对象", type);
            return Collections.emptyList();
        }
    }

    @Data
    @NoArgsConstructor
    @ApiModel("触发时间")
    public static class TriggerrDateTime {

        @ApiModelProperty("天数")
        private Integer dayNum;

        @ApiModelProperty("分钟数")
        private Integer minutes;

        @ApiModelProperty("触发时间")
        private Date triggerDateTime;
    }

}
