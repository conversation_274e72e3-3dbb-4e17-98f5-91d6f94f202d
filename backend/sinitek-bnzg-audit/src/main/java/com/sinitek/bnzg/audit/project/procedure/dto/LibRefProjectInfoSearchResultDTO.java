package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/13/2024 11:24
 */
@Data
@NoArgsConstructor
@ApiModel("被审计项目引用的审计程序库信息")
public class LibRefProjectInfoSearchResultDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("审计程序库id")
    private Long libId;

}
