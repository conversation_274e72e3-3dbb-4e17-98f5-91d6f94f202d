package com.sinitek.bnzg.audit.risk.config;

import com.sinitek.bnzg.audit.risk.job.UpdateRectifyStateJob;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 整改状态定时变为逾期未整改
 *
 * <AUTHOR>
 * @since 2024/9/11
 */
@Configuration
public class UpdateRectifyStateJobConfig {

    private static final String JOB_NAME = "整改状态定时变为逾期未整改";
    private static final String JOB_GROUP = "AUDIT";

    @Bean
    public JobDetail updateRectifyStateJobDetail() {
        return JobBuilder.newJob(UpdateRectifyStateJob.class)
            .withIdentity(JOB_NAME, JOB_GROUP)
            .storeDurably()
            .build();
    }


    @Bean
    public Trigger updateRectifyStateJobTrigger(JobDetail updateRectifyStateJobDetail) {
        return TriggerBuilder.newTrigger()
            .forJob(updateRectifyStateJobDetail)
            .withIdentity(JOB_NAME, JOB_GROUP)
            .startNow()
            // 每晚凌晨一点自动执行
            .withSchedule(CronScheduleBuilder.cronSchedule("0 0 1 * * ?"))
            .usingJobData("name", JOB_NAME)
            .build();
    }
}
