package com.sinitek.bnzg.audit.stage.controller;

import com.sinitek.bnzg.audit.stage.dto.StageExampleDetailLoadParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleResultDTO;
import com.sinitek.bnzg.audit.stage.service.IAuditStageExampleService;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阶段实例 Controller
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/audit/stage/example")
@Api(value = "/frontend/api/audit/stage/example", tags = "审计系统-审计阶段实例")
public class StageExampleController {

    @Autowired
    private IAuditStageExampleService auditStageExampleService;

    @PostMapping("/detail")
    @ApiOperation("获取阶段实例详情列表")
    public RequestResult<StageExampleResultDTO> loadStageExample(
        @RequestBody @Valid StageExampleDetailLoadParamDTO param) {
        return new RequestResult<>(this.auditStageExampleService.loadDetail(param));
    }

}
