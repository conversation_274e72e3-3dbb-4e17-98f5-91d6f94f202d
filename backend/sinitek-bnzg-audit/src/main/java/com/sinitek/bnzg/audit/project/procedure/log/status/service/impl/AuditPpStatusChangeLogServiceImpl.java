package com.sinitek.bnzg.audit.project.procedure.log.status.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.project.procedure.log.status.dao.AuditPpStatusLogDAO;
import com.sinitek.bnzg.audit.project.procedure.log.status.entity.AuditPpStatusLog;
import com.sinitek.bnzg.audit.project.procedure.log.status.service.IAuditPpStatusChangeLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class AuditPpStatusChangeLogServiceImpl extends
    AbstractReecordChangeLogService<AuditPpStatusLog, Integer> implements
    IAuditPpStatusChangeLogService {

    @Autowired
    private AuditPpStatusLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<AuditPpStatusLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected AuditPpStatusLog generateNewOne() {
        return new AuditPpStatusLog();
    }

    @Override
    protected void handlerForeignKey(AuditPpStatusLog entity, Long foreignKey) {
        entity.setPpId(foreignKey);
    }
}
