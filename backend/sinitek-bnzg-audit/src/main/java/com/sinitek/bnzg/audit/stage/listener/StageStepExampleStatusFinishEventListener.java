package com.sinitek.bnzg.audit.stage.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.log.status.step.event.StageStepExampleStatusChangeEvent;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStepStatusUtil;
import com.sinitek.bnzg.audit.stage.util.StageStepUtil;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.event.annotation.SiniCubeEventListener;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class StageStepExampleStatusFinishEventListener {

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IStageStepExampleStatusService stepExampleStatusService;

    /**
     * 步骤实例状态变化事件
     */
    @SiniCubeEventListener
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        StageStepExampleStatusChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            this.dealSingleData((RecordChangeLogAddParamDTO) source);
        } else if (source instanceof AbstractRecordChangeLogBatchAddBaseParamDTO) {
            this.dealBatchData((AbstractRecordChangeLogBatchAddBaseParamDTO) source);
        }
    }

    private void dealBatchData(AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> source) {
        Collection<Long> foreignKeys = source.getForeignKeys();
        List<Long> needDealList = foreignKeys.stream().filter(k -> {
            Integer status = source.getNewValue(k);
            return this.isNeedDealStageStepStatus(status);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(needDealList)) {
            List<StageStepExampleDTO> stageStepExamples = this.stageStepExampleService.findByIds(
                needDealList);
            // 这里只关注审计报告完成后开启整改跟踪
            StageStepExampleDTO reportStepExample = stageStepExamples.stream()
                .filter(item -> Objects.equals(StageStepConstant.AUDIT_REPORT, item.getStepValue()))
                .findFirst().orElse(null);

            if (Objects.nonNull(reportStepExample)) {
                Long id = reportStepExample.getId();
                String operatorId = source.getOperatorId(id);
                Date opTime = source.getOpTime(id);

                this.dealSingleData(RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(id)
                    .newValue(source.getNewValue(id))
                    .oldValue(source.getOldValue(id))
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark(source.getRemark(id))
                    .build());
            } else {
                log.warn("当前步骤批量完成,不存在审计报告步骤,无法开启下一步骤,data: {}", source);
            }
        }
    }

    private void dealSingleData(RecordChangeLogAddParamDTO<Integer> source) {
        Integer newValue = source.getNewValue();
        if (this.isNeedDealStageStepStatus(newValue)) {
            Long foreignKey = source.getForeignKey();
            String operatorId = source.getOperatorId();
            Date opTime = source.getOpTime();

            List<StageStepExampleDTO> stageStepExamples = this.stageStepExampleService.findByIds(
                Collections.singleton(foreignKey));
            if (CollUtil.isNotEmpty(stageStepExamples)) {
                StageStepExampleDTO stageStepExample = stageStepExamples.get(0);

                Integer stepValue = stageStepExample.getStepValue();
                Long stageExampleId = stageStepExample.getStageExampleId();

                List<StageStepExampleDTO> stepExamples = this.stageStepExampleService.findByStageExampleIds(
                    Collections.singleton(stageExampleId));

                Integer nextStepValue = StageStepUtil.getNextStepValueByStepValue(
                    stepValue);
                if (Objects.nonNull(nextStepValue)) {

                    List<StageStepExampleDTO> nextStepExamples = stepExamples.stream()
                        .filter(item -> Objects.equals(item.getStepValue(), nextStepValue)).collect(
                            Collectors.toList());
                    if (CollUtil.isNotEmpty(nextStepExamples)) {

                        StageStepExampleDTO nextStepExample = nextStepExamples.get(0);

                        Long id = nextStepExample.getId();
                        Integer status = nextStepExample.getStatus();
                        boolean canChange = StageStepStatusUtil.checkCanChangeAsReadyStatus(status);
                        if (canChange) {
                            this.stepExampleStatusService.updateStatus(
                                StageStepExampleStatusChangeParamDTO.builder()
                                    .ids(Collections.singletonList(id))
                                    .newStatus(StageStepStatusConstant.READY)
                                    .opOrgId(operatorId)
                                    .opTime(opTime)
                                    .opRemark("前置步骤完成")
                                    .publishEventFlag(true)
                                    .build());
                        } else {
                            log.warn(
                                "阶段步骤实例[{}]步骤值[{}]对应下一步骤[{}],实例[{}],实例状态[{}],无需将下一步骤更新为就绪状态",
                                foreignKey, stepValue, nextStepValue, id, status);
                        }

                    } else {
                        log.warn(
                            "阶段步骤实例[{}]步骤值[{}]对应下一步骤[{}],但不存在实例数据,无法更新下一步骤状态",
                            foreignKey, stepValue, nextStepValue);
                    }

                } else {
                    log.info("阶段步骤实例[{}]步骤值[{}]对应下一步骤为空,无法更新下一步骤状态",
                        foreignKey, stepValue);
                }
            } else {
                log.warn("根据阶段步骤实例id[{}]无法获取阶段步骤实例数据,无法更新下一步骤状态",
                    foreignKey);
            }

        }
    }

    private boolean isNeedDealStageStepStatus(Integer status) {
        return Objects.equals(StageStepStatusConstant.FINISHED, status);
    }
}
