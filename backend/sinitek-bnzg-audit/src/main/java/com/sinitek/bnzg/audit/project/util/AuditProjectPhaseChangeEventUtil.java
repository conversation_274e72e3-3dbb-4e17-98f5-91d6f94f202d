package com.sinitek.bnzg.audit.project.util;

import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.event.AuditProjectPhaseChangeEvent;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectPhaseChangeEventUtil {

    public static void publishEvent(AuditProjectPhaseChangeEventSourceDTO source) {
        LcEventUtil.publishEvent(new AuditProjectPhaseChangeEvent(source));
    }
}
