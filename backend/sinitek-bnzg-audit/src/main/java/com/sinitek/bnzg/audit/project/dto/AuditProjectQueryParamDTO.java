package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目查询参数")
public class AuditProjectQueryParamDTO {

    @ApiModelProperty("计划id")
    private List<Long> planIds;

    @ApiModelProperty("项目名称")
    private String projectName;

}
