package com.sinitek.bnzg.audit.project.procedure.approve.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApprovePageParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSingleApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.support.AuditPpApproveResultSearchResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风险点审批 Controller
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@RestController
@RequestMapping("/frontend/api/audit/project/procedure/approve/result")
@Api(value = "/frontend/api/audit/project/procedure/approve/result", tags = "审计系统-项目审计程序-审批结果")
public class PpApproveResultController {

    @Autowired
    private IPpApproveResultService ppApproveResultService;

    @Autowired
    private AuditPpApproveResultSearchResultFormat auditPpApproveResultSearchResultFormat;

    @ApiOperation(value = "查询")
    @PostMapping("/search")
    public TableResult<PpApproveResultSearchResultDTO> search(
        @RequestBody @Validated PpApproveResultSearchParamDTO param) {
        IPage<PpApproveResultSearchResultDTO> page = this.ppApproveResultService.search(
            param);
        return param.build(page, this.auditPpApproveResultSearchResultFormat);
    }

    @ApiOperation(value = "审批")
    @PostMapping("/save")
    public RequestResult<Void> approve(
        @RequestBody @Validated PpApproveResultSingleApproveParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.ppApproveResultService.approve(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "审批详情")
    @GetMapping("/detail")
    public RequestResult<PpApproveResultDetailDTO> approve(
        @RequestParam("id") Long id) {
        return new RequestResult<>(this.ppApproveResultService.loadDetailById(id));
    }

    @ApiOperation(value = "获取审批中数据")
    @GetMapping("/load-page-detail")
    public RequestResult<PpApprovePageParamDTO> getApprovePageParam(
        @RequestParam("ppId") Long ppId) {
        return new RequestResult<>(this.ppApproveResultService.getApprovePageParam(ppId));
    }
}
