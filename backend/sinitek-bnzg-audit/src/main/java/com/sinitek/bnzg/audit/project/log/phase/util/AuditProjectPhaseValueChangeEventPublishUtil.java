package com.sinitek.bnzg.audit.project.log.phase.util;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.audit.project.log.phase.event.AuditProjectPhaseValueChangeEvent;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/09/2024 10:02
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectPhaseValueChangeEventPublishUtil {

    public static <T extends AbstractRecordChangeLogAddParamBaseDTO> void publishEvent(
        T event) {
        LcEventUtil.publishEvent(new AuditProjectPhaseValueChangeEvent<>(event));
    }

}
