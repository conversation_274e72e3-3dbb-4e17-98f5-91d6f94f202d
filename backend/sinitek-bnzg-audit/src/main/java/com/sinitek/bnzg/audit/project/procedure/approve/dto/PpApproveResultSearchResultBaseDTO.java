package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目审计程序审批-返回DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "查询结果基本DTO")
public class PpApproveResultSearchResultBaseDTO {

    @ApiModelProperty(value = "数据类型")
    private Integer dataType;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("项目审计程序审批id")
    private Long approveId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "程序id")
    private Long procedureId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批结果名")
    private String approveResultName;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    @ApiModelProperty("审批人名称")
    private String operatorName;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date opTime;

}
