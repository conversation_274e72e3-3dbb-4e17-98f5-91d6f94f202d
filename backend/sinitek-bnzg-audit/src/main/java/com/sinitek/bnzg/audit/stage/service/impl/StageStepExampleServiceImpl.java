package com.sinitek.bnzg.audit.stage.service.impl;

import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.CANT_FIND_STAGE_STEP_EXAMPLE_DATA;
import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.PROJECT_STAGE_EXAMPLE_NOT_EXISTS;
import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.STEP_EXAMPLE_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.dto.AuditProjectAuthParamDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectAuthService;
import com.sinitek.bnzg.audit.stage.dao.StageStepExampleDAO;
import com.sinitek.bnzg.audit.stage.dto.BatchGenerateStageStepExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageStepExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepDefInfoDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.entity.StageStepExample;
import com.sinitek.bnzg.audit.stage.log.status.step.util.StageStepExampleStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.stage.service.IStageExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.util.StageStepExampleConvertUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 阶段步骤实例 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Slf4j
@Service
public class StageStepExampleServiceImpl implements IStageStepExampleService {

    @Autowired
    private StageStepExampleDAO dao;

    @Autowired
    private IAuditProjectAuthService auditProjectAuthService;

    @Autowired
    private IStageExampleService stageExampleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateStageStepExamples(GenerateStageStepExampleParamDTO param) {
        Long projectId = param.getProjectId();
        Long stageExampleId = param.getStageExampleId();
        List<StageStepDefInfoDTO> list = param.getList();
        String opOrgId = param.getOpOrgId();
        Date opTime = param.getOpTime();
        Boolean publishEventFlag = param.getPublishEventFlag();

        if (CollUtil.isNotEmpty(list)) {
            List<StageStepExample> needSaveList = list.stream().map(item -> {
                Integer stepValue = item.getStepValue();
                Integer status = item.getStatus();
                Integer sort = item.getSort();

                StageStepExample stageStepExample = new StageStepExample();
                stageStepExample.setProjectId(projectId);
                stageStepExample.setStageExampleId(stageExampleId);
                stageStepExample.setStepValue(stepValue);
                stageStepExample.setStatus(status);
                stageStepExample.setSort(sort);
                return stageStepExample;
            }).collect(Collectors.toList());
            this.dao.saveBatch(needSaveList);

            Map<Long, Integer> idAndOldStatusMap = new LinkedHashMap<>();
            Map<Long, Integer> idAndNewStatusMap = new LinkedHashMap<>();
            List<Long> ids = new LinkedList<>();

            needSaveList.forEach(item -> {
                Long id = item.getId();
                Integer status = item.getStatus();
                ids.add(id);
                idAndOldStatusMap.put(id, null);
                idAndNewStatusMap.put(id, status);
            });

            if (Objects.equals(Boolean.TRUE, publishEventFlag)) {
                StageStepExampleStatusChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                        .foreignKeys(ids)
                        .oldValueMap(idAndOldStatusMap)
                        .newValueMap(idAndNewStatusMap)
                        .operatorId(opOrgId)
                        .opTime(opTime)
                        .remark("阶段步骤生成")
                        .build());
            } else {
                log.info("当前无需发布阶段步骤实例[{}]状态变动事件", ids);
            }
        } else {
            log.warn("为项目[{}],阶段实例[{}]生成审计阶段步骤实例时,传入步骤信息集合为空",
                projectId, stageExampleId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchGenerateStageStepExamples(BatchGenerateStageStepExampleParamDTO param) {
        List<Long> projectIds = param.getProjectIds();
        Map<Long, List<StageStepDefInfoDTO>> projectIdAndStepsMap = param.getProjectIdAndStepsMap();
        Map<Long, Long> projectIdAndstageExampleIdMap = param.getProjectIdAndstageExampleIdMap();

        String opOrgId = param.getOperatorId();
        Date opTime = param.getOpTime();
        Boolean publishEventFlag = param.getPublishEventFlag();

        if (CollUtil.isNotEmpty(projectIds)) {
            List<StageStepExample> needSaveList = new LinkedList<>();

            projectIds.forEach(projectId -> {
                Long stageExampleId = projectIdAndstageExampleIdMap.get(projectId);
                if (IdUtil.isNotDataId(stageExampleId)) {
                    log.error("为项目[{}]生成审计阶段步骤实例时,传入阶段实例ID为空", projectId);
                    throw new BussinessException(PROJECT_STAGE_EXAMPLE_NOT_EXISTS, projectId);
                }
                List<StageStepDefInfoDTO> stepDefList = projectIdAndStepsMap.get(projectId);
                if (CollUtil.isEmpty(stepDefList)) {
                    log.error("为项目[{}]生成审计阶段步骤实例时,对应步骤数据为空", projectId);
                    throw new BussinessException(STEP_EXAMPLE_NOT_EXISTS, projectId);
                }

                stepDefList.forEach(item -> {
                    Integer stepValue = item.getStepValue();
                    Integer status = item.getStatus();
                    Integer sort = item.getSort();

                    StageStepExample stageStepExample = new StageStepExample();
                    stageStepExample.setProjectId(projectId);
                    stageStepExample.setStageExampleId(stageExampleId);
                    stageStepExample.setStepValue(stepValue);
                    stageStepExample.setStatus(status);
                    stageStepExample.setSort(sort);

                    needSaveList.add(stageStepExample);
                });
            });
            this.dao.saveBatch(needSaveList);

            Map<Long, Integer> idAndOldStatusMap = new LinkedHashMap<>();
            Map<Long, Integer> idAndNewStatusMap = new LinkedHashMap<>();
            List<Long> ids = new LinkedList<>();

            needSaveList.forEach(item -> {
                Long id = item.getId();
                Integer status = item.getStatus();
                ids.add(id);
                idAndOldStatusMap.put(id, null);
                idAndNewStatusMap.put(id, status);
            });

            if (Objects.equals(Boolean.TRUE, publishEventFlag)) {
                StageStepExampleStatusChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                        .foreignKeys(ids)
                        .oldValueMap(idAndOldStatusMap)
                        .newValueMap(idAndNewStatusMap)
                        .operatorId(opOrgId)
                        .opTime(opTime)
                        .remark("阶段步骤批量生成")
                        .build());
            } else {
                log.info("阶段步骤批量生成时,当前无需发布阶段步骤实例[{}]状态变动事件", ids);
            }
        } else {
            log.warn(
                "阶段步骤批量生成时,为项目[{}],阶段实例[{}]生成审计阶段步骤实例时,传入步骤信息集合为空",
                projectIds, projectIdAndstageExampleIdMap);
        }
    }

    @Override
    public List<StageStepExampleDTO> findByIds(Collection<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            List<StageStepExample> stageStepExamples = this.dao.listByIds(ids);
            if (CollUtil.isNotEmpty(stageStepExamples)) {
                return stageStepExamples.stream().map(StageStepExampleConvertUtil::makeEntity2DTO)
                    .collect(
                        Collectors.toList());
            } else {
                log.warn("根据阶段步骤实例id[{}]获取到的实例数据为空", ids);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public StageStepExampleDTO getByProjectIdAndStepValue(Long projectId, Integer stepValue) {
        return StageStepExampleConvertUtil.makeEntity2DTO(
            this.dao.getByProjectIdAndStepValue(projectId, stepValue));
    }

    @Override
    public List<StageStepExampleDTO> findByStageExampleIds(Collection<Long> stageExampleIds) {
        if (CollUtil.isNotEmpty(stageExampleIds)) {
            List<StageStepExample> stageStepExamples = this.dao.findByStageExampleIds(
                stageExampleIds);
            if (CollUtil.isNotEmpty(stageStepExamples)) {
                return stageStepExamples.stream().map(StageStepExampleConvertUtil::makeEntity2DTO)
                    .collect(
                        Collectors.toList());
            } else {
                log.warn("根据阶段实例id[{}]获取到的阶段步骤实例数据为空", stageExampleIds);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 当前只有一个阶段,就是审计项目全景图
     * 所以这里就简单处理，根据审计项目角色进行权限判断
     */
    @Override
    public StageStepAuthAndConfigDTO getStepAuthAndConfig(StageStepAuthAndConfigParamDTO param) {
        String operatorId = param.getOperatorId();
        Long stageStepExampleId = param.getStageStepExampleId();

        log.info("操作人[{}]尝试访问[{}]节点", operatorId, stageStepExampleId);

        StageStepExample stageStepExample = this.dao.getById(stageStepExampleId);
        if (Objects.isNull(stageStepExample)) {
            log.error("id[{}]对应阶段步骤实例不存在", stageStepExampleId);
            throw new BussinessException(CANT_FIND_STAGE_STEP_EXAMPLE_DATA);
        }

        StageStepExampleDTO stageStepExampleDTO = StageStepExampleConvertUtil.makeEntity2DTO(
            stageStepExample);

        Long stageExampleId = stageStepExample.getStageExampleId();
        Long projectId = stageStepExample.getProjectId();

        StageExampleDTO stageExampleDTO = this.stageExampleService.getStageExampleById(
            stageExampleId);

        return this.auditProjectAuthService.getAuth(
            AuditProjectAuthParamDTO.builder()
                .projectId(projectId)
                .orgId(operatorId)
                .stageStepExample(stageStepExampleDTO)
                .stageExample(stageExampleDTO)
                .debugFlag(param.getDebugFlag())
                .build());
    }
}