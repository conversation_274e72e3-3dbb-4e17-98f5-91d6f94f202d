package com.sinitek.bnzg.audit.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.common.dto.AuditCommonImportParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectDetailDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectQueryParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditReStartProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditStartProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditStopProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditWordFileParamDTO;
import com.sinitek.bnzg.audit.project.dto.StageStepFinishCheckParamDTO;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectExcelImportWrapperDTO;
import com.sinitek.bnzg.audit.project.excelimport.service.IAuditProjectExcelImportService;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectStepExampleService;
import com.sinitek.bnzg.audit.project.support.AuditProjectResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 07/29/2024 14:27
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/audit/project")
@Api(value = "/frontend/api/audit/project", tags = "审计系统-审计项目管理")
public class AuditProjectController {

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private AuditProjectResultFormat format;

    @Autowired
    private IAuditProjectExcelImportService auditProjectExcelImportService;

    @Autowired
    private IAuditProjectStepExampleService auditProjectStepExampleService;

    @ApiOperation(value = "启动项目")
    @PostMapping(path = "/start")
    public void startProject(@Validated @RequestBody AuditStartProjectParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        log.info("[{}]启动项目[{}]", orgId, param.getProjectId());
        this.projectService.start(param);
    }

    @ApiOperation(value = "重开项目")
    @PostMapping(path = "/re-start")
    public void startProject(@Validated @RequestBody AuditReStartProjectParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        log.info("[{}]重开项目[{}]", orgId, param.getProjectId());
        this.projectService.reStart(param);
    }

    @ApiOperation(value = "终止项目")
    @PostMapping(path = "/stop")
    public void stopProject(@Validated @RequestBody AuditStopProjectParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        log.info("操作人[{}]终止项目[{}]", orgId, param.getProjectId());
        this.projectService.stop(param);
    }

    @ApiOperation(value = "加载项目详情")
    @GetMapping(path = "/load-project-detail")
    public RequestResult<AuditProjectDetailDTO> loadProjectDetail(
        @Validated @RequestParam("projectId") Long projectId) {
        return new RequestResult<>(this.projectService.loadExistsProjectInfoById(projectId));
    }

    @ApiOperation(value = "自动生成文档")
    @PostMapping(path = "/generate-audit-file")
    public RequestResult<UploadFileDTO> generateWordFile(
        @Validated @RequestBody AuditWordFileParamDTO param)
        throws Exception {
        return new RequestResult<>(this.projectService.generateWordFile(param));
    }

    @ApiOperation(value = "查询所有的审计项目")
    @PostMapping(path = "/find-audit-project")
    public RequestResult<List<AuditProjectInfoDTO>> findAllAuditProject() {
        return new RequestResult<>(this.projectService.findAllAuditProject());
    }

    @ApiOperation(value = "查询审计项目")
    @PostMapping(path = "/list-project")
    public RequestResult<List<AuditProjectInfoDTO>> listAuditProject(
        @RequestBody AuditProjectQueryParamDTO param) {
        return new RequestResult<>(this.projectService.listAuditProject(param));
    }


    @ApiOperation(value = "查询审计项目统计分页列表")
    @PostMapping(path = "/search")
    public TableResult<AuditProjectSearchResultDTO> search(
        @RequestBody @Valid AuditProjectSearchParamDTO param) {
        IPage<AuditProjectSearchResultDTO> search = this.projectService.search(param);
        return param.build(search, this.format);
    }

    @ApiOperation(value = "根据项目id查询项目审计年份")
    @GetMapping(path = "/load-project-audit-year")
    public RequestResult<Integer> loadProjectAuditYear(
        @Validated @RequestParam("projectId") Long projectId) {
        return new RequestResult<Integer>(this.projectService.loadProjectAuditYearById(projectId));
    }

    @ApiOperation(value = "导入审计项目")
    @PostMapping(path = "/import")
    public RequestResult<ProjectExcelImportWrapperDTO> importLib(
        @Validated @RequestBody AuditCommonImportParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        return new RequestResult<>(this.auditProjectExcelImportService.importAuditProject(param));
    }

    @ApiOperation(value = "保存导入审计项目数据")
    @PostMapping(path = "/save-import-data")
    public RequestResult<Void> saveImportLib(
        @Validated @RequestBody ProjectExcelImportWrapperDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        this.auditProjectExcelImportService.saveProject(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "检查能否完成审计报告")
    @PostMapping(path = "/check-can-finish-audit-report")
    public RequestResult<AuditCheckResultDTO> checkCanFinishAuditReport(
        @RequestBody @Valid StageStepFinishCheckParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        AuditCheckResultDTO result = this.auditProjectStepExampleService.checkCanFinishAuditReport(
            param);
        return new RequestResult<>(result);
    }
}
