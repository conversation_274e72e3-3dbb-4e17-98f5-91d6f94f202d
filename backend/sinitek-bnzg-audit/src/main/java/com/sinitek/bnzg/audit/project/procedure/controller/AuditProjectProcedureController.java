package com.sinitek.bnzg.audit.project.procedure.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeStatisticsResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureDeleteParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchParamOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchResultOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.procedure.support.AuditProjectProcedureSearchOnProjectConfigResultFormat;
import com.sinitek.bnzg.audit.project.procedure.support.LibRefProjectInfoDTOSearchResultFormat;
import com.sinitek.bnzg.audit.project.support.AuditPpHomeSearchResultFormat;
import com.sinitek.bnzg.audit.project.support.AuditProjectProcedureSearchResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import com.sinitek.sirm.lowcode.model.dto.LcIdDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 07/29/2024 14:27
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/audit/project/procedure")
@Api(value = "/frontend/api/audit/project/procedure", tags = "审计系统-项目程序管理")
public class AuditProjectProcedureController {

    @Autowired
    private IAuditProjectProcedureService projectProcedureService;

    @Autowired
    private AuditProjectProcedureSearchResultFormat formatter;

    @Autowired
    private LibRefProjectInfoDTOSearchResultFormat libRefProjectInfoDTOSearchResultFormat;

    @Autowired
    private AuditProjectProcedureSearchOnProjectConfigResultFormat resultFormat;

    @Autowired
    private AuditPpHomeSearchResultFormat auditPpHomeSearchResultFormat;

    @ApiOperation(value = "保存项目程序")
    @PostMapping(path = "/save")
    public void saveProjectProcedure(
        @Validated @RequestBody AuditProjectProcedureSaveParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        this.projectProcedureService.save(param);
    }

    @ApiOperation(value = "删除项目程序")
    @PostMapping(path = "/delete-by-key")
    public RequestResult<Void> deleteProjectProcedure(
        @Validated @RequestBody AuditProjectProcedureDeleteParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        this.projectProcedureService.delete(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "根据审计程序库id查询被引用的项目")
    @PostMapping(path = "/list-by-lib-id")
    public RequestResult<List<LibRefProjectInfoSearchResultDTO>> findLibRefInfo(
        @Validated @RequestBody LcIdDTO param) {
        Long id = param.getId();
        return new RequestResult<>(this.projectProcedureService.findLibRefInfo(
            Collections.singleton(id)));
    }

    @ApiOperation(value = "根据审计程序库id查询被引用的项目")
    @PostMapping(path = "/search-by-lib-id")
    public TableResult<LibRefProjectInfoSearchResultDTO> searchLibRefInfo(
        @Validated @RequestBody LibRefProjectInfoSearchParamDTO param) {
        IPage<LibRefProjectInfoSearchResultDTO> result = this.projectProcedureService.searchLibRefInfo(
            param);
        return param.build(result, this.libRefProjectInfoDTOSearchResultFormat);
    }

    @PostMapping(value = "/search")
    @ApiOperation("审计程序查询")
    public TableResult<LcBaseModel> search(
        @RequestBody @Valid AuditProjectProcedureSearchParamDTO param) {
        return this.projectProcedureService.search(param);
    }

    @PostMapping(value = "/search-on-pp-config")
    @ApiOperation("审计程序设置/表格查询")
    public TableResult<AuditProjectProcedureSearchResultOnProjectConfigDTO> search(
        @RequestBody @Valid AuditProjectProcedureSearchParamOnProjectConfigDTO param) {
        IPage<AuditProjectProcedureSearchResultOnProjectConfigDTO> pageResult = this.projectProcedureService.searchOnProjectConfig(
            param);
        return param.build(pageResult, this.resultFormat);
    }

    @GetMapping(value = "/list-by-project-id")
    @ApiOperation("审计程序查询")
    public RequestResult<List<AuditProjectProcedureInfoDTO>> findByProjectId(
        @RequestParam("projectId") Long projectId) {
        List<AuditProjectProcedureInfoDTO> list = this.projectProcedureService.findByProjectId(
            projectId);
        return new RequestResult<>(this.formatter.format(list));
    }


    @GetMapping(value = "/count-home-procedure")
    @ApiOperation("项目审计程序首页统计")
    public RequestResult<AuditPpHomeStatisticsResultDTO> countHomeProcedure() {
        String orgId = CurrentUserFactory.getOrgId();
        AuditPpHomeStatisticsResultDTO result = this.projectProcedureService.countHomeProcedure(
            orgId);
        return new RequestResult<>(result);
    }

    @PostMapping(value = "/search-home-procedure")
    @ApiOperation("项目审计程序首页查询")
    public TableResult<AuditPpHomeSearchResultDTO> searchHomeProcedure(
        @RequestBody @Valid AuditPpHomeSearchParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setCurrentOrgId(orgId);
        IPage<AuditPpHomeSearchResultDTO> pageResult = this.projectProcedureService.searchHomeProcedure(
            param);
        return param.build(pageResult, this.auditPpHomeSearchResultFormat);
    }
}
