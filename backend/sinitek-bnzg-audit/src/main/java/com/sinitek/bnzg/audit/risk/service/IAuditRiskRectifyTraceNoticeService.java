package com.sinitek.bnzg.audit.risk.service;

/**
 * 项目风险点 整改跟踪提醒 接口
 *
 * <AUTHOR>
 * date 2024-08-28
 */
public interface IAuditRiskRectifyTraceNoticeService {

    /**
     * 风险点整改状态变动当天提醒
     */
    void saveRectifyChangeNotice(Long riskId, String operatorId);

    /**
     * 删除日程时，同时删除对应提醒
     */
    void deleteRectifyChangeCalendar(Long riskId, String operatorId);

    void deleteRectifyChangeNotice(Long riskId, String operatorId);

    /**
     * 保存整改跟踪提醒
     */
    void saveRectifyTraceNotice(Long riskId, String operatorId);

    /**
     * 删除日程时，同时删除对应提醒
     */
    void deleteRectifyTraceCalendar(Long riskId, String operatorId);

    void deleteRectifyTraceNotice(Long riskId, String operatorId);
}
