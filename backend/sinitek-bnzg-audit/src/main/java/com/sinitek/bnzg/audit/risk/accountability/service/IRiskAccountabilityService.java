package com.sinitek.bnzg.audit.risk.accountability.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.DeleteAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.SaveOrEditAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import java.util.List;

/**
 * <AUTHOR>
 * @Date：2024/11/15 11:11
 */
public interface IRiskAccountabilityService {

    IPage<AuditRiskAcSearchResultDTO> search(AuditRiskAcSearchParamDTO searchDTO);

    AuditRiskAcSearchResultDTO loadDetailByRiskId(Long riskId);

    void editAuditRiskAc(SaveOrEditAuditRiskAcParamDTO param);

    void saveAuditRiskAc(SaveOrEditAuditRiskAcParamDTO param);

    void deleteByIdList(DeleteAuditRiskAcParamDTO param);

    AuditRiskAc getAuditRiskAcByRiskId(Long riskId);

    List<AuditRiskAc> findAuditRiskAcByRiskIds(List<Long> riskIds);

    void updateByRiskId(Long riskId, Long newRiskId);


}
