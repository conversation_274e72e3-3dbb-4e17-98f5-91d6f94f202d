package com.sinitek.bnzg.audit.risk.accountability.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/11/15 10:32
 */


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目风险点问责情况列表参数DTO")
public class AuditRiskAcSearchParamDTO extends PageDataParam {

    @ApiModelProperty(value = "审计计划")
    private List<Long> planIds;

    @ApiModelProperty("审计项目字符串")
    private String projectName;

    @ApiModelProperty("审计程序字符串")
    private String procedureName;

    @ApiModelProperty("风险点字符串")
    private String riskName;

    @ApiModelProperty("责任部门")
    private List<String> respDeptIds;

    @ApiModelProperty("责任人")
    private List<String> respManIds;

}

