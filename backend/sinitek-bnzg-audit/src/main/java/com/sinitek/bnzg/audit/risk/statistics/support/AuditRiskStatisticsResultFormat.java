package com.sinitek.bnzg.audit.risk.statistics.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsReasultDTO;
import com.sinitek.bnzg.common.constant.EnumConstant;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date：2024/9/24 14:13
 */
@Component
public class AuditRiskStatisticsResultFormat extends AuditRiskStatisticsReasultDTO implements
    ITableResultFormat<AuditRiskStatisticsReasultDTO> {

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Override
    public List<AuditRiskStatisticsReasultDTO> format(List<AuditRiskStatisticsReasultDTO> data) {
        if (CollUtil.isEmpty(data)) {
            return data;
        }

        List<String> orgIds = new LinkedList<>();
        data.forEach(item -> {
            String auditorId = item.getAuditorId();
            orgIds.add(auditorId);
        });
        List<String> respDeptIds = data.stream()
            .map(item -> item.getRespDeptIds())
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .collect(Collectors.toList());
        List<String> businessUnits = data.stream()
            .map(item -> item.getBusinessUnits())
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .collect(Collectors.toList());
        orgIds.addAll(respDeptIds);
        orgIds.addAll(businessUnits);

        // 审计人名称
        Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
            orgIds);
        // 风险类型
        Map<String, String> riskTypeValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_TYPE);
        // 风险等级
        Map<String, String> riskLevelValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_LEVEL);
        // 整改状态
        Map<String, String> rectifyStateValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RECTIFY_STATUS);
        //责任部门建议
        Map<String, String> respDeptSugTypeValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RESP_DEPT_SUGGEST_TYPE);
        //制度方向
        Map<String, String> firstCatalogValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.FIRST_CATALOG);

        //是否重复发生
        Map<String, String> repeatFlagValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            EnumConstant.COMMON, EnumConstant.BOOLEAN_TYPE);

        data.forEach(item -> {
            Integer riskType = item.getRiskType();
            Integer riskLevel = item.getRiskLevel();
            Integer rectifyState = item.getRectifyState();
            String auditorId = item.getAuditorId();
            Integer respDeptSugType = item.getRespDeptSugType();

            item.setRiskTypeName(MapUtils.getString(riskTypeValueAndNameMap, riskType));
            item.setRiskLevelName(MapUtils.getString(riskLevelValueAndNameMap, riskLevel));
            item.setRectifyStateName(MapUtils.getString(rectifyStateValueAndNameMap, rectifyState));
            item.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId));
            item.setRespDeptSugTypeName(
                MapUtils.getString(respDeptSugTypeValueAndNameMap, respDeptSugType));
            item.setFirstCatalogName(
                MapUtils.getString(firstCatalogValueAndNameMap, item.getFirstCatalog()));
            String planName = item.getPlanName();
            String projectName = item.getProjectName();
            if (StringUtils.isNotBlank(planName)) {
                item.setPlanProjectName(String.format("%s/%s", planName, projectName));
            } else {
                item.setPlanProjectName(item.getProjectName());
            }
            String respDeptNames = Optional.ofNullable(item.getRespDeptIds())
                .orElse(Collections.emptyList())
                .stream()
                .map(id -> org.apache.commons.collections4.MapUtils.getString(orgIdAndNameMap, id,
                    ""))
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(","));
            item.setRespDeptName(respDeptNames);

            String businessUnitsName = Optional.ofNullable(item.getBusinessUnits())
                .orElse(Collections.emptyList())
                .stream()
                .map(id -> org.apache.commons.collections4.MapUtils.getString(orgIdAndNameMap, id,
                    ""))
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(","));
            item.setBusinessUnitsName(businessUnitsName);
            item.setRepeatFlagName(
                org.apache.commons.collections4.MapUtils.getString(repeatFlagValueAndNameMap,
                    String.valueOf(item.getRepeatFlag())));

        });
        return data;
    }
}
