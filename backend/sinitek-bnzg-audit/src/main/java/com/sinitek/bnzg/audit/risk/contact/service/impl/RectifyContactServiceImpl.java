package com.sinitek.bnzg.audit.risk.contact.service.impl;


import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.RISK_RECTIFY_STATE_ALREADY_FINISH_CONTACT;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.contact.dao.RectifyContactDAO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactBaseDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactLoadResultDTO;
import com.sinitek.bnzg.audit.risk.contact.entity.RectifyContact;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.bnzg.audit.risk.contact.util.RectifyContactConvertUtil;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import com.sinitek.bnzg.audit.risk.log.rectify.contact.util.AuditRectifyContactChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespManService;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 整改联系人 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-29
 */
@Slf4j
@Service
public class RectifyContactServiceImpl implements IRectifyContactService {

    @Autowired
    private RectifyContactDAO dao;

    @Autowired
    private AuditRiskDAO riskDAO;

    @Autowired
    private IAuditRiskRespDeptService deptService;

    @Autowired
    private IAuditRiskRespManService respManService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveContacts(RectifyContactEditParamDTO param) {
        List<String> contacts = param.getContacts();
        Long riskId = param.getRiskId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditRisk risk = this.riskDAO.getById(riskId);
        if (Objects.nonNull(risk)) {
            Integer rectifyState = risk.getRectifyState();
            if (ObjectUtil.isNotEmpty(rectifyState) &&
                rectifyState.equals(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH)) {
                log.error("风险点[{}]目前已经是整改完成状态，无法再次保存整改联系人", riskId);
                throw new BussinessException(RISK_RECTIFY_STATE_ALREADY_FINISH_CONTACT);
            }
        }

        List<RectifyContact> existsContacts = this.dao.findByRiskId(riskId);
        List<String> existsContactOrgIds = null;
        if (CollUtil.isNotEmpty(existsContacts)) {
            existsContactOrgIds = existsContacts.stream().map(RectifyContact::getOrgId)
                .collect(Collectors.toList());
            List<Long> existsIds = existsContacts.stream().map(RectifyContact::getId)
                .collect(Collectors.toList());
            this.dao.removeByIds(existsIds);
        }
        List<RectifyContact> needSaveList = new LinkedList<>();
        if (CollUtil.isNotEmpty(contacts)) {
            for (int i = 0; i < contacts.size(); i++) {
                String value = contacts.get(i);
                if (StringUtils.isNotBlank(value)) {
                    RectifyContact data = new RectifyContact();
                    data.setSort(i);
                    data.setOrgId(value);
                    data.setRiskId(riskId);
                    needSaveList.add(data);
                } else {
                    log.warn("保存整改联系人时,传入数据为空");
                }
            }
            if (CollUtil.isNotEmpty(needSaveList)) {
                log.info("当前需保存整改联系人[{}]条", needSaveList);
                this.dao.saveBatch(needSaveList);
            } else {
                log.info("当前无需保存整改联系人");
            }
        }
        if (Objects.isNull(existsContactOrgIds) && CollUtil.isEmpty(needSaveList)) {
            // 无变动
            log.info("本次操作整改联系人数据未变动,无需发布整改联系人变动事件");
            return;
        }
        String remark = "更新";
        if (Objects.isNull(existsContactOrgIds)) {
            remark = "新增";
        }

        String existsContactOrgIdsData = JsonUtil.toJsonString(existsContactOrgIds);
        String contactsData = JsonUtil.toJsonString(contacts);

        AuditRectifyContactChangeEventPublishUtil.publishEvent(
            RecordChangeLogAddParamDTO.<String>builder()
                .foreignKey(riskId)
                .oldValue(existsContactOrgIdsData)
                .newValue(contactsData)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark(remark)
                .build());
    }

    @Override
    public RectifyContactLoadResultDTO getContactByRiskId(Long riskId) {
        RectifyContactLoadResultDTO dto = new RectifyContactLoadResultDTO();
        List<RectifyContact> contacts = this.dao.findByRiskId(riskId);
        List<String> deptIds = this.deptService.findByRiskId(riskId).stream()
            .map(AuditRiskRespDept::getRespDeptId)
            .collect(Collectors.toList());
        List<String> respManIds = this.respManService.findByRiskId(riskId).stream()
            .map(AuditRiskRespMan::getRespManId)
            .collect(Collectors.toList());
        dto.setRespDeptId(deptIds);
        dto.setRespManId(respManIds);
        dto.setContacts(
            contacts.stream().map(RectifyContact::getOrgId).collect(Collectors.toList()));
        return dto;
    }

    @Override
    @SuppressWarnings("squid:ReturnMapCheck")
    public Map<Long, List<String>> getContactMap(Collection<Long> riskIds) {
        if (CollUtil.isNotEmpty(riskIds)) {
            List<RectifyContact> list = this.dao.findByRiskId(riskIds);
            if (CollUtil.isNotEmpty(list)) {
                // key: 风险id,value: 整改联系人
                Map<Long, List<RectifyContact>> riskIdAndContactMap = list.stream()
                    .collect(Collectors.groupingBy(RectifyContact::getRiskId));
                Map<Long, List<String>> result = new HashMap<>();
                riskIdAndContactMap.forEach((riskId, contacts) -> {
                    List<String> contactNames = contacts.stream()
                        .sorted(Comparator.comparingInt(RectifyContact::getSort))
                        .map(RectifyContact::getOrgId).collect(
                            Collectors.toList());
                    result.put(riskId, contactNames);
                });
                return result;
            }
        }
        return Collections.emptyMap();
    }

    @Override
    public List<RectifyContactBaseDTO> findContactByRiskIds(Collection<Long> riskIds) {
        if (CollUtil.isNotEmpty(riskIds)) {
            List<RectifyContact> list = this.dao.findByRiskId(riskIds);
            if (CollUtil.isNotEmpty(list)) {
                return list.stream().map(RectifyContactConvertUtil::makeEntity2BaseDTO).collect(
                    Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<RectifyContact> findByRiskId(Long riskId) {
        return this.dao.findByRiskId(riskId);
    }

    @Override
    @Transactional
    public void deleteByIds(Collection<Long> ids) {
        this.dao.removeByIds(ids);
    }
}
