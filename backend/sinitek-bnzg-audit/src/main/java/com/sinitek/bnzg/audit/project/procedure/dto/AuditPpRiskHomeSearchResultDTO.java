package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@ApiModel("项目审计程序风险点首页查询 - 返回DTO")
public class AuditPpRiskHomeSearchResultDTO {

    @ApiModelProperty(value = "风险id")
    private Long id;

    @ApiModelProperty(value = "审计程序id")
    private Long procedureId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态名")
    private String statusName;

    @ApiModelProperty(value = "审计程序名称/风险点")
    private String name;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "支持审批标识")
    private Boolean supportApproveFlag;

    @ApiModelProperty(value = "审批反馈")
    private String approveRemark;
}
