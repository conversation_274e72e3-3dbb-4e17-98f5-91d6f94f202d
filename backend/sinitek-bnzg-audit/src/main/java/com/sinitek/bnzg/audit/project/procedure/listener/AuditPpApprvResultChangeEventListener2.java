package com.sinitek.bnzg.audit.project.procedure.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditPpApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpStatusUpdateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureExecutionService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpApprvResultChangeEventListener2 {

    @Autowired
    private IPpApproveResultService approveResultService;

    @Autowired
    private IAuditProjectProcedureExecutionService ppExecutionService;

    /**
     * 监听项目审计程序审批事件,把审批结果,审批反馈写回项目审计程序
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditPpApprvResultChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        AuditPpApprvResultChangeEvent<T> event) {
        T source = event.getSource();
        log.info("监听项目审计程序审批事件,把审批结果,审批反馈写回项目审计程序");
        if (source instanceof RecordChangeLogAddParamDTO) {
            // 同步项目审计程序审批结果
            RecordChangeLogAddParamDTO<Integer> data = ((RecordChangeLogAddParamDTO<Integer>) source);
            this.syncPpData(Collections.singleton(data.getForeignKey()), data.getOperatorId(),
                data.getOpTime());
        } else {
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> data = ((AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source);
            Collection<Long> foreignKeys = data.getForeignKeys();
            Long oneKey = null;
            if (CollUtil.isNotEmpty(foreignKeys)) {
                oneKey = foreignKeys.iterator().next();
            }
            this.syncPpData(data.getForeignKeys(), data.getOperatorId(oneKey),
                data.getOpTime(oneKey));
        }
    }

    private void syncPpData(Collection<Long> keys, String gloablOperator, Date globalOpTime) {
        List<PpApproveResultBaseInfoDTO> list = this.approveResultService.findExistByIds(
            keys);

        List<PpApproveResultBaseInfoDTO> needSyncDataList = list.stream().filter(item -> {
            Integer approveResult = item.getApproveResult();
            return Objects.equals(approveResult, AuditRiskApproveResultConstant.APPROVED)
                || Objects.equals(approveResult, AuditRiskApproveResultConstant.NOT_APPROVED);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(needSyncDataList)) {
            List<Long> ppIds = new LinkedList<>();
            // key:项目审计程序id,value:新状态
            Map<Long, Integer> idAndStatusMap = new HashMap<>(list.size());
            // key:项目审计程序id,value:审批人
            Map<Long, String> idAndApproverIdMap = new HashMap<>(list.size());
            // key:项目审计程序id,value:审批时间
            Map<Long, Date> idAndApproveTimeMap = new HashMap<>(list.size());
            // key:项目审计程序id,value:审批反馈
            Map<Long, String> idAndApproveRemarkMap = new HashMap<>(list.size());

            list.forEach(item -> {
                Long id = item.getId();
                Long ppId = item.getPpId();
                Long projectId = item.getProjectId();
                Long procedureId = item.getProcedureId();
                Integer approveResult = item.getApproveResult();
                String operatorId = item.getOperatorId();
                Date opTime = item.getOpTime();
                String approveRemark = item.getApproveRemark();

                if (Objects.equals(AuditRiskApproveResultConstant.NO_NEED, approveResult)) {
                    log.info(
                        "监听项目审计程序审批 {} 提交,项目审计审计程序 [id: {},projectId: {},procedureId: {}] 为无需审批,不需要同步状态和审批反馈",
                        id, ppId, projectId, procedureId);
                } else if (Objects.equals(AuditRiskApproveResultConstant.DRAFT, approveResult)) {
                    log.info(
                        "监听项目审计程序审批 {} 创建,项目审计审计程序 [id: {},projectId: {},procedureId: {}] 为待审批,不需要同步状态和审批反馈",
                        id, ppId, projectId, procedureId);
                } else {
                    ppIds.add(ppId);
                    if (Objects.equals(AuditRiskApproveResultConstant.APPROVED,
                        approveResult)) {
                        idAndStatusMap.put(ppId, AuditRiskStatusConstant.APPROVED);
                    } else {
                        idAndStatusMap.put(ppId, AuditRiskStatusConstant.NOT_APPROVED);
                    }
                    idAndApproverIdMap.put(ppId, operatorId);
                    idAndApproveTimeMap.put(ppId, opTime);
                    idAndApproveRemarkMap.put(ppId, approveRemark);
                }
            });

            // 1,2
            if (CollUtil.isNotEmpty(ppIds)) {
                log.info(
                    "监听项目审计程序审批 {} 提交,项目审计审计程序 [ppIds: {}] 需要同步状态和审批反馈",
                    keys, ppIds);
                this.ppExecutionService.updateAfterApprove(
                    AuditPpStatusUpdateParamDTO.builder()
                        .ppIds(ppIds)
                        .idAndStatusMap(idAndStatusMap)
                        .idAndApproveRemarkMap(idAndApproveRemarkMap)
                        .idAndApproverIdMap(idAndApproverIdMap)
                        .idAndApproveTimeMap(idAndApproveTimeMap)
                        .operatorId(gloablOperator)
                        .opTime(globalOpTime)
                        .build());
            } else {
                log.info(
                    "监听项目审计程序审批 {} 提交,项目审计审计程序 [ppIds: {}] 不需要同步状态和审批反馈",
                    keys, ppIds);
            }
        } else {
            log.info(
                "项目审计程序审批 id {} 下不存在需要同步的项目审计程序审批数据,提交后无需同步数据",
                keys);
        }
    }

}
