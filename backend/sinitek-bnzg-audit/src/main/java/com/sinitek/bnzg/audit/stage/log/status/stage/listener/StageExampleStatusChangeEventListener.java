package com.sinitek.bnzg.audit.stage.log.status.stage.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import com.sinitek.bnzg.audit.stage.log.status.stage.event.StageExampleStatusChangeEvent;
import com.sinitek.bnzg.audit.stage.log.status.stage.service.IStageExampleStatusLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class StageExampleStatusChangeEventListener<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, Integer> {

    @Autowired
    private IStageExampleStatusLogService logService;

    @Override
    protected String getEventName() {
        return "阶段实例状态改变";
    }

    @Override
    protected IStageExampleStatusLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = StageExampleStatusChangeEvent.class, fallbackExecution = true)
    public void listen(
        StageExampleStatusChangeEvent<E> event) {
        this.doListen(event);
    }
}
