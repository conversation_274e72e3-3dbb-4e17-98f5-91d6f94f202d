package com.sinitek.bnzg.audit.project.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "审计项目信息(附带成员)")
public class AuditProjectDetailDTO {

    @ApiModelProperty("主键")
    private Long id;

    /**
     * 计划id
     */
    @ApiModelProperty("计划id")
    private Long planId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("结束日期")
    private Date endDate;

    /**
     * 项目进度
     */
    @ApiModelProperty("项目进度")
    private Integer projectPhase;

    /**
     * 启动日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("启动日期")
    private Date startupDate;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /*
     * 审计期开始日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计期开始日期")
    private Date periodStartDate;

    /*
     * 审计期结束日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计期结束日期")
    private Date periodEndDate;

    @ApiModelProperty("管理员OrgId")
    private List<String> ownerIds;

    @ApiModelProperty("管理员名称(多个值,中间以,分割)")
    private String ownerNames;


    /**
     * 现场审计开始日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("现场审计开始日期")
    private Date localAuditStartDate;

    /**
     * 现场审计结束日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("现场审计结束日期")
    private Date localAuditEndDate;

    /**
     * 征求意见开始日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("征求意见开始日期")
    private Date consultationStartDate;

    /**
     * 征求意见结束日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("征求意见结束日期")
    private Date consultationEndDate;

    /**
     * 审计报告完成日期
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计报告完成日期")
    private Date reportCompleteDate;

    /**
     * 上报时间
     */
    @ApiModelProperty("上报时间")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date regulatoryTime;

    /**
     * 项目类型
     */
    @ApiModelProperty("项目类型")
    private Integer projectType;

    @ApiModelProperty("项目类型名称")
    private String projectTypeName;

    @ApiModelProperty("是否报送")
    private Integer regulatoryFlag;

    @ApiModelProperty("是否报送名称")
    private String regulatoryFlagName;

    @ApiModelProperty("项目进度名")
    private String projectPhaseName;

    @ApiModelProperty("备注")
    private String remark;
}
