package com.sinitek.bnzg.audit.project.procedure.approve.log.result.listener;

import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditRiskApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.service.IAuditRiskApprvResultChangeLogService;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRiskApprvResultChangeEventListener3<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, Integer> {

    @Autowired
    private IAuditRiskApprvResultChangeLogService logService;

    @Override
    protected String getEventName() {
        return "风险点审批结果变动";
    }

    @Override
    protected IAuditRiskApprvResultChangeLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditRiskApprvResultChangeEvent.class, fallbackExecution = true)
    public void listen(
        AuditRiskApprvResultChangeEvent<E> event) {
        this.doListen(event);
    }
}
