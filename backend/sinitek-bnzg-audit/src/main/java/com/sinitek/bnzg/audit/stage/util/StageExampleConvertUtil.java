package com.sinitek.bnzg.audit.stage.util;

import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.entity.StageExample;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 08/15/2024 16:50
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StageExampleConvertUtil {

    public static StageExampleDTO makeEntity2DTO(StageExample entity) {
        if (Objects.nonNull(entity)) {
            StageExampleDTO dto = new StageExampleDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }
        return null;
    }

}
