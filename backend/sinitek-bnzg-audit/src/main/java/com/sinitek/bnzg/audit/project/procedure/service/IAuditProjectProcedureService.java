package com.sinitek.bnzg.audit.project.procedure.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.dto.AuditProjectCopyResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureBatchSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpChangeParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeStatisticsResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpSaveOrSubmitWorkParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureDeleteParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchParamOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchResultOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.DeletePpAfterProcedureDeleteParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpExecutionSearchParamDTO;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 08/08/2024 09:54
 */
public interface IAuditProjectProcedureService {

    void saveWork(AuditPpSaveOrSubmitWorkParamDTO param);

    void submitWork(AuditPpSaveOrSubmitWorkParamDTO param);

    void save(AuditProjectProcedureSaveParamDTO param);

    void batchSave(AuditProjectProcedureBatchSaveParamDTO param);

    void copyCascadeByProject(AuditProjectCopyResultDTO param);

    void delete(AuditProjectProcedureDeleteParamDTO param);

    /**
     * 审计程序删除后删除相关联的项目审计程序
     */
    void deleteProjectprocedureAfterProcedureDelete(DeletePpAfterProcedureDeleteParamDTO param);

    /**
     * 变更审计项目程序
     */
    Long changeProjectProcedure(AuditPpChangeParamDTO param);

    void cancelChangeProjectProcedure(AuditPpChangeParamDTO param);

    /**
     * 获取审计程序库被引用项目信息
     */
    @SuppressWarnings("squid:ReturnMapCheck")
    Map<Long, Integer> getLibRefCountInfo(Collection<Long> libIds);

    List<LibRefProjectInfoSearchResultDTO> findLibRefInfo(Collection<Long> libIds);

    IPage<LibRefProjectInfoSearchResultDTO> searchLibRefInfo(LibRefProjectInfoSearchParamDTO param);

    TableResult<LcBaseModel> search(AuditProjectProcedureSearchParamDTO param);

    IPage<AuditProjectProcedureSearchResultOnProjectConfigDTO> searchOnProjectConfig(
        AuditProjectProcedureSearchParamOnProjectConfigDTO param);

    List<AuditProjectProcedureInfoDTO> findByProjectId(Long projectId);

    List<AuditProjectProcedureInfoDTO> findByProjectIds(Collection<Long> projectIds);

    List<AuditProjectProcedureInfoDTO> findExistsThreadLatestByProcedureIds(
        Collection<Long> procedureIds);

    IPage<AuditProjectProcedureInfoDTO> searchExecutionList(PpExecutionSearchParamDTO param);

    List<AuditProjectProcedureInfoDTO> findByIds(Collection<Long> ids);

    AuditProjectProcedureInfoDTO getById(Long id);

    AuditPpHomeStatisticsResultDTO countHomeProcedure(String orgId);

    IPage<AuditPpHomeSearchResultDTO> searchHomeProcedure(AuditPpHomeSearchParamDTO param);
}
