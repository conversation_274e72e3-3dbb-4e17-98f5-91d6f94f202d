package com.sinitek.bnzg.audit.risk.contact.service;

import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactBaseDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactLoadResultDTO;
import com.sinitek.bnzg.audit.risk.contact.entity.RectifyContact;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 整改联系人 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-29
 */
public interface IRectifyContactService {

    void saveContacts(RectifyContactEditParamDTO param);

    RectifyContactLoadResultDTO getContactByRiskId(Long riskId);

    @SuppressWarnings("squid:ReturnMapCheck")
    Map<Long, List<String>> getContactMap(Collection<Long> riskIds);

    List<RectifyContactBaseDTO> findContactByRiskIds(Collection<Long> riskIds);

    List<RectifyContact> findByRiskId(Long riskId);

    void deleteByIds(Collection<Long> ids);

}
