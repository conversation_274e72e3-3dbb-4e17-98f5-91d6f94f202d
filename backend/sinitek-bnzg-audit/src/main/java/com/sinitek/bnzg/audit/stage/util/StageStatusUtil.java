package com.sinitek.bnzg.audit.stage.util;


import static com.sinitek.bnzg.audit.stage.constant.StageStatusConstant.FINISHED;
import static com.sinitek.bnzg.audit.stage.constant.StageStatusConstant.PROCESSING;
import static com.sinitek.bnzg.audit.stage.constant.StageStatusConstant.READY;
import static com.sinitek.bnzg.audit.stage.constant.StageStatusConstant.TERMINATE;

import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 7/11/2023 1:01 PM
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStatusUtil {

    /**
     * 检查阶段状态能否转换
     *
     * @param oldStatus 原阶段状态
     * @param newStatus 现阶段状态
     * @return boolean true: 继续执行，false: 不再继续执行
     */
    public static boolean checkCanChangeStatus(Integer oldStatus, Integer newStatus) {
        if (Objects.isNull(newStatus)) {
            log.warn(
                "检查阶段状态能否从[oldStatus:{}]变为[{newStatus:{}}]时传入新状态为null,返回false",
                oldStatus,
                newStatus);
            return false;
        }
        switch (newStatus) {
            case PROCESSING:
                return checkCanChangeAsProcessiongStatus(oldStatus);
            case FINISHED:
                return checkCanChangeAsFinishedStatus(oldStatus);
            case TERMINATE:
                return checkCanChangeAsTerminatedStatus(oldStatus);
            default:
                log.warn(
                    "检查阶段状态能否从[oldStatus:{}]变为[{newStatus:{}}]时未处理旧状态,返回false",
                    oldStatus,
                    newStatus);
                return false;
        }
    }

    /**
     * 检查能否变成处理中状态
     */
    public static boolean checkCanChangeAsProcessiongStatus(Integer oldStatus) {
        if (Objects.equals(PROCESSING, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前状态为 处理中 ,返回false,不再继续");
            return false;
        }
        // 只有 就绪状态 才能转为 处理中状态
        if (Objects.equals(READY, oldStatus)) {
            log.info("当前状态[{}]符合变更为 处理中 的条件,允许变为 处理中", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不符合变更为 处理中 的条件,不允许变为 处理中", oldStatus);
        return false;
    }

    /**
     * 检查能否变成完成状态
     */
    public static boolean checkCanChangeAsFinishedStatus(Integer oldStatus) {
        if (Objects.equals(FINISHED, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前状态为 完成,返回false,不再继续");
            return false;
        }
        // 只有 就绪 或者为 处理中 才能转为 完成状态
        if (Objects.equals(READY, oldStatus)
            || Objects.equals(PROCESSING, oldStatus)) {
            log.info("当前状态[{}]符合变更为 完成 的条件,允许变为 完成", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不符合变更为 完成 的条件,不允许变为 完成", oldStatus);
        return false;
    }

    /**
     * 检查能否变成已终止状态
     */
    public static boolean checkCanChangeAsTerminatedStatus(Integer oldStatus) {
        if (Objects.equals(TERMINATE, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前状态为 终止,返回false,不再继续");
            return false;
        }
        // 只有 就绪 或者 处理中 才能转为 已终止状态
        if (Objects.equals(READY, oldStatus)
            || Objects.equals(PROCESSING, oldStatus)) {
            log.info("当前状态[{}]符合变更为 终止 的条件,允许变为 已终止", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不符合变更为 终止 的条件,不允许变为 终止", oldStatus);
        return false;
    }

}
