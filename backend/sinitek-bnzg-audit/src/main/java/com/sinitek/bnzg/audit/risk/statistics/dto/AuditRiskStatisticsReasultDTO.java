package com.sinitek.bnzg.audit.risk.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点查询统计-返回DTO
 *
 * <AUTHOR>
 * date 2024-09-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "风险点查询统计-返回DTO")
public class AuditRiskStatisticsReasultDTO extends PageDataParam {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "程序id")
    private Long procedureId;

    @ApiModelProperty(value = "程序名称")
    private String procedureName;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "项目名称")
    private String planProjectName;

    @ApiModelProperty(value = "风险点id")
    private Long riskId;

    @ApiModelProperty(value = "风险点名称")
    private String riskName;

    @ApiModelProperty(value = "审计年份")
    private Integer auditYear;

    @ApiModelProperty(value = "风险类型")
    private Integer riskType;

    @ApiModelProperty(value = "风险类型名")
    private String riskTypeName;

    @ApiModelProperty(value = "风险等级")
    private Integer riskLevel;

    @ApiModelProperty(value = "风险等级名")
    private String riskLevelName;

    @ApiModelProperty(value = "制度方向")
    private Integer firstCatalog;

    @ApiModelProperty(value = "制度方向名称")
    private String firstCatalogName;

    @ApiModelProperty(value = "内部分类")
    private String innerCategory;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    @ApiModelProperty(value = "整改状态")
    private Integer rectifyState;

    @ApiModelProperty(value = "整改状态名")
    private String rectifyStateName;

    @ApiModelProperty(value = "责任部门建议")
    private Integer respDeptSugType;

    @ApiModelProperty(value = "责任部门建议枚举名")
    private String respDeptSugTypeName;

    @ApiModelProperty(value = "责任部门建议备注")
    private String respDeptSuggestion;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "要求整改日期")
    private Date reqRectifyDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "实际整改日期")
    private Date actRectifyDate;

    @ApiModelProperty(value = "责任部门")
    private List<String> respDeptIds;

    @ApiModelProperty(value = "责任部门名称")
    private String respDeptName;

    @ApiModelProperty(value = "业务部门")
    private List<String> businessUnits;

    @ApiModelProperty(value = "业务部门名称")
    private String businessUnitsName;

    @ApiModelProperty(value = "是否重复发生")
    private Integer repeatFlag;

    @ApiModelProperty(value = "是否重复发生名")
    private String repeatFlagName;

    @ApiModelProperty(value = "风险点描述")
    private String description;
}
