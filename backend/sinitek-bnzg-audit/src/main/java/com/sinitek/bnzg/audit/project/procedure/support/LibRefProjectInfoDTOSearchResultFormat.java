package com.sinitek.bnzg.audit.project.procedure.support;

import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchResultDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class LibRefProjectInfoDTOSearchResultFormat implements
    ITableResultFormat<LibRefProjectInfoSearchResultDTO> {

    @Autowired
    private IAuditProjectService projectService;

    @Override
    public List<LibRefProjectInfoSearchResultDTO> format(
        List<LibRefProjectInfoSearchResultDTO> data) {

        List<Long> projectIds = data.stream().map(LibRefProjectInfoSearchResultDTO::getProjectId)
            .collect(
                Collectors.toList());

        // key: Id
        // value: 名称
        List<AuditProjectInfoDTO> projects = this.projectService.findExistsProjctByIds(
            projectIds);
        // key: projectId
        // value: name
        Map<Long, String> projectIdAndNameMap = projects.stream().collect(
            Collectors.toMap(AuditProjectInfoDTO::getId, AuditProjectInfoDTO::getName));

        data.forEach(item -> {
            Long projectId = item.getProjectId();

            item.setProjectName(MapUtils.getString(projectIdAndNameMap, projectId, ""));
        });

        return data;
    }
}
