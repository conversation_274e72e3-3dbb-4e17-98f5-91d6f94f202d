package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/19/2024 14:46
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("项目阶段更新参数")
public class UpdateProjectPhaseParamDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("新阶段")
    private Integer newProjectPhase;

    @ApiModelProperty("操作人")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date opTime;

    @ApiModelProperty("备注")
    private String remark;
}
