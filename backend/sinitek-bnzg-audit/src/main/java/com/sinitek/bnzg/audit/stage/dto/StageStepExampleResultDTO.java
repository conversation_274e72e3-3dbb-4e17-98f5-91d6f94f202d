package com.sinitek.bnzg.audit.stage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@Data
@ApiModel("阶段步骤实例")
public class StageStepExampleResultDTO {

    /**
     * 步骤id
     */
    @ApiModelProperty("步骤id")
    private Long id;

    /**
     * 对应阶段实例id
     */
    @ApiModelProperty("对应阶段实例id")
    private Long stageExampleId;

    /**
     * 步骤状态值
     */
    @ApiModelProperty("步骤状态值")
    private Integer status;

    /**
     * 步骤状态名称
     */
    @ApiModelProperty("步骤状态名称")
    private String statusName;

    /**
     * 步骤名称
     */
    @ApiModelProperty("步骤名称")
    private String stepName;

    /**
     * 阶段步骤值
     */
    @ApiModelProperty("阶段步骤值")
    private Integer stepValue;

    @ApiModelProperty("顺序")
    private Integer sort;

    /**
     * 开始处理时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("开始处理时间")
    private Date startTime;

    /**
     * 结束处理时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("结束处理时间")
    private Date endTime;

    /**
     * 终止处理时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("终止处理时间")
    private Date terminatedTime;

    /**
     * 开始处理人姓名
     */
    @ApiModelProperty("开始处理人")
    private String starterId;

    /**
     * 开始处理人
     */
    @ApiModelProperty("开始处理人姓名")
    private String starterName;

    /**
     * 结束处理人姓名
     */
    @ApiModelProperty("结束处理人")
    private String enderId;

    /**
     * 结束处理人
     */
    @ApiModelProperty("结束处理人姓名")
    private String enderName;

    /**
     * 终止处理人
     */
    @ApiModelProperty("终止处理人")
    private String terminatorId;

    /**
     * 终止处理人
     */
    @ApiModelProperty("终止处理人姓名")
    private String terminatorName;

}
