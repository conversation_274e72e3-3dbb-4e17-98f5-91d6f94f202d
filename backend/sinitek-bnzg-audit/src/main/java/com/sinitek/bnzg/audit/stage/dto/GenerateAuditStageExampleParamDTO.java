package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@SuperBuilder
@NoArgsConstructor
@ApiModel("生成审计阶段实例参数")
@EqualsAndHashCode(callSuper = true)
public class GenerateAuditStageExampleParamDTO extends GenerateStageExampleParamDTO {

}
