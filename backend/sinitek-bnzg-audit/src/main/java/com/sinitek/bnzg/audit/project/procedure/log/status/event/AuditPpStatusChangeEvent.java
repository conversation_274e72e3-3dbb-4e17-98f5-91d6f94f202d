package com.sinitek.bnzg.audit.project.procedure.log.status.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.event.AbstractRecordChangeLogEvent;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:12
 */
public class AuditPpStatusChangeEvent<T extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEvent<T> {

    public AuditPpStatusChangeEvent(T source) {
        super(source);
    }

}
