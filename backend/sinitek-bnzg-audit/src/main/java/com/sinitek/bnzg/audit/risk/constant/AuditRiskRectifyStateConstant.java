package com.sinitek.bnzg.audit.risk.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/29/2024 10:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskRectifyStateConstant {

    /**
     * 待反馈
     */
//    public static final int PENDING_FEEDBACK = 1;

    /**
     * 整改中
     */
    public static final int IN_RECTIFICATION = 2;

    /**
     * 整改完成
     */
    public static final int RECTIFICATION_FINISH = 3;

    /**
     * 逾期未整改
     */
    public static final int OVERDUE_UNRECTIFIED = 4;

    /**
     * 不适用
     */
//    public static final int NOT_APPLICABLE = 5;

}