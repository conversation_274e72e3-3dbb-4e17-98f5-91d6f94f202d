package com.sinitek.bnzg.audit.risk.contact.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.risk.contact.entity.RectifyContact;
import com.sinitek.bnzg.audit.risk.contact.mapper.RectifyContactMapper;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/29/2024 17:00
 */
@Slf4j
@Component
public class RectifyContactDAO extends ServiceImpl<RectifyContactMapper, RectifyContact> {

    public List<RectifyContact> findByRiskId(Long riskId) {
        LambdaQueryWrapper<RectifyContact> queryWrapper = Wrappers.lambdaQuery(
            RectifyContact.class);
        queryWrapper.eq(RectifyContact::getRiskId, riskId);
        queryWrapper.orderByDesc(RectifyContact::getSort);
        return this.list(queryWrapper);
    }

    public List<RectifyContact> findByRiskId(Collection<Long> riskIds) {
        if (CollUtil.isNotEmpty(riskIds)) {
            LambdaQueryWrapper<RectifyContact> queryWrapper = Wrappers.lambdaQuery(
                RectifyContact.class);
            queryWrapper.in(RectifyContact::getRiskId, riskIds);
            return this.list(queryWrapper);
        } else {
            return Collections.emptyList();
        }
    }

}
