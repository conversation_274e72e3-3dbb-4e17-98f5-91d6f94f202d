package com.sinitek.bnzg.audit.project.procedure.approve.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批操作人 Entity
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_pp_approver")
@ApiModel(description = "项目审计程序审批操作人")
public class AuditPpApprover extends BaseEntity {

    /**
     * 审批id
     */
    @ApiModelProperty("审批id")
    private Long approveId;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人")
    private String opOrgid;

    @ApiModelProperty("顺序")
    private Integer sort;

}
