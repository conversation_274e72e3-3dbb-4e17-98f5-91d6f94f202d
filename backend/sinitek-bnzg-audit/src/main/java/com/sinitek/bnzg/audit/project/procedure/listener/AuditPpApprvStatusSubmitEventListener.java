package com.sinitek.bnzg.audit.project.procedure.listener;

import com.sinitek.bnzg.audit.project.procedure.approve.log.status.event.AuditPpApprvStatusChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskFinishParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditPpApproveTodoTaskService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpApprvStatusSubmitEventListener {

    @Autowired
    private IAuditPpApproveTodoTaskService auditPpApproveTodoTaskService;


    /**
     * 监听项目审计程序审批批次提交:
     *
     * 把当前待办任务置为已完成
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditPpApprvStatusChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen4TodoTask(
        AuditPpApprvStatusChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            this.finishTask((RecordChangeLogAddParamDTO<Integer>) source);
        } else {
            if (log.isWarnEnabled()) {
                log.warn("风险点的审批结果提交只能是一个个提交的,出现批量提交情况忽略 {}",
                    JsonUtil.toJsonString(event));
            }
        }
    }

    private void finishTask(RecordChangeLogAddParamDTO<Integer> source) {
        Integer newStatus = source.getNewValue();
        if (Objects.equals(newStatus, AuditRiskApproveStatusConstant.SUBMIT)) {
            Long foreignKey = source.getForeignKey();

            log.info("监听到 项目审计程序审批提交[{}]事件,开始完成代办任务", foreignKey);

            this.auditPpApproveTodoTaskService.finish(PpApproveTodoTaskFinishParamDTO.builder()
                .ppApproveId(foreignKey)
                .operatorId(source.getOperatorId())
                .opTime(source.getOpTime())
                .build());
        }
    }
}
