package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/20/2024 13:59
 */
@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("审计项目程序")
public class AuditProjectProcedureInfoDTO {

    @ApiModelProperty("项目审计程序id")
    private Long id;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("程序id")
    private Long procedureId;

    @ApiModelProperty("程序名")
    private String procedureName;

    @ApiModelProperty("执行情况")
    private String execution;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("审计人")
    private String auditorId;

    @ApiModelProperty("审计日期")
    private Date auditDate;

    @ApiModelProperty("审批人")
    private String approverId;

    @ApiModelProperty("审批时间")
    private Date approveTime;

    @ApiModelProperty("审批反馈")
    private String approveRemark;
}

