package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("获取授权用户参数")
public class AuditLoadPpAuthParamDTO {

    @NotNull(message = "项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotNull(message = "审计程序不能为空")
    @ApiModelProperty("程序id")
    private Long procedureId;

}
