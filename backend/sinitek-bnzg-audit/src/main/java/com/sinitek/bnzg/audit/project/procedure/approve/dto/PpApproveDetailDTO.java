package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 09/10/2024 17:45
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "项目审计程序审批")
public class PpApproveDetailDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("计划id")
    private Long planId;

    @ApiModelProperty("计划名称")
    private String planName;

}
