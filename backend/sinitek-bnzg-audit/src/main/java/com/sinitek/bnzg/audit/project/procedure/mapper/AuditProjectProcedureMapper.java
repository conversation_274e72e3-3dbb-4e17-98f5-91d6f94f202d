package com.sinitek.bnzg.audit.project.procedure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchParamOnProjectConfigPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectCountInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.PpExecutionSearchParamPO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:40
 */
public interface AuditProjectProcedureMapper extends BaseMapper<AuditProjectProcedure> {

    void logicDeletePp(@Param("ids") Collection<Long> ids, @Param("operatorId") String operatorId);

    IPage<AuditProjectProcedureInfoPO> searchExecutionList(
        Page<AuditProjectProcedureInfoPO> page, @Param("param") PpExecutionSearchParamPO param);

    List<LibRefProjectCountInfoPO> findLibRefCountInfo(@Param("libIds") Collection<Long> libIds);

    List<LibRefProjectInfoSearchResultPO> findLibRefInfo(@Param("libIds") Collection<Long> libIds);

    IPage<LibRefProjectInfoSearchResultPO> searchLibRefInfo(
        Page<LibRefProjectInfoSearchResultPO> page,
        @Param("param") LibRefProjectInfoSearchParamPO param);

    IPage<AuditProjectProcedureSearchResultPO> searchOnProjectConfig(
        Page<AuditProjectProcedureSearchResultPO> page,
        @Param("param") AuditProjectProcedureSearchParamOnProjectConfigPO param);

    /**
     * 突破逻辑删除限制,主要用于项目审计程序绑定，同一个项目，同一个审计程序维护threadId不变
     */
    List<AuditProjectProcedure> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 突破逻辑删除限制,主要用于项目审计程序绑定，同一个项目，同一个审计程序维护threadId不变
     */
    List<AuditProjectProcedure> findByProjectIds(@Param("projectIds") Collection<Long> projectIds);

    AuditProjectProcedure getLatestOne(@Param("id") Long id, @Param("threadId") Long threadId);

    Integer countWaitApproveProjectProcedure(@Param("orgId") String orgId);

    Integer countWaitHandleProjectProcedure(@Param("orgId") String orgId);

    /**
     * 这里查询人员名下所有审批不通过或含有审批不通过风险点的审计程序数量
     */
    Integer countNotPassPpCount(@Param("orgId") String orgId);

    IPage<AuditPpHomeSearchResultPO> searchHomeWaitHandleProcedure(
        Page<AuditPpHomeSearchResultPO> objectPage,
        @Param("param") AuditPpHomeSearchParamPO paramPO);

    IPage<AuditPpHomeSearchResultPO> searchHomeWaitApproveProcedure(
        Page<AuditPpHomeSearchResultPO> objectPage,
        @Param("param") AuditPpHomeSearchParamPO paramPO);

    IPage<AuditPpHomeSearchResultPO> searchHomeNotPassData(
        Page<AuditPpHomeSearchResultPO> objectPage,
        @Param("param") AuditPpHomeSearchParamPO paramPO);
}
