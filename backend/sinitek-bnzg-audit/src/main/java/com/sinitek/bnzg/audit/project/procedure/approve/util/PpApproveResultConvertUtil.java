package com.sinitek.bnzg.audit.project.procedure.approve.util;

import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchResultPO;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 08/30/2024 14:45
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PpApproveResultConvertUtil {

    public static PpApproveResultSearchParamPO makeSearchParamDTO2PO(
        PpApproveResultSearchParamDTO dto) {
        PpApproveResultSearchParamPO po = new PpApproveResultSearchParamPO();

        po.setApproveId(dto.getApproveId());

        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());
        return po;
    }

    public static PpApproveResultSearchResultDTO makeSearchResultPO2DTO(
        PpApproveResultSearchResultPO po) {
        if (Objects.nonNull(po)) {
            PpApproveResultSearchResultDTO dto = new PpApproveResultSearchResultDTO();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }
        return null;
    }

    public static PpApproveResultBaseInfoDTO makeEntity2BaseInfoDTO(AuditPpApproveResult entity) {
        return LcConvertUtil.convert(entity, PpApproveResultBaseInfoDTO::new);
    }

}
