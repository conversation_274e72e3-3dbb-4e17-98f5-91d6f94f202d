package com.sinitek.bnzg.audit.project.procedure.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;

/**
 * <AUTHOR>
 * @date 08/29/2024 17:31
 */
public class AuditPpSaveOrEditEvent<T extends AbstractRecordChangeLogAddParamBaseDTO> extends
    SiniCubeEvent<T> {

    public AuditPpSaveOrEditEvent(T source) {
        super(source);
    }
}
