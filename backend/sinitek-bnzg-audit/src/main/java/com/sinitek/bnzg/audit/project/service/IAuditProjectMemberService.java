package com.sinitek.bnzg.audit.project.service;

import com.sinitek.bnzg.audit.project.dto.AuditProjectCopyResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMember4ImportSaveParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberAppendParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberSaveParamWrapperDTO;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:45
 */
public interface IAuditProjectMemberService {

    /**
     * 保存除项目负责人外其他项目成员
     */
    void saveMembersWithoutOwners(AuditProjectMemberSaveParamWrapperDTO param);

    /**
     * 追加除项目负责人外其他项目成员
     */
    void appendMembersWithoutOwners(AuditProjectMemberAppendParamWrapperDTO param);

    /**
     * 导入时保存项目成员数据
     */
    void saveMembers4Import(AuditProjectMember4ImportSaveParamWrapperDTO param);

    /**
     * 设置项目负责人
     */
    void saveProjectOwners(Collection<String> orgIds, Long projectId);

    void copyCascadeByProject(AuditProjectCopyResultDTO param);

    List<AuditProjectMemberInfoDTO> findOwnerByProjectIds(Collection<Long> projectIds);

    List<AuditProjectMemberInfoDTO> findMembers(Collection<Long> projectIds,
        Collection<Integer> roles);

    List<AuditProjectMemberInfoDTO> findMemberRoles(Collection<Long> projectIds,
        Collection<String> orgIds);
}
