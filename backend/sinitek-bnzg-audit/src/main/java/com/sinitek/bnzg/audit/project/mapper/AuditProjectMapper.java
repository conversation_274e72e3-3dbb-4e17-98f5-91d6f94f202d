package com.sinitek.bnzg.audit.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.project.entity.AuditProject;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchParamPO;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchResultPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:40
 */
public interface AuditProjectMapper extends BaseMapper<AuditProject> {

    IPage<AuditProjectSearchResultPO> search(
            Page<AuditProjectSearchResultPO> page, @Param("param")AuditProjectSearchParamPO param);

    Integer loadProjectAuditYearById(@Param("projectId") Long projectId);
}
