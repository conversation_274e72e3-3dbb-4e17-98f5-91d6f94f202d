package com.sinitek.bnzg.audit.project.procedure.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 08/13/2024 11:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("项目审计程序配置时查询参数")
public class AuditProjectProcedureSearchParamOnProjectConfigPO extends PageDataParam {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("程序库名")
    private List<String> libNames;

    @ApiModelProperty("程序名")
    private List<String> procedureNames;

    @ApiModelProperty("审计年份")
    private List<Integer> auditYears;
}
