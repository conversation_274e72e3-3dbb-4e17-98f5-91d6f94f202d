package com.sinitek.bnzg.audit.project.util;

import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_VIEW_PROJECT_DATA_NOT_BELONG_ANY_ROLE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_VIEW_PROJECT_DATA_NO_ROLER;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectRoleConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:14
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectCheckUtil {

    public static final List<Integer> HAS_VIEW_AUTH = new LinkedList<>();
    public static final List<Integer> HAS_EDIT_AUTH = new LinkedList<>();
    public static final List<Integer> HAS_APPROVE_AUTH = new LinkedList<>();

    static {
        // 所有角色均能查看
        HAS_VIEW_AUTH.add(AuditProjectRoleConstant.VIEW);
        HAS_VIEW_AUTH.add(AuditProjectRoleConstant.EDIT);
        HAS_VIEW_AUTH.add(AuditProjectRoleConstant.APPROVE);
        HAS_VIEW_AUTH.add(AuditProjectRoleConstant.APPROVE_AND_EDIT);
        HAS_VIEW_AUTH.add(AuditProjectRoleConstant.OWNER);

        // 只有编辑角色才能编辑
        HAS_EDIT_AUTH.add(AuditProjectRoleConstant.EDIT);
        HAS_EDIT_AUTH.add(AuditProjectRoleConstant.APPROVE_AND_EDIT);

        // 拥有审批角色
        HAS_APPROVE_AUTH.add(AuditProjectRoleConstant.APPROVE);
        HAS_APPROVE_AUTH.add(AuditProjectRoleConstant.APPROVE_AND_EDIT);
    }


    /**
     * 检查是否为项目负责人
     */
    public static void checkProjectOwner(IAuditProjectAndOpeatorId param, String opName,
        String notOwnerErrCode, String noOwnerErrCode) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();

        // 项目负责人才有权限配置审计程序
        IAuditProjectMemberService projectMemberService = SpringFactory.getBean(
            IAuditProjectMemberService.class);
        List<AuditProjectMemberInfoDTO> owners = projectMemberService.findOwnerByProjectIds(
            Collections.singleton(projectId));
        if (CollUtil.isNotEmpty(owners)) {
            boolean isOwner = owners.stream()
                .anyMatch(item -> Objects.equals(item.getOrgId(), operatorId));
            if (!isOwner) {
                log.error("当前登录人[{}]不是当前审计项目[{}]的负责人,无法 {}", operatorId,
                    projectId, opName);
                throw new BussinessException(notOwnerErrCode);
            }
        } else {
            log.error("当前审计项目[{}]未配置项目负责人,无法 {}", projectId, opName);
            throw new BussinessException(noOwnerErrCode);
        }
    }

    /**
     * 检查是否为项目审批人
     */
    public static void checkProjectApprover(IAuditProjectAndOpeatorId param, String opName,
        String notApproverErrCode, String noApproverErrCode) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();

        IAuditProjectMemberService projectMemberService = SpringFactory.getBean(
            IAuditProjectMemberService.class);
        List<AuditProjectMemberInfoDTO> owners = projectMemberService.findMembers(
            Collections.singleton(projectId),
            HAS_APPROVE_AUTH);
        if (CollUtil.isNotEmpty(owners)) {
            boolean isApprover = owners.stream()
                .anyMatch(item -> Objects.equals(item.getOrgId(), operatorId));
            if (!isApprover) {
                log.error("当前登录人[{}]不是当前审计项目[{}]的审批人,无法 {}", operatorId,
                    projectId, opName);
                throw new BussinessException(notApproverErrCode);
            }
        } else {
            log.error("当前审计项目[{}]未配置项目审批人,无法 {}", projectId, opName);
            throw new BussinessException(noApproverErrCode);
        }
    }

    /**
     * 校验能否查看
     */
    public static void checkCanView(IAuditProjectAndOpeatorId param) {
        String operatorId = param.getOperatorId();
        Long projectId = param.getProjectId();

        IAuditProjectMemberService projectMemberService = SpringFactory.getBean(
            IAuditProjectMemberService.class);
        List<AuditProjectMemberInfoDTO> owners = projectMemberService.findMembers(
            Collections.singleton(projectId),
            HAS_VIEW_AUTH);
        if (CollUtil.isNotEmpty(owners)) {
            boolean canView = owners.stream()
                .anyMatch(item -> Objects.equals(item.getOrgId(), operatorId));
            if (!canView) {
                log.error("当前登录人[{}]未配置当前审计项目[{}]的角色,无法查看项目数据", operatorId,
                    projectId);
                throw new BussinessException(CANT_VIEW_PROJECT_DATA_NOT_BELONG_ANY_ROLE);
            }
        } else {
            log.error("当前审计项目[{}]未配置项目角色,无法查看项目数据", projectId);
            throw new BussinessException(CANT_VIEW_PROJECT_DATA_NO_ROLER);
        }
    }
}
