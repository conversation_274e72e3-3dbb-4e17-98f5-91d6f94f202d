package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 风险点提交审批确认信息
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "项目审计程序确认信息")
public class PpConfirmResultDTO {

    @ApiModelProperty(value = "项目审计程序id")
    private Long id;

    @ApiModelProperty(value = "审计程序id")
    private Long procedureId;

    @ApiModelProperty(value = "审计程序名称")
    private String name;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;
}


