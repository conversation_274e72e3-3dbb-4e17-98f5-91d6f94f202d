package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import java.util.Date;
import lombok.Getter;

/**
 * 审计项目
 *
 * <AUTHOR>
 * @since 2025-01-02 13:36:48
 */
@Getter
public class AuditProjectModelDTO extends LcBaseModel {

    /*
     * 征求意见结束日期
     */
    private Date consultationEndDate;

    /*
     * 征求意见开始日期
     */
    private Date consultationStartDate;

    /*
     * 新增时间
     */
    private Date createtimestamp;

    /*
     * 结束日期
     */
    private Date endDate;

    /*
     * 现场审计结束日期
     */
    private Date localAuditEndDate;

    /*
     * 现场审计开始日期
     */
    private Date localAuditStartDate;

    /*
     * 名称
     */
    private String name;

    /*
     * 审计期结束日期
     */
    private Date periodEndDate;

    /*
     * 审计期开始日期
     */
    private Date periodStartDate;

    /*
     * 项目进度
     */
    private Integer projectPhase;

    /*
     * 项目类型
     */
    private Integer projectType;

    /*
     * 是否报送
     */
    private Integer regulatoryFlag;

    /*
     * 报送时间
     */
    private Date regulatoryTime;

    /*
     * 备注
     */
    private String remark;

    /*
     * 删除标志
     */
    private Integer removeFlag;

    /*
     * 审计报告完成日期
     */
    private Date reportCompleteDate;

    /*
     * 开始日期
     */
    private Date startDate;

    /*
     * 启动日期
     */
    private Date startupDate;

    /*
     * 更新时间
     */
    private Date updatetimestamp;

    /*
     * 乐观锁
     */
    private Integer version;

    private Long planId;

    public void setPlanId(Long planId) {
        this.planId = planId;
        meta.put("planId", planId);
    }

    public void setConsultationEndDate(Date consultationEndDate) {
        this.consultationEndDate = consultationEndDate;
        meta.put("consultationEndDate", consultationEndDate);
    }

    public void setConsultationStartDate(Date consultationStartDate) {
        this.consultationStartDate = consultationStartDate;
        meta.put("consultationStartDate", consultationStartDate);
    }

    public void setCreatetimestamp(Date createtimestamp) {
        this.createtimestamp = createtimestamp;
        meta.put("createtimestamp", createtimestamp);
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
        meta.put("endDate", endDate);
    }

    public void setLocalAuditEndDate(Date localAuditEndDate) {
        this.localAuditEndDate = localAuditEndDate;
        meta.put("localAuditEndDate", localAuditEndDate);
    }

    public void setLocalAuditStartDate(Date localAuditStartDate) {
        this.localAuditStartDate = localAuditStartDate;
        meta.put("localAuditStartDate", localAuditStartDate);
    }

    public void setName(String name) {
        this.name = name;
        meta.put("name", name);
    }

    public void setPeriodEndDate(Date periodEndDate) {
        this.periodEndDate = periodEndDate;
        meta.put("periodEndDate", periodEndDate);
    }

    public void setPeriodStartDate(Date periodStartDate) {
        this.periodStartDate = periodStartDate;
        meta.put("periodStartDate", periodStartDate);
    }

    public void setProjectPhase(Integer projectPhase) {
        this.projectPhase = projectPhase;
        meta.put("projectPhase", projectPhase);
    }

    public void setProjectType(Integer projectType) {
        this.projectType = projectType;
        meta.put("projectType", projectType);
    }

    public void setRegulatoryFlag(Integer regulatoryFlag) {
        this.regulatoryFlag = regulatoryFlag;
        meta.put("regulatoryFlag", regulatoryFlag);
    }

    public void setRegulatoryTime(Date regulatoryTime) {
        this.regulatoryTime = regulatoryTime;
        meta.put("regulatoryTime", regulatoryTime);
    }

    public void setRemark(String remark) {
        this.remark = remark;
        meta.put("remark", remark);
    }

    public void setRemoveFlag(Integer removeFlag) {
        this.removeFlag = removeFlag;
        meta.put("removeFlag", removeFlag);
    }

    public void setReportCompleteDate(Date reportCompleteDate) {
        this.reportCompleteDate = reportCompleteDate;
        meta.put("reportCompleteDate", reportCompleteDate);
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
        meta.put("startDate", startDate);
    }

    public void setStartupDate(Date startupDate) {
        this.startupDate = startupDate;
        meta.put("startupDate", startupDate);
    }

    public void setUpdatetimestamp(Date updatetimestamp) {
        this.updatetimestamp = updatetimestamp;
        meta.put("updatetimestamp", updatetimestamp);
    }

    public void setVersion(Integer version) {
        this.version = version;
        meta.put("version", version);
    }

    @Override
    public String getModelCode() {
        return "AUDIT_PROJECT";
    }
}
