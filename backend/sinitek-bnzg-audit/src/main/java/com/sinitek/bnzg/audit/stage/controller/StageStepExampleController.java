package com.sinitek.bnzg.audit.stage.controller;

import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleFinishParamDTO;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.Objects;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阶段步骤实例 Controller
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/audit/stage/step/example")
@Api(value = "/frontend/api/audit/stage/step/example", tags = "审计系统-审计阶段步骤实例")
public class StageStepExampleController {

    @Autowired
    private IStageStepExampleStatusService stageStepExampleStatusService;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @PostMapping("/finish")
    @ApiOperation("手动完成")
    public RequestResult<Void> manuallyFinish(
        @RequestBody @Valid StageStepExampleFinishParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOpOrgId(orgId);
        param.setOpTime(new Date());
        this.stageStepExampleStatusService.manuallyFinish(param);
        return new RequestResult<>();
    }

    /**
     * 获取某一具体步骤的权限和配置
     */
    @GetMapping("/node-click")
    @ApiOperation("获取某一具体步骤的权限和配置")
    public RequestResult<StageStepAuthAndConfigDTO> getStepAuthAndConfig(
        @Valid StageStepAuthAndConfigParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        Boolean debugFlag = param.getDebugFlag();
        if (Objects.equals(Boolean.FALSE, debugFlag)) {
            param.setOperatorId(orgId);
        } else {
            log.info("使用参数中用户id[{}]获取步骤[{}]的权限和配置", param.getOperatorId(),
                param.getStageStepExampleId());
        }

        return new RequestResult<>(this.stageStepExampleService.getStepAuthAndConfig(param));
    }
}
