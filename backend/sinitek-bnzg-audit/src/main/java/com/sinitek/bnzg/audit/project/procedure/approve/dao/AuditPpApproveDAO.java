package com.sinitek.bnzg.audit.project.procedure.approve.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApprove;
import com.sinitek.bnzg.audit.project.procedure.approve.mapper.AuditPpApproveMapper;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Collection;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风险点审批
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Slf4j
@Component
public class AuditPpApproveDAO extends ServiceImpl<AuditPpApproveMapper, AuditPpApprove> {

    public AuditPpApprove create(Long projectId) {
        AuditPpApprove entity = new AuditPpApprove();
        entity.setProjectId(projectId);
        entity.setStatus(AuditRiskApproveStatusConstant.NOT_SUBMIT);
        this.save(entity);
        return entity;
    }

    public List<AuditPpApprove> findExistsByProjectId(Collection<Long> projectIds) {
        LambdaQueryWrapper<AuditPpApprove> queryWrapper = Wrappers.lambdaQuery(
            AuditPpApprove.class);
        queryWrapper.in(AuditPpApprove::getProjectId, projectIds);
        queryWrapper.eq(AuditPpApprove::getRemoveFlag, CommonBooleanEnum.FALSE.getValue());
        return this.list(queryWrapper);
    }

}
