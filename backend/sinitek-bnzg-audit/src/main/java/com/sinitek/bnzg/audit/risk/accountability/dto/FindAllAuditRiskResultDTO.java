package com.sinitek.bnzg.audit.risk.accountability.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date：2024/11/26 14:16
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "项目风险点问责情况查询结果DTO")
public class FindAllAuditRiskResultDTO{

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("程序id")
    private Long procedureId;

    @ApiModelProperty("计划id")
    private String planId;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("审计程序名称")
    private String procedureName;

    @ApiModelProperty("风险点名称")
    private String riskName;

    @ApiModelProperty("责任部门id")
    private List<String> respDeptIds;

    @ApiModelProperty("责任部门名称")
    private String respDeptName;

    @ApiModelProperty("责任人")
    private List<String> respManIds;

    @ApiModelProperty("责任人")
    private String respManName;


}
