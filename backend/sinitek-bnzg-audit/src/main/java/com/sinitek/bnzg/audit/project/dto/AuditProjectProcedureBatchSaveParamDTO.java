package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "项目程序批量保存参数")
public class AuditProjectProcedureBatchSaveParamDTO {

    @ApiModelProperty(value = "批量数据")
    private List<BatchSaveParamItem> list;

    @ApiModelProperty(value = "审计程序与人员对应关系")
    private Map<Long, String> procedureIdAndOrgId;

    @ApiModelProperty(value = "审计人员与审批人员对应关系")
    private Map<String, String> auditorAndApproverMap;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

    @Data
    @SuperBuilder
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "项目程序批量保存参数")
    public static class BatchSaveParamItem {

        @NotNull(message = "所属项目不能为空")
        @ApiModelProperty("项目id")
        private Long projectId;

        @NotEmpty(message = "项目下审计程序不能为空")
        @ApiModelProperty("程序id(集合)")
        private List<Long> procedureIdList;

    }
}
