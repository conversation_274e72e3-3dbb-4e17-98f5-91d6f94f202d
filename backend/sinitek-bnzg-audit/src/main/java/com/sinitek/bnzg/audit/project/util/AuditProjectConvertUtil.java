package com.sinitek.bnzg.audit.project.util;

import com.sinitek.bnzg.audit.project.dto.AuditProjectQueryParamDTO;
import com.sinitek.bnzg.audit.project.po.AuditProjectQueryParamPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectConvertUtil {

    public static AuditProjectQueryParamPO makeDTO2PO(AuditProjectQueryParamDTO source) {
        if (Objects.nonNull(source)) {
            AuditProjectQueryParamPO result = new AuditProjectQueryParamPO();
            result.setPlanIds(source.getPlanIds());
            result.setProjectNames(CommonStringUtil.toSearchStrList(source.getProjectName()));
            return result;
        }
        return null;
    }
}
