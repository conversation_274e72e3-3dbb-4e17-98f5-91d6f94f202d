package com.sinitek.bnzg.audit.project.procedure.approve.service.impl;

import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.CANT_SUBMIT_APPROVE_RISK_NOT_APPROVER;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.CANT_SUBMIT_APPROVE_RISK_NO_APPROVER;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_NOT_EXISTS_ON_APPROVE;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_SUBMITED_CANT_APPROVE;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_TERMINATED_CANT_APPROVE;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectAndOpeatorIdBaseDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dao.RiskApproveResultDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApprovePageParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpRiskApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultSingleApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.RiskApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.util.AuditRiskApprvResultChangeEventPublishUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpRiskApproveResultSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.RiskApproveResultCreateParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.util.RiskApproveResultConvertUtil;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.risk.support.AuditRiskSearchResultFormat;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParamDTO;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 风险点审批结果 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Slf4j
@Service
public class RiskApproveResultServiceImpl implements IRiskApproveResultService {

    @Autowired
    private RiskApproveResultDAO dao;

    @Autowired
    private IPpApproveService ppApproveService;

    @Autowired
    private AuditRiskSearchResultFormat<AuditRiskDetailDTO> format;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Autowired
    private IRiskAccountabilityService riskAccountabilityService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IAuditPlanService planService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(RiskApproveResultCreateParamDTO param) {
        Long approveId = param.getApproveId();
        Long projectId = param.getProjectId();
        List<Long> riskIds = param.getRiskIds();
        Map<Long, Long> riskIdAndProcedureIdMap = param.getRiskIdAndProcedureIdMap();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        if (CollUtil.isNotEmpty(riskIds)) {
            List<RiskApproveResult> list = this.dao.create(RiskApproveResultCreateParamPO.builder()
                .approveId(approveId)
                .projectId(projectId)
                .riskIds(riskIds)
                .riskIdAndProcedureIdMap(riskIdAndProcedureIdMap)
                .build());

            List<Long> ids = new LinkedList<>();
            Map<Long, Integer> idAndNewResultMap = new HashMap<>(list.size());

            list.forEach(item -> {
                Long id = item.getId();
                Integer approveResult = item.getApproveResult();
                ids.add(id);
                idAndNewResultMap.put(id, approveResult);
            });

            AuditRiskApprvResultChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(ids)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(idAndNewResultMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("新增")
                    .build());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approve(RiskApproveResultSingleApproveParamDTO param) {
        Long approveResultId = param.getApproveResultId();
        Integer approveResultValue = param.getApproveResult();
        String approveRemark = param.getApproveRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        log.info("操作人[{}]审批风险点,审批数据(approveResultIds)[{}],审批结果: {}", operatorId,
            approveResultId, approveResultValue);

        RiskApproveResult riskApproveResult = this.dao.getById(approveResultId);
        if (Objects.nonNull(riskApproveResult)) {
            Long approveId = riskApproveResult.getApproveId();

            PpApproveBaseInfoDTO approveDTO = this.ppApproveService.getBaseInfo(approveId);
            Integer status = approveDTO.getStatus();
            if (Objects.equals(status, AuditRiskApproveStatusConstant.SUBMIT)) {
                log.error("当前审批数据approvId {} 已提交,无法再次审批 {} ", approveId,
                    approveResultId);
                throw new BussinessException(DATA_SUBMITED_CANT_APPROVE);
            }
            if (Objects.equals(status, AuditRiskApproveStatusConstant.TERMINATE)) {
                log.error("当前审批数据approvId {} 已终止,无法再次审批", approveId);
                throw new BussinessException(DATA_TERMINATED_CANT_APPROVE);
            }

            Long projectId = approveDTO.getProjectId();

            AuditProjectCheckUtil.checkProjectApprover(
                AuditProjectAndOpeatorIdBaseDTO.builder()
                    .projectId(projectId)
                    .operatorId(operatorId)
                    .build(), "提交风险点审批", CANT_SUBMIT_APPROVE_RISK_NOT_APPROVER,
                CANT_SUBMIT_APPROVE_RISK_NO_APPROVER);

            Long id = riskApproveResult.getId();
            Integer oldApproveResult = riskApproveResult.getApproveResult();

            riskApproveResult.setOperatorId(operatorId);
            riskApproveResult.setApproveResult(approveResultValue);
            riskApproveResult.setApproveRemark(approveRemark);
            riskApproveResult.setOpTime(opTime);

            this.dao.updateById(riskApproveResult);

            AuditRiskApprvResultChangeEventPublishUtil.publishEvent(
                RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(id)
                    .oldValue(oldApproveResult)
                    .newValue(approveResultValue)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审批")
                    .build());
        } else {
            log.error("被审批的数据 {} 再数据库中不存在", approveResultId);
            throw new BussinessException(DATA_NOT_EXISTS_ON_APPROVE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchApprove(RiskApproveResultBatchApproveParamDTO param) {
        List<Long> approveResultIds = param.getApproveResultIds();
        Integer approveResultValue = param.getApproveResult();
        String approveRemark = param.getApproveRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        if (CollUtil.isEmpty(approveResultIds)) {
            log.warn(
                "操作人[{}]批量审批风险点,审批数据(approveResultIds)[{}],审批结果: {},审批备注: {}",
                operatorId,
                approveResultIds, approveResultValue, approveRemark);
            return Collections.emptyList();
        }

        log.info(
            "操作人[{}]批量审批风险点,审批数据(approveResultIds)[{}],审批结果: {},审批备注: {}",
            operatorId,
            approveResultIds, approveResultValue, approveRemark);

        List<RiskApproveResult> approveResults = this.dao.listByIds(approveResultIds);
        if (CollUtil.isNotEmpty(approveResults)) {
            RiskApproveResult firstPpApproveResult = approveResults.get(0);
            Long approveId = firstPpApproveResult.getApproveId();

            PpApproveBaseInfoDTO approveDTO = this.ppApproveService.getBaseInfo(approveId);
            Integer status = approveDTO.getStatus();
            if (Objects.equals(status, AuditRiskApproveStatusConstant.SUBMIT)) {
                log.error("当前审批数据approvId {} 已提交,无法再次审批", approveId);
                throw new BussinessException(DATA_SUBMITED_CANT_APPROVE);
            }
            if (Objects.equals(status, AuditRiskApproveStatusConstant.TERMINATE)) {
                log.error("当前审批数据approvId {} 已终止,无法再次审批", approveId);
                throw new BussinessException(DATA_TERMINATED_CANT_APPROVE);
            }

            Long projectId = approveDTO.getProjectId();

            AuditProjectCheckUtil.checkProjectApprover(
                AuditProjectAndOpeatorIdBaseDTO.builder()
                    .projectId(projectId)
                    .operatorId(operatorId)
                    .build(), "提交风险点批量审批",
                CANT_SUBMIT_APPROVE_RISK_NOT_APPROVER,
                CANT_SUBMIT_APPROVE_RISK_NO_APPROVER);

            List<Long> foreignKeys = new LinkedList<>();
            Map<Long, Integer> idAndOldApproveResultMap = new LinkedHashMap<>();

            approveResults.forEach(approveResult -> {
                Long id = approveResult.getId();
                Integer oldApproveResult = approveResult.getApproveResult();
                approveResult.setOperatorId(operatorId);
                approveResult.setApproveResult(approveResultValue);
                approveResult.setApproveRemark(approveRemark);
                approveResult.setOpTime(opTime);

                foreignKeys.add(id);
                idAndOldApproveResultMap.put(id, oldApproveResult);
            });

            this.dao.updateBatchById(approveResults);

            AuditRiskApprvResultChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParamDTO.<Integer>builder()
                    .foreignKeys(foreignKeys)
                    .oldValueMap(idAndOldApproveResultMap)
                    .newValue(approveResultValue)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("批量审批")
                    .build());

            return foreignKeys;
        } else {
            log.error("被审批的风险点数据 {} 在数据库中不存在", approveResultIds);
            throw new BussinessException(DATA_NOT_EXISTS_ON_APPROVE);
        }
    }

    @Override
    public RiskApproveResultDetailDTO loadDetail(Long id) {
        RiskApproveResult riskApproveResult = this.dao.getById(id);
        if (Objects.nonNull(riskApproveResult)) {
            Long projectId = riskApproveResult.getProjectId();
            Long procedureId = riskApproveResult.getProcedureId();
            Long riskId = riskApproveResult.getRiskId();

            AuditRiskDetailDTO riskDetail = this.auditRiskService.loadDetail(riskId);
            if (Objects.nonNull(riskDetail)) {
                List<AuditRiskDetailDTO> formatList = this.format.format(
                    Collections.singletonList(riskDetail));
                if (CollUtil.isNotEmpty(formatList)) {
                    riskDetail = formatList.get(0);
                }
            }

            AuditProcedureBaseInfoDTO procedureInfo = this.procedureService.getBaseInfoById(
                procedureId);
            AuditProjectInfoDTO projectInfo = this.projectService.getExistsProjectInfoById(
                projectId);

            RiskApproveResultDetailDTO result = new RiskApproveResultDetailDTO();
            result.setId(riskApproveResult.getId());
            result.setApproveId(riskApproveResult.getApproveId());
            if (Objects.nonNull(projectInfo)) {
                Long planId = projectInfo.getPlanId();
                result.setProjectId(projectId);
                result.setProjectName(projectInfo.getName());

                AuditPlanInfoDTO planInfo = this.planService.getInfoById(planId);

                result.setPlanId(planId);
                if (Objects.nonNull(planInfo)) {
                    result.setPlanName(planInfo.getName());
                } else {
                    log.warn("根据计划id {} 查不到具体计划数据", planId);
                }
            } else {
                log.warn("根据项目id {} 查不到具体项目数据", projectId);
            }

            result.setProcedureId(procedureId);
            if (Objects.nonNull(procedureInfo)) {
                result.setProcedureName(procedureInfo.getName());
            } else {
                log.warn("根据审计程序id {} 查不到具体审计程序数据", procedureId);
            }

            if (Objects.nonNull(riskDetail)) {
                result.setAuditorId(riskDetail.getAuditorId());
                result.setAuditDate(riskDetail.getAuditDate());
            } else {
                log.warn("根据风险点id {} 查不到具体风险点数据", riskId);
            }

            result.setOperatorId(riskApproveResult.getOperatorId());
            result.setOpTime(riskApproveResult.getOpTime());

            String auditorId = result.getAuditorId();
            String operatorId = result.getOperatorId();

            List<String> orgIds = new LinkedList<>();
            orgIds.add(auditorId);
            orgIds.add(operatorId);

            Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
                orgIds);
            result.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId));
            result.setOperatorName(MapUtils.getString(orgIdAndNameMap, operatorId));

            Integer approveResult = riskApproveResult.getApproveResult();
            result.setApproveResult(approveResult);

            // key: 类型值字符串
            // value: 名称
            // 审批结果
            Map<String, String> approveResultMap = this.enumService.getSirmEnumByCataLogAndType(
                AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_APPROVE_RESULT);

            result.setApproveResultName(
                MapUtils.getString(approveResultMap, String.valueOf(approveResult)));

            result.setApproveRemark(riskApproveResult.getApproveRemark());

            result.setRiskDetail(riskDetail);
            return result;
        }
        return null;
    }

    @Override
    public List<RiskApproveResultBaseInfoDTO> findExistByApproveId(Long approveId) {
        List<RiskApproveResult> list = this.dao.findExistByApproveId(approveId);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(RiskApproveResultConvertUtil::makeEntity2BaseInfoDTO).collect(
                Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<RiskApproveResultBaseInfoDTO> findExistByIds(Collection<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            List<RiskApproveResult> list = this.dao.listByIds(ids);
            if (CollUtil.isNotEmpty(list)) {
                return list.stream().map(RiskApproveResultConvertUtil::makeEntity2BaseInfoDTO)
                    .collect(
                        Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<PpRiskApproveResultSearchResultDTO> findPpRiskApproveResultListByApproveId(
        Long approveId) {
        List<PpRiskApproveResultSearchResultPO> result = this.dao.findPpRiskApproveResultListByApproveId(
            approveId);
        if (CollUtil.isNotEmpty(result)) {
            return LcConvertUtil.convert(result, PpRiskApproveResultSearchResultDTO::new);
        }
        return Collections.emptyList();
    }

    @Override
    public PpApprovePageParamDTO getApprovePageParam(Long riskId) {
        RiskApproveResult data = this.dao.getByRiskId(riskId);
        return PpApprovePageParamDTO.builder()
            .id(data.getId())
            .projectId(data.getProjectId())
            .procedureId(data.getProcedureId()).build();
    }
}
