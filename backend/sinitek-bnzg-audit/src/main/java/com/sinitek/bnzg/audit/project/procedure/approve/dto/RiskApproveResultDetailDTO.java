package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 风险点审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "风险点审批结果DTO")
public class RiskApproveResultDetailDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("项目审计程序审批id")
    private Long approveId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "程序id")
    private Long procedureId;

    @ApiModelProperty(value = "程序名称")
    private String procedureName;

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批结果名")
    private String approveResultName;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    @ApiModelProperty("审批人名称")
    private String operatorName;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date opTime;

    @ApiModelProperty("风险点详情")
    private AuditRiskDetailDTO riskDetail;

    @ApiModelProperty("制度依据")
    private String sysBasis;
}
