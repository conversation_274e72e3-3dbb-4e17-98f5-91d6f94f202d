package com.sinitek.bnzg.audit.stage.util;


import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 7/11/2023 1:01 PM
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStepUtil {

    // key: 阶段步骤值
    // value: 阶段步骤值对应的产品阶段
    private static final Map<Integer, Integer> STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP = new HashMap<>();

    // key: 当前步骤值
    // value: 下一步骤值
    private static final Map<Integer, Integer> STAGE_STEP_VALUE_AND_NEXT_STEP_VALUE_MAP = new HashMap<>();

    static {
        // 审计准备
        STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP.put(StageStepConstant.AUDIT_PREPARING,
            AuditProjectPhaseConstant.READY);
        STAGE_STEP_VALUE_AND_NEXT_STEP_VALUE_MAP.put(StageStepConstant.AUDIT_PREPARING,
            StageStepConstant.AUDIT_EXECUTION);
        // 审计实施
        STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP.put(StageStepConstant.AUDIT_EXECUTION,
            AuditProjectPhaseConstant.IMPL);
        STAGE_STEP_VALUE_AND_NEXT_STEP_VALUE_MAP.put(StageStepConstant.AUDIT_EXECUTION,
            StageStepConstant.AUDIT_REPORT);
        // 审计报告
        STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP.put(StageStepConstant.AUDIT_REPORT,
            AuditProjectPhaseConstant.REPORT);
        STAGE_STEP_VALUE_AND_NEXT_STEP_VALUE_MAP.put(StageStepConstant.AUDIT_REPORT,
            StageStepConstant.RECTIFICATION_TRACKING);
        // 整改跟踪
        STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP.put(StageStepConstant.RECTIFICATION_TRACKING,
            AuditProjectPhaseConstant.TRACKING);
    }

    /**
     * 根据阶段步骤值,获取到它完成后对应的下一个产品阶段
     */
    public static Integer getNextProjectPhaseByStageStepValue(Integer stageStepValue) {
        if (Objects.equals(stageStepValue, StageStepConstant.RECTIFICATION_TRACKING)) {
            // 针对 整改跟踪 返回 CLOSE
            return AuditProjectPhaseConstant.CLOSE;
        }
        // 针对: 审计准备,审计实施,审计报告,就获取到它下一个步骤对应的产品阶段
        Integer nextStepValue = MapUtils.getInteger(STAGE_STEP_VALUE_AND_NEXT_STEP_VALUE_MAP,
            stageStepValue);
        // 获取下一个步骤对应的产品阶段
        return MapUtils.getInteger(STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP, nextStepValue);
    }

    /**
     * 根据阶段步骤值获取下一阶段步骤
     */
    public static Integer getNextStepValueByStepValue(Integer stepValue) {
        return MapUtils.getInteger(STAGE_STEP_VALUE_AND_NEXT_STEP_VALUE_MAP, stepValue);
    }

    public static Integer getProjectPhaseByStepValue(Integer stepValue) {
        return MapUtils.getInteger(STAGE_STEP_VALUE_AND_PROJECT_PHASE_MAP, stepValue);
    }
}
