package com.sinitek.bnzg.audit.stage.controller;

import com.sinitek.bnzg.audit.stage.dto.GenerateAuditStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleStatusChangeParamInControllerDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamInControllerDTO;
import com.sinitek.bnzg.audit.stage.service.IAuditStageExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageExampleStatusService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阶段实例 Controller
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/audit/stage-or-step/manage")
@Api(value = "/frontend/api/audit/stage-or-step/manage", tags = "审计系统-审计阶段或步骤管理")
public class StageOrStepExampleManageController {

    @Autowired
    private IAuditStageExampleService auditStageExampleService;

    @Autowired
    private IStageExampleStatusService stageExampleStatusService;

    @Autowired
    private IStageStepExampleStatusService stageStepExampleStatusService;

    @PostMapping("/generate-audit-stage-example")
    @ApiOperation("初始化审计阶段实例")
    public RequestResult<Long> initAuditStage(
        @RequestBody @Valid GenerateAuditStageExampleParamDTO param) {
        param.setOpTime(new Date());
        param.setOpOrgId(CurrentUserFactory.getOrgId());
        return new RequestResult<>(this.auditStageExampleService.generateAuditStageExample(param));
    }

    @PostMapping("/change-stage-example-status")
    @ApiOperation("修改阶段实例状态")
    public RequestResult<Void> changeStageExampleStatus(
        @RequestBody @Valid StageExampleStatusChangeParamInControllerDTO param) {
        param.setOpTime(new Date());
        param.setOpOrgId(CurrentUserFactory.getOrgId());
        this.stageExampleStatusService.changeStageExampleStatus(param);
        return RequestResult.success();
    }

    @PostMapping("/change-step-example-status")
    @ApiOperation("修改步骤实例状态")
    public RequestResult<Void> changeStepExampleStatus(
        @RequestBody @Valid StageStepExampleStatusChangeParamInControllerDTO param) {
        param.setOpTime(new Date());
        param.setOpOrgId(CurrentUserFactory.getOrgId());
        this.stageStepExampleStatusService.updateStatus(param);
        return RequestResult.success();
    }
}
