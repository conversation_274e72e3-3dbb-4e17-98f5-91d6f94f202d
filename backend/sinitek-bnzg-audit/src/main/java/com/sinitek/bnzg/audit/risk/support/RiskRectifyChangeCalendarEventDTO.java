package com.sinitek.bnzg.audit.risk.support;

import com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant;
import com.sinitek.sirm.calendar.annotation.CalendarEventDefinition;
import com.sinitek.sirm.calendar.dto.BaseCalendarEventDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Date  2022/3/23
 */
@CalendarEventDefinition(code = AuditRiskConstant.RISK_RECTIFY_CHANGE_CALENDAR_EVENT, name = "风险点预计完成", color = "#336bff")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskRectifyChangeCalendarEventDTO extends BaseCalendarEventDTO {

}
