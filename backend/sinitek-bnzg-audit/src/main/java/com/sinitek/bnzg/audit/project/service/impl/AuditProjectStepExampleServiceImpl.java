package com.sinitek.bnzg.audit.project.service.impl;

import static com.sinitek.bnzg.audit.stage.constant.StageStepDocConstant.STAG_DOC_DEFAULT_SOURCE_NAME;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.doc.dto.DocumentBaseDTO;
import com.sinitek.bnzg.audit.doc.plan.dto.DocTypePlanMapDTO;
import com.sinitek.bnzg.audit.doc.plan.dto.DocTypePlanQueryParamDTO;
import com.sinitek.bnzg.audit.doc.plan.service.IDocTypePlanMapService;
import com.sinitek.bnzg.audit.doc.plan.service.IDocTypePlanService;
import com.sinitek.bnzg.audit.doc.service.IDocUploadLogService;
import com.sinitek.bnzg.audit.doc.service.IDocumentService;
import com.sinitek.bnzg.audit.doc.type.constant.DocTypeEnumConstant;
import com.sinitek.bnzg.audit.project.dto.StageStepFinishCheckParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureExecutionService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectStepExampleService;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.enumation.StageStepEnum;
import com.sinitek.bnzg.audit.stage.service.IStageExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.sirmenum.utils.EnumUtils;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:45
 */
@Slf4j
@Service
public class AuditProjectStepExampleServiceImpl implements IAuditProjectStepExampleService {

    @Autowired
    private IStageExampleService stageExampleService;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IDocTypePlanService docTypePlanService;

    @Autowired
    private IDocTypePlanMapService docTypePlanMapService;

    @Autowired
    private IDocumentService documentService;

    @Autowired
    private IDocUploadLogService docUploadLogService;

    @Autowired
    private IAuditProjectProcedureExecutionService ppExecutionService;

    @Override
    public AuditCheckResultDTO checkCanFinishAuditReport(StageStepFinishCheckParamDTO param) {
        Long projectId = param.getProjectId();

        List<StageExampleDTO> stageExamples = this.stageExampleService.findByProjectIds(
            Collections.singleton(projectId));
        StageExampleDTO stageExample = null;
        if (CollUtil.isNotEmpty(stageExamples)) {
            if (stageExamples.size() > 1) {
                log.warn("审计项目[{}]存在多个阶段实例", projectId);
            }
            stageExample = stageExamples.get(0);
        } else {
            log.warn("审计项目[{}]不存在阶段实例,无法检查审计报告是否可以完成", projectId);
            return AuditCheckResultDTO.builder().result(false).msg("该项目阶段实例不存在").build();
        }

        Long stageExampleId = stageExample.getId();

        List<StageStepExampleDTO> stepExamples = this.stageStepExampleService.findByStageExampleIds(
            Collections.singleton(stageExampleId));
        if (CollUtil.isEmpty(stepExamples)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在步骤实例,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false).msg("该项目阶段步骤实例不存在")
                .build();
        }

        // 审计准备
        StageStepExampleDTO preparingStepExample = null;
        // 审计实施
        StageStepExampleDTO executionStepExample = null;
        // 审计报告
        StageStepExampleDTO reportStepExample = null;

        for (StageStepExampleDTO stepExample : stepExamples) {
            Integer stepValue = stepExample.getStepValue();
            if (Objects.equals(StageStepConstant.AUDIT_PREPARING, stepValue)) {
                preparingStepExample = stepExample;
            } else if (Objects.equals(StageStepConstant.AUDIT_EXECUTION, stepValue)) {
                executionStepExample = stepExample;
            } else if (Objects.equals(StageStepConstant.AUDIT_REPORT, stepValue)) {
                reportStepExample = stepExample;
            }
        }

        // 审计报告步骤实例不能为已完成状态
        if (Objects.isNull(reportStepExample)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在审计报告步骤实例,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false).msg("该项目审计报告步骤实例不存在")
                .build();
        } else {
            Long stepExampleId = reportStepExample.getId();
            Integer status = reportStepExample.getStatus();
            if (Objects.equals(StageStepStatusConstant.FINISHED, status)) {
                log.warn("审计项目[{}]阶段实例[{}]审计报告步骤实例[{}]已完成,无需重复完成",
                    projectId, stageExampleId, stepExampleId);
                return AuditCheckResultDTO.builder().result(false)
                    .msg("该项目审计报告步骤实例已完成").build();
            }
            log.info("审计项目[{}]阶段实例[{}]审计报告步骤实例[{}]状态[{}],通过检查",
                projectId, stageExampleId, stepExampleId, status);
        }

        // 获取文档
        // 审计准备文档方案
        String importDocAuditPreparing = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IMPORT_DOC_AUDIT_PREPARING);
        if (StringUtils.isBlank(importDocAuditPreparing)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在审计准备文档方案,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false)
                .msg("项目配置中审计准备步骤对应文档方案不存在").build();
        }

        // 审计实施文档方案
        String importDocAuditExecution = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IMPORT_DOC_AUDIT_EXECUTION);
        if (StringUtils.isBlank(importDocAuditExecution)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在审计实施文档方案,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false)
                .msg("项目配置中审计实施步骤对应文档方案不存在").build();
        }

        // 审计报告文档方案
        String importDocAuditReport = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IMPORT_DOC_AUDIT_REPORT);
        if (StringUtils.isBlank(importDocAuditReport)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在审计报告文档方案,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false)
                .msg("项目配置中审计报告步骤对应文档方案不存在").build();
        }

        Map<String, List<DocTypePlanMapDTO>> docTypePlanCodeAndPlanMapsMap = this.getDocTypePlanCodeAndPlanMapsMap(
            projectId, importDocAuditPreparing, importDocAuditExecution, importDocAuditReport);

        // 审计准备步骤
        // 文档是否已上传
        if (Objects.isNull(preparingStepExample)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在审计准备步骤实例,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false).msg("该项目审计准备步骤实例不存在")
                .build();
        } else {
            Long stepExampleId = preparingStepExample.getId();

            AuditCheckResultDTO result = this.checkStepUploadDoc(projectId,
                stageExampleId, stepExampleId,
                StageStepEnum.AUDIT_PREPARING.getName(), importDocAuditPreparing,
                docTypePlanCodeAndPlanMapsMap);
            if (!result.getResult()) {
                return result;
            }
        }

        // 审计实施步骤
        if (Objects.isNull(executionStepExample)) {
            log.warn("审计项目[{}]阶段实例[{}]不存在审计实施步骤实例,无法检查审计报告是否可以完成",
                projectId, stageExampleId);
            return AuditCheckResultDTO.builder().result(false).msg("该项目审计实施步骤实例不存在")
                .build();
        } else {
            Long stepExampleId = executionStepExample.getId();

            // 文档是否已上传
            AuditCheckResultDTO result = this.checkStepUploadDoc(projectId,
                stageExampleId, stepExampleId,
                StageStepEnum.AUDIT_EXECUTION.getName(), importDocAuditExecution,
                docTypePlanCodeAndPlanMapsMap);
            if (!result.getResult()) {
                return result;
            }
            // 审计程序均已审批通过
            AuditCheckResultDTO checkResult = this.ppExecutionService.checkCanFinish(
                projectId);
            if (!checkResult.getResult()) {
                return checkResult;
            }
        }

        // 审计报告是否已上传
        Long stepExampleId = reportStepExample.getId();

        AuditCheckResultDTO result = this.checkStepUploadDoc(projectId,
            stageExampleId, stepExampleId,
            StageStepEnum.AUDIT_REPORT.getName(), importDocAuditReport,
            docTypePlanCodeAndPlanMapsMap);
        if (!result.getResult()) {
            return result;
        }

        // 都通过检查
        return AuditCheckResultDTO.builder().result(true).build();
    }


    private AuditCheckResultDTO checkStepUploadDoc(Long projectId, Long stageExampleId,
        Long stepExampleId, String stepName,
        String docTypePlanCode,
        Map<String, List<DocTypePlanMapDTO>> docTypePlanCodeAndPlanMapsMap) {
        List<DocTypePlanMapDTO> docTypeList = MapUtils.getObject(
            docTypePlanCodeAndPlanMapsMap,
            docTypePlanCode);

        if (CollUtil.isNotEmpty(docTypeList)) {
            List<Integer> needUploadDocTypeList = docTypeList.stream()
                .filter(docType -> Objects.equals(CommonBooleanEnum.TRUE.getValue(),
                    docType.getRequired())).map(DocTypePlanMapDTO::getDocType)
                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(needUploadDocTypeList)) {

                List<Long> alreadDeleteDicIds = this.docUploadLogService.findDeletedLogDocIds(
                    stepExampleId,
                    STAG_DOC_DEFAULT_SOURCE_NAME);

                List<DocumentBaseDTO> documentList = this.documentService.findMaxPublishVersionBySourceWithIgnoreDocIds(
                    stepExampleId,
                    STAG_DOC_DEFAULT_SOURCE_NAME, alreadDeleteDicIds);

                // key:文档类型，value:是否已上传标志
                Map<Integer, Boolean> docTypeAndExistsFlagMap = documentList.stream()
                    .map(DocumentBaseDTO::getDocType)
                    .collect(Collectors.toMap(v -> v, v -> true));
                List<Integer> notUploadDocTypeList = needUploadDocTypeList.stream().filter(
                    item -> !MapUtils.getBooleanValue(docTypeAndExistsFlagMap,
                        item, false)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(notUploadDocTypeList)) {
                    Map<String, String> docTypeEnumMap = EnumUtils.getSirmEnumByCataLogAndType(
                        DocTypeEnumConstant.DOC_TYPE_CATALOG, DocTypeEnumConstant.DOC_TYPE_TYPE);
                    String notUploadDocTypeNames = notUploadDocTypeList.stream()
                        .map(String::valueOf).map(docTypeEnumMap::get).collect(
                            Collectors.joining(","));
                    return AuditCheckResultDTO.builder().result(false)
                        .msg(String.format("%s下[%s]未上传", stepName, notUploadDocTypeNames))
                        .build();
                }
            } else {
                log.warn(
                    "审计项目[{}]阶段实例[{}],[{}]步骤[{}]对应的文档方案[{}]中没有需要必传的文档,忽略检查",
                    projectId,
                    stageExampleId, stepName, stepExampleId, docTypePlanCode);
            }
        } else {
            log.warn(
                "审计项目[{}]阶段实例[{}],[{}]步骤不存在[{}]对应的文档方案[{}],忽略检查",
                projectId,
                stageExampleId, stepName, stepExampleId, docTypePlanCode);
        }
        return AuditCheckResultDTO.builder().result(true).build();
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<String, List<DocTypePlanMapDTO>> getDocTypePlanCodeAndPlanMapsMap(
        Long projectId, String... docCodes) {
        DocTypePlanQueryParamDTO param = new DocTypePlanQueryParamDTO();
        param.setProjectId(projectId);
        param.setCodes(Arrays.asList(docCodes));
        return this.docTypePlanMapService.getDocTypePlanMapByDocTypePlanCodes(
            param);
    }
}
