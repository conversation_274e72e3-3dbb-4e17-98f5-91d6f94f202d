package com.sinitek.bnzg.audit.stage.log.status.stage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.bnzg.log.entity.AbstractRecordChangeLogEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计程序库状态日志 Entity
 *
 * <AUTHOR>
 * date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_stage_example_status_log")
@ApiModel(description = "阶段实例状态变动日志实体")
public class StageExampleStatusLog extends AbstractRecordChangeLogEntity<Integer> {

    @ApiModelProperty("阶段实例id")
    private Long stageExampleId;

}