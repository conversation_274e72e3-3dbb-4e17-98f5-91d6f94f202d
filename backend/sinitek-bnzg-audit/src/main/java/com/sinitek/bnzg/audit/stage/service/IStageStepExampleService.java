package com.sinitek.bnzg.audit.stage.service;

import com.sinitek.bnzg.audit.stage.dto.BatchGenerateStageStepExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageStepExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import java.util.Collection;
import java.util.List;

/**
 * 阶段步骤实例 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-15
 */
public interface IStageStepExampleService {

    void generateStageStepExamples(GenerateStageStepExampleParamDTO param);

    void batchGenerateStageStepExamples(BatchGenerateStageStepExampleParamDTO param);

    List<StageStepExampleDTO> findByIds(Collection<Long> ids);

    StageStepExampleDTO getByProjectIdAndStepValue(Long projectId, Integer stepValue);

    List<StageStepExampleDTO> findByStageExampleIds(Collection<Long> stageExampleIds);

    StageStepAuthAndConfigDTO getStepAuthAndConfig(StageStepAuthAndConfigParamDTO param);

}
