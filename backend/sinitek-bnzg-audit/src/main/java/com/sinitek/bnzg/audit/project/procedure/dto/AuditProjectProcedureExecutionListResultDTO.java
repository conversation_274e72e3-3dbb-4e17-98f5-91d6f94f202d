package com.sinitek.bnzg.audit.project.procedure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/9/29 10:27
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "项目审计程序审计实施查询结果")
public class AuditProjectProcedureExecutionListResultDTO {

    @ApiModelProperty(value = "数据类型")
    private Integer dataType;

    @ApiModelProperty(value = "项目审计程序id")
    private Long id;

    @ApiModelProperty(value = "审计项目id")
    private Long projectId;

    @ApiModelProperty(value = "审计程序id")
    private Long procedureId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "审计程序/风险点状态")
    private Integer status;

    @ApiModelProperty(value = "审计程序/风险点状态名称")
    private String statusName;

    @ApiModelProperty(value = "审批反馈")
    private String approveRemark;

    @ApiModelProperty(value = "审计人id")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    @ApiModelProperty(value = "支持审批标识")
    private Boolean supportApproveFlag;

    @ApiModelProperty(value = "风险点")
    private List<AuditPpRiskExecutionListResultDTO> children;
}
