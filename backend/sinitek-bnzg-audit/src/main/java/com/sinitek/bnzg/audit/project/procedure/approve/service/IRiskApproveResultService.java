package com.sinitek.bnzg.audit.project.procedure.approve.service;

import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApprovePageParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpRiskApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultSingleApproveParamDTO;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import java.util.Collection;
import java.util.List;

/**
 * 风险点审批结果 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-30
 */
public interface IRiskApproveResultService {

    void save(RiskApproveResultCreateParamDTO param);

    void approve(RiskApproveResultSingleApproveParamDTO param);

    List<Long> batchApprove(RiskApproveResultBatchApproveParamDTO param);

    RiskApproveResultDetailDTO loadDetail(Long id);

    List<RiskApproveResultBaseInfoDTO> findExistByApproveId(Long approveId);

    List<RiskApproveResultBaseInfoDTO> findExistByIds(Collection<Long> ids);

    List<PpRiskApproveResultSearchResultDTO> findPpRiskApproveResultListByApproveId(Long approveId);

    PpApprovePageParamDTO getApprovePageParam(Long riskId);
}
