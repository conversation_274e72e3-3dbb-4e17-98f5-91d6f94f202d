package com.sinitek.bnzg.audit.project.support;

import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_DELETE_BECAUSEOF_ILL_PROJECT_PHASE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_EDIT_BECAUSEOF_CURRENT_PHASE;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.sinitek.bnzg.audit.plan.constant.AuditPlanStatusConstant;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.constant.AuditProjectConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectModelConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.enumation.AuditProjectPhaseEnum;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.util.AuditProjectPhaseChangeEventUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectUtil;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.lowcode.common.model.constant.LcModelConstant;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import com.sinitek.sirm.lowcode.model.dto.LcModelDynamicDTO;
import com.sinitek.sirm.lowcode.model.support.context.LcModelDeleteContext;
import com.sinitek.sirm.lowcode.model.support.context.LcModelQueryContext;
import com.sinitek.sirm.lowcode.model.support.context.LcModelSaveContext;
import com.sinitek.sirm.lowcode.model.support.context.LcModelSearchContext;
import com.sinitek.sirm.lowcode.model.support.context.LcModelUpdateContext;
import com.sinitek.sirm.lowcode.model.support.interceptor.ILcModelOperateSimpleInterceptor;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:26
 */
@Slf4j
@Component
public class AuditProjectModelOpInterceptor implements ILcModelOperateSimpleInterceptor {

    @Autowired
    private IAuditProjectService projctService;

    @Autowired
    private IAuditProjectMemberService memberService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IAuditPlanService planService;


    @Override
    public void beforeUpdateByKey(LcModelDynamicDTO model, LcBaseModel data,
        LcModelUpdateContext ctx, Long id) {
        AuditProjectInfoDTO existsProject = this.projctService.getExistsProjectInfoById(id);
        if (ObjectUtils.isNotEmpty(existsProject)) {
            Integer projectPhase = existsProject.getProjectPhase();
            AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(projectPhase);
            if (AuditProjectUtil.isPhaseInvalidForOperation(projectPhase)) {
                log.error("当前项目[{}]的项目进度为[{}],无法编辑", id, phaseEnum.getName());
                throw new BussinessException(CANT_EDIT_BECAUSEOF_CURRENT_PHASE,
                    phaseEnum.getName());
            }
        }
    }


    @Override
    public List<String> getMatchModelCode() {
        return Collections.singletonList(AuditProjectModelConstant.MODEL_CODE);
    }

    @Override
    public void beforeSave(LcModelDynamicDTO model, LcBaseModel data, LcModelSaveContext ctx) {
        if (CollUtil.isNotEmpty(data)) {
            data.put(AuditProjectModelConstant.PROJECT_PHASE_NAME, AuditProjectPhaseConstant.INIT);
        }
    }

    @Override
    public void afterSave(LcModelDynamicDTO model, LcBaseModel data, LcModelSaveContext ctx,
        Long id) {
        String orgId = CurrentUserFactory.getOrgId();
        Date opTime = new Date();
        int currentProjectPhase = AuditProjectPhaseConstant.INIT;

        // 如果审计计划为已完成,此时需要变更为进行中
        AuditProjectInfoDTO project = this.projctService.getExistsProjectInfoById(id);
        Long planId = project.getPlanId();

        List<AuditPlanInfoDTO> plans = this.planService.findExistsInfoByIds(
            Collections.singletonList(planId));
        if (CollUtil.isNotEmpty(plans)) {
            AuditPlanInfoDTO plan = plans.get(0);
            Integer status = plan.getStatus();

            if (Objects.equals(AuditPlanStatusConstant.END, status)) {
                log.warn("项目[{}]所属计划[{}]已完成,项目新增后变更计划状态为进行中", id, planId);
                this.planService.updatePlanAsProcessing(planId, orgId);
            }
        } else {
            log.warn("项目[{}]所属计划[{}]不存在,无法在项目新增后变更计划状态", id, planId);
        }

        AuditProjectPhaseChangeEventUtil.publishEvent(
            AuditProjectPhaseChangeEventSourceDTO.builder()
                .projectId(id)
                .operatorId(orgId)
                .opTime(opTime)
                .oldProjectPhase(null)
                .newProjectPhase(currentProjectPhase)
                .remark("初始化")
                .build());
    }

    @Override
    public void beforeDeleteByKey(LcModelDynamicDTO model, List<Long> ids, List<LcBaseModel> data,
        LcModelDeleteContext ctx) {
        // 仅能删除未启动的项目
        List<AuditProjectInfoDTO> projects = this.projctService.findExistsProjctByIds(ids);
        if (CollUtil.isNotEmpty(projects)) {

            List<AuditProjectInfoDTO> cantDeleteList = projects.stream().filter(item -> {
                Integer projectPhase = item.getProjectPhase();
                return !Objects.equals(AuditProjectPhaseConstant.INIT, projectPhase);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cantDeleteList)) {
                List<Long> cantDeleteIds = cantDeleteList.stream().map(AuditProjectInfoDTO::getId)
                    .collect(Collectors.toList());
                log.error("存在项目阶段不为未启动的项目 {} 无法删除", cantDeleteIds);
                throw new BussinessException(CANT_DELETE_BECAUSEOF_ILL_PROJECT_PHASE);
            }
        }
    }

    @Override
    public void afterDeleteByKey(LcModelDynamicDTO model, List<Long> ids, List<LcBaseModel> data,
        LcModelDeleteContext ctx) {
        String orgId = CurrentUserFactory.getOrgId();
        // 项目删除后，如果计划下存在的项目都已完成，计划要变成已完成
        List<AuditProjectInfoDTO> projects = this.projctService.findExistsProjctByIds(ids);
        List<Long> planIds = projects.stream().map(AuditProjectInfoDTO::getPlanId)
            .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(planIds)) {
            List<AuditProjectInfoDTO> projectByPlans = this.projctService.findExistsProjctByPlanIds(
                planIds);
            //key:计划id,value:对应项目
            Map<Long, List<AuditProjectInfoDTO>> planIdAndProjectsMap = projectByPlans.stream()
                .collect(Collectors.groupingBy(AuditProjectInfoDTO::getPlanId));

            List<AuditPlanInfoDTO> plans = this.planService.findExistsInfoByIds(planIds);
            // key:计划id;value:计划
            Map<Long, AuditPlanInfoDTO> planIdAndPlanMap = plans.stream()
                .collect(Collectors.toMap(AuditPlanInfoDTO::getId, v -> v));

            planIds.forEach(planId -> {
                List<AuditProjectInfoDTO> projectsInPlanIdInner = planIdAndProjectsMap.get(planId);
                if (CollUtil.isNotEmpty(projectsInPlanIdInner)) {
                    // 检查项目是否都已完成
                    List<AuditProjectInfoDTO> notFinishProjects = projectsInPlanIdInner.stream()
                        .filter(item ->
                            !Objects.equals(AuditProjectPhaseConstant.STOP, item.getProjectPhase())
                                && !Objects.equals(AuditProjectPhaseConstant.CLOSE,
                                item.getProjectPhase())).collect(Collectors.toList());
                    if (CollUtil.isEmpty(notFinishProjects)) {
                        AuditPlanInfoDTO plan = planIdAndPlanMap.get(planId);
                        if (Objects.nonNull(plan)) {
                            Integer status = plan.getStatus();
                            if (Objects.equals(AuditPlanStatusConstant.PROCESSING, status)) {
                                log.error(
                                    "计划id[{}]在项目删除后变更需要从[进行中]变更为[已完成]状态",
                                    planId);
                                this.planService.updatePlanAsEnd(planId, orgId);
                            } else {
                                log.info(
                                    "计划id[{}]在项目删除后状态不为[进行中]无需变更为[已完成]状态",
                                    planId);
                            }
                        } else {
                            log.error(
                                "计划id[{}]对应的审计计划数据不存在,无法在项目删除后变更计划状态",
                                planId);
                        }
                    } else {
                        log.info("计划[{}]下存在项目且有项目未完成,在项目删除后计划状态无需变更",
                            planId);
                    }
                } else {
                    log.info("计划[{}]下不存在项目无需在项目删除后变更计划状态", planId);
                }
            });
        }

    }

    @Override
    public void afterLoadByKey(LcModelDynamicDTO model, LcModelQueryContext ctx, Long id,
        Map<String, Object> pageData) {
        Long planId = MapUtils.getLong(pageData, AuditProjectModelConstant.PLAN_ID_NAME);
        if (IdUtil.isDataId(planId)) {
            List<AuditPlanInfoDTO> plans = this.planService.findExistsInfoByIds(
                Collections.singletonList(planId));
            if (CollUtil.isNotEmpty(plans)) {
                pageData.put(AuditProjectModelConstant.PLAN_NAME, plans.get(0).getName());
            } else {
                log.warn("审计项目[{}]关联的审计计划[{}]在数据库中没有对应的审计计划数据", id,
                    planId);
            }
        }
        // 填充负责人信息
        this.fillOwnerInfo(Collections.singletonList(id), Collections.singletonList(pageData));
    }

    @Override
    public void afterSearch(LcModelDynamicDTO model, LcModelSearchContext ctx,
        IPage<Map<String, Object>> searchResult) {
        List<Map<String, Object>> records = searchResult.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> ids = records.stream()
                .map(item -> MapUtils.getLong(item, LcModelConstant.KEY_COL_NAME)).collect(
                    Collectors.toList());
            // 填充负责人信息
            this.fillOwnerInfo(ids, records);
        }

    }

    private void fillOwnerInfo(Collection<Long> ids, List<Map<String, Object>> records) {
        // 填充负责人信息
        List<AuditProjectMemberInfoDTO> owners = this.memberService.findOwnerByProjectIds(
            ids);
        if (CollUtil.isNotEmpty(owners)) {
            List<String> orgIds = owners.stream().map(AuditProjectMemberInfoDTO::getOrgId)
                .distinct().collect(
                    Collectors.toList());
            // key: orgId
            // value: 名称
            Map<String, String> orgNameMap = this.orgService.getOrgNameMapByOrgIdList(
                orgIds);

            Map<Long, List<AuditProjectMemberInfoDTO>> projectIdAndOwnerMap = owners.stream()
                .collect(Collectors.groupingBy(AuditProjectMemberInfoDTO::getProjectId));

            records.forEach(record -> {
                Long projectId = MapUtils.getLong(record, LcModelConstant.KEY_COL_NAME);
                List<AuditProjectMemberInfoDTO> projectOwners = projectIdAndOwnerMap.get(
                    projectId);
                if (CollUtil.isNotEmpty(projectOwners)) {
                    List<String> ownerOrgIds = new LinkedList<>();
                    List<String> ownerNames = new LinkedList<>();
                    projectOwners.forEach(item -> {
                        String orgId = item.getOrgId();
                        ownerOrgIds.add(orgId);
                        String name = orgNameMap.get(orgId);
                        if (StringUtils.isNotBlank(name)) {
                            ownerNames.add(name);
                        } else {
                            log.warn("根据人员orgId[{}]获取到名称为空", orgId);
                        }
                    });
                    record.put(AuditProjectConstant.OWNER_ORGID, ownerOrgIds);
                    record.put(AuditProjectConstant.OWNER_NAME, String.join(",", ownerNames));
                } else {
                    record.put(AuditProjectConstant.OWNER_ORGID, Collections.emptyList());
                    record.put(AuditProjectConstant.OWNER_NAME, StringPool.EMPTY);
                }
            });
        } else {
            records.forEach(record -> {
                record.put(AuditProjectConstant.OWNER_ORGID, Collections.emptyList());
                record.put(AuditProjectConstant.OWNER_NAME, StringPool.EMPTY);
            });
        }
    }
}
