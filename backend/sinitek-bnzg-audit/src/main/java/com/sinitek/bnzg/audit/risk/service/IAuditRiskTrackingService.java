package com.sinitek.bnzg.audit.risk.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.dto.RectifyResultLoadResultDTO;
import com.sinitek.bnzg.audit.risk.dto.RectifyResultSaveParamDTO;

/**
 * 项目风险点 整改追踪 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-28
 */
public interface IAuditRiskTrackingService {

    /**
     * 保存整改建议
     */
    void saveRectifyResult(RectifyResultSaveParamDTO param);


    RectifyResultLoadResultDTO loadByRiskId(Long riskId);

    IPage<AuditRiskSearchResultDTO> search(AuditRiskSearchParamDTO searchDTO);

    /**
     * 如果风险点整改状态存在`整改中`或`延期整改`的数据，则不允许完成
     */
    AuditCheckResultDTO checkCanFinish(Long projectId);

    //保存相关部门相关人员
    void saveRelevantPeopleAndDepartment(RectifyContactEditParamDTO param);

}
