package com.sinitek.bnzg.audit.risk.service;


import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import java.util.Collection;
import java.util.List;

/**
 * 项目风险点责任部门 Service 接口
 *
 * <AUTHOR>
 * date 2024-11-08
 */
public interface IAuditRiskRespDeptService {

    List<AuditRiskRespDept> findByRiskId(Long riskId);

    void saveBatchRespDept(Long riskId, List<String> respDeptIds);

    void saveOrUpdateBatch(List<AuditRiskRespDept> auditRiskRespDepts);

    void removeByIds(List<Long> ids);

    List<AuditRiskRespDept> findByRiskIds(Collection<Long> riskIds);

}
