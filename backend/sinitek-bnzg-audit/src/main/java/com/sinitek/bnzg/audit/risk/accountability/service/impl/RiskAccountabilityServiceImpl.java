package com.sinitek.bnzg.audit.risk.accountability.service.impl;

import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.COMPANY_REVIEW_RESULT_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.COMPANY_REVIEW_RESULT_UPLOAD_TYPE2;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.IMPLEMENTATION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.INVESTIGATION_RESULT_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.INVESTIGATION_RESULT_UPLOAD_TYPE2;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.PUNISH_DECISION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.PUNISH_NOTICE_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.PUNISH_OPINION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.PUNISH_OPINION_UPLOAD_TYPE2;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.RECONSIDERATION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.REVIEW_RECORD_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant.REVIEW_RECORD_UPLOAD_TYPE2;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcMessageConstant.AUDIT_RISK_AC_EXISTS;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcMessageConstant.AUDIT_RISK_AC_NOT_FINISH;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcMessageConstant.AUDIT_RISK_AC_NOT_SELECT_RISK;
import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcMessageConstant.AUDIT_RISK_AC_RESP_DEPT_NOT_AGREE;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.RISK_DATA_NOT_EXISTS;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskRespDeeptSuggestTypeConstant.AGREE;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant;
import com.sinitek.bnzg.audit.risk.accountability.dao.RiskAccountabilityDAO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcDetailDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.DeleteAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.SaveOrEditAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchResultPO;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.accountability.util.RiskAccountabilityEventUtil;
import com.sinitek.bnzg.audit.risk.accountability.util.RiskAccountabilityUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespManService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.sirm.calendar.service.ICalendarEventService;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.message.service.ISirmMessageService;
import com.sinitek.sirm.common.message.template.dto.MessageContextDTO;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.message.template.enumerate.SendModeTypeEnum;
import com.sinitek.sirm.common.message.template.service.IMessageTemplateExtService;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.common.utils.StringUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.routine.holiday.util.HolidayUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.mail.Message.RecipientType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date：2024/11/15 11:13
 */
@Slf4j
@Service
public class RiskAccountabilityServiceImpl implements IRiskAccountabilityService {

    private static final int DAY_SEP = 4;

    @Autowired
    private RiskAccountabilityDAO dao;

    @Autowired
    private IAuditRiskRespDeptService deptService;

    @Autowired
    private IAuditRiskRespManService respManService;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Autowired
    private IAttachmentExtService attachmentExtService;

    @Autowired
    private ICalendarEventService calendarEventService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IMessageTemplateExtService messageTemplateExtService;

    @Autowired
    private ISirmMessageService sirmMessageService;

    @Override
    public IPage<AuditRiskAcSearchResultDTO> search(AuditRiskAcSearchParamDTO searchDTO) {
        AuditRiskAcSearchParamPO param = RiskAccountabilityUtil.makeSearchParamDTO2PO(searchDTO);
        Page<AuditRiskAcSearchResultPO> page = param.buildPage();
        IPage<AuditRiskAcSearchResultPO> search = this.dao.search(page, param);
        search.getRecords().stream()
            .filter(Objects::nonNull)
            .forEach(record -> {
                // 设置责任人 ID
                List<AuditRiskRespMan> respManList = respManService.findByRiskId(
                    record.getRiskId());
                if (CollectionUtils.isNotEmpty(respManList)) {
                    List<String> respManIdList = respManList.stream()
                        .map(AuditRiskRespMan::getRespManId)
                        .collect(Collectors.toList());
                    record.setRespManIds(respManIdList);
                }

                // 设置责任部门 ID
                List<AuditRiskRespDept> respDeptList = deptService.findByRiskId(record.getRiskId());
                if (CollectionUtils.isNotEmpty(respDeptList)) {
                    List<String> respDeptIdList = respDeptList.stream()
                        .map(AuditRiskRespDept::getRespDeptId)
                        .collect(Collectors.toList());
                    record.setRespDeptIds(respDeptIdList);
                }
            });
        return search.convert(RiskAccountabilityUtil::makeSearchResultPO2DTO);
    }

    @Override
    public AuditRiskAcSearchResultDTO loadDetailByRiskId(Long riskId) {
        AuditRiskAcSearchResultPO result = this.dao.loadDetail(riskId);
        return RiskAccountabilityUtil.makeSearchResultPO2DTO(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAuditRiskAc(SaveOrEditAuditRiskAcParamDTO param) {
        Long riskId = param.getRiskId();
        String reviewStage = param.getReviewStage();
        String auditReconcile = param.getAuditReconcile();
        String punishExecution = param.getPunishExecution();
        Date reviewStartDate = param.getReviewStartDate();
        Date reviewEndDate = param.getReviewEndDate();
        Date companyReviewDate = param.getCompanyReviewDate();
        Date punishNoticeDate = param.getPunishNoticeDate();
        Date reconcileApplyDate = param.getReconcileApplyDate();
        Date punishDecisionDate = param.getPunishDecisionDate();
        Date punishImplementDate = param.getPunishImplementDate();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditRiskAc riskAc = this.dao.getAuditRiskAcByRiskId(riskId);
        if (Objects.nonNull(riskAc)) {
            AuditRiskAc oldData = JsonUtil.jsonCopy(riskAc, AuditRiskAc.class);

            riskAc.setPunishExecution(punishExecution);
            riskAc.setAuditReconcile(auditReconcile);
            riskAc.setReviewStage(reviewStage);
            riskAc.setReviewStartDate(reviewStartDate);
            riskAc.setReviewEndDate(reviewEndDate);
            riskAc.setCompanyReviewDate(companyReviewDate);
            riskAc.setPunishNoticeDate(punishNoticeDate);
            riskAc.setReconcileApplyDate(reconcileApplyDate);
            riskAc.setPunishDecisionDate(punishDecisionDate);
            riskAc.setPunishImplementDate(punishImplementDate);
            // 二次审议字段
            riskAc.setReviewStartDate2(param.getReviewStartDate2());
            riskAc.setReviewEndDate2(param.getReviewEndDate2());
            riskAc.setCompanyReviewDate2(param.getCompanyReviewDate2());
            riskAc.setReviewStage2(param.getReviewStage2());
            this.dao.updateById(riskAc);

            //审计调查结果附件
            UploadDTO investigationResultUpload = param.getInvestigationResultUpload();
            if (!Objects.isNull(investigationResultUpload)) {
                investigationResultUpload.setType(INVESTIGATION_RESULT_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(investigationResultUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //审议记录附件
            UploadDTO reviewRecordUpload = param.getReviewRecordUpload();
            if (!Objects.isNull(reviewRecordUpload)) {
                reviewRecordUpload.setType(REVIEW_RECORD_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(reviewRecordUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //处分意见
            UploadDTO punishOpinionUpload = param.getPunishOpinionUpload();
            if (!Objects.isNull(punishOpinionUpload)) {
                punishOpinionUpload.setType(PUNISH_OPINION_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(punishOpinionUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //处罚告知书
            UploadDTO punishNoticeUpload = param.getPunishNoticeUpload();
            if (!Objects.isNull(punishNoticeUpload)) {
                punishNoticeUpload.setType(PUNISH_NOTICE_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(punishNoticeUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //复议申请文件
            UploadDTO reconsiderationUpload = param.getReconsiderationUpload();
            if (!Objects.isNull(reconsiderationUpload)) {
                reconsiderationUpload.setType(RECONSIDERATION_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(reconsiderationUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //处罚决定书
            UploadDTO punishDecisionUpload = param.getPunishDecisionUpload();
            if (!Objects.isNull(punishDecisionUpload)) {
                punishDecisionUpload.setType(PUNISH_DECISION_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(punishDecisionUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //处罚落实文件
            UploadDTO implementationUpload = param.getImplementationUpload();
            if (!Objects.isNull(implementationUpload)) {
                implementationUpload.setType(IMPLEMENTATION_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(implementationUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            //公司党委会审议结果
            UploadDTO companyReviewUpload = param.getCompanyReviewResultUpload();
            if (!Objects.isNull(companyReviewUpload)) {
                companyReviewUpload.setType(COMPANY_REVIEW_RESULT_UPLOAD_TYPE);
                AttachmentUtils.saveAttachmentList(companyReviewUpload, riskAc.getId(),
                    AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }
            // 二次审议附件
            this.saveAttachment(param.getInvestigationResultUpload2(),
                INVESTIGATION_RESULT_UPLOAD_TYPE2, riskAc.getId());
            this.saveAttachment(param.getReviewRecordUpload2(),
                REVIEW_RECORD_UPLOAD_TYPE2, riskAc.getId());
            this.saveAttachment(param.getPunishOpinionUpload2(),
                PUNISH_OPINION_UPLOAD_TYPE2, riskAc.getId());
            this.saveAttachment(param.getCompanyReviewResultUpload2(),
                COMPANY_REVIEW_RESULT_UPLOAD_TYPE2, riskAc.getId());

            // 发送邮件通知
            this.sendEamilNotice(riskAc.getId());

            RiskAccountabilityEventUtil.publish(RecordChangeLogAddParamDTO.builder()
                .foreignKey(riskAc.getId())
                .oldValue(oldData)
                .newValue(riskAc)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("问责管理-编辑问责管理结果")
                .build());

        } else {
            log.error("根据风险点id {} 找不到对应的风险点数据", riskId);
            throw new BussinessException(RISK_DATA_NOT_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAuditRiskAc(SaveOrEditAuditRiskAcParamDTO param) {
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        Long riskId = param.getRiskId();
        if (Objects.isNull(riskId)) {
            log.error("未选择风险点，无法添加问责管理数据");
            throw new BussinessException(AUDIT_RISK_AC_NOT_SELECT_RISK);
        }
        AuditRiskDetailDTO risk = auditRiskService.loadDetail(riskId);
        if (Objects.isNull(risk)) {
            log.error("根据风险点id {} 找不到对应的风险点数据", riskId);
            throw new BussinessException(RISK_DATA_NOT_EXISTS);
        }
        Integer status = risk.getStatus();
        if (!Objects.equals(AuditRiskStatusConstant.APPROVED, status)) {
            log.error("当前风险点{}未审批通过，无法添加问责管理数据", riskId);
            throw new BussinessException(AUDIT_RISK_AC_NOT_FINISH);
        }
        Integer respDeptSugType = risk.getRespDeptSugType();
        if (!Objects.equals(AGREE, respDeptSugType)) {
            log.error("当前风险点{}责任部门意见不同意，无法添加问责管理数据", riskId);
            throw new BussinessException(AUDIT_RISK_AC_RESP_DEPT_NOT_AGREE);
        }
        AuditRiskAc riskAc = this.dao.getAuditRiskAcByRiskId(riskId);
        if (Objects.nonNull(riskAc)) {
            log.error("当前风险点{}已存在问责管理数据,请前往问责结果管理查看", riskId);
            throw new BussinessException(AUDIT_RISK_AC_EXISTS);
        }
        AuditRiskAc auditRiskAc = new AuditRiskAc();
        auditRiskAc.setAuditReconcile(param.getAuditReconcile());
        auditRiskAc.setPunishExecution(param.getPunishExecution());
        auditRiskAc.setReviewStage(param.getReviewStage());
        auditRiskAc.setReviewStartDate(param.getReviewStartDate());
        auditRiskAc.setReviewEndDate(param.getReviewEndDate());
        auditRiskAc.setCompanyReviewDate(param.getCompanyReviewDate());
        auditRiskAc.setPunishNoticeDate(param.getPunishNoticeDate());
        auditRiskAc.setReconcileApplyDate(param.getReconcileApplyDate());
        auditRiskAc.setPunishDecisionDate(param.getPunishDecisionDate());
        auditRiskAc.setPunishImplementDate(param.getPunishImplementDate());
        auditRiskAc.setRiskId(riskId);
        // 二次审议字段
        auditRiskAc.setReviewStartDate2(param.getReviewStartDate2());
        auditRiskAc.setReviewEndDate2(param.getReviewEndDate2());
        auditRiskAc.setCompanyReviewDate2(param.getCompanyReviewDate2());
        auditRiskAc.setReviewStage2(param.getReviewStage2());

        this.dao.save(auditRiskAc);

        //审计调查结果附件
        UploadDTO investigationResultUpload = param.getInvestigationResultUpload();
        if (!Objects.isNull(investigationResultUpload)) {
            investigationResultUpload.setType(INVESTIGATION_RESULT_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(investigationResultUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //审议记录附件
        UploadDTO reviewRecordUpload = param.getReviewRecordUpload();
        if (!Objects.isNull(reviewRecordUpload)) {
            reviewRecordUpload.setType(REVIEW_RECORD_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(reviewRecordUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //处分意见
        UploadDTO punishOpinionUpload = param.getPunishOpinionUpload();
        if (!Objects.isNull(punishOpinionUpload)) {
            punishOpinionUpload.setType(PUNISH_OPINION_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(punishOpinionUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //处罚告知书
        UploadDTO punishNoticeUpload = param.getPunishNoticeUpload();
        if (!Objects.isNull(punishNoticeUpload)) {
            punishNoticeUpload.setType(PUNISH_NOTICE_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(punishNoticeUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //复议申请文件
        UploadDTO reconsiderationUpload = param.getReconsiderationUpload();
        if (!Objects.isNull(reconsiderationUpload)) {
            reconsiderationUpload.setType(RECONSIDERATION_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(reconsiderationUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //处罚决定书
        UploadDTO punishDecisionUpload = param.getPunishDecisionUpload();
        if (!Objects.isNull(punishDecisionUpload)) {
            punishDecisionUpload.setType(PUNISH_DECISION_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(punishDecisionUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //处罚落实文件
        UploadDTO implementationUpload = param.getImplementationUpload();
        if (!Objects.isNull(implementationUpload)) {
            implementationUpload.setType(IMPLEMENTATION_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(implementationUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        //公司党委会审议结果
        UploadDTO companyReviewUpload = param.getCompanyReviewResultUpload();
        if (!Objects.isNull(companyReviewUpload)) {
            companyReviewUpload.setType(COMPANY_REVIEW_RESULT_UPLOAD_TYPE);
            AttachmentUtils.saveAttachmentList(companyReviewUpload, auditRiskAc.getId(),
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
        // 二次审议附件
        this.saveAttachment(param.getInvestigationResultUpload2(),
            INVESTIGATION_RESULT_UPLOAD_TYPE2, auditRiskAc.getId());
        this.saveAttachment(param.getReviewRecordUpload2(),
            REVIEW_RECORD_UPLOAD_TYPE2, auditRiskAc.getId());
        this.saveAttachment(param.getPunishOpinionUpload2(),
            PUNISH_OPINION_UPLOAD_TYPE2, auditRiskAc.getId());
        this.saveAttachment(param.getCompanyReviewResultUpload2(),
            COMPANY_REVIEW_RESULT_UPLOAD_TYPE2, auditRiskAc.getId());

        // 发送邮件通知
        this.sendEamilNotice(auditRiskAc.getId());

        RiskAccountabilityEventUtil.publish(RecordChangeLogAddParamDTO.builder()
            .foreignKey(auditRiskAc.getId())
            .newValue(auditRiskAc)
            .operatorId(operatorId)
            .opTime(opTime)
            .remark("问责管理-新增问责管理结果")
            .build());
    }

    private void saveAttachment(UploadDTO upload, Integer attachmentType, Long sourceId) {
        if (Objects.nonNull(upload)) {
            upload.setType(attachmentType);
            AttachmentUtils.saveAttachmentList(upload, sourceId,
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
        }
    }

    /**
     * 上传处罚告知书并填写处罚告知书下发日期，如果未上传复议申请文件
     *
     * 则在处罚告知书下发日期4个工作日后早9:00发送邮件提醒，如果邮件提醒时间已超过当前系统时间则不发送
     *
     * 发送给风险点的责任人，抄送给风险点审计人
     */
    private void sendEamilNotice(Long riskAcId) {
        AuditRiskAc riskAc = this.dao.getById(riskAcId);
        Long riskId = riskAc.getRiskId();
        // 处罚告知书下发日期
        Date punishNoticeDate = riskAc.getPunishNoticeDate();
        // 处罚告知书
        AttachmentDTO punishNoticeUpload = this.attachmentExtService.getAttachment(
            AuditRiskAcConstant.DEFAULT_SOURCE_NAME, riskAcId, PUNISH_NOTICE_UPLOAD_TYPE);
        // 复议申请文件
        AttachmentDTO reconsiderationUpload = this.attachmentExtService.getAttachment(
            AuditRiskAcConstant.DEFAULT_SOURCE_NAME, riskAcId, RECONSIDERATION_UPLOAD_TYPE);
        boolean hasPunishNoticeDate = Objects.nonNull(punishNoticeDate);
        boolean hasPunishNoticeUpload = Objects.nonNull(punishNoticeUpload);
        boolean hasReconsiderationUpload = Objects.nonNull(reconsiderationUpload);
        if (hasPunishNoticeDate && hasPunishNoticeUpload && !hasReconsiderationUpload) {
            this.doSendEamilNotice(riskAc);
            log.info(
                "风险点[{}}问责[{}]处罚告知书下发日期: {},处罚告知书: {}, 复议申请文件: {} 满足条件创建问责提醒",
                riskId,
                riskAcId, hasPunishNoticeDate, hasPunishNoticeUpload, hasReconsiderationUpload);
        } else {
            this.sirmMessageService.deleteUnSendTimingMessageBySourceIdAndSourceEntity(
                riskAcId, AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            log.warn(
                "风险点[{}}问责[{}]处罚告知书下发日期: {},处罚告知书: {}, 复议申请文件: {} 不满足条件无法创建问责提醒",
                riskId,
                riskAcId, hasPunishNoticeDate, hasPunishNoticeUpload, hasReconsiderationUpload);
        }
    }

    private void doSendEamilNotice(AuditRiskAc riskAc) {
        Long riskAcId = riskAc.getId();
        Long riskId = riskAc.getRiskId();
        // 处罚告知书下发日期
        Date punishNoticeDate = riskAc.getPunishNoticeDate();

        String riskAcEmailTime = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TIME, "09:00");

        Integer daySep = SettingUtils.getIntegerValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TIME, DAY_SEP);

        Date sendDate = HolidayUtil.addWorkdays(punishNoticeDate, daySep);
        String reqRectifyDateYYYYMMDDStr = DateUtil.format(sendDate,
            GlobalConstant.TIME_FORMAT_TEN);
        String reqRectifyDateTimeStr = String.format("%s %s:00", reqRectifyDateYYYYMMDDStr,
            riskAcEmailTime);
        Date calDateTime = DateUtil.parse(reqRectifyDateTimeStr,
            GlobalConstant.TIME_FORMAT_THIRTEEN);
        if ((calDateTime).before(new Date())) {
            log.info(
                "风险点[{}]问责[{}]处罚告知书下发日期为[{}],daySep: {},计算出提醒时间为[{}],发送时间为过去的时间,不再继续处理",
                riskId,
                riskAcId,
                DateUtil.format(punishNoticeDate,
                    GlobalConstant.TIME_FORMAT_TEN), daySep, reqRectifyDateTimeStr);
            return;
        } else {
            log.info("风险点[{}]问责[{}]处罚告知书下发日期为[{}],daySep: {},计算出提醒时间为[{}]",
                riskId,
                riskAcId,
                DateUtil.format(punishNoticeDate,
                    GlobalConstant.TIME_FORMAT_TEN), daySep, reqRectifyDateTimeStr);
        }

        String riskAcEmailTitle = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TITLE);
        if (StringUtils.isBlank(riskAcEmailTitle)) {
            log.warn("系统配置 风险点问责提醒邮件标题 [{},{}]为空,无法创建问责提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_AC_EMAIL_TITLE);
            return;
        }

        String riskAcEmailTemplate = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TEMPLATE);
        if (StringUtils.isBlank(riskAcEmailTemplate)) {
            log.warn("系统配置 风险点问责提醒模板 [{},{}]为空,无法创建问责提醒",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.RISK_AC_EMAIL_TEMPLATE);
            return;
        }

        log.info("系统参数 风险点问责提醒时间 [module:{},name:{}]配置为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TIME, riskAcEmailTime);
        log.info("系统参数 风险点整改提醒标题 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TITLE, riskAcEmailTitle);
        log.info("系统参数 风险点问责提醒模板 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_TEMPLATE, riskAcEmailTemplate);
        log.info("系统参数 处罚告知书下发日期推延工作日天数 [module:{},name:{}]配置值为[{}]",
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_AC_EMAIL_SEND_DAY_SEP, daySep);

        AuditRiskDetailDTO risk = this.auditRiskService.getFormatedData(riskId);
        AuditRiskAcDetailDTO detail = RiskAccountabilityUtil.makeEntity2DetailDTO(riskAc);

        // 邮件标题
        Map<String, Object> replaceVariablesCtx = new HashMap<>();
        replaceVariablesCtx.putAll(JsonUtil.toMap(risk));
        // 问责数据优先级最高
        replaceVariablesCtx.putAll(JsonUtil.toMap(detail));
        String realEmailTitle = StringUtil.replaceVariables(riskAcEmailTitle, replaceVariablesCtx);
        if (StringUtils.isBlank(realEmailTitle)) {
            log.warn("风险点[{}]问责[{}]邮件标题[{}]为空,无法创建问责提醒", riskId, riskAcId,
                realEmailTitle);
            return;
        }

        // 责任人
        List<String> respManIds = risk.getRespManId();
        if (CollUtil.isEmpty(respManIds)) {
            log.warn("风险点[{}]问责[{}]责任人[{}]为空,无法创问责提醒", riskId, riskAcId,
                respManIds);
            return;
        }

        // 内容 content;
        String content = StringUtil.replaceVariables(riskAcEmailTemplate,
            replaceVariablesCtx);

        MessageContextDTO contextDTO = new MessageContextDTO();

        //设置消息发送方式 1-邮件 4-系统消息 8-企业微信 如果多种方式值叠加，例如 5：邮件、系统消息这两种发送方式，优先于消息模板中的设置
        contextDTO.setSendMode(SendModeTypeEnum.SENDMODE_EMAIL.getEnumItemValue());

        //设置消息发送的标题，优先于消息模板中的设置
        contextDTO.setTitle(realEmailTitle);

        //设置消息发送的内容，优先于消息模板中的设置
        contextDTO.setContent(content);

        //设置消息发送的时间，直接发送可以不进行设置，定时发送需要设置定时发送时间
        contextDTO.setSendTime(calDateTime);

        //设置定时消息覆蓋标识，默认为false，如果为true，则会清空相同sourceId与sourceEntity的未发送(status=1)的定时消息
        contextDTO.setTimingMsgOverwriteFlag(true);

        //设置消息接收人
        List<MessageReceiverTemplateDTO> receivers = new ArrayList<>();
        // empId:员工id userName:员工登录名 empName:员工名称 email:邮箱 mobile：手机号码
        // receiveType：收件人类型, 'To':邮件收件人，'Cc':邮件抄送人

        // 发送给责任人
        for (String respManId : respManIds) {
            MessageReceiverTemplateDTO receiverTemplateDTO = new MessageReceiverTemplateDTO();
            receiverTemplateDTO.setEmpId(respManId);
            receiverTemplateDTO.setReceiveType(RecipientType.TO.toString());
            receivers.add(receiverTemplateDTO);
        }

        // 抄送给审计人
        String auditorId = risk.getAuditorId();
        if (StringUtils.isNotBlank(auditorId)) {
            MessageReceiverTemplateDTO ccReceiverTemplateDTO = new MessageReceiverTemplateDTO();
            ccReceiverTemplateDTO.setEmpId(auditorId);
            ccReceiverTemplateDTO.setReceiveType(RecipientType.CC.toString());
            receivers.add(ccReceiverTemplateDTO);
        } else {
            log.warn("风险点[{}]问责[{}]审计人[{}]为空,无法抄送给审计人", riskId, riskAcId,
                auditorId);
        }

        contextDTO.setReceivers(receivers);

        //设置数据来源记录id
        contextDTO.setSourceId(riskAcId);
        //设置数据来源记录名称
        contextDTO.setSourceEntity(AuditRiskAcConstant.DEFAULT_SOURCE_NAME);

        //发送消息
        this.messageTemplateExtService.sendMessage(contextDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdList(DeleteAuditRiskAcParamDTO param) {
        List<Long> idList = param.getIds();
        String opRemark = param.getOpRemark();
        String operatorId = CurrentUserFactory.getOrgId();
        Date opTime = new Date();
        if (CollUtil.isNotEmpty(idList)) {
            List<AuditRiskAc> auditRiskAcs = this.dao.listByIds(idList);

            this.dao.deleteByIds(idList, operatorId);

            //删除关联附件
            for (Long id : idList) {
                attachmentExtService.removeAttachment(AuditRiskAcConstant.DEFAULT_SOURCE_NAME, id);

                this.sirmMessageService.deleteUnSendTimingMessageBySourceIdAndSourceEntity(
                    id, AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            }

            Map<Long, AuditRiskAc> oldValueMap = auditRiskAcs.stream()
                .collect(Collectors.toMap(AuditRiskAc::getId, v -> v));

            RiskAccountabilityEventUtil.publish(
                RecordChangeLogBatchAddParam2DTO.<AuditRiskAc>builder()
                    .foreignKeys(idList)
                    .newValueMap(null)
                    .oldValueMap(oldValueMap)
                    .remark(
                        StringUtils.isNotBlank(opRemark) ? opRemark : "问责管理-删除问责管理结果")
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .build());
        }
    }

    @Override
    public AuditRiskAc getAuditRiskAcByRiskId(Long riskId) {
        return this.dao.getAuditRiskAcByRiskId(riskId);
    }

    @Override
    public List<AuditRiskAc> findAuditRiskAcByRiskIds(List<Long> riskIds) {
        return this.dao.findAuditRiskAcByRiskIds(riskIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByRiskId(Long riskId, Long newRiskId) {
        this.dao.updateByRiskId(riskId, newRiskId);
    }


}
