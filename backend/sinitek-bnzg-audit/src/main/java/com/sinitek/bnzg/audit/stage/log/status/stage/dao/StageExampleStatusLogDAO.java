package com.sinitek.bnzg.audit.stage.log.status.stage.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.stage.log.status.stage.entity.StageExampleStatusLog;
import com.sinitek.bnzg.audit.stage.log.status.stage.mapper.StageExampleStatusLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class StageExampleStatusLogDAO extends
    ServiceImpl<StageExampleStatusLogMapper, StageExampleStatusLog> {

    public List<StageExampleStatusLog> findByStageExampleId(Long stageExampleId) {
        LambdaQueryWrapper<StageExampleStatusLog> queryWrapper = Wrappers.lambdaQuery(
            StageExampleStatusLog.class);
        queryWrapper.eq(StageExampleStatusLog::getStageExampleId, stageExampleId);
        return this.list(queryWrapper);
    }

}
