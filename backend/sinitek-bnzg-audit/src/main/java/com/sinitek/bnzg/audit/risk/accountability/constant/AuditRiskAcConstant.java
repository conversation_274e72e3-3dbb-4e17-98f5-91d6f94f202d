package com.sinitek.bnzg.audit.risk.accountability.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date：2024/11/15 15:11
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskAcConstant {

    public static final String DEFAULT_SOURCE_NAME = "AUDIT_RISK_AC";

    //审计调查结果附件
    public static final Integer INVESTIGATION_RESULT_UPLOAD_TYPE = 107;

    //审议记录附件
    public static final Integer REVIEW_RECORD_UPLOAD_TYPE = 108;

    //处分意见
    public static final Integer PUNISH_OPINION_UPLOAD_TYPE = 109;

    //处罚告知书
    public static final Integer PUNISH_NOTICE_UPLOAD_TYPE = 110;

    //复议申请文件
    public static final Integer RECONSIDERATION_UPLOAD_TYPE = 111;

    //处罚决定书
    public static final Integer PUNISH_DECISION_UPLOAD_TYPE = 112;

    //处罚落实文件
    public static final Integer IMPLEMENTATION_UPLOAD_TYPE = 113;

    //公司党委会审议结果
    public static final Integer COMPANY_REVIEW_RESULT_UPLOAD_TYPE = 116;

    //审计调查结果附件(二次审议)
    public static final Integer INVESTIGATION_RESULT_UPLOAD_TYPE2 = 117;

    //审议记录附件(二次审议)
    public static final Integer REVIEW_RECORD_UPLOAD_TYPE2 = 118;

    //处分意见(二次审议)
    public static final Integer PUNISH_OPINION_UPLOAD_TYPE2 = 119;

    //公司党委会审议结果(二次审议)
    public static final Integer COMPANY_REVIEW_RESULT_UPLOAD_TYPE2 = 120;
}
