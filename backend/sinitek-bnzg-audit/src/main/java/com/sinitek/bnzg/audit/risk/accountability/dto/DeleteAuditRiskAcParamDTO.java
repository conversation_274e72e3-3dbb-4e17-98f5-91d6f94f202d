package com.sinitek.bnzg.audit.risk.accountability.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date：2024/11/15 16:19
 */

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(value = "项目风险点-问责情况-删除DTO")
public class DeleteAuditRiskAcParamDTO {

    @ApiModelProperty(value = "主键")
    private List<Long> ids;

    @ApiModelProperty(value = "操作备注")
    private String opRemark;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
