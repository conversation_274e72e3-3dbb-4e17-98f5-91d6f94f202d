package com.sinitek.bnzg.audit.risk.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 风险点提交审批确认信息
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "风险点提交审批确认信息")
public class RiskApproveConfirmInfoDTO {

    @ApiModelProperty(value = "风险点id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;
}


