package com.sinitek.bnzg.audit.risk.accountability.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/11/15 10:32
 */


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目风险点问责情况列表返回DTO")
public class AuditRiskAcSearchResultDTO extends PageDataParam {

    @ApiModelProperty("问责id")
    private Long id;

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("审计程序id")
    private Long procedureId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("计划id")
    private Long planId;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("审计程序名称")
    private String procedureName;

    @ApiModelProperty("风险点名称")
    private String riskName;

    @ApiModelProperty("审议阶段")
    private String reviewStage;

    @ApiModelProperty("审计复议")
    private String auditReconcile;

    @ApiModelProperty("处罚执行")
    private String punishExecution;

    @ApiModelProperty("责任部门id")
    private List<String> respDeptIds;

    @ApiModelProperty("责任部门名称")
    private String respDeptName;

    @ApiModelProperty("责任人")
    private List<String> respManIds;

    @ApiModelProperty("责任人")
    private String respManName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审议开始日期")
    private Date reviewStartDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审议结束日期")
    private Date reviewEndDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("公司党委会审议日期")
    private Date companyReviewDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("处罚告知书下发日期")
    private Date punishNoticeDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计复议申请日期")
    private Date reconcileApplyDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("处罚决定书下发日期")
    private Date punishDecisionDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("处罚落实日期")
    private Date punishImplementDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审议开始日期(二次审议)")
    private Date reviewStartDate2;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审议结束日期(二次审议)")
    private Date reviewEndDate2;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("公司党委会审议日期(二次审议)")
    private Date companyReviewDate2;

    @ApiModelProperty("审议阶段(二次审议)")
    private String reviewStage2;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("创建时间")
    private Date createTimeStamp;
}

