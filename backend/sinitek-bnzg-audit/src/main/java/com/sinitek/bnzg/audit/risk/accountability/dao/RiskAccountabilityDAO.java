package com.sinitek.bnzg.audit.risk.accountability.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.mapper.RiskAccountabilityMapper;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchResultPO;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskRespDeptDAO;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskRespManDAO;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date：2024/11/15 11:14
 */
@Service
public class RiskAccountabilityDAO extends ServiceImpl<RiskAccountabilityMapper, AuditRiskAc> {

    @Autowired
    private AuditRiskRespDeptDAO deptDAO;

    @Autowired
    private AuditRiskRespManDAO respManDAO;

    public IPage<AuditRiskAcSearchResultPO> search(
            Page<AuditRiskAcSearchResultPO> page, AuditRiskAcSearchParamPO param) {
        return this.baseMapper.searchRiskAccountability(page, param);
    }

    public AuditRiskAcSearchResultPO loadDetail(Long riskId){
        AuditRiskAcSearchResultPO po = this.baseMapper.loadDetail(riskId);
        List<String> deptIds = this.deptDAO.findByRiskId(po.getRiskId()).stream()
                .map(AuditRiskRespDept::getRespDeptId)
                .collect(Collectors.toList());
        List<String> respManIds = this.respManDAO.findByRiskId(po.getRiskId()).stream()
                .map(AuditRiskRespMan::getRespManId)
                .collect(Collectors.toList());
        po.setRespDeptIds(deptIds);
        po.setRespManIds(respManIds);
        return po;
    }

    public AuditRiskAc getAuditRiskAcByRiskId(Long riskId){
        return this.getOne(Wrappers.<AuditRiskAc>lambdaQuery()
                .eq(AuditRiskAc::getRiskId, riskId)
                .eq(AuditRiskAc::getRemoveFlag, CommonBooleanEnum.FALSE.getValue()));
    }

    public List<AuditRiskAc> findAuditRiskAcByRiskIds(List<Long> riskIds) {
        if (CollUtil.isNotEmpty(riskIds)) {
            return this.list(Wrappers.<AuditRiskAc>lambdaQuery()
                .in(AuditRiskAc::getRiskId, riskIds)
                .eq(AuditRiskAc::getRemoveFlag, CommonBooleanEnum.FALSE.getValue()));
        }
        return Collections.emptyList();
    }

    public void deleteByIds(Collection<Long> ids, String operatorId) {
        if (CollUtil.isNotEmpty(ids)) {
            this.baseMapper.deleteByIds(ids, operatorId);
        }
    }

    public void updateByRiskId(Long riskId,Long newRiskId) {
        LambdaUpdateWrapper<AuditRiskAc> updateWrapper = Wrappers.lambdaUpdate(AuditRiskAc.class);
        updateWrapper.eq(AuditRiskAc::getRiskId, riskId)
                .set(AuditRiskAc::getRiskId, newRiskId);
        this.baseMapper.update(null, updateWrapper);
    }

}
