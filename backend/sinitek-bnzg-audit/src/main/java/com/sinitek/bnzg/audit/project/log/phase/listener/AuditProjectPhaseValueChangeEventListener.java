package com.sinitek.bnzg.audit.project.log.phase.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import com.sinitek.bnzg.audit.project.log.phase.event.AuditProjectPhaseValueChangeEvent;
import com.sinitek.bnzg.audit.project.log.phase.service.IAuditProjectPhaseLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditProjectPhaseValueChangeEventListener<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, Integer> {

    @Autowired
    private IAuditProjectPhaseLogService logService;

    @Override
    protected String getEventName() {
        return "审计项目阶段变动";
    }

    @Override
    protected IAuditProjectPhaseLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditProjectPhaseValueChangeEvent.class, fallbackExecution = true)
    public void listen(AuditProjectPhaseValueChangeEvent<E> event) {
        this.doListen(event);
    }
}
