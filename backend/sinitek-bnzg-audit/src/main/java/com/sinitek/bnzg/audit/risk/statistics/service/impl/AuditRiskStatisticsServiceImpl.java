package com.sinitek.bnzg.audit.risk.statistics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.lib.dao.AuditProcedureBusUnitMvDAO;
import com.sinitek.bnzg.audit.lib.entity.AuditProcedureBusUnitMv;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.bnzg.audit.risk.statistics.dao.AuditRiskStatisticsDAO;
import com.sinitek.bnzg.audit.risk.statistics.dto.*;
import com.sinitek.bnzg.audit.risk.statistics.po.*;
import com.sinitek.bnzg.audit.risk.statistics.service.IAuditRiskStatisticsService;
import com.sinitek.bnzg.audit.risk.statistics.util.AuditRiskStatisticsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date：2024/9/20 17:22
 */
@Slf4j
@Service
public class AuditRiskStatisticsServiceImpl implements IAuditRiskStatisticsService {

    @Autowired
    private AuditRiskStatisticsDAO dao;

    @Autowired
    private AuditProcedureBusUnitMvDAO auditProcedureBusUnitMvDAO;

    @Autowired
    private IAuditRiskRespDeptService deptService;



    @Override
    public IPage<AuditRiskStatisticsReasultDTO> search(AuditRiskStatisticsParamDTO searchDTO) {
        AuditRiskStatisticsParamPO param = AuditRiskStatisticsUtil.makeAuditRiskStatisticsParamPO2DTO(
            searchDTO);
        Page<AuditRiskStatisticsReasultPO> page = param.buildPage();
        IPage<AuditRiskStatisticsReasultPO> search = this.dao.search(page,param);
        search.getRecords().stream()
                .filter(Objects::nonNull)
                .forEach(record -> {
                    // 设置业务部门id
                    List<AuditProcedureBusUnitMv> busUnitMvs =
                            this.auditProcedureBusUnitMvDAO.findByPId(record.getProcedureId());
                    if (CollectionUtils.isNotEmpty(busUnitMvs)) {
                        List<String> busUnitMvsList = busUnitMvs.stream()
                                .map(AuditProcedureBusUnitMv::getOrgId)
                                .collect(Collectors.toList());
                        record.setBusinessUnits(busUnitMvsList);
                    }

                    // 设置责任部门 ID
                    List<AuditRiskRespDept> respDeptList = this.deptService.findByRiskId(record.getRiskId());
                    if (CollectionUtils.isNotEmpty(respDeptList)) {
                        List<String> respDeptIdList = respDeptList.stream()
                                .map(AuditRiskRespDept::getRespDeptId)
                                .collect(Collectors.toList());
                        record.setRespDeptIds(respDeptIdList);
                    }
                });
        return search.convert(AuditRiskStatisticsUtil::makeAuditRiskStatisticsReasultPO2DTO);
    }

    @Override
    public List<Integer> findAuditYear() {
        return this.dao.findAuditYear();
    }

    @Override
    public List<RiskStatisticsChartReasultDTO> findRiskStatisticsChart(RiskStatisticsChartParamDTO searchDTO) {
        RiskStatisticsChartParamPO param = AuditRiskStatisticsUtil.makeRiskStatisticsChartParamDTO2PO(
            searchDTO);
        List<RiskStatisticsChartReasultPO> statisticsChart = this.dao.findRiskStatisticsChart(param);
        return statisticsChart.stream().map(AuditRiskStatisticsUtil::makeRiskStatisticsChartReasultPO2DTO)
                .collect(Collectors.toList());    }

    @Override
    public List<AuditFindRectifyChartReasultDTO> findAuditFindRectifyChart(AuditFindRectifyChartParamDTO searchDTO) {
        AuditFindRectifyChartParamPO param = AuditRiskStatisticsUtil.makeAuditFindRectifyChartParamDTO2PO(
            searchDTO);
        List<AuditFindRectifyChartReasultPO> rectifyChart = this.dao.findAuditFindRectifyChart(param);
        rectifyChart.stream()
                .forEach(reasultPO -> reasultPO.setPlanAndProjectName(
                        reasultPO.getPlanName() + "/" + reasultPO.getProjectName()
                ));
        return rectifyChart.stream().map(AuditRiskStatisticsUtil::makeAuditFindRectifyChartReasultPO2DTO)
                .collect(Collectors.toList());
    }

    @Override
    public IPage<AuditRiskStatisticsReasultDTO> searchAuditFindRectifyChartDetail(
        AuditFindRectifyChartDetailParamDTO searchDTO) {
        AuditFindRectifyChartDetailParamPO param = AuditRiskStatisticsUtil.makeAuditFindRectifyChartDetailParamDTO2PO(
            searchDTO);
        Page<AuditRiskStatisticsReasultPO> page = param.buildPage();
        IPage<AuditRiskStatisticsReasultPO> chartDetail = this.dao.searchAuditFindRectifyChartDetail(
            page, param);
        return chartDetail.convert(AuditRiskStatisticsUtil::makeAuditRiskStatisticsReasultPO2DTO);
    }


}
