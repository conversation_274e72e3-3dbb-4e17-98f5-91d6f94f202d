package com.sinitek.bnzg.audit.project.procedure.approve.util;

import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApprove;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/30/2024 14:45
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PpApproveConvertUtil {

    public static PpApproveBaseInfoDTO makeEntity2BaseInfoDTO(AuditPpApprove entity) {
        return LcConvertUtil.convert(entity, PpApproveBaseInfoDTO::new);
    }

    public static PpApproveDetailDTO makeEntity2DetailDTO(AuditPpApprove entity) {
        return LcConvertUtil.convert(entity, PpApproveDetailDTO::new);
    }
}
