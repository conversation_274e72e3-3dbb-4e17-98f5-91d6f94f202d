package com.sinitek.bnzg.audit.risk.accountability.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcModelConstant;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.event.RiskAccountabilitySaveOrEditEvent;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.lowcode.audit.constant.LcDataAuditOpTypeConstant;
import com.sinitek.sirm.lowcode.audit.dto.DataAuditEventSourceDTO;
import com.sinitek.sirm.lowcode.audit.dto.LcAuditDataDTO;
import com.sinitek.sirm.lowcode.audit.event.DataAuditEventDTO;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRiskAcSaveOrEditEventListener {

    /**
     * 监听风险点改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = RiskAccountabilitySaveOrEditEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        RiskAccountabilitySaveOrEditEvent<T> event) {

        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            log.info("风险点问责管理数据留痕事件监听器 监听到 风险点问责管理 事件,数据: {}",
                JsonUtil.toJsonString(event));
            this.handleSingleData((RecordChangeLogAddParamDTO) source);
        } else {
            log.info("风险点问责管理数据留痕事件监听器 监听到 风险点问责管理 批量事件,数据: {}",
                JsonUtil.toJsonString(event));
            AbstractRecordChangeLogBatchAddBaseParamDTO<AuditRiskAc> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO) source;
            this.handleBatchData(batchParam);
        }
    }

    private void handleBatchData(AbstractRecordChangeLogBatchAddBaseParamDTO<AuditRiskAc> param) {
        Collection<Long> foreignKeys = param.getForeignKeys();
        if (CollUtil.isNotEmpty(foreignKeys)) {
            String operatorId = null;
            Date opTime = null;
            String remark = null;
            List<LcAuditDataDTO<?>> list = new LinkedList<>();
            for (Long foreignKey : foreignKeys) {
                RecordChangeLogAddParamDTO<AuditRiskAc> data = new RecordChangeLogAddParamDTO<>();
                AuditRiskAc newValue = param.getNewValue(foreignKey);
                AuditRiskAc oldValue = param.getOldValue(foreignKey);

                if (StringUtils.isBlank(remark)) {
                    remark = param.getRemark(foreignKey);
                }
                if (StringUtils.isBlank(operatorId)) {
                    operatorId = param.getOperatorId(foreignKey);
                }
                if (Objects.isNull(opTime)) {
                    opTime = param.getOpTime(foreignKey);
                }

                data.setForeignKey(foreignKey);
                data.setNewValue(newValue);
                data.setOldValue(oldValue);
                data.setRemark(remark);
                data.setOperatorId(param.getOperatorId(foreignKey));
                data.setOpTime(param.getOpTime(foreignKey));

                list.add(toAuditData(data));
            }

            this.publishLcEvent(remark, operatorId, opTime, list);
        }
    }

    private void handleSingleData(RecordChangeLogAddParamDTO<AuditRiskAc> param) {
        String remark = param.getRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        this.publishLcEvent(remark, operatorId, opTime,
            Collections.singletonList(toAuditData(param)));
    }

    private LcAuditDataDTO<AuditRiskAc> toAuditData(RecordChangeLogAddParamDTO<AuditRiskAc> param) {
        LcAuditDataDTO<AuditRiskAc> result = new LcAuditDataDTO<>();

        AuditRiskAc oldValue = param.getOldValue();
        AuditRiskAc newValue = param.getNewValue();
        result.setId(param.getForeignKey());
        result.setModelCode(AuditRiskAcModelConstant.MODEL_CODE);
        result.setOldData(oldValue);
        result.setNewData(newValue);

        if (Objects.nonNull(oldValue) && Objects.isNull(newValue)) {
            // 旧数据存在,新数据不存在 为删除
            result.setOpType(LcDataAuditOpTypeConstant.DELETE);
        }
        if (Objects.isNull(oldValue) && Objects.nonNull(newValue)) {
            // 旧数据不存在,新数据存在 为新增
            result.setOpType(LcDataAuditOpTypeConstant.ADD);
        }
        if (Objects.nonNull(oldValue) && Objects.nonNull(newValue)) {
            // 新旧数据都存在为更新
            result.setOpType(LcDataAuditOpTypeConstant.UPDATE);
        }

        return result;
    }

    private void publishLcEvent(String remark, String operatorId, Date opTime,
        List<LcAuditDataDTO<?>> list) {
        DataAuditEventSourceDTO sourceEvent = new DataAuditEventSourceDTO();
        sourceEvent.setRemark(remark);
        sourceEvent.setOpDate(opTime);
        sourceEvent.setOpOrgId(operatorId);
        sourceEvent.setTraceSeq(UUID.fastUUID().toString(true));
        sourceEvent.setData(list);

        DataAuditEventDTO event = new DataAuditEventDTO(sourceEvent);
        LcEventUtil.publishEvent(event);
    }
}
