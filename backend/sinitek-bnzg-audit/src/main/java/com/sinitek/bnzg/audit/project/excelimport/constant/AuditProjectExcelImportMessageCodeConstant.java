package com.sinitek.bnzg.audit.project.excelimport.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectExcelImportMessageCodeConstant {

    /**
     * excel中serialno为[{0}]的项目编码为空
     */
    public static final String PROJECT_CODE_CANT_BE_EMPTY = "9902010019001";

    /**
     * excel中serialno为[{0}]的项目编码[{1}]前4位不为年份数字
     */
    public static final String PROJECT_CODE_NOT_YEAR = "9902010019002";

    /**
     * 9902010019003=excel中serialno为[{0}]的审计发现类型[{1}]无法匹配
     */
    public static final String FINDING_TYPE_EXCEL_DATA_NOT_MATCH = "9902010019003";

    /**
     * 9902010019004=excel中serialno为[{0}]的风险级别[{1}]无法匹配
     */
    public static final String RISK_LEVEL_EXCEL_DATA_NOT_MATCH = "9902010019004";

    /**
     * 9902010019005=excel中serialno为[{0}]的一级分类[{1}]无法匹配
     */
    public static final String FIRST_CATALOG_EXCEL_DATA_NOT_MATCH = "9902010019005";

    /**
     * 9902010019006=枚举[{0},{1}]枚举名称非唯一,数据处理异常
     */
    public static final String ENUM_NAME_NOT_UNIQUE = "9902010019006";

    /**
     * 待保存数据为空,无法导入
     */
    public static final String DATA_TO_SAVE_IS_EMPTY = "9902010019007";

    /**
     * 读取excel文件失败
     */
    public static final String READ_EXCEL_FILE_FAILED = "9902001019008";

    /**
     * 审计项目[{0}]找不到匹配的审计计划[{1}]
     */
    public static final String AUDIT_PLAN_NOT_FOUND = "9902001019009";

    /**
     * 导入数据时,默认项目启动人不能为空
     */
    public static final String DEFAULT_PROJECT_USER_IS_EMPTY = "9902001019010";

    /**
     * 导入数据时,默认项目所有人不能为空
     */
    public static final String DEFAULT_PROJECT_OWNER_IS_EMPTY = "9902001019011";

    /**
     * 导入数据时,默认项目成员不能为空
     */
    public static final String DEFAULT_PROJECT_MEMBER_IS_EMPTY = "9902001019012";

    /**
     * 导入数据时,默认审计程序库不能为空
     */
    public static final String DEFAULT_AUDIT_PROGRAM_LIBRARY_IS_EMPTY = "9902001019013";

    /**
     * 导入数据时,默认审计程序库下审计程序不能为空
     */
    public static final String DEFAULT_AUDIT_PROGRAM_IS_EMPTY = "9902001019014";

    /**
     * 9902001019015=导入数据时,项目[{0}]下审计程序[{1}]不存在对应项目审计程序数据
     */
    public static final String AUDIT_PROGRAM_NOT_FOUND = "9902001019015";

    /**
     * 导入数据时,无法获取项目[{0}]对应实例数据
     */
    public static final String AUDIT_PROJECT_INSTANCE_NOT_FOUND = "9902001019016";
}
