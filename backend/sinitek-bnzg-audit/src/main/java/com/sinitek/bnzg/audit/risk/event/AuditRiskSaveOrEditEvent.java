package com.sinitek.bnzg.audit.risk.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;

/**
 * <AUTHOR>
 * @date 08/29/2024 17:31
 */
public class AuditRiskSaveOrEditEvent<T extends AbstractRecordChangeLogAddParamBaseDTO> extends
    SiniCubeEvent<T> {

    public AuditRiskSaveOrEditEvent(T source) {
        super(source);
    }
}
