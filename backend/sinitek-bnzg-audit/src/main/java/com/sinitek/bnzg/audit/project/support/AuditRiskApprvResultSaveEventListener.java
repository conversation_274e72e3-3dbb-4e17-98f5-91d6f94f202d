package com.sinitek.bnzg.audit.project.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditRiskApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskProcessingParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditPpApproveTodoTaskService;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRiskApprvResultSaveEventListener {

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    @Autowired
    private IAuditPpApproveTodoTaskService ppApproveTodoTaskService;

    /**
     * 监听风险点审批
     *
     * 把待办任务置为处理中
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditRiskApprvResultChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        AuditRiskApprvResultChangeEvent<T> event) {
        // 无论是单条审批还是批量审批,对应的都是一个事项

        Collection<Long> ids;
        T source = event.getSource();
        String operatorId = null;
        Date opTime = null;
        if (source instanceof RecordChangeLogAddParamDTO) {
            log.info("监听到 风险点数据变动 事件,数据: {}",
                JsonUtil.toJsonString(event));
            RecordChangeLogAddParamDTO<AuditRisk> singleData = (RecordChangeLogAddParamDTO<AuditRisk>) source;
            Long foreignKey = singleData.getForeignKey();
            ids = Collections.singletonList(foreignKey);
            operatorId = singleData.getOperatorId();
            opTime = singleData.getOpTime();
        } else {
            log.info("监听到 风险点数据变动 批量事件,数据: {}",
                JsonUtil.toJsonString(event));
            AbstractRecordChangeLogBatchAddBaseParamDTO<AuditRisk> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO<AuditRisk>) source;
            ids = batchParam.getForeignKeys();

            if (CollUtil.isNotEmpty(ids)) {
                Long id = ids.iterator().next();
                operatorId = batchParam.getOperatorId(id);
                opTime = batchParam.getOpTime(id);
            }
        }

        List<RiskApproveResultBaseInfoDTO> approveResults = this.riskApproveResultService.findExistByIds(
            ids);
        if (CollUtil.isNotEmpty(approveResults)) {
            for (RiskApproveResultBaseInfoDTO baseInfo : approveResults) {
                this.ppApproveTodoTaskService.processing(
                    PpApproveTodoTaskProcessingParamDTO.builder()
                        .ppApproveId(baseInfo.getApproveId())
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .build());
            }
        } else {
            log.warn("根据ids {} 查不到对应的风险点审批结果,无法将对应待办事项置为处理中", ids);
        }

    }
}
