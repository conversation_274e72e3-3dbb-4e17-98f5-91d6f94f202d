package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "项目审计程序及风险点批量审批参数DTO")
public class PpAndRiskBatchApproveParamDTO {

    @ApiModelProperty("项目审计程序审批结果id")
    private List<Long> ppApproveResultIds;

    @ApiModelProperty("风险点审批结果id")
    private List<Long> riskApproveResultIds;

    @NotNull(message = "审批结果不能为空")
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
