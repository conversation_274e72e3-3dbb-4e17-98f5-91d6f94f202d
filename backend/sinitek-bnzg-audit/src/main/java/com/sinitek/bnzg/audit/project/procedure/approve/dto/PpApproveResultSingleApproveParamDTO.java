package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 项目审计程序审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "项目审计程序单个审批参数DTO")
public class PpApproveResultSingleApproveParamDTO extends PpOrRiskApproveResultApproveBaseParamDTO {

    @NotNull(message = "审批数据不能为空")
    @ApiModelProperty("审批数据id")
    private Long approveResultId;

}
