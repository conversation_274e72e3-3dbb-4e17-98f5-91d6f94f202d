package com.sinitek.bnzg.audit.risk.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskChangeOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDeleteOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSaveOrEditOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskExecutionService;
import com.sinitek.bnzg.audit.risk.support.AuditRiskSearchResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.lowcode.model.dto.LcIdAndIdListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目风险点 Controller
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@RestController
@RequestMapping("/frontend/api/audit/project/risk/execution")
@Api(value = "/frontend/api/audit/project/risk/execution", tags = "审计系统-项目风险点-审计实施")
public class AuditRiskExecutionController {

    @Autowired
    private IAuditRiskExecutionService riskExecutionService;

    @Autowired
    private AuditRiskSearchResultFormat<AuditRiskSearchResultDTO> format;

    @ApiOperation(value = "新增或编辑")
    @PostMapping("/save-or-edit")
    public RequestResult<Long> saveOrEdit(
        @RequestBody @Validated AuditRiskSaveOrEditOnExecutionParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        Long id = this.riskExecutionService.saveOrEdit(param);
        return new RequestResult<>(id);
    }

    @ApiOperation(value = "变更")
    @PostMapping("/change")
    public RequestResult<Long> change(
        @RequestBody @Validated AuditRiskChangeOnExecutionParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        Boolean copyAttachmentFlag = param.getCopyAttachmentFlag();
        if (Objects.nonNull(copyAttachmentFlag)) {
            param.setCopyAttachmentFlag(true);
        }
        Long id = this.riskExecutionService.change(param);
        return new RequestResult<>(id);
    }

    @ApiOperation(value = "取消变更")
    @PostMapping("/cancel-change")
    public RequestResult<Void> cancelChange(
        @RequestBody @Validated AuditRiskChangeOnExecutionParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.riskExecutionService.cancelChange(param);
        return RequestResult.success();
    }

    @ApiOperation("根据idList批量删除")
    @PostMapping("/delete")
    public RequestResult<Void> delete(@RequestBody LcIdAndIdListDTO param) {
        AuditRiskDeleteOnExecutionParamDTO deleteParam = new AuditRiskDeleteOnExecutionParamDTO();
        deleteParam.setOpTime(new Date());
        deleteParam.setOperatorId(CurrentUserFactory.getOrgId());
        deleteParam.setIds(param.mergeParam());
        this.riskExecutionService.deleteByIdList(deleteParam);
        return RequestResult.success();
    }

    @ApiOperation(value = "查询项目风险点分页列表")
    @PostMapping("/search")
    public TableResult<AuditRiskSearchResultDTO> search(@RequestBody AuditRiskSearchParamDTO dto) {
        IPage<AuditRiskSearchResultDTO> resultPage = this.riskExecutionService.search(dto);
        return dto.build(resultPage, format);
    }
}
