package com.sinitek.bnzg.audit.project.procedure.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureDetailDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.constant.AuditProjectRoleConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.constant.ProcedureAndRiskTypeConstant;
import com.sinitek.bnzg.audit.project.procedure.dao.AuditProjectProcedureDAO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpFullDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpRiskExecutionListResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpStatusUpdateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureExecutionListResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpExecutionSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure;
import com.sinitek.bnzg.audit.project.procedure.log.status.util.AuditPpStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureExecutionListResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.PpExecutionSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureExecutionService;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.procedure.util.AuditPpChangeEventUtil;
import com.sinitek.bnzg.audit.project.procedure.util.AuditPpConvertUtil;
import com.sinitek.bnzg.audit.project.procedure.util.AuditPpStatusUtil;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam4DTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-11-13 11:10
 */
@Slf4j
@Service
public class AuditProjectProcedureExecutionServiceImpl implements
    IAuditProjectProcedureExecutionService {

    @Autowired
    private IAuditProjectProcedureService ppService;

    @Autowired
    private AuditProjectProcedureDAO dao;

    @Autowired
    private AuditRiskDAO riskDAO;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Autowired
    private IAuditProcedureService auditProcedureService;

    @Autowired
    private IAuditProjectMemberService auditProjectMemberService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAfterApprove(AuditPpStatusUpdateParamDTO param) {
        List<Long> ppIds = param.getPpIds();
        Map<Long, Integer> idAndStatusMap = param.getIdAndStatusMap();
        Map<Long, String> idAndApproveRemarkMap = param.getIdAndApproveRemarkMap();
        Map<Long, String> idAndApproverIdMap = param.getIdAndApproverIdMap();
        Map<Long, Date> idAndApproveTimeMap = param.getIdAndApproveTimeMap();

        String operatorId = param.getOperatorId();

        if (CollUtil.isNotEmpty(ppIds)) {

            List<AuditProjectProcedure> pps = this.dao.listByIds(ppIds);

            List<AuditProjectProcedure> needUpdateRisk = new LinkedList<>();

            List<Long> needUpdateIds = new LinkedList<>();
            Map<Long, Integer> idAndOldStatusMap = new HashMap<>(pps.size());
            Map<Long, Integer> idAndNewStatusMap = new HashMap<>(pps.size());

            Map<Long, AuditProjectProcedure> idAndOldDataMap = new HashMap<>(pps.size());
            Map<Long, AuditProjectProcedure> idAndNewDataMap = new HashMap<>(pps.size());

            pps.forEach(item -> {
                Long id = item.getId();
                Integer oldStatus = item.getStatus();
                Integer newStatus = idAndStatusMap.get(id);

                boolean canChange = AuditPpStatusUtil.checkCanChangeStatus(oldStatus, newStatus);
                if (canChange) {
                    needUpdateIds.add(id);
                    idAndOldStatusMap.put(id, oldStatus);
                    idAndNewStatusMap.put(id, newStatus);

                    idAndOldDataMap.put(id, JsonUtil.jsonCopy(item, AuditProjectProcedure.class));

                    item.setStatus(newStatus);
                    // 审批反馈
                    item.setApproveRemark(idAndApproveRemarkMap.get(id));
                    // 审批人
                    item.setApproverId(idAndApproverIdMap.get(id));
                    // 审批时间
                    item.setApproveTime(idAndApproveTimeMap.get(id));

                    idAndNewDataMap.put(id, item);

                    needUpdateRisk.add(item);
                } else {
                    log.info("风险点[{}]当前状态[{}]无法更新成[{}]", id, oldStatus, newStatus);
                }
            });

            if (CollUtil.isNotEmpty(needUpdateRisk)) {
                this.dao.updateBatchById(needUpdateRisk);

                AuditPpStatusChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam4DTO.<Integer>builder()
                        .foreignKeys(needUpdateIds)
                        .oldValueMap(idAndOldStatusMap)
                        .newValueMap(idAndNewStatusMap)
                        .operatorIdMap(idAndApproverIdMap)
                        .opTimeMap(idAndApproveTimeMap)
                        .remark("审计实施-项目审计程序审批")
                        .build());

                AuditPpChangeEventUtil.publish(
                    RecordChangeLogBatchAddParam4DTO.<AuditProjectProcedure>builder()
                        .foreignKeys(needUpdateIds)
                        .oldValueMap(idAndOldDataMap)
                        .newValueMap(idAndNewDataMap)
                        .remark("审计实施-项目审计程序审批")
                        .operatorIdMap(idAndApproverIdMap)
                        .opTimeMap(idAndApproveTimeMap)
                        .build());
            }
        } else {
            log.warn("操作人: {} 更新项目审计程序状态时传入数据为空", operatorId);
        }
    }

    @Override
    public AuditCheckResultDTO checkCanFinish(Long projectId) {
        List<AuditRisk> risks = this.riskDAO.findThreadLatestByProjectId(projectId);
        AuditCheckResultDTO riskCheckResult = this.checkRiskStatus(projectId, risks);
        Boolean result = riskCheckResult.getResult();
        if (result) {
            List<AuditProjectProcedure> pps = this.dao.findExistsThreadLatestByProjectId(
                Collections.singleton(projectId));
            if (CollUtil.isNotEmpty(pps)) {
                // 未处理
                List<AuditProjectProcedure> notDealPps = new LinkedList<>();
                // 未审批通过的
                List<AuditProjectProcedure> notApprovedPps = new LinkedList<>();

                pps.forEach(pp -> {
                    Long ppId = pp.getId();
                    Integer status = pp.getStatus();
                    if (Objects.isNull(status)) {
                        log.warn("项目审计程序 {} 状态为null,未审批通过,校验不通过", ppId);
                        notDealPps.add(pp);
                    } else {
                        if (!Objects.equals(AuditRiskStatusConstant.APPROVED, status)) {
                            log.warn("项目审计程序 {} 未审批通过,校验不通过", ppId);
                            notApprovedPps.add(pp);
                        } else {
                            log.info("项目审计程序 {} 已审批通过,校验通过", ppId);
                        }
                    }
                });

                if (CollUtil.isNotEmpty(notDealPps)
                    || CollUtil.isNotEmpty(notApprovedPps)) {
                    if (CollUtil.isNotEmpty(notDealPps)) {
                        log.warn("项目 {} 存在审计程序 {} 未处理,无法完成审计实施", projectId,
                            notDealPps.stream().map(AuditProjectProcedure::getId)
                                .map(String::valueOf).collect(
                                    Collectors.joining(",")));
                        return AuditCheckResultDTO.builder().result(false)
                            .msg("存在审计程序未处理,无法完成审计实施")
                            .build();
                    } else {
                        log.warn("项目 {} 存在审计程序 {} 未审批通过,无法完成审计实施", projectId,
                            notApprovedPps.stream().map(AuditProjectProcedure::getId)
                                .map(String::valueOf).collect(
                                    Collectors.joining(",")));
                        return AuditCheckResultDTO.builder().result(false)
                            .msg("存在审计程序未审批通过,无法完成审计实施")
                            .build();
                    }
                } else {
                    log.warn("项目 {} 项目审计程序,允许完成整改跟踪校验通过", projectId);
                    return AuditCheckResultDTO.builder().result(true).build();
                }
            } else {
                // 不存在项目审计程序
                log.warn("项目 {} 不存在项目审计程序,允许完成整改跟踪校验失败", projectId);
                return AuditCheckResultDTO.builder().result(false).msg("请配置项目审计程序")
                    .build();
            }
        } else {
            return riskCheckResult;
        }
    }

    private AuditCheckResultDTO checkRiskStatus(Long projectId, List<AuditRisk> risks) {
        if (CollUtil.isNotEmpty(risks)) {
            List<AuditRisk> notMatchRiskList = risks.stream().filter(item -> {
                Integer status = item.getStatus();
                return Objects.equals(AuditRiskStatusConstant.DRAFT, status)
                    || Objects.equals(AuditRiskStatusConstant.APPROVING, status);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notMatchRiskList)) {
                String riskNames = notMatchRiskList.stream().map(AuditRisk::getName)
                    .collect(Collectors.joining(","));
                log.warn("项目[{}]下风险点[{}]未审批完成,无法完成审计实施", projectId,
                    notMatchRiskList.stream().map(AuditRisk::getId).map(String::valueOf)
                        .collect(Collectors.joining(",")));
                String msg = String.format("风险点[%s]未审批完成,无法完成审计实施", riskNames);
                return AuditCheckResultDTO.builder().result(false).msg(msg).build();
            } else {
                log.info("项目[{}]下风险点都已审批完成,允许完成审计实施", projectId);
                return AuditCheckResultDTO.builder().result(true).build();
            }
        } else {
            // 不存在风险点
            log.info("当前项目 {} 不存在风险点,风险点校验通过", projectId);
            return AuditCheckResultDTO.builder().result(true).build();
        }
    }

    @Override
    public IPage<AuditProjectProcedureExecutionListResultDTO> searchPpExecutionList(
        PpExecutionSearchParamDTO param) {
        String operatorId = param.getOperatorId();

        PpExecutionSearchParamPO paramPO = AuditPpConvertUtil.makeSearchParamDTO2PO(param);
        Long projectId = paramPO.getProjectId();

        IPage<AuditProjectProcedureExecutionListResultPO> result = this.dao.searchExecutionList(
            paramPO);

        List<AuditProjectProcedureExecutionListResultPO> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {

            List<AuditProjectMemberInfoDTO> memberRoles = this.auditProjectMemberService.findMemberRoles(
                Collections.singleton(projectId), Collections.singleton(operatorId));
            boolean isApprover;
            if (CollUtil.isNotEmpty(memberRoles)) {
                isApprover = memberRoles.stream().map(AuditProjectMemberInfoDTO::getRole)
                    .anyMatch(
                        item -> Objects.equals(item, AuditProjectRoleConstant.APPROVE)
                            || Objects.equals(item, AuditProjectRoleConstant.APPROVE_AND_EDIT));
            } else {
                isApprover = false;
            }

            List<Long> procedureIds = records.stream()
                .map(AuditProjectProcedureExecutionListResultPO::getProcedureId).collect(
                    Collectors.toList());

            List<AuditRiskBaseInfoDTO> risks = this.auditRiskService.findProjectRiskInfoByProjectIdAndProcedureIds(
                projectId, procedureIds);
            Map<Long, List<AuditRiskBaseInfoDTO>> procedureIdAndRiskMap;
            if (CollUtil.isNotEmpty(risks)) {
                procedureIdAndRiskMap = risks.stream()
                    .collect(Collectors.groupingBy(AuditRiskBaseInfoDTO::getProcedureId));
            } else {
                procedureIdAndRiskMap = Collections.emptyMap();
            }

            return result.convert(item -> {
                AuditProjectProcedureExecutionListResultDTO resultItem = AuditPpConvertUtil.makePO2DTO(
                    item);
                Long ppId = resultItem.getId();
                Long procedureId = resultItem.getProcedureId();
                Integer status = resultItem.getStatus();
                String auditorId = resultItem.getAuditorId();

                resultItem.setSupportApproveFlag(
                    Objects.equals(AuditRiskStatusConstant.APPROVING, status)
                        && !Objects.equals(operatorId, auditorId)
                        && isApprover);

                List<AuditRiskBaseInfoDTO> riskItems = procedureIdAndRiskMap.get(
                    procedureId);
                if (CollUtil.isNotEmpty(riskItems)) {
                    List<AuditPpRiskExecutionListResultDTO> riskResultList = riskItems.stream()
                        .map(riskItem -> {
                            AuditPpRiskExecutionListResultDTO riskOne = this.convert2Result(
                                riskItem, ppId);
                            Integer riskStatus = riskOne.getStatus();
                            String riskAuditorId = riskOne.getAuditorId();

                            riskOne.setSupportApproveFlag(
                                Objects.equals(AuditRiskStatusConstant.APPROVING, riskStatus)
                                    && !Objects.equals(operatorId, riskAuditorId)
                                    && isApprover);

                            return riskOne;
                        }).collect(
                            Collectors.toList());
                    resultItem.setChildren(riskResultList);
                }
                return resultItem;
            });
        }

        return result.convert(AuditPpConvertUtil::makePO2DTO);
    }

    @Override
    public AuditPpFullDetailDTO loadDetailById(Long id) {
        List<AuditProjectProcedureInfoDTO> pps = this.ppService.findByIds(
            Collections.singletonList(id));
        if (CollUtil.isNotEmpty(pps)) {
            AuditProjectProcedureInfoDTO ppInfo = pps.get(0);
            Long procedureId = ppInfo.getProcedureId();

            AuditPpFullDetailDTO result = LcConvertUtil.convert(ppInfo, AuditPpFullDetailDTO::new);
            AuditProcedureDetailDTO procedureDetail = this.auditProcedureService.getDetail(
                procedureId);
            result.setProcedureDetail(procedureDetail);
            return result;
        } else {
            log.warn("根据ppid {} 查不到对应的数据", id);
        }
        return null;
    }

    private AuditPpRiskExecutionListResultDTO convert2Result(
        AuditRiskBaseInfoDTO source, Long ppId) {
        AuditPpRiskExecutionListResultDTO result = new AuditPpRiskExecutionListResultDTO();
        result.setId(source.getId());
        result.setProjectId(source.getProjectId());
        result.setProcedureId(source.getProcedureId());
        result.setPpId(ppId);
        result.setName(source.getName());
        result.setStatus(source.getStatus());
        result.setAuditorId(source.getAuditorId());
        result.setAuditDate(source.getAuditDate());
        result.setDataType(ProcedureAndRiskTypeConstant.RISK_TYPE);
        result.setApproveRemark(source.getApproveRemark());
        return result;
    }

    private AuditProjectProcedureExecutionListResultDTO convert2Result(
        AuditProjectProcedureInfoDTO source) {
        AuditProjectProcedureExecutionListResultDTO result = new AuditProjectProcedureExecutionListResultDTO();
        result.setId(source.getId());
        result.setProjectId(source.getProjectId());
        result.setProcedureId(source.getProcedureId());
        result.setStatus(source.getStatus());
        result.setAuditorId(source.getAuditorId());
        result.setAuditDate(source.getAuditDate());
        result.setDataType(ProcedureAndRiskTypeConstant.PROCEDURE_TYPE);
        return result;
    }
}
