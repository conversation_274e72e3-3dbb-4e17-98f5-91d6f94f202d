package com.sinitek.bnzg.audit.project.procedure.util;

import com.sinitek.bnzg.audit.project.procedure.approve.entity.RiskApproveResult;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultSearchParamDTO;
import com.sinitek.bnzg.audit.risk.approve.po.RiskApproveResultSearchParamPO;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/30/2024 14:45
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RiskApproveResultConvertUtil {

    public static RiskApproveResultSearchParamPO makeSearchParamDTO2PO(
        RiskApproveResultSearchParamDTO dto) {
        RiskApproveResultSearchParamPO po = new RiskApproveResultSearchParamPO();

        po.setApproveId(dto.getApproveId());

        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());
        return po;
    }

    public static RiskApproveResultBaseInfoDTO makeEntity2BaseInfoDTO(RiskApproveResult entity) {
        return LcConvertUtil.convert(entity, RiskApproveResultBaseInfoDTO::new);
    }
}
