package com.sinitek.bnzg.audit.project.procedure.approve.log.result.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.event.AbstractRecordChangeLogEvent;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:12
 */
public class AuditPpApprvResultChangeEvent<T extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEvent<T> {

    public AuditPpApprvResultChangeEvent(T source) {
        super(source);
    }

}
