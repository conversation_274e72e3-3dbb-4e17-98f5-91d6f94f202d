package com.sinitek.bnzg.audit.project.procedure.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目审计程序首页类型
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditPpHomeTypeConstant {

    /**
     * 待处理
     */
    public static final int WAIT_HANDLE = 1;

    /**
     * 待审批
     */
    public static final int WAIT_APPROVE = 2;


    /**
     * 审批不通过数量
     */
    public static final int NOT_PASS = 3;

}
