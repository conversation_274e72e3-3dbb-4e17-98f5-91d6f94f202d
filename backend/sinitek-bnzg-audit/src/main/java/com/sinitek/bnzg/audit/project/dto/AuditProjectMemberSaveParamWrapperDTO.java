package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(description = "项目成员保存参数")
public class AuditProjectMemberSaveParamWrapperDTO implements IAuditProjectAndOpeatorId {

    @NotNull(message = "所属项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @Valid
    @NotEmpty(message = "项目成员不能为空")
    @ApiModelProperty("项目成员(集合)")
    private List<AuditProjectMemberSaveParamDTO> memberList;

    @ApiModelProperty(value = "检查权限(true:检查;false:不需要检查)")
    private Boolean checkAuthFlag;

    @ApiModelProperty("操作人(前端不需要传值)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;
}
