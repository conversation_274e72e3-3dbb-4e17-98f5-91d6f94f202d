package com.sinitek.bnzg.audit.risk.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartDetailParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartReasultPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditRiskStatisticsParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditRiskStatisticsReasultPO;
import com.sinitek.bnzg.audit.risk.statistics.po.RiskStatisticsChartParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.RiskStatisticsChartReasultPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date：2024/9/20 17:39
 */
public interface AuditRiskStatisticsMapper extends BaseMapper<AuditRisk> {

    IPage<AuditRiskStatisticsReasultPO> searchRiskStatistics(
            Page<AuditRiskStatisticsReasultPO> page,@Param("param") AuditRiskStatisticsParamPO param);

    List<Integer> findAuditYear();

    List<RiskStatisticsChartReasultPO> findRiskStatisticsChart(
            @Param("param") RiskStatisticsChartParamPO param);

    List<AuditFindRectifyChartReasultPO> findAuditFindRectifyChart(
            @Param("param") AuditFindRectifyChartParamPO param);

    IPage<AuditRiskStatisticsReasultPO> searchAuditFindRectifyChartDetail(
            Page<AuditRiskStatisticsReasultPO> page,@Param("param") AuditFindRectifyChartDetailParamPO param);
}
