<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.project.procedure.approve.mapper.AuditRiskApproveResultMapper">

  <select id="findPpRiskApproveResultListByApproveId" resultType="com.sinitek.bnzg.audit.project.procedure.approve.po.PpRiskApproveResultSearchResultPO">
       select 1 data_type,
              r.id,
              r.approve_id,
              r.project_id,
              r.procedure_id,
              r.risk_id,
              risk.name,
              risk.auditor_id,
              risk.audit_date,
              r.approve_result,
              r.operator_id,
              r.op_time,
              risk.type,
              risk.level,
              risk.first_catalog,
              risk.inner_category
         from audit_risk_approve_result r,
              audit_risk risk
        where r.risk_id  = risk.id
          and r.remove_flag = 0
          and r.approve_id = #{approveId}
        order by r.sort asc
  </select>

</mapper>
