package com.sinitek.bnzg.audit.project.listener;

import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.event.AuditProjectPhaseChangeEvent;
import com.sinitek.bnzg.audit.project.log.phase.util.AuditProjectPhaseValueChangeEventPublishUtil;
import com.sinitek.bnzg.audit.stage.dto.GenerateAuditStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.service.IAuditStageExampleService;
import com.sinitek.sirm.common.event.annotation.SiniCubeEventListener;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class AuditProjectPhaseChangeEventListenerInProject {

    @Autowired
    private IAuditStageExampleService auditStageExampleService;

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditProjectPhaseChangeEvent.class, fallbackExecution = true)
    public void listen(AuditProjectPhaseChangeEvent event) {
        AuditProjectPhaseChangeEventSourceDTO source = event.getSource();
        Long projectId = source.getProjectId();
        String opOrgId = source.getOperatorId();
        Date opTime = source.getOpTime();
        Integer oldProjectPhase = source.getOldProjectPhase();
        Integer newProjectPhase = source.getNewProjectPhase();
        String note = source.getRemark();

        log.info(
            "监听到审计项目[{}]阶段变动事件,操作人[{}],操作时间[{}],旧阶段[{}] => 新阶段[{}],备注[{}]",
            projectId, opOrgId, opTime, oldProjectPhase, newProjectPhase, note
        );

        AuditProjectPhaseValueChangeEventPublishUtil.publishEvent(
            RecordChangeLogAddParamDTO.builder()
                .foreignKey(projectId)
            .oldValue(oldProjectPhase)
            .newValue(newProjectPhase)
                .operatorId(opOrgId)
            .opTime(opTime)
                .remark(note)
            .build());
    }

    /**
     * 监听项目启动,自动创建审计阶段
     *
     * 这里采用同步事件监听，确保项目启动后,审计阶段创建成功
     */
    @SiniCubeEventListener
    public void listenOnProjectReady(AuditProjectPhaseChangeEvent event) {
        AuditProjectPhaseChangeEventSourceDTO source = event.getSource();
        Long projectId = source.getProjectId();
        String opOrgId = source.getOperatorId();
        Date opTime = source.getOpTime();
        Integer newProjectPhase = source.getNewProjectPhase();
        Integer oldProjectPhase = source.getOldProjectPhase();
        if (Objects.equals(AuditProjectPhaseConstant.READY, newProjectPhase)
            && Objects.equals(AuditProjectPhaseConstant.INIT, oldProjectPhase)) {
            log.info("监听到审计项目[{}] 阶段变为 审计准备, 生成审计阶段", projectId);

            auditStageExampleService.generateAuditStageExample(
                GenerateAuditStageExampleParamDTO.builder()
                    .projectId(projectId)
                    .opOrgId(opOrgId)
                    .opTime(opTime)
                    .publishEventFlag(true)
                    .build());
        }
    }

}
