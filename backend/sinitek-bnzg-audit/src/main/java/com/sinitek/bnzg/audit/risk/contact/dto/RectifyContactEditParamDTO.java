package com.sinitek.bnzg.audit.risk.contact.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/29/2024 17:16
 */
@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("整改联系人编辑参数")
public class RectifyContactEditParamDTO {

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("整改联系人")
    private List<String> contacts;

    @ApiModelProperty(value = "责任人")
    private List<String> respManId;

    @ApiModelProperty(value = "责任部门")
    private List<String> respDeptId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
