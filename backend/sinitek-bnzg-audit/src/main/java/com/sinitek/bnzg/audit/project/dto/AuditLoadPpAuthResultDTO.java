package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("获取授权用户参数")
public class AuditLoadPpAuthResultDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("程序id")
    private Long procedureId;

    @ApiModelProperty("查看")
    private List<String> viewers;

    @ApiModelProperty("审阅")
    private List<String> approvers;

    @ApiModelProperty("编辑")
    private List<String> editors;
}
