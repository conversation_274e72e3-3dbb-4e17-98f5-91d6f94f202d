package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/20/2024 13:59
 */
@Getter
@NoArgsConstructor
public class AuditProjectProcedureDTO extends LcBaseModel {

    /*
     * 乐观锁
     */
    private Integer version;

    /*
     * 新增时间
     */
    private Date createtimestamp;

    /*
     * 更新时间
     */
    private Date updatetimestamp;

    private Long projectId;

    private Long procedureId;

    /*
     * 删除标志
     */
    private Integer removeFlag;

    /*
     * 创建人
     */
    private String creatorId;

    /*
     * 更新人
     */
    private String updaterId;

    /*
     * 删除人
     */
    private String removerId;

    public void setVersion(Integer version) {
        this.version = version;
        meta.put("version", version);
    }

    public void setCreatetimestamp(Date createtimestamp) {
        this.createtimestamp = createtimestamp;
        meta.put("createtimestamp", createtimestamp);
    }

    public void setUpdatetimestamp(Date updatetimestamp) {
        this.updatetimestamp = updatetimestamp;
        meta.put("updatetimestamp", updatetimestamp);
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
        meta.put("projectId", projectId);
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
        meta.put("procedureId", procedureId);
    }

    public void setRemoveFlag(Integer removeFlag) {
        this.removeFlag = removeFlag;
        meta.put("removeFlag", removeFlag);
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
        meta.put("creatorId", creatorId);
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
        meta.put("updaterId", updaterId);
    }

    public void setRemoverId(String removerId) {
        this.removerId = removerId;
        meta.put("removerId", removerId);
    }

    @Override
    public String getModelCode() {
        return "AUDIT_PROJECT_PROCEDURE";
    }
}

