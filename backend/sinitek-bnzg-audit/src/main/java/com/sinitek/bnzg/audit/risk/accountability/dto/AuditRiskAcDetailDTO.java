package com.sinitek.bnzg.audit.risk.accountability.dto;

import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date：2024/11/15 10:32
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目风险点问责情况DTO")
public class AuditRiskAcDetailDTO extends AuditRiskAc {

}

