package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 风险点审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "风险点批量审批参数DTO")
public class RiskApproveResultBatchApproveParamDTO extends
    PpOrRiskApproveResultApproveBaseParamDTO {

    @NotNull(message = "审批数据不能为空")
    @ApiModelProperty("审批数据id")
    private List<Long> approveResultIds;

}
