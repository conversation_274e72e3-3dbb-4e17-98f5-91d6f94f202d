package com.sinitek.bnzg.audit.risk.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 风险点查询统计-参数DTO
 *
 * <AUTHOR>
 * date 2024-09-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "风险点查询统计-参数DTO")
public class AuditRiskStatisticsParamDTO extends PageDataParam {

    @ApiModelProperty(value = "项目Ids")
    private List<Long> projectIds;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "风险点名称")
    private String riskName;

    @ApiModelProperty(value = "风险类型")
    private List<Integer> riskType;

    @ApiModelProperty(value = "风险等级")
    private List<Integer> riskLevel;

    @ApiModelProperty(value = "制度方向")
    private List<Integer> firstCatalog;

    @ApiModelProperty(value = "内部分类")
    private List<String> innerCategory;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计开始日期")
    private Date auditStartDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计结束日期")
    private Date auditEndDate;

    @ApiModelProperty(value = "审计年份")
    private List<Integer> auditYears;

    @ApiModelProperty(value = "整改状态")
    private List<Integer> rectifyStates;

    @ApiModelProperty(value = "责任部门建议")
    private List<Integer> respDeptSugTypes;

    @ApiModelProperty(value = "责任部门")
    private List<String> respDeptIds;

    @ApiModelProperty(value = "业务部门")
    private List<String> businessUnits;

    @ApiModelProperty(value = "是否重复发生")
    private Integer repeatFlag;
}
