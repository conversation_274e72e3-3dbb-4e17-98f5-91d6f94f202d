package com.sinitek.bnzg.audit.risk.approve.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批 Entity
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "风险点审批")
public class RiskApproveBaseInfoDTO {

    private Long id;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人
     */
    @ApiModelProperty("删除人")
    private String removerId;

}
