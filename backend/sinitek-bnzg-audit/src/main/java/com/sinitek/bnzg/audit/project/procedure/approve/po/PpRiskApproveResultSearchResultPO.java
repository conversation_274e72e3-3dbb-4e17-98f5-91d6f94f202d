package com.sinitek.bnzg.audit.project.procedure.approve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 项目审计程序审批-返回DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "项目审计程序审批-查询结果PO")
public class PpRiskApproveResultSearchResultPO {

    @ApiModelProperty(value = "数据类型")
    private Integer dataType;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("项目审计程序审批id")
    private Long approveId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "程序id")
    private Long procedureId;

    @ApiModelProperty(value = "风险点id")
    private Long riskId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批结果名")
    private String approveResultName;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    @ApiModelProperty("审批人名称")
    private String operatorName;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date opTime;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "类型名")
    private String typeName;

    @ApiModelProperty(value = "风险等级")
    private Integer level;

    @ApiModelProperty(value = "风险等级名")
    private String levelName;

    @ApiModelProperty(value = "一级分类")
    private Integer firstCatalog;

    @ApiModelProperty(value = "一级分类名")
    private String firstCatalogName;

    @ApiModelProperty(value = "二级分类")
    private String innerCategory;

}
