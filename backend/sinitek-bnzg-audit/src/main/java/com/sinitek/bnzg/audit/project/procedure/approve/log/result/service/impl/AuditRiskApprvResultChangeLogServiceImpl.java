package com.sinitek.bnzg.audit.project.procedure.approve.log.result.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.dao.AuditRiskApprvResultLogDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.entity.AuditRiskApprvResultLog;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.service.IAuditRiskApprvResultChangeLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class AuditRiskApprvResultChangeLogServiceImpl extends
    AbstractReecordChangeLogService<AuditRiskApprvResultLog, Integer> implements
    IAuditRiskApprvResultChangeLogService {

    @Autowired
    private AuditRiskApprvResultLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<AuditRiskApprvResultLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected AuditRiskApprvResultLog generateNewOne() {
        return new AuditRiskApprvResultLog();
    }

    @Override
    protected void handlerForeignKey(AuditRiskApprvResultLog entity, Long foreignKey) {
        entity.setApprvResultId(foreignKey);
    }
}
