package com.sinitek.bnzg.audit.risk.approve.properties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 项目Portal全局配置
 *
 * <AUTHOR>
 * @date 8/25/2023 4:58 PM
 */
@Data
@Slf4j
@Component
@ConfigurationProperties("audit.risk.approve")
public class AuditRiskApproveGlobalProperties {

    /**
     * 风险点审批处理地址
     */
    private String approveProcessUrl = "/lowcode/form/form-render/audit-risk-approve?id=%s&title=%s";

}
