package com.sinitek.bnzg.audit.stage.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.stage.entity.StageExample;
import com.sinitek.bnzg.audit.stage.mapper.StageExampleMapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/15/2024 13:04
 */
@Component
public class StageExampleDAO extends ServiceImpl<StageExampleMapper, StageExample> {

    public StageExample getExampleByProjectId(Long projectId) {
        LambdaQueryWrapper<StageExample> queryWrapper = Wrappers.lambdaQuery(
            StageExample.class);
        queryWrapper.eq(StageExample::getProjectId, projectId);
        return this.getOne(queryWrapper);
    }

}
