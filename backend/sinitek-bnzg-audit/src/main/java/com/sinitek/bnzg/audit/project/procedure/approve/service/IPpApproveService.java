package com.sinitek.bnzg.audit.project.procedure.approve.service;

import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpAndRiskBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveSubmitParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveTerminateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmBatchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmSingleParamDTO;
import java.util.List;

/**
 * 风险点审批 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-30
 */
public interface IPpApproveService {

    void create(PpApproveCreateParamDTO param);

    void submit(PpApproveSubmitParamDTO param);

    void terminate(PpApproveTerminateParamDTO param);

    void batchApprove(PpAndRiskBatchApproveParamDTO param);

    List<PpConfirmResultDTO> findPpConfirmResult(PpConfirmBatchParamDTO param);

    PpConfirmResultDTO loadPpConfirmResult(PpConfirmSingleParamDTO param);

    PpApproveBaseInfoDTO getBaseInfo(Long id);

    PpApproveDetailDTO loadDetail(Long id);
}
