package com.sinitek.bnzg.audit.project.service.impl;

import static com.sinitek.bnzg.audit.doc.type.constant.DocTypeTemplateConstant.DEFAULT_TEMPLATE_ATTACHMENT_SOUCE_NAME;
import static com.sinitek.bnzg.audit.doc.type.constant.DocTypeTemplateConstant.DEFAULT_TEMPLATE_ATTACHMENT_TYPE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectConstant.AUDIT_GROUP_MEMBERS;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectConstant.AUDIT_REPORT_NAME;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectConstant.RISKS;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.AUDIT_DOC_TYPE_TEMPLATE_EXISTS;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.AUDIT_REPORT_GENERATE_FAIL;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.AUDIT_REPORT_TEMPLATE_NOT_EXISTS;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CNAT_RESTART_BECAUSEOF_CURRENT_PHASE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CNAT_START_BECAUSEOF_CURRENT_PHASE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CURRENT_PROJECT_NOT_EXISTS;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_STOP_BECAUSEOF_NOT_PROJECT_LEADER;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_STOP_BECAUSEOF_NO_PROJECT_LEADER;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_STOP_BECAUSEOF_PROGRESS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.sinitek.bnzg.audit.doc.dto.DocRenderParamDTO;
import com.sinitek.bnzg.audit.doc.service.IDocumentService;
import com.sinitek.bnzg.audit.doc.util.WordUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectEnumConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectModelConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectRoleConstant;
import com.sinitek.bnzg.audit.project.dao.AuditProjectDAO;
import com.sinitek.bnzg.audit.project.dao.AuditProjectMemberDAO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectCopyResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectDetailDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberAppendParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberSaveParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectQueryParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditReStartProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditStartProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditStopProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditWordFileParamDTO;
import com.sinitek.bnzg.audit.project.dto.UpdateProjectPhaseParamDTO;
import com.sinitek.bnzg.audit.project.entity.AuditProject;
import com.sinitek.bnzg.audit.project.entity.AuditProjectMember;
import com.sinitek.bnzg.audit.project.enumation.AuditProjectPhaseEnum;
import com.sinitek.bnzg.audit.project.log.phase.service.IAuditProjectPhaseLogService;
import com.sinitek.bnzg.audit.project.po.AuditProjectQueryParamPO;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchParamPO;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.util.AuditProjectChangeEventUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectConvertUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectPhaseChangeEventUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskRespDeptDAO;
import com.sinitek.bnzg.audit.risk.dto.AuditReportTemplateSourceDataDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.enumation.AuditRiskFirstCatalogEnum;
import com.sinitek.bnzg.audit.stage.dto.ReStartAuditStageParamDTO;
import com.sinitek.bnzg.audit.stage.service.IAuditStageExampleService;
import com.sinitek.bnzg.common.constant.AuditModelAuditOpTypeConstant;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.FileUtil;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.utils.UploadCommonUtils;
import com.sinitek.sirm.lowcode.audit.dto.DataAuditEventSourceDTO;
import com.sinitek.sirm.lowcode.audit.dto.LcAuditDataDTO;
import com.sinitek.sirm.lowcode.audit.event.DataAuditEventDTO;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.io.File;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:45
 */
@Slf4j
@Service
public class AuditProjectServiceImpl implements IAuditProjectService {

    @Autowired
    private AuditProjectDAO dao;

    @Autowired
    private IAuditProjectMemberService projectMemberService;

    @Autowired
    private IAuditProjectProcedureService projectProcedureService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private AuditRiskDAO riskDAO;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private AuditProjectMemberDAO auditProjectMemberDAO;

    @Autowired
    private UploadCommonUtils uploadCommonUtils;

    @Autowired
    private AuditRiskRespDeptDAO deptDAO;

    @Autowired
    private IAuditStageExampleService auditStageExampleService;

    @Autowired
    private IAuditProjectPhaseLogService auditProjectPhaseLogService;

    @Autowired
    private IDocumentService documentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reStart(AuditReStartProjectParamDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditProject project = this.dao.getById(projectId);
        if (Objects.nonNull(project)) {
            AuditProject oldData = JsonUtil.jsonCopy(project, AuditProject.class);
            Integer currentProjectPhase = project.getProjectPhase();
            if (!Objects.equals(AuditProjectPhaseConstant.INIT, currentProjectPhase)
                && !Objects.equals(AuditProjectPhaseConstant.READY, currentProjectPhase)
                && !Objects.equals(AuditProjectPhaseConstant.STOP, currentProjectPhase)) {
                // 设置项目进度
                this.reSetProjectPhase(param, project);
                // 更新全景图阶段
                this.auditStageExampleService.reStartAuditStage(ReStartAuditStageParamDTO.builder()
                    .projectId(projectId)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .build());

                AuditProjectChangeEventUtil.publish(
                    RecordChangeLogAddParamDTO.builder()
                        .foreignKey(projectId)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .newValue(project)
                        .oldValue(oldData)
                        .remark("审计项目-重开项目")
                        .build());

            } else {
                AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(
                    currentProjectPhase);
                log.error("重开项目时,当前项目[{}]进度[{},{}]无法重开", projectId,
                    currentProjectPhase,
                    phaseEnum);
                throw new BussinessException(CNAT_RESTART_BECAUSEOF_CURRENT_PHASE,
                    phaseEnum.getName());
            }
        } else {
            log.error("重开项目时,当前项目[{}]不存在", projectId);
            throw new BussinessException(CURRENT_PROJECT_NOT_EXISTS);
        }
    }

    private void reSetProjectPhase(AuditReStartProjectParamDTO param, AuditProject project) {
        Long projectId = project.getId();
        Date opTime = param.getOpTime();
        String operatorId = param.getOperatorId();

        Integer oldProjectPhase = project.getProjectPhase();

        int newProjectPhase = AuditProjectPhaseConstant.READY;
        // 仅更新项目阶段
        project.setProjectPhase(newProjectPhase);
        // 不保存启动日期，开始日期
        this.dao.updateById(project);
        log.info("审计项目[{}]重开,项目阶段由 {} -> {}", projectId, oldProjectPhase,
            newProjectPhase);

        // 发布项目阶段变动事件
        AuditProjectPhaseChangeEventUtil.publishEvent(
            AuditProjectPhaseChangeEventSourceDTO.builder()
                .projectId(projectId)
                .operatorId(operatorId)
                .opTime(opTime)
                .oldProjectPhase(oldProjectPhase)
                .newProjectPhase(newProjectPhase)
                .remark("项目重开")
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void start(AuditStartProjectParamDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();

        AuditProject project = this.dao.getById(projectId);
        if (Objects.nonNull(project)) {
            AuditProject oldData = JsonUtil.jsonCopy(project, AuditProject.class);
            Integer oldProjectPhase = project.getProjectPhase();
            if (Objects.equals(AuditProjectPhaseConstant.INIT, oldProjectPhase)) {
                // 保存项目所有人
                List<AuditProjectMemberSaveParamDTO> memberList = param.getMemberList();
                if (CollUtil.isNotEmpty(memberList)) {
                    AuditProjectMemberSaveParamWrapperDTO memberSaveParam = new AuditProjectMemberSaveParamWrapperDTO();
                    memberSaveParam.setCheckAuthFlag(false);
                    memberSaveParam.setProjectId(projectId);
                    memberSaveParam.setMemberList(memberList);
                    memberSaveParam.setOperatorId(param.getOperatorId());
                    memberSaveParam.setOpTime(param.getOpTime());
                    this.projectMemberService.saveMembersWithoutOwners(memberSaveParam);
                } else {
                    log.info("当前项目[{}]启动时,项目成员为空,无需保存额外的项目成员", projectId);
                }

                // 项目负责人
                List<String> orgIds = param.getOrgIds();
                this.projectMemberService.saveProjectOwners(orgIds, projectId);

                // 保存项目成员
                // 项目负责人自动拥有编辑审批角色
                List<AuditProjectMemberSaveParamDTO> list = orgIds.stream()
                    .map(orgId -> AuditProjectMemberSaveParamDTO.builder().orgId(orgId).role(
                        AuditProjectRoleConstant.APPROVE_AND_EDIT).build())
                    .collect(Collectors.toList());
                this.projectMemberService.appendMembersWithoutOwners(
                    AuditProjectMemberAppendParamWrapperDTO.builder()
                        .operatorId(operatorId)
                        .appendMemberList(list)
                        .projectId(projectId)
                        .build());

                int newProjectPhase = AuditProjectPhaseConstant.READY;
                // 更新项目阶段
                project.setProjectPhase(newProjectPhase);
                // 保存启动日期,需判断是否存在手动录入的值，如果有则不覆盖，以手动录入的时间或日期为准
                if (Objects.isNull(project.getStartupDate())) {
                    project.setStartupDate(new Date());
                    log.info("当前项目[{}]启动时间[{}]采用系统时间", projectId, new Date());
                } else {
                    log.info("当前项目[{}]启动时间[{}]采用手动录入时间", projectId,
                        project.getStartupDate());
                }
                // 开始日期
                if (Objects.isNull(project.getStartDate())) {
                    project.setStartDate(new Date());
                    log.info("当前项目[{}]开始时间[{}]采用系统时间", projectId, new Date());
                } else {
                    log.info("当前项目[{}]开始时间[{}]采用手动录入时间", projectId,
                        project.getStartDate());
                }

                this.dao.updateById(project);

                // 发布项目阶段变动事件
                AuditProjectPhaseChangeEventUtil.publishEvent(
                    AuditProjectPhaseChangeEventSourceDTO.builder()
                        .projectId(projectId)
                        .operatorId(operatorId)
                        .opTime(project.getStartupDate())
                        .oldProjectPhase(oldProjectPhase)
                        .newProjectPhase(newProjectPhase)
                        .remark("项目启动")
                        .build());

                AuditProjectChangeEventUtil.publish(
                    RecordChangeLogAddParamDTO.builder()
                        .foreignKey(projectId)
                        .newValue(project)
                        .oldValue(oldData)
                        .operatorId(operatorId)
                        .opTime(project.getStartupDate())
                        .remark("审计项目-启动项目")
                        .build());
            } else {
                AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(oldProjectPhase);
                log.error("启动项目时,当前项目[{}]进度[{},{}]无法启动", projectId, oldProjectPhase,
                    phaseEnum);
                throw new BussinessException(CNAT_START_BECAUSEOF_CURRENT_PHASE,
                    phaseEnum.getName());
            }
        } else {
            log.error("启动项目时,当前项目[{}]不存在", projectId);
            throw new BussinessException(CURRENT_PROJECT_NOT_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stop(AuditStopProjectParamDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();

        AuditProject project = this.dao.getById(projectId);
        if (Objects.nonNull(project)) {
            AuditProject oldData = JsonUtil.jsonCopy(project, AuditProject.class);
            Integer oldProjectPhase = project.getProjectPhase();
            // 项目进度非已关闭状态才能终止
            if (!Objects.equals(AuditProjectPhaseConstant.CLOSE, oldProjectPhase)) {
                if (Objects.equals(AuditProjectPhaseConstant.STOP, oldProjectPhase)) {
                    log.warn("操作人[{}]终止项目[{}]时,当前项目已终止,无需重复终止", operatorId,
                        projectId);
                    return;
                }

                // 检查是否为项目负责人
                AuditProjectCheckUtil.checkProjectOwner(param, "中止项目",
                    CANT_STOP_BECAUSEOF_NOT_PROJECT_LEADER, CANT_STOP_BECAUSEOF_NO_PROJECT_LEADER);

                Integer newProjectPhase = AuditProjectPhaseConstant.STOP;

                // 更新项目阶段
                project.setProjectPhase(newProjectPhase);
                this.dao.updateById(project);

                // 发布项目阶段变动事件
                AuditProjectPhaseChangeEventUtil.publishEvent(
                    AuditProjectPhaseChangeEventSourceDTO.builder()
                        .projectId(projectId)
                        .operatorId(operatorId)
                        .opTime(project.getStartupDate())
                        .oldProjectPhase(oldProjectPhase)
                        .newProjectPhase(newProjectPhase)
                        .remark("项目中止")
                        .build());

                AuditProjectChangeEventUtil.publish(
                    RecordChangeLogAddParamDTO.builder()
                        .foreignKey(projectId)
                        .newValue(project)
                        .oldValue(oldData)
                        .operatorId(operatorId)
                        .opTime(project.getStartupDate())
                        .remark("审计项目-中止项目")
                        .build());
            } else {
                AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(oldProjectPhase);
                log.error("终止项目时,当前项目[{}]进度[{},{}]无法终止", projectId,
                    oldProjectPhase,
                    phaseEnum);
                throw new BussinessException(CANT_STOP_BECAUSEOF_PROGRESS,
                    phaseEnum.getName());
            }
        } else {
            log.error("终止项目时,当前项目[{}]不存在", projectId);
            throw new BussinessException(CURRENT_PROJECT_NOT_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyCascadeByPlanId(Long oldPlanId, Long newPlanId, String opOrgId) {
        List<AuditProject> list = this.dao.findExistsProjectByPlanIds(
            Collections.singleton(oldPlanId));

        if (CollUtil.isNotEmpty(list)) {
            Map<String, Long> nameAndOldProjectIdMap = new HashMap<>();
            Map<String, AuditProject> nameAndNewProjectMap = new HashMap<>();

            List<AuditProject> needSaveList = list.stream().map(item -> {
                String name = item.getName();

                nameAndOldProjectIdMap.put(name, item.getId());
                AuditProject newOne = new AuditProject();

                newOne.setName(name);
                newOne.setPlanId(newPlanId);
                // 审计期时间置为空
                newOne.setPeriodEndDate(null);
                newOne.setPeriodStartDate(null);
                newOne.setRegulatoryFlag(item.getRegulatoryFlag());
                newOne.setRemark(null);
                newOne.setProjectType(item.getProjectType());
                // 开始日期结束日期置为空
                newOne.setStartDate(null);
                newOne.setEndDate(null);
                // 现场审计日期置为空
                newOne.setLocalAuditEndDate(null);
                newOne.setLocalAuditStartDate(null);
                // 征求意见日期置为空
                newOne.setConsultationEndDate(null);
                newOne.setConsultationStartDate(null);
                // 审计报告完成日期置为空
                newOne.setReportCompleteDate(null);
                // 启动日期置为空
                newOne.setStartupDate(null);
                // 上报时间置为空
                newOne.setRegulatoryTime(null);

                // 项目进度置为待启动
                newOne.setProjectPhase(AuditProjectPhaseConstant.INIT);

                nameAndNewProjectMap.put(name, newOne);
                return newOne;
            }).collect(Collectors.toList());

            log.info("复制审计计划 {} -> {} 时,复制项目数据共[{}]条", oldPlanId, newPlanId,
                needSaveList.size());

            this.dao.saveBatch(needSaveList);

            if (CollectionUtils.isNotEmpty(needSaveList)) {
                // 手动数据审计
                this.publishAuditDataEvent(needSaveList);
            }
            Map<Long, Long> projectIdMap = new HashMap<>();
            nameAndNewProjectMap.forEach((name, newProject) -> {
                Long oldId = nameAndOldProjectIdMap.get(name);
                Long newId = newProject.getId();
                projectIdMap.put(oldId, newId);
            });
            AuditProjectCopyResultDTO copyResult = new AuditProjectCopyResultDTO();
            copyResult.setOperatorId(opOrgId);
            copyResult.setProjectIdMap(projectIdMap);
            copyResult.setOpTime(new Date());

            // 复制项目成员
            this.projectMemberService.copyCascadeByProject(copyResult);
            // 项目审计程序无需复制
        } else {
            log.warn("根据审计计划id[{}]复制审计项目到新计划[{}]时,该计划下不存在审计项目",
                oldPlanId, newPlanId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectPhase(UpdateProjectPhaseParamDTO param) {
        Long projectId = param.getProjectId();
        Integer newProjectPhase = param.getNewProjectPhase();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        String remark = param.getRemark();
        AuditProject project = this.dao.getById(projectId);
        if (Objects.nonNull(project)) {
            Integer currentProjectPhase = project.getProjectPhase();
            AuditProject oldData = JsonUtil.jsonCopy(project, AuditProject.class);

            if (Objects.equals(currentProjectPhase, newProjectPhase)) {
                log.info(
                    "更新项目[{}]阶段为[{}]时,操作人:{},操作时间:{},备注:{},当前项目阶段已为该阶段,无需更新",
                    projectId, newProjectPhase,
                    operatorId, opTime, remark);
            } else {
                project.setProjectPhase(newProjectPhase);
                if (currentProjectPhase.equals(AuditProjectPhaseEnum.REPORT.getValue())
                    && newProjectPhase.equals(AuditProjectPhaseEnum.TRACKING.getValue())) {
                    log.info("当前项目[{}]当前阶段是[{}],下一个阶段是[{}],更新项目结束时间[{}]",
                        projectId, currentProjectPhase, newProjectPhase, opTime);
                    if (Objects.isNull(project.getEndDate())) {
                        project.setEndDate(opTime);
                    }
                }
                this.dao.updateById(project);

                // 发布项目阶段变动事件
                AuditProjectPhaseChangeEventUtil.publishEvent(
                    AuditProjectPhaseChangeEventSourceDTO.builder()
                        .projectId(projectId)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .oldProjectPhase(currentProjectPhase)
                        .newProjectPhase(newProjectPhase)
                        .remark(remark)
                        .build());

                // 发布项目变动事件
                AuditProjectChangeEventUtil.publish(
                    RecordChangeLogAddParamDTO.builder()
                        .foreignKey(projectId)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .newValue(project)
                        .oldValue(oldData)
                        .remark("审计项目-项目阶段完成更新产品阶段")
                        .build());
            }
        } else {
            log.warn(
                "更新项目[{}]阶段为[{}]时,操作人:{},操作时间:{},备注:{},根据项目id无法获取项目数据",
                projectId, newProjectPhase,
                operatorId, opTime, remark);
        }
    }

    @Override
    public AuditProjectDetailDTO loadExistsProjectInfoById(Long id) {
        AuditProject project = this.dao.getById(id);
        AuditProjectDetailDTO result = AuditProjectUtil.makeEntity2DetailDTO(
            project);
        if (Objects.nonNull(result)) {
            List<AuditProjectMemberInfoDTO> owners = this.projectMemberService.findOwnerByProjectIds(
                Collections.singleton(id));
            if (CollUtil.isNotEmpty(owners)) {
                List<String> ownerOrgIds = owners.stream().map(AuditProjectMemberInfoDTO::getOrgId)
                    .collect(Collectors.toList());
                result.setOwnerIds(ownerOrgIds);

                List<OrgObjectDTO> orgObjects = this.orgService.findOrgObjectsByOrgIds(
                    ownerOrgIds);
                if (CollUtil.isNotEmpty(orgObjects)) {
                    String names = orgObjects.stream().map(OrgObjectDTO::getOrgName)
                        .collect(Collectors.joining(","));
                    result.setOwnerNames(names);
                } else {
                    log.warn("项目[{}]成员人员orgId[{}]对应人员信息为空", id, ownerOrgIds);
                }
            }
            //项目类型
            Map<String, String> projectTypeEnum = this.enumService.getSirmEnumByCataLogAndType(
                AuditProjectEnumConstant.DEFAULT_CATALOG, AuditProjectEnumConstant.PROJECT_TYPE);
            result.setProjectTypeName(
                MapUtils.getString(projectTypeEnum, String.valueOf(result.getProjectType())));
            // 是否报送
            Integer regulatoryFlag = result.getRegulatoryFlag();
            result.setRegulatoryFlagName(
                CommonBooleanEnum.isTrue(regulatoryFlag) ? "是" : "否");
            // 项目进度
            result.setProjectPhaseName(
                AuditProjectPhaseEnum.getByValue(result.getProjectPhase()).getName());
        }
        return result;
    }

    @Override
    public AuditProjectInfoDTO getExistsProjectInfoById(Long id) {
        AuditProject project = this.dao.getById(id);
        return AuditProjectUtil.makeEntity2InfoDTO(project);
    }

    @Override
    public List<AuditProjectInfoDTO> findExistsProjctByPlanId(Long planId) {
        List<AuditProject> projects = this.dao.findExistsProjectByPlanIds(
            Collections.singleton(planId));
        if (CollUtil.isNotEmpty(projects)) {
            return projects.stream().map(AuditProjectUtil::makeEntity2InfoDTO)
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditProjectInfoDTO> findExistsProjctByPlanIds(Collection<Long> planIds) {
        if (CollUtil.isNotEmpty(planIds)) {
            List<AuditProject> projects = this.dao.findExistsProjectByPlanIds(
                planIds);
            if (CollUtil.isNotEmpty(projects)) {
                return projects.stream().map(AuditProjectUtil::makeEntity2InfoDTO)
                    .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditProjectInfoDTO> findExistsProjctByIds(Collection<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            List<AuditProject> projects = this.dao.listByIds(ids);
            if (CollUtil.isNotEmpty(projects)) {
                return projects.stream().map(AuditProjectUtil::makeEntity2InfoDTO)
                    .collect(Collectors.toList());
            }
        }

        return Collections.emptyList();
    }

    @Override
    public UploadFileDTO generateWordFile(AuditWordFileParamDTO dto) throws Exception {
        DocRenderParamDTO param = new DocRenderParamDTO();
        List<Attachment> attachments = Optional.ofNullable(
                this.attachmentService.findAttachments(
                    DEFAULT_TEMPLATE_ATTACHMENT_SOUCE_NAME,
                    DEFAULT_TEMPLATE_ATTACHMENT_TYPE,
                    Collections.singletonList(dto.getDocType())
                )
            ).orElse(Collections.emptyList())
            .stream()
            .filter(attachment -> !CommonBooleanEnum.isTrue(attachment.getDeletedFlag()))
            .collect(Collectors.toList());
        File template = null;
        if (CollUtil.isNotEmpty(attachments)) {
            if (attachments.size() == 1) {
                Attachment attachment = attachments.get(0);
                InputStream inputStream = this.attachmentService.getAttachmentAsInputStreamById(
                    attachment.getObjId());
                template = IOUtil.copyFile(IOUtil.createTempFile().getCanonicalPath(), inputStream);
                param.setFileName(attachment.getName());
                param.setFileSuffix(attachment.getFileType());
            } else {
                log.error("当前文档类型[{}]模板存在[{}]个", dto.getDocType(), attachments.size());
                throw new BussinessException(AUDIT_DOC_TYPE_TEMPLATE_EXISTS, attachments.size());
            }
        } else {
            log.error("当前文档类型[{}]模板不存在，无法生成文档", dto.getDocType());
            throw new BussinessException(AUDIT_REPORT_TEMPLATE_NOT_EXISTS, dto.getDocType());
        }
        Long projectId = dto.getProjectId();
        Date signDate = dto.getSignDate();

        SimpleDateFormat dateFormat = new SimpleDateFormat(GlobalConstant.TIME_FORMAT_TEN);

        // 风险级别
        Map<String, String> riskLevelMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_LEVEL);
        //责任部门建议
        Map<String, String> respDeptSuggestTypeMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RESP_DEPT_SUGGEST_TYPE);
        //查询风险点是未删除remove_flag = 0  最新thread_latest_flag = 1  状态为status=100  责任部门建议类型为同意resp_dept_sug_type=1
        List<AuditRisk> auditRisks = this.riskDAO.findEfficientRisksByProjectId(projectId);
        // 通过风险ID获取每个风险对应的部门信息
        List<String> deptIds = auditRisks.stream()
            .map(auditRisk -> this.deptDAO.findByRiskId(auditRisk.getId()))
            .filter(Objects::nonNull)
            .flatMap(List::stream) //
            .map(AuditRiskRespDept::getRespDeptId)
            .distinct()
            .collect(Collectors.toList());

        Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(deptIds);

        List<AuditReportTemplateSourceDataDTO.RiskItem> risks = new LinkedList<>();
        List<AuditReportTemplateSourceDataDTO.RiskItem> buildRisks = new LinkedList<>();
        List<AuditReportTemplateSourceDataDTO.RiskItem> executeRisks = new LinkedList<>();

        int index = 1;

        for (AuditRisk auditRisk : auditRisks) {
            List<AuditRiskRespDept> respDepts = this.deptDAO.findByRiskId(auditRisk.getId());

            // 添加到 risks 列表（仅基本信息和索引）
            risks.add(buildRiskItem(respDepts,
                auditRisk, riskLevelMap, orgIdAndNameMap,
                respDeptSuggestTypeMap, dateFormat, index++, false));

            // 根据 firstCatalog 筛选数据到对应列表
            if (AuditRiskFirstCatalogEnum.BUILD_RISKS.getValue()
                .equals(auditRisk.getFirstCatalog())) {
                buildRisks.add(
                    buildRiskItem(respDepts, auditRisk, riskLevelMap, orgIdAndNameMap,
                        respDeptSuggestTypeMap, dateFormat, null, true));
            } else if (AuditRiskFirstCatalogEnum.EXECUTE_RISKS.getValue()
                .equals(auditRisk.getFirstCatalog())) {
                executeRisks.add(
                    buildRiskItem(respDepts, auditRisk, riskLevelMap, orgIdAndNameMap,
                        respDeptSuggestTypeMap, dateFormat, null, true));
            }
        }
        // 获取审计项目
        AuditProject auditProject = dao.getById(projectId);

        // 获取项目成员和 OrgId 列表
        List<AuditProjectMember> projectMembers =
            auditProjectMemberDAO.findExistsMemberByProjectIds(
                Collections.singletonList(projectId));
        Set<String> uniqueOrgIds = projectMembers.stream()
            .map(AuditProjectMember::getOrgId)
            .collect(Collectors.toSet());

        String ownerOrgIds = String.join(",", uniqueOrgIds);

        // 获取 OrgId 对应的名称映射
        Map<String, String> projectMemberOrgIdAndNameMap = orgService.getOrgNameMapByOrgIdList(
            new ArrayList<>(uniqueOrgIds)
        );

        // 获取主审计人名称（逗号分隔）
        String mainAuditerName = uniqueOrgIds.stream()
            .map(orgId -> MapUtils.getString(projectMemberOrgIdAndNameMap, orgId))
            .collect(Collectors.joining(","));

        // 生成 auditGroupMembers 列表
        List<AuditReportTemplateSourceDataDTO.AuditGroupMemberItem> auditGroupMembers =
            projectMemberOrgIdAndNameMap.entrySet().stream()
                .map(entry -> AuditReportTemplateSourceDataDTO.AuditGroupMemberItem.builder()
                    .orgId(entry.getKey())
                    .name(entry.getValue())
                    .build())
                .collect(Collectors.toList());

        AuditReportTemplateSourceDataDTO data = AuditReportTemplateSourceDataDTO.builder()
            .projectName(auditProject.getName())
            .signDate(signDate)
            .signDateStr(signDate != null ? dateFormat.format(signDate) : null)
            .auditPeriodStartDate(auditProject.getPeriodStartDate())
            .auditPeriodStartDateStr(auditProject.getPeriodStartDate() != null ?
                dateFormat.format(auditProject.getPeriodStartDate()) : null)
            .auditPeriodEndDate(auditProject.getPeriodEndDate())
            .auditPeriodEndDateStr(auditProject.getPeriodEndDate() != null ?
                dateFormat.format(auditProject.getPeriodEndDate()) : null)
            .mainAuditerId(ownerOrgIds)
            .mainAuditerName(mainAuditerName)
            .auditGroupMemberNames(mainAuditerName)
            .auditGroupMembers(auditGroupMembers)
            .risks(risks)
            .buildRisks(buildRisks)
            .executeRisks(executeRisks)
            .build();
        param.setData(JsonUtil.toMap(data));
        param.setConfigureBuilderConsumer((configureBuilder -> {
            configureBuilder.bind(RISKS, new LoopRowTableRenderPolicy());
            configureBuilder.bind(AUDIT_GROUP_MEMBERS, new LoopRowTableRenderPolicy());
        }));
        File file = WordUtil.generateWord4File(template, param);

        if (Objects.nonNull(file)) {
            UploadFileDTO uploadFileDTO = uploadCommonUtils.uploadTempFile(file);
            String originalFileName = uploadFileDTO.getName();
            String filePrefix = FileUtil.getFileSuffix(originalFileName);
            String newFileName = String.format("%s%s.%s", auditProject.getName(), AUDIT_REPORT_NAME,
                filePrefix);
            uploadFileDTO.setName(newFileName);
            return uploadFileDTO;
        } else {
            log.error("当前项目[{}]生成审计报告失败", auditProject.getName());
            throw new BussinessException(AUDIT_REPORT_GENERATE_FAIL, auditProject.getName());
        }
    }

    @Override
    public void updateRegulatoryTimeById(Long id, Date regulatoryTime) {
        this.dao.updateRegulatoryTimeById(id, regulatoryTime);
    }

    @Override
    public List<AuditProjectInfoDTO> findAllAuditProject() {
        List<AuditProject> allAuditProject = this.dao.findAllAuditProject();
        return allAuditProject.stream().map(AuditProjectUtil::makeEntity2InfoDTO)
            .collect(Collectors.toList());
    }

    @Override
    public List<AuditProjectInfoDTO> listAuditProject(AuditProjectQueryParamDTO param) {
        AuditProjectQueryParamPO paramPO = AuditProjectConvertUtil.makeDTO2PO(
            param);
        List<AuditProject> list = this.dao.listAuditProject(paramPO);
        return list.stream().map(AuditProjectUtil::makeEntity2InfoDTO).collect(Collectors.toList());
    }

    @Override
    public List<AuditProjectInfoDTO> findExistsProjectByNames(Collection<String> names) {
        List<AuditProject> list = this.dao.findAuditProjectByNames(names);
        return list.stream().map(AuditProjectUtil::makeEntity2InfoDTO).collect(Collectors.toList());
    }

    @Override
    public List<AuditProjectInfoDTO> findExistsProjectByNamesAndPlanId(Collection<String> names,
        Long planId) {
        List<AuditProject> list = this.dao.findAuditProjectByNamesAndPlanId(names, planId);
        return list.stream().map(AuditProjectUtil::makeEntity2InfoDTO).collect(Collectors.toList());
    }

    @Override
    public IPage<AuditProjectSearchResultDTO> search(AuditProjectSearchParamDTO param) {
        AuditProjectSearchParamPO searchParamPO = AuditProjectUtil.makeSearchParamDTO2PO(param);
        Page<AuditProjectSearchResultPO> page = searchParamPO.buildPage();
        IPage<AuditProjectSearchResultPO> search = this.dao.search(page, searchParamPO);
        List<AuditProjectSearchResultPO> records = search.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> ids = records.stream()
                .map(AuditProjectSearchResultPO::getId)
                .collect(Collectors.toList());

            List<AuditProjectMemberInfoDTO> ownerByProjectIds = this.projectMemberService.findOwnerByProjectIds(
                ids);

            Map<Long, List<String>> projectIdToOrgIdsMap = ownerByProjectIds.stream()
                .collect(Collectors.groupingBy(
                    AuditProjectMemberInfoDTO::getProjectId,
                    Collectors.mapping(AuditProjectMemberInfoDTO::getOrgId, Collectors.toList())
                ));

            records.forEach(record -> {
                List<String> orgIds = projectIdToOrgIdsMap.getOrDefault(record.getId(),
                    Collections.emptyList());
                record.setOrgIds(orgIds);
            });
        }

        return search.convert(AuditProjectUtil::makeSearchResultPO2DTO);
    }

    @Override
    public Integer loadProjectAuditYearById(Long projectId) {
        return this.dao.loadProjectAuditYearById(projectId);
    }


    private void publishAuditDataEvent(List<AuditProject> allProjects) {
        List<LcAuditDataDTO<?>> data = allProjects.stream().map(item -> {
            LcAuditDataDTO<AuditProject> auditData = new LcAuditDataDTO<>();
            auditData.setNewData(item);
            auditData.setId(item.getId());
            auditData.setModelCode(AuditProjectModelConstant.MODEL_CODE);
            auditData.setOpType(AuditModelAuditOpTypeConstant.COPY);
            return auditData;
        }).collect(Collectors.toList());

        DataAuditEventSourceDTO sourceEvent = new DataAuditEventSourceDTO();
        sourceEvent.setRemark("审计项目-复制项目");
        sourceEvent.setOpDate(new Date());
        sourceEvent.setOpOrgId(CurrentUserFactory.getOrgId());
        sourceEvent.setTraceSeq(UUID.fastUUID().toString(true));
        sourceEvent.setData(data);
        DataAuditEventDTO event = new DataAuditEventDTO(sourceEvent);
        LcEventUtil.publishEvent(event);
    }

    // 构建通用的 RiskItem 构建方法
    private AuditReportTemplateSourceDataDTO.RiskItem buildRiskItem(
        List<AuditRiskRespDept> respDepts,
        AuditRisk auditRisk,
        Map<String, String> riskLevelMap,
        Map<String, String> orgIdAndNameMap,
        Map<String, String> respDeptSuggestTypeMap,
        SimpleDateFormat dateFormat,
        Integer index,
        boolean includeDetails) {

        List<String> deptNames = respDepts.stream()
            .map(AuditRiskRespDept::getRespDeptId)
            .map(orgIdAndNameMap::get) // 从映射中获取部门名称
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        AuditReportTemplateSourceDataDTO.RiskItem.RiskItemBuilder<?, ?> builder = AuditReportTemplateSourceDataDTO.RiskItem.builder()
            .name(auditRisk.getName())
            .level(auditRisk.getLevel())
            .levelName(MapUtils.getString(riskLevelMap, String.valueOf(auditRisk.getLevel())));

        if (index != null) {
            builder.index(index);
        }

        if (includeDetails) {
            builder.audtiFinding(auditRisk.getDescription())
                .managementSuggestion(auditRisk.getAuditSuggestion())
                .managementResperName(String.join(",", deptNames))
                .managementReply(
                    MapUtils.getString(respDeptSuggestTypeMap,
                        String.valueOf(auditRisk.getRespDeptSugType())) +
                        Optional.ofNullable(auditRisk.getRespDeptSuggestion())
                            .filter(s -> !s.isEmpty())
                            .map(s -> "," + s)
                            .orElse(""))
                .expendRectifyDate(auditRisk.getReqRectifyDate())
                .expendRectifyDateStr(auditRisk.getReqRectifyDate() != null ?
                    dateFormat.format(auditRisk.getReqRectifyDate()) : null);
        }

        return builder.build();
    }
}
