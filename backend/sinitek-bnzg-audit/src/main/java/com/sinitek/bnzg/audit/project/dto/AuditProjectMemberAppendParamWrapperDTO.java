package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(description = "项目成员追加参数")
public class AuditProjectMemberAppendParamWrapperDTO implements IAuditProjectAndOpeatorId {

    @NotNull(message = "所属项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @Valid
    @NotEmpty(message = "项目成员不能为空")
    @ApiModelProperty("项目成员(集合)")
    private List<AuditProjectMemberSaveParamDTO> appendMemberList;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;


}
