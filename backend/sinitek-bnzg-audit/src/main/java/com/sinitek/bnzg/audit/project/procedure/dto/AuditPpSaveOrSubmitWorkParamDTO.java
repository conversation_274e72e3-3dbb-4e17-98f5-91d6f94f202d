package com.sinitek.bnzg.audit.project.procedure.dto;

import com.sinitek.bnzg.audit.project.procedure.service.impl.AuditProjectProcedureServiceImpl;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 修改dto注意 {@link AuditProjectProcedureServiceImpl#submitWork(AuditPpSaveOrSubmitWorkParamDTO)} 存在判断数据是否变化的逻辑需要同步更新
 *
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目程序保存或提交参数")
public class AuditPpSaveOrSubmitWorkParamDTO {

    @ApiModelProperty("项目审计程序id")
    private Long id;

    @ApiModelProperty("执行情况")
    private String execution;

    @ApiModelProperty("审计证据")
    private UploadDTO upload;

    @ApiModelProperty("访谈笔录")
    private UploadDTO interviewTranscript;

    @ApiModelProperty("调查问卷")
    private UploadDTO questionnaire;

//    @ApiModelProperty("征求意见")
//    private UploadDTO solicitOpinions;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
