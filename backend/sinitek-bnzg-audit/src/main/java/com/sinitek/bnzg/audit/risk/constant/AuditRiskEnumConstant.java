package com.sinitek.bnzg.audit.risk.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/29/2024 10:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskEnumConstant {

    public static final String DEFAULT_CATALOG = "AUDIT";

    /**
     * 风险点状态
     */
    public static final String RISK_STATUS = "risk-status";

    /**
     * ### 风险点审批状态(AUDIT,risk-approve-status)
     */
    public static final String RISK_APPROVE_STATUS = "risk-approve-status";

    /**
     * ### 风险点审批结果(AUDIT,risk-approve-result)
     */
    public static final String RISK_APPROVE_RESULT = "risk-approve-result";

    /**
     * ### 风险类型(AUDIT,risk-type)
     */
    public static final String RISK_TYPE = "risk-type";

    /**
     * ### 风险级别(AUDIT,risk-level)
     */
    public static final String RISK_LEVEL = "risk-level";

    /**
     * ### 责任部门建议类型(AUDIT,resp-dept-suggest-type)
     */
    public static final String RESP_DEPT_SUGGEST_TYPE = "resp-dept-suggest-type";

    /**
     * ### 整改状态(AUDIT,rectify-status)
     */
    public static final String RECTIFY_STATUS = "rectify-status";

    /**
     * ### 制度方向(AUDIT,first-catalog)
     */
    public static final String FIRST_CATALOG = "first-catalog";

}
