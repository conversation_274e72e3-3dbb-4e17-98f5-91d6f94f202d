package com.sinitek.bnzg.audit.project.procedure.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/29/2024 10:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditPpUploadConstant {

    // 审计证据
    public static final Integer INVESTIGATION_RESULT_UPLOAD_TYPE  = 0;

    //访谈笔录
    public static final Integer PUNISH_OPINION_UPLOAD_TYPE  = 1;

    //调查问卷
    public static final Integer PUNISH_NOTICE_UPLOAD_TYPE  = 2;

    //征求意见
    public static final Integer RECONSIDERATION_UPLOAD_TYPE = 3;


}
