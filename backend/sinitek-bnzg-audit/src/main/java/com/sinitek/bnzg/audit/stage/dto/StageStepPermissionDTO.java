package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-06-16 16:11:07
 */

@Data
@ApiModel("步骤权限")
public class StageStepPermissionDTO {

    @ApiModelProperty("组织结构对象")
    private String orgId;
    /**
     * 是否有“查看”权限
     */
    @ApiModelProperty("是否有“查看”权限")
    private Boolean view;

    /**
     * 是否有“编辑”权限
     */
    @ApiModelProperty("是否有“编辑”权限")
    private Boolean edit;

    /**
     * 是否有“手动结束”权限
     */
    @ApiModelProperty("是否有“手动结束”权限")
    private Boolean manuallyFinish;

    public StageStepPermissionDTO() {
        this.view = false;
        this.edit = false;
        this.manuallyFinish = false;
    }

}
