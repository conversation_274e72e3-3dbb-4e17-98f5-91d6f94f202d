package com.sinitek.bnzg.audit.stage.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.event.AuditProjectPhaseChangeEvent;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.service.IStageExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageExampleStatusService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class AuditProjectPhaseStopEventListenerInStage {

    @Autowired
    private IStageExampleService stageExampleService;

    @Autowired
    private IStageExampleStatusService stageExampleStatusService;

    @Autowired
    private IStageStepExampleService stepExampleService;

    @Autowired
    private IStageStepExampleStatusService stepExampleStatusService;

    /**
     * 监听项目阶段终止事件
     *
     * 把全景图中所有正在进行的阶段步骤置为已终止
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditProjectPhaseChangeEvent.class, fallbackExecution = true)
    public void listen(AuditProjectPhaseChangeEvent event) {
        AuditProjectPhaseChangeEventSourceDTO source = event.getSource();
        Long projectId = source.getProjectId();
        String opOrgId = source.getOperatorId();
        Date opTime = source.getOpTime();
        Integer oldProjectPhase = source.getOldProjectPhase();
        Integer newProjectPhase = source.getNewProjectPhase();

        if (Objects.equals(newProjectPhase, AuditProjectPhaseConstant.STOP)) {
            log.info(
                "监听到审计项目[{}]阶段终止事件,操作人[{}],操作时间[{}],旧阶段[{}] => 新阶段[{}],全景图中所有正在进行的阶段步骤将会被置为已终止",
                projectId, opOrgId, opTime, oldProjectPhase, newProjectPhase
            );

            List<StageExampleDTO> stageExamples = this.stageExampleService.findByProjectIds(
                Collections.singleton(projectId));
            if (CollUtil.isNotEmpty(stageExamples)) {
                if (stageExamples.size() > 1) {
                    log.warn("项目[{}]终止时,根据项目id获取到多个阶段示例", projectId);
                }
                StageExampleDTO stageExampleDTO = stageExamples.get(0);
                Long stageExampleId = stageExampleDTO.getId();
                List<StageStepExampleDTO> stepExamples = this.stepExampleService.findByStageExampleIds(
                    Collections.singleton(stageExampleId));
                if (CollUtil.isNotEmpty(stepExamples)) {

                    List<StageStepExampleDTO> allInProgressStepList = stepExamples.stream()
                        .filter(item -> {
                            Integer status = item.getStatus();
                            return ((Objects.equals(status, StageStepStatusConstant.PROCESSING))
                                || (Objects.equals(status, StageStepStatusConstant.READY)));
                        }).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(allInProgressStepList)) {
                        List<Long> stepExampleIdList = allInProgressStepList.stream()
                            .map(StageStepExampleDTO::getId).collect(
                                Collectors.toList());
                        log.info(
                            "项目[{}]终止时,根据对应阶段实例[{}]获取到步骤实例,正在进行的步骤[{}]将会变更为终止状态",
                            projectId,
                            stageExampleId, stepExampleIdList);
                        this.stepExampleStatusService.updateStatus(
                            StageStepExampleStatusChangeParamDTO.builder()
                                .ids(stepExampleIdList)
                                .newStatus(StageStepStatusConstant.TERMINATE)
                                .opOrgId(opOrgId)
                                .opTime(opTime)
                                .opRemark("项目中止")
                                .publishEventFlag(true)
                                .force(false)
                                .build());
                    } else {
                        log.warn(
                            "项目[{}]终止时,根据对应阶段实例[{}]获取到步骤实例正在进行的步骤为空",
                            projectId,
                            stageExampleId);
                    }

                } else {
                    log.warn("项目[{}]终止时,根据对应阶段实例[{}]获取到步骤实例为空", projectId,
                        stageExampleId);
                }

            } else {
                log.warn("项目[{}]终止时,根据项目id无法获取对应阶段实例", projectId);
            }

        }


    }

}
