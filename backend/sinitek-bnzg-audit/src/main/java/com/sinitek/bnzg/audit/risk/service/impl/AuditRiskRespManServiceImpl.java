package com.sinitek.bnzg.audit.risk.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskRespManDAO;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import com.sinitek.bnzg.audit.risk.log.respman.util.AuditRespManChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespManService;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 项目风险点责任人 Service 实现类
 *
 * <AUTHOR>
 * date 2024-11-08
 */
@Slf4j
@Service
public class AuditRiskRespManServiceImpl implements IAuditRiskRespManService {

    @Autowired
    private AuditRiskRespManDAO respManDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchRespMan(Long riskId, List<String> respManIds) {
        List<String> existsResManOrgIds = null;
        List<AuditRiskRespMan> respManList = this.findByRiskId(riskId);
        if (CollectionUtils.isNotEmpty(respManList)) {
            existsResManOrgIds = respManList.stream().map(AuditRiskRespMan::getRespManId)
                .collect(Collectors.toList());
            List<Long> idList = respManList.stream()
                .map(AuditRiskRespMan::getId)
                .collect(Collectors.toList());
            this.respManDAO.removeByIds(idList);
        }

        List<AuditRiskRespMan> list = respManIds.stream()
            .filter(StringUtils::isNotBlank)
            .map(respManId -> {
                AuditRiskRespMan respMan = new AuditRiskRespMan();
                respMan.setRiskId(riskId);
                respMan.setRespManId(respManId);
                respMan.setRemoveFlag(CommonBooleanEnum.FALSE.getValue());
                return respMan;
            })
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(list)) {
            log.info("本次操作需保存项目风险点责任人数据为[{}]条", list.size());
            this.respManDAO.saveBatch(list);
        } else {
            log.info("本次操作需保存项目风险点责任人数据为空");
        }

        if (Objects.isNull(existsResManOrgIds) && CollUtil.isEmpty(list)) {
            log.info("本次操作项目风险点责任人数据未变动,无需发布项目风险点责任人变动事件");
            return;
        }

        String respDeptremark = "风险点-更新责任人";
        if (Objects.isNull(existsResManOrgIds)) {
            respDeptremark = "风险点-新增责任人";
        }

        AuditRespManChangeEventPublishUtil.publishEvent(
            RecordChangeLogAddParamDTO.<String>builder()
                .foreignKey(riskId)
                .oldValue(JsonUtil.toJsonString(existsResManOrgIds))
                .newValue(JsonUtil.toJsonString(respManIds))
                .operatorId(CurrentUserFactory.getOrgId())
                .opTime(new Date())
                .remark(respDeptremark)
                .build());
    }


    @Override
    public List<AuditRiskRespMan> findByRiskId(Long riskId) {
        return this.respManDAO.findByRiskId(riskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatch(List<AuditRiskRespMan> respManList) {
        this.respManDAO.saveOrUpdateBatch(respManList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Long> ids) {
        this.respManDAO.removeByIds(ids);
    }

    @Override
    public List<AuditRiskRespMan> findByRiskIds(Collection<Long> riskIds) {
        return respManDAO.findByRiskIds(riskIds);
    }
}
