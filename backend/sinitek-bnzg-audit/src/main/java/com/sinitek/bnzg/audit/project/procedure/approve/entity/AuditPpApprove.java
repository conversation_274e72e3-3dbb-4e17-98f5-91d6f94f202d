package com.sinitek.bnzg.audit.project.procedure.approve.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批 Entity
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_pp_approve")
@ApiModel(description = "项目审计程序审批")
public class AuditPpApprove extends BaseAuditEntity {

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人
     */
    @ApiModelProperty("删除人")
    private String removerId;

}
