package com.sinitek.bnzg.audit.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 12/05/2024 14:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "审计项目列表返回DTO")
public class AuditProjectSearchResultDTO extends PageDataParam {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("计划id")
    private Long planId;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty(value = "审计年份")
    private Integer auditYear;

    @ApiModelProperty("项目负责人")
    private List<String> orgIds;

    @ApiModelProperty("项目负责人")
    private String ownerNames;

    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date startDate;

    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date endDate;

    @ApiModelProperty("审计期开始日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date periodStartDate;

    @ApiModelProperty("审计期结束日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date periodEndDate;

    @ApiModelProperty("项目进度")
    private Integer projectPhase;

    @ApiModelProperty("项目进度名")
    private String projectPhaseName;

    @ApiModelProperty("启动日期")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date startupDate;

}
