package com.sinitek.bnzg.audit.risk.enumation;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 11/06/2024
 */
@Slf4j
@Getter
public enum AuditRiskFirstCatalogEnum {
    BUILD_RISKS("制度建设", 1),
    EXECUTE_RISKS("制度执行", 2);
    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    AuditRiskFirstCatalogEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }
}
