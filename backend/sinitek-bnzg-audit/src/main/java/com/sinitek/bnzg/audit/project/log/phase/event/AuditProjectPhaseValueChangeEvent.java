package com.sinitek.bnzg.audit.project.log.phase.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.event.AbstractRecordChangeLogEvent;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:12
 */
public class AuditProjectPhaseValueChangeEvent<T extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEvent<T> {

    public AuditProjectPhaseValueChangeEvent(T source) {
        super(source);
    }

}
