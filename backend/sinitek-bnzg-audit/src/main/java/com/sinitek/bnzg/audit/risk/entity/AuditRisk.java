package com.sinitek.bnzg.audit.risk.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目风险点 Entity
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_risk")
@ApiModel(description = "项目风险点实体")
public class AuditRisk extends BaseAuditEntity {

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 程序id
     */
    @ApiModelProperty("程序id")
    private Long procedureId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("制度依据")
    private String sysBasis;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private Integer type;

    /**
     * 风险级别
     */
    @ApiModelProperty("风险级别")
    private Integer level;

    /**
     * 制度方向
     */
    @ApiModelProperty("制度方向")
    private Integer firstCatalog;
    /**
     * 内部分类
     */
    @ApiModelProperty("内部分类")
    private String innerCategory;

    /**
     * 审计建议
     */
    @ApiModelProperty("审计建议")
    private String auditSuggestion;

    /**
     * 责任部门建议类型
     */
    @ApiModelProperty("责任部门建议类型")
    private Integer respDeptSugType;

    /**
     * 责任部门建议
     */
    @ApiModelProperty("责任部门建议")
    private String respDeptSuggestion;


    /**
     * 审计人
     */
    @ApiModelProperty("审计人")
    private String auditorId;

    /**
     * 审计日期
     */
    @ApiModelProperty("审计日期")
    private Date auditDate;

    /**
     * 要求整改日期
     */
    @ApiModelProperty("要求整改日期")
    private Date reqRectifyDate;

    /**
     * 实际整改日期
     */
    @ApiModelProperty("实际整改日期")
    private Date actRectifyDate;

    /**
     * 整改反馈
     */
    @ApiModelProperty("整改反馈")
    private String rectifyFeedback;

    /**
     * 整改状态
     */
    @ApiModelProperty("整改状态")
    private Integer rectifyState;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String approverId;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date approveTime;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    /**
     * 留痕id
     */
    @ApiModelProperty("留痕id")
    private Long threadId;

    /**
     * 标识最新生成的记录
     */
    @ApiModelProperty("标识最新生成的记录")
    private Integer threadLatestFlag;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人
     */
    @ApiModelProperty("删除人")
    private String removerId;

    @ApiModelProperty("是否重复发生")
    private Integer repeatFlag;


}
