package com.sinitek.bnzg.audit.risk.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpRiskHomeSearchResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskResultPO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditProjectRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSaveOrEditOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.po.AuditProjectRiskRefCountPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskDataResultPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskRefCountPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 08/28/2024 17:52
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskConvertUtil {

    public static AuditRisk makeSaveOrEditDTO2Entity(AuditRiskSaveOrEditOnExecutionParamDTO param) {
        return LcConvertUtil.convert(param, AuditRisk::new);
    }

    public static void makeSaveOrEditDTO2Entity(AuditRiskSaveOrEditOnExecutionParamDTO param,
        AuditRisk entity) {
        BeanUtils.copyProperties(param, entity);
    }

    public static AuditRiskSearchParamPO makeSearchParamDTO2PO(AuditRiskSearchParamDTO dto) {
        AuditRiskSearchParamPO po = new AuditRiskSearchParamPO();

        po.setProjectId(dto.getProjectId());
        po.setProcedureId(dto.getProcedureId());

        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());
        return po;
    }

    public static FindAllAuditRiskParamPO makeSearchParamDTO3PO(FindAllAuditRiskParamDTO dto) {

        List<Long> planIds = dto.getPlanIds();
        String projectName = dto.getProjectName();
        String procedureName = dto.getProcedureName();

        FindAllAuditRiskParamPO po = new FindAllAuditRiskParamPO();

        if (CollUtil.isNotEmpty(planIds)) {
            po.setPlanIds(planIds);
        }
        List<String> projectNames = CommonStringUtil.toSearchStrList(projectName);
        if (CollUtil.isNotEmpty(projectNames)) {
            po.setProjectNames(projectNames);
        }

        List<String> procedureNames = CommonStringUtil.toSearchStrList(procedureName);
        if (CollUtil.isNotEmpty(procedureNames)) {
            po.setProcedureNames(procedureNames);
        }
        return po;
    }

    public static AuditRiskSearchResultDTO makeSearchResultPO2DTO(AuditRiskSearchResultPO po) {
        return LcConvertUtil.convert(po, AuditRiskSearchResultDTO::new);
    }

    public static AuditRiskDetailDTO makeEntity2DetailDTO(AuditRisk entity) {
        return LcConvertUtil.convert(entity, AuditRiskDetailDTO::new);
    }

    public static AuditRiskBaseInfoDTO makeEntity2BaseInfoDTO(AuditRisk entity) {
        return LcConvertUtil.convert(entity, AuditRiskBaseInfoDTO::new);
    }

    public static FindAllAuditRiskResultDTO makeEntity3BaseInfoDTO(FindAllAuditRiskResultPO po) {
        return LcConvertUtil.convert(po, FindAllAuditRiskResultDTO::new);
    }

    public static AuditRiskRefCountDTO makeRiskRefCountPO2DTO(AuditRiskRefCountPO po) {
        return LcConvertUtil.convert(po, AuditRiskRefCountDTO::new);
    }

    public static AuditProjectRiskRefCountDTO makeProjectRiskRefCountPO2DTO(
        AuditProjectRiskRefCountPO po) {
        return LcConvertUtil.convert(po, AuditProjectRiskRefCountDTO::new);
    }

    public static AuditRiskDetailDTO makeEntity3BaseInfoDTO(AuditRiskDataResultPO po) {
        return LcConvertUtil.convert(po, AuditRiskDetailDTO::new);
    }

    public static AuditPpRiskHomeSearchResultDTO makeAuditRiskBaseInfoDTO2SearchResult(
        AuditRiskBaseInfoDTO riskItem,
        AuditPpHomeSearchResultDTO dto, String currentOrgId,
        Map<String, String> riskStatusMap) {
        AuditPpRiskHomeSearchResultDTO riskResult = new AuditPpRiskHomeSearchResultDTO();
        riskResult.setId(riskItem.getId());
        riskResult.setStatus(riskItem.getStatus());
        riskResult.setName(riskItem.getName());
        riskResult.setProcedureId(dto.getProcedureId());
        riskResult.setProjectId(dto.getProjectId());
        riskResult.setProjectName(dto.getProjectName());
        riskResult.setPlanId(dto.getPlanId());
        riskResult.setPlanName(dto.getPlanName());
        riskResult.setSupportApproveFlag(
            Objects.equals(AuditRiskStatusConstant.APPROVING,
                riskItem.getStatus())
                && !Objects.equals(currentOrgId,
                riskItem.getAuditorId()));
        riskResult.setApproveRemark(riskItem.getApproveRemark());

        Integer status = riskResult.getStatus();
        String statusName = MapUtils.getString(riskStatusMap, String.valueOf(status));
        riskResult.setStatusName(statusName);

        return riskResult;
    }
}
