package com.sinitek.bnzg.audit.project.procedure.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchResultOnProjectConfigDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditProjectRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditProjectProcedureSearchOnProjectConfigResultFormat implements
    ITableResultFormat<AuditProjectProcedureSearchResultOnProjectConfigDTO> {

    @Autowired
    private IAuditRiskService auditRiskService;

    @Override
    public List<AuditProjectProcedureSearchResultOnProjectConfigDTO> format(
        List<AuditProjectProcedureSearchResultOnProjectConfigDTO> data) {

        if (CollUtil.isNotEmpty(data)) {
            AuditProjectProcedureSearchResultOnProjectConfigDTO firstData = data.get(0);
            Long projectId = firstData.getProjectId();
            List<Long> procedureIds = data.stream()
                .map(AuditProjectProcedureSearchResultOnProjectConfigDTO::getProcedureId)
                .collect(Collectors.toList());

            // 项目审计程序风险点
            List<AuditProjectRiskRefCountDTO> riskRefCounts = this.auditRiskService.findProjectRiskRefCountByProjectIdAndProcedureIds(
                projectId, procedureIds);
            Map<Long, Integer> procedureIdAndRefCountMap = riskRefCounts.stream().collect(
                Collectors.toMap(AuditProjectRiskRefCountDTO::getProcedureId,
                    AuditProjectRiskRefCountDTO::getRefCount));

            data.forEach(item -> {
                Long procedureId = item.getProcedureId();
                // 填充风险点数据
                item.setRiskCount(MapUtils.getInteger(procedureIdAndRefCountMap, procedureId, 0));
            });
        }

        return data;
    }
}
