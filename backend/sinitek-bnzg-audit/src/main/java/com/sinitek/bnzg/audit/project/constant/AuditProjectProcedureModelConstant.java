package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectProcedureModelConstant {

    public static final String MODEL_CODE = "AUDIT_PROJECT_PROCEDURE";


    public static final String GROUP_ID_SEARCH_PARAM_NAME = "procedureId.groupId";

    public static final String PROCEDURE_ID_NAME = "procedureId";

    public static final String PROJECT_ID_NAME = "projectId";

    public static final String VIEWER_ID_NAME = "viewers";
    public static final String VIEWER_NAME_NAME = "viewerNames";
    public static final String EDITOR_ID_NAME = "editors";
    public static final String EDITOR_NAME_NAME = "editorNames";
    public static final String APPROVER_ID_NAME = "approvers";
    public static final String APPROVER_NAME_NAME = "approverNames";

    /**
     * 风险点数量
     */
    public static final String RISK_COUNT_NAME = "riskCount";
}
