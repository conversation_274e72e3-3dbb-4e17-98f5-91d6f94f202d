package com.sinitek.bnzg.audit.project.procedure.approve.service.impl;

import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.APPROVING_DATA_CAT_SUBMIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.CANT_APPROVE_PP_NO_STATUS;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.CANT_SUBMIT_BECAUSE_PROCEDURE_NOT_AUDIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.CANT_SUBMIT_BECAUSE_RISK_NOT_AUDIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.DATA_EMPTY_CANT_SAVE;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.LESS_APPROVER_CANT_SUBMIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.LESS_AUDIT_DATA_CAT_SUBMIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.NOT_APPROVED_DATA_CAT_SUBMIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.NOT_DRAFT_AND_DONT_HAS_RISK_CAT_SUBMIT;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.NOT_DRAFT_CAT_SUBMIT;
import static com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil.HAS_APPROVE_AUTH;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.ALREADY_SUBMITED_CANT_SUBMIT_AGAIN;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.APPROVE_DATA_NOT_EXISTS_CANT_SUBMIT;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_TERMINATED_CANT_APPROVE;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.NO_APPROVER_CANT_SUBMIT;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectAndOpeatorIdBaseDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant;
import com.sinitek.bnzg.audit.project.procedure.approve.dao.AuditPpApproveDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpAndRiskBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveSubmitParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveTerminateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApprove;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.util.AuditPpApprvStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproverService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.util.PpApproveConvertUtil;
import com.sinitek.bnzg.audit.project.procedure.dao.AuditProjectProcedureDAO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmBatchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmSingleParamDTO;
import com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure;
import com.sinitek.bnzg.audit.project.procedure.log.status.util.AuditPpStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.procedure.util.AuditPpChangeEventUtil;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.log.status.util.AuditRiskStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.risk.util.AuditRiskChangeEventUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParamDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-11-13 15:19
 */
@Slf4j
@Service
public class PpApproveServiceImpl implements IPpApproveService {

    @Autowired
    private AuditPpApproveDAO dao;

    @Autowired
    private AuditProjectProcedureDAO ppDao;

    @Autowired
    private AuditRiskDAO riskDAO;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Autowired
    private IAuditProjectProcedureService projectProcedureService;

    @Autowired
    private IAuditProjectMemberService projectMemberService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IPpApproverService ppApproverService;

    @Autowired
    private IPpApproveResultService ppApproveResultService;

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    @Autowired
    private IAuditProjectProcedureService ppService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditPlanService planService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(PpApproveCreateParamDTO param) {
        Long projectId = param.getProjectId();
        List<Long> ppIds = param.getIds();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        List<AuditProjectMemberInfoDTO> approvers = this.projectMemberService.findMembers(
            Collections.singleton(projectId),
            HAS_APPROVE_AUTH);

        if (CollUtil.isEmpty(approvers)) {
            log.error("项目 {} 不存在审批人员无法提交审计程序", projectId);
            throw new BussinessException(NO_APPROVER_CANT_SUBMIT);
        }

        List<AuditProjectProcedure> pps = this.ppDao.listByIds(ppIds);
        if (CollUtil.isEmpty(pps)) {
            log.error("项目审计程序 {} 对应数据库数据为空", ppIds);
            throw new BussinessException(AuditPpApproveMessageConstant.EMPTY_DATA_CANT_SUBMIT);
        }

        List<Long> procedureIds = pps.stream().map(AuditProjectProcedure::getProcedureId)
            .collect(Collectors.toList());

        List<AuditRiskBaseInfoDTO> risks = this.auditRiskService.findProjectRiskInfoByProjectIdAndProcedureIds(
            projectId, procedureIds);

        // key:审计程序id;value:风险点数量
        Map<Long, List<AuditRiskBaseInfoDTO>> procedureIdAndRiskMap;
        if (CollUtil.isNotEmpty(risks)) {
            procedureIdAndRiskMap = risks.stream().collect(
                Collectors.groupingBy(AuditRiskBaseInfoDTO::getProcedureId));
        } else {
            procedureIdAndRiskMap = Collections.emptyMap();
        }

        List<AuditProjectProcedure> approvingDataList = new LinkedList<>();
        List<AuditProjectProcedure> notApprovedDataList = new LinkedList<>();
        List<AuditProjectProcedure> lessAuditorDataList = new LinkedList<>();
        List<AuditProjectProcedure> notAuditDataList = new LinkedList<>();
        List<AuditProjectProcedure> notDraftDataList = new LinkedList<>();
        List<AuditProjectProcedure> noNeedGenerateApprovePpList = new LinkedList<>();
        List<AuditProjectProcedure> needGenerateApprovePpList = new LinkedList<>();
        Map<Long, List<AuditRiskBaseInfoDTO>> ppIdAndNeedApproveRiskMap = new LinkedHashMap<>();

        pps.forEach(item -> {
            Long id = item.getId();
            Long procedureId = item.getProcedureId();
            Integer status = item.getStatus();
            String auditorId = item.getAuditorId();

            List<AuditRiskBaseInfoDTO> riskItems = procedureIdAndRiskMap.get(
                procedureId);

            // 未审计的数据
            if (Objects.equals(AuditRiskStatusConstant.NOT_AUDIT, status)) {
                notAuditDataList.add(item);
                return;
            }

            // 缺少审计人数据
            if (Objects.isNull(auditorId)) {
                lessAuditorDataList.add(item);
                return;
            }

            // 草稿数据
            if (Objects.equals(status, AuditRiskStatusConstant.DRAFT)) {
                needGenerateApprovePpList.add(item);
            }

            if (CollUtil.isNotEmpty(riskItems)) {
                List<AuditRiskBaseInfoDTO> needApproveRiskList = riskItems.stream()
                    .filter(riskItem -> {
                        Integer riskStatus = riskItem.getStatus();
                        return Objects.equals(AuditRiskStatusConstant.DRAFT, riskStatus);
                    }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(needApproveRiskList)) {
                    ppIdAndNeedApproveRiskMap.put(id, needApproveRiskList);

                    if (!Objects.equals(status, AuditRiskStatusConstant.DRAFT)) {
                        needGenerateApprovePpList.add(item);
                    }
                } else {
                    // 非草稿且没有待审批的风险点
                    if (!Objects.equals(status, AuditRiskStatusConstant.DRAFT)) {
                        noNeedGenerateApprovePpList.add(item);
                    }
                }
            } else {
                // 非草稿状态且没有风险点 抛出异常
                if (!Objects.equals(status, AuditRiskStatusConstant.DRAFT)) {
                    notDraftDataList.add(item);
                }
            }
        });

        if (CollUtil.isNotEmpty(notAuditDataList)) {
            log.error("项目审计程序id: {} 为未审计数据,未保存过无法提交审批",
                notAuditDataList.stream().map(AuditProjectProcedure::getId).collect(
                    Collectors.toList()));
            throw new BussinessException(CANT_APPROVE_PP_NO_STATUS, this.formatProcedureName(
                notAuditDataList.stream().map(AuditProjectProcedure::getProcedureId).collect(
                    Collectors.toList())));
        }

        if (CollUtil.isNotEmpty(approvingDataList)) {
            log.error("项目审计程序id: {} 为审批中数据,未保存过无法提交审批",
                approvingDataList.stream().map(AuditProjectProcedure::getId).collect(
                    Collectors.toList()));
            throw new BussinessException(APPROVING_DATA_CAT_SUBMIT, this.formatProcedureName(
                approvingDataList.stream().map(AuditProjectProcedure::getProcedureId).collect(
                    Collectors.toList())));
        }

        if (CollUtil.isNotEmpty(notApprovedDataList)) {
            log.error("项目审计程序id: {} 为未审批通过数据,未保存过无法提交审批",
                notApprovedDataList.stream().map(AuditProjectProcedure::getId).collect(
                    Collectors.toList()));
            throw new BussinessException(NOT_APPROVED_DATA_CAT_SUBMIT, this.formatProcedureName(
                notApprovedDataList.stream().map(AuditProjectProcedure::getProcedureId).collect(
                    Collectors.toList())));
        }

        if (CollUtil.isNotEmpty(lessAuditorDataList)) {
            log.error("项目审计程序id: {} 缺少审计人数据,无法提交审批",
                lessAuditorDataList.stream().map(AuditProjectProcedure::getId).collect(
                    Collectors.toList()));
            throw new BussinessException(LESS_AUDIT_DATA_CAT_SUBMIT, this.formatProcedureName(
                lessAuditorDataList.stream().map(AuditProjectProcedure::getProcedureId).collect(
                    Collectors.toList())));
        }

        if (CollUtil.isNotEmpty(notDraftDataList)) {
            log.error("项目审计程序id: {} 非草稿状态且没有风险点,状态值: {},无法提交",
                notDraftDataList.stream().map(AuditProjectProcedure::getId).collect(
                    Collectors.toList()),
                notDraftDataList.stream().map(AuditProjectProcedure::getStatus).collect(
                    Collectors.toList()));
            throw new BussinessException(NOT_DRAFT_CAT_SUBMIT, this.formatProcedureName(
                notDraftDataList.stream().map(AuditProjectProcedure::getProcedureId).collect(
                    Collectors.toList())));
        }

        if (CollUtil.isNotEmpty(noNeedGenerateApprovePpList)) {
            log.error("项目审计程序id: {} 非草稿状态存在风险点但没有需要审批的风险点,无法提交",
                noNeedGenerateApprovePpList.stream().map(AuditProjectProcedure::getId).collect(
                    Collectors.toList()));
            throw new BussinessException(NOT_DRAFT_AND_DONT_HAS_RISK_CAT_SUBMIT,
                this.formatProcedureName(
                    noNeedGenerateApprovePpList.stream().map(AuditProjectProcedure::getProcedureId)
                        .collect(
                            Collectors.toList())));
        }

        // 根据项目审计程序审计人分组处理
        // key:审计人id,value:审计人对应风险点
        Map<String, List<AuditProjectProcedure>> auditorIdAndPpsMap = needGenerateApprovePpList.stream()
            .collect(Collectors.groupingBy(AuditProjectProcedure::getAuditorId));

        // 项目上拥有审批角色的所有审批人
        List<String> allApproverIds = approvers.stream().map(AuditProjectMemberInfoDTO::getOrgId)
            .collect(Collectors.toList());

        // 项目审计程序审批批次id
        List<Long> ppApproveIds = new LinkedList<>();
        // key:项目审计程序审批批次id,value:状态
        Map<Long, Integer> ppApproveIdAndStatusMap = new LinkedHashMap<>();

        List<Long> globalRiskIds = new LinkedList<>();

        auditorIdAndPpsMap.forEach((auditoryId, innerPps) -> {
            AuditPpApprove auditPpApprove = this.dao.create(projectId);

            Long id = auditPpApprove.getId();
            Integer status = auditPpApprove.getStatus();

            // 审批人 = 所有审批人 - 当前项目审计程序审计人
            List<String> currentApproverIds = allApproverIds.stream()
                .filter(item -> !Objects.equals(auditoryId, item)).collect(
                    Collectors.toList());

            // 项目审计程序id
            List<Long> currentInnerPpIds = new LinkedList<>();
            // key: 项目审计程序id,value: 审计程序idMap
            Map<Long, Long> ppIdAndProcedureIdMap = new HashMap<>(innerPps.size());
            // key: 项目审计程序id,value: 状态Map
            Map<Long, Integer> ppidAndStatusMap = new HashMap<>(innerPps.size());

            List<Long> riskIds = new LinkedList<>();
            // key:风险点id,value:审计程序idMap
            Map<Long, Long> riskIdAndProcedureIdMap = new LinkedHashMap<>();

            for (AuditProjectProcedure ppInfo : innerPps) {
                Long ppInfoId = ppInfo.getId();
                Long procedureId = ppInfo.getProcedureId();
                Integer ppStatus = ppInfo.getStatus();
                currentInnerPpIds.add(ppInfoId);
                ppIdAndProcedureIdMap.put(ppInfoId, procedureId);
                ppidAndStatusMap.put(ppInfoId, ppStatus);

                List<AuditRiskBaseInfoDTO> innerRisks = ppIdAndNeedApproveRiskMap.get(
                    ppInfoId);
                if (CollUtil.isNotEmpty(innerRisks)) {
                    innerRisks.forEach(item -> {
                        Long riskId = item.getId();
                        Long innerProcedureId = item.getProcedureId();
                        riskIds.add(riskId);
                        globalRiskIds.add(riskId);
                        riskIdAndProcedureIdMap.put(riskId, innerProcedureId);
                    });
                }
            }

            if (CollUtil.isEmpty(currentApproverIds)) {
                log.error(
                    "项目审计程序 {} 对应审计人为 {},当前项目 {} 缺少除当前审计人之外的审批人,无法提交审批风险点",
                    currentInnerPpIds, auditoryId, projectId);
                Map<String, String> orgIdAndOrgNameMap = this.orgService.getOrgNameMapByOrgIdList(
                    Collections.singletonList(auditoryId));
                String orgName = MapUtils.getString(orgIdAndOrgNameMap, auditoryId, auditoryId);
                throw new BussinessException(LESS_APPROVER_CANT_SUBMIT, orgName);
            }

            this.ppApproverService.saveApprovers(id, currentApproverIds);

            // 项目审计程序审批结果
            this.ppApproveResultService.save(PpApproveResultCreateParamDTO.builder()
                .approveId(id)
                .projectId(projectId)
                .ppIds(currentInnerPpIds)
                .ppIdAndProcedureIdMap(ppIdAndProcedureIdMap)
                .ppidAndStatusMap(ppidAndStatusMap)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

            // 风险点审批结果
            this.riskApproveResultService.save(RiskApproveResultCreateParamDTO.builder()
                .approveId(id)
                .projectId(projectId)
                .riskIds(riskIds)
                .riskIdAndProcedureIdMap(riskIdAndProcedureIdMap)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

            ppApproveIds.add(id);
            ppApproveIdAndStatusMap.put(id, status);
        });

        // 更新项目审计程序审批状态
        this.publishPpStatusChangeEvent(pps, operatorId, opTime);

        // 更新审计风险点审批状态
        if (CollUtil.isNotEmpty(globalRiskIds)) {
            this.publishRiskStatusChangeEvent(globalRiskIds, operatorId, opTime);
        }

        if (CollUtil.isNotEmpty(ppApproveIds)) {
            // 发布 项目审计程序审批 状态变动
            AuditPpApprvStatusChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(ppApproveIds)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(ppApproveIdAndStatusMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审计实施-提交")
                    .build());
        }
    }

    private void publishPpStatusChangeEvent(List<AuditProjectProcedure> pps, String operatorId,
        Date opTime) {
        List<AuditProjectProcedure> needUpdatePps = new LinkedList<>();
        Map<Long, Integer> ppIdAndOldStatuaMap = new HashMap<>();
        Map<Long, AuditProjectProcedure> ppIdAndOldValueMap = new HashMap<>();
        Map<Long, AuditProjectProcedure> ppIdAndNewValueMap = new HashMap<>();
        Integer newStatus = AuditRiskStatusConstant.APPROVING;
        pps.forEach(pp -> {
            Long ppId = pp.getId();
            Integer oldStatus = pp.getStatus();

            if (Objects.equals(AuditRiskStatusConstant.DRAFT, oldStatus)) {
                AuditProjectProcedure oldValue = JsonUtil.jsonCopy(pp,
                    AuditProjectProcedure.class);
                ppIdAndOldValueMap.put(ppId, oldValue);

                pp.setStatus(newStatus);
                ppIdAndOldStatuaMap.put(ppId, oldStatus);
                needUpdatePps.add(pp);
                ppIdAndNewValueMap.put(ppId, pp);
            }
        });

        if (CollUtil.isNotEmpty(needUpdatePps)) {
            this.ppDao.updateBatchById(needUpdatePps);

            // 发布 项目审计程序 变动时间
            AuditPpChangeEventUtil.publish(
                RecordChangeLogBatchAddParam2DTO.<AuditProjectProcedure>builder()
                    .foreignKeys(ppIdAndOldValueMap.keySet())
                    .oldValueMap(ppIdAndOldValueMap)
                    .newValueMap(ppIdAndNewValueMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审计实施-提交")
                    .build());

            // 发布 项目审计程序状态 变动时间
            AuditPpStatusChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParamDTO.<Integer>builder()
                    .foreignKeys(ppIdAndOldStatuaMap.keySet())
                    .oldValueMap(ppIdAndOldStatuaMap)
                    .newValue(newStatus)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审计实施-提交")
                    .build());
        }
    }

    private void publishRiskStatusChangeEvent(List<Long> riskIds, String operatorId,
        Date opTime) {
        List<AuditRisk> risks = this.riskDAO.listByIds(riskIds);

        Map<Long, Integer> idAndOldStatuaMap = new HashMap<>();
        Map<Long, AuditRisk> idAndOldValueMap = new HashMap<>();
        Map<Long, AuditRisk> idAndNewValueMap = new HashMap<>();
        Integer newStatus = AuditRiskStatusConstant.APPROVING;
        risks.forEach(risk -> {
            Long riskId = risk.getId();
            Integer oldStatus = risk.getStatus();

            AuditRisk oldValue = JsonUtil.jsonCopy(risk,
                AuditRisk.class);
            idAndOldValueMap.put(riskId, oldValue);

            risk.setStatus(newStatus);
            idAndOldStatuaMap.put(riskId, oldStatus);
            idAndNewValueMap.put(riskId, risk);
        });

        this.riskDAO.updateBatchById(risks);

        // 发布 项目审计程序 变动时间
        AuditRiskChangeEventUtil.publish(
            RecordChangeLogBatchAddParam2DTO.<AuditRisk>builder()
                .foreignKeys(idAndOldValueMap.keySet())
                .oldValueMap(idAndOldValueMap)
                .newValueMap(idAndNewValueMap)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("审计实施-提交")
                .build());

        // 发布 项目审计程序状态 变动时间
        AuditRiskStatusChangeEventPublishUtil.publishEvent(
            RecordChangeLogBatchAddParamDTO.<Integer>builder()
                .foreignKeys(idAndOldStatuaMap.keySet())
                .oldValueMap(idAndOldStatuaMap)
                .newValue(newStatus)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("审计实施-提交")
                .build());
    }

    private String formatProcedureName(List<Long> procedureIds) {
        List<AuditProcedureBaseInfoDTO> list = this.procedureService.findExistsBaseInfoIds(
            procedureIds);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(AuditProcedureBaseInfoDTO::getName)
                .collect(Collectors.joining(","));
        }
        return procedureIds.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(PpApproveSubmitParamDTO param) {
        Long approveId = param.getApproveId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        log.info("操作人 [{}] 提交项目审计程序审批 [{}]", operatorId, approveId);

        AuditPpApprove entity = this.dao.getById(approveId);
        if (Objects.isNull(entity)) {
            log.error("审批数据 {} 不存在", approveId);
            throw new BussinessException(APPROVE_DATA_NOT_EXISTS_CANT_SUBMIT);
        }

        Integer status = entity.getStatus();
        if (Objects.equals(AuditRiskApproveStatusConstant.SUBMIT, status)) {
            log.error("当前数据 {} 已提交,无法再次提交", approveId);
            throw new BussinessException(ALREADY_SUBMITED_CANT_SUBMIT_AGAIN);
        }
        if (Objects.equals(status, AuditRiskApproveStatusConstant.TERMINATE)) {
            log.error("当前审批数据approvId {} 已终止,无法再次审批", approveId);
            throw new BussinessException(DATA_TERMINATED_CANT_APPROVE);
        }

        Long projectId = entity.getProjectId();

        AuditProjectCheckUtil.checkProjectApprover(
            AuditProjectAndOpeatorIdBaseDTO.builder()
                .projectId(projectId)
                .operatorId(operatorId)
                .build(), "提交项目审计程序审批",
            AuditPpApproveMessageConstant.CANT_APPROVE_PP_NOT_APPROVER,
            AuditPpApproveMessageConstant.CANT_APPROVE_PP_NO_APPROVER);

        this.checkPpApproveResultStatus(approveId);

        List<RiskApproveResultBaseInfoDTO> riskApproveResultList = this.riskApproveResultService.findExistByApproveId(
            approveId);
        if (CollUtil.isNotEmpty(riskApproveResultList)) {
            this.checkRiskApproveResultStatus(riskApproveResultList);
        }

        int newStatus = AuditRiskApproveStatusConstant.SUBMIT;
        entity.setStatus(newStatus);
        this.dao.updateById(entity);

        AuditPpApprvStatusChangeEventPublishUtil.publishEvent(
            RecordChangeLogAddParamDTO.<Integer>builder()
                .foreignKey(approveId)
                .oldValue(status)
                .newValue(newStatus)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminate(PpApproveTerminateParamDTO param) {
        Long projectId = param.getProjectId();
        String opRemark = param.getOpRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        List<AuditPpApprove> list = this.dao.findExistsByProjectId(
            Collections.singleton(projectId));
        if (CollUtil.isNotEmpty(list)) {
            List<AuditPpApprove> needTerminateData = new LinkedList<>();
            int newStatus = AuditRiskApproveStatusConstant.TERMINATE;

            List<Long> needTerminatedIdList = new LinkedList<>();
            Map<Long, Integer> idAndOldStatusMap = new HashMap<>(list.size());

            list.forEach(item -> {
                Long id = item.getId();
                Integer oldStatus = item.getStatus();

                // 只看未提交的数据
                if (Objects.equals(oldStatus, AuditRiskApproveStatusConstant.NOT_SUBMIT)) {
                    item.setStatus(newStatus);
                    needTerminatedIdList.add(id);
                    idAndOldStatusMap.put(id, oldStatus);
                    needTerminateData.add(item);
                }
            });

            if (CollUtil.isNotEmpty(needTerminateData)) {
                this.dao.updateBatchById(needTerminateData);

                AuditPpApprvStatusChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParamDTO.<Integer>builder()
                        .foreignKeys(needTerminatedIdList)
                        .oldValueMap(idAndOldStatusMap)
                        .newValue(newStatus)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark(opRemark)
                        .build());
            }
        } else {
            log.info("[{}]时根据项目id[{}]查询不到项目审计程序审批数据", opRemark, projectId);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchApprove(PpAndRiskBatchApproveParamDTO param) {
        List<Long> ppApproveResultIds = param.getPpApproveResultIds();
        List<Long> riskApproveResultIds = param.getRiskApproveResultIds();
        Integer approveResult = param.getApproveResult();
        String approveRemark = param.getApproveRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        List<Long> ppApprovedResultIds = this.ppApproveResultService.batchApprove(
            PpApproveResultBatchApproveParamDTO.builder()
                .ppApproveResultIds(ppApproveResultIds)
                .approveResult(approveResult)
                .approveRemark(approveRemark)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());
        List<Long> riskApprovedResultIds = this.riskApproveResultService.batchApprove(
            RiskApproveResultBatchApproveParamDTO.builder()
                .approveResultIds(riskApproveResultIds)
                .approveResult(approveResult)
                .approveRemark(approveRemark)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

        if (CollUtil.isEmpty(ppApprovedResultIds)
            && CollUtil.isEmpty(riskApprovedResultIds)) {
            log.error("操作人[{}]批量审批提交的数据 {} 为空", operatorId,
                JsonUtil.toJsonString(param));
            throw new BussinessException(DATA_EMPTY_CANT_SAVE);
        }

    }

    private void checkRiskApproveResultStatus(
        List<RiskApproveResultBaseInfoDTO> riskApproveResultList) {
        List<RiskApproveResultBaseInfoDTO> draftList = riskApproveResultList.stream()
            .filter(item -> {
                Integer approveResult = item.getApproveResult();
                return Objects.equals(AuditRiskApproveResultConstant.DRAFT, approveResult);
            }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(draftList)) {
            List<Long> riskApproveIds = draftList.stream().map(RiskApproveResultBaseInfoDTO::getId)
                .collect(
                    Collectors.toList());
            List<Long> procedureIds = draftList.stream()
                .map(RiskApproveResultBaseInfoDTO::getProcedureId)
                .collect(Collectors.toList());
            List<Long> riskIds = draftList.stream().map(RiskApproveResultBaseInfoDTO::getRiskId)
                .collect(Collectors.toList());
            log.error("存在未审批的风险点审批数据 {} 无法提交", riskApproveIds);
            throw new BussinessException(CANT_SUBMIT_BECAUSE_RISK_NOT_AUDIT,
                this.formatProcedureName(procedureIds), this.formatRiskName(riskIds));
        }
    }

    private String formatRiskName(List<Long> riskIds) {
        List<AuditRiskBaseInfoDTO> list = this.auditRiskService.findExistsByIds(riskIds);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(AuditRiskBaseInfoDTO::getName)
                .collect(Collectors.joining(","));
        }
        return riskIds.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    private void checkPpApproveResultStatus(Long approveId) {
        List<PpApproveResultBaseInfoDTO> list = this.ppApproveResultService.findExistByApproveId(
            approveId);

        List<PpApproveResultBaseInfoDTO> draftList = list.stream().filter(item -> {
            Integer approveResult = item.getApproveResult();
            return Objects.equals(AuditRiskApproveResultConstant.DRAFT, approveResult);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(draftList)) {
            List<Long> approveIds = draftList.stream().map(PpApproveResultBaseInfoDTO::getId)
                .collect(Collectors.toList());
            List<Long> procedureIds = draftList.stream()
                .map(PpApproveResultBaseInfoDTO::getProcedureId)
                .collect(Collectors.toList());
            log.error("存在未审批的项目审计程序审批数据 {} 无法提交", approveIds);

            throw new BussinessException(CANT_SUBMIT_BECAUSE_PROCEDURE_NOT_AUDIT,
                this.formatProcedureName(procedureIds));
        }
    }

    @Override
    public List<PpConfirmResultDTO> findPpConfirmResult(PpConfirmBatchParamDTO param) {
        List<Long> ppIds = param.getIds();
        String operatorId = param.getOperatorId();

        List<AuditProjectProcedureInfoDTO> infos = this.ppService.findByIds(ppIds);
        if (CollUtil.isNotEmpty(infos)) {
            List<PpConfirmResultDTO> resultList = new LinkedList<>();

            List<AuditProjectProcedureInfoDTO> notAuditProcedureList = new LinkedList<>();

            infos.forEach(item -> {
                Integer status = item.getStatus();
                String auditorId = item.getAuditorId();

                if (Objects.equals(AuditRiskStatusConstant.NOT_AUDIT, status)) {
                    notAuditProcedureList.add(item);
                    return;
                }

                if (!Objects.equals(auditorId, operatorId)) {
                    PpConfirmResultDTO result = new PpConfirmResultDTO();
                    result.setId(item.getId());
                    result.setProcedureId(item.getProcedureId());
                    result.setAuditorId(item.getAuditorId());
                    resultList.add(result);
                }
            });

            if (CollUtil.isNotEmpty(notAuditProcedureList)) {
                List<Long> ids = notAuditProcedureList.stream()
                    .map(AuditProjectProcedureInfoDTO::getId).collect(
                        Collectors.toList());
                List<Long> procedureIds = notAuditProcedureList.stream()
                    .map(AuditProjectProcedureInfoDTO::getProcedureId).collect(
                        Collectors.toList());
                log.error("项目审计程序id: {} 为未审计数据,未保存过无法提交审批",
                    ids);
                throw new BussinessException(CANT_APPROVE_PP_NO_STATUS, this.formatProcedureName(
                    procedureIds));
            }

            return resultList;
        }
        return Collections.emptyList();
    }

    @Override
    public PpConfirmResultDTO loadPpConfirmResult(PpConfirmSingleParamDTO param) {
        Long ppId = param.getId();
        String operatorId = param.getOperatorId();

        AuditProjectProcedureInfoDTO info = this.ppService.getById(ppId);
        if (Objects.nonNull(info)) {
            String auditorId = info.getAuditorId();

            // 允许未保存过直接提交
            if (Objects.nonNull(auditorId)) {
                if (!Objects.equals(auditorId, operatorId)) {
                    PpConfirmResultDTO result = new PpConfirmResultDTO();
                    result.setId(info.getId());
                    result.setProcedureId(info.getProcedureId());
                    result.setAuditorId(info.getAuditorId());
                    return result;
                }
            }
        }
        return null;
    }

    @Override
    public PpApproveBaseInfoDTO getBaseInfo(Long id) {
        return PpApproveConvertUtil.makeEntity2BaseInfoDTO(this.dao.getById(id));
    }

    @Override
    public PpApproveDetailDTO loadDetail(Long id) {
        PpApproveDetailDTO result = PpApproveConvertUtil.makeEntity2DetailDTO(
            this.dao.getById(id));

        Long projectId = result.getProjectId();

        AuditProjectInfoDTO project = this.projectService.getExistsProjectInfoById(
            projectId);
        if (Objects.nonNull(project)) {
            String projectName = project.getName();
            result.setProjectName(projectName);

            Long planId = project.getPlanId();

            AuditPlanInfoDTO planInfo = this.planService.getInfoById(planId);
            if (Objects.nonNull(planInfo)) {
                result.setPlanName(planInfo.getName());
            } else {
                log.warn("根据计划id {} 查不到对应计划数据", planId);
            }
        } else {
            log.warn("根据项目id {} 查不到对应项目数据", projectId);
        }

        return result;
    }
}
