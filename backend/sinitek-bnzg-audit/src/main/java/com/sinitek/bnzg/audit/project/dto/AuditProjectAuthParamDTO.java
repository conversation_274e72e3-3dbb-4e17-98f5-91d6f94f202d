package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目权限参数")
public class AuditProjectAuthParamDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("人员Id")
    private String orgId;

    @ApiModelProperty("当前阶段步骤实例")
    private StageStepExampleDTO stageStepExample;

    @ApiModelProperty("当前阶段实例")
    private StageExampleDTO stageExample;

    @ApiModelProperty(value = "当前是否调试")
    private Boolean debugFlag = false;
}
