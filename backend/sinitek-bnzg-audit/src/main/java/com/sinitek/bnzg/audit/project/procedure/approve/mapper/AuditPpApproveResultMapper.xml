<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.project.procedure.approve.mapper.AuditPpApproveResultMapper">


  <select id="search" resultType="com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchResultPO">
          select
                '0' data_type,
                r.id,
                r.approve_id,
                r.pp_id,
                r.project_id,
                r.procedure_id,
                p.name,
                pp.execution,
                pp.auditor_id,
                pp.audit_date,
                r.approve_result,
                r.operator_id,
                r.op_time
           from audit_pp_approve_result r,
                audit_project_procedure pp,
                audit_procedure p
          where pp.id = r.pp_id
            and r.remove_flag = 0
            and r.approve_id = #{param.approveId}
            and pp.procedure_id = p.id
           <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
              order by r.sort asc
           </if>
  </select>

</mapper>
