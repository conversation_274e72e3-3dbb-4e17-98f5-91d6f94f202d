package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目审计程序审批结果-分页查询DTO")
public class PpApproveResultSearchParamDTO extends PageDataParam {

    @NotNull(message = "审批id不能为空")
    @ApiModelProperty(value = "审批id")
    private Long approveId;

}
