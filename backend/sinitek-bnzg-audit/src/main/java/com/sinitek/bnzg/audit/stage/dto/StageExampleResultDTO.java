package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel("阶段步骤实例")
public class StageExampleResultDTO {

    @ApiModelProperty("阶段实例id")
    private Long id;

    @ApiModelProperty("阶段实例状态")
    private Integer status;

    @ApiModelProperty("阶段实例状态名称")
    private String statusName;

    @ApiModelProperty("步骤")
    private List<StageStepExampleResultDTO> stepList;

}
