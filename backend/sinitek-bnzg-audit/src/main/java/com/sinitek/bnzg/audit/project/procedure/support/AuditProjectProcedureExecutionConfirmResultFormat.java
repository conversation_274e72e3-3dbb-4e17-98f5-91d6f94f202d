package com.sinitek.bnzg.audit.project.procedure.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmResultDTO;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditProjectProcedureExecutionConfirmResultFormat implements
    ITableResultFormat<PpConfirmResultDTO> {

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Override
    public List<PpConfirmResultDTO> format(
        List<PpConfirmResultDTO> data) {

        List<Long> procedureIds = new LinkedList<>();
        List<String> auditorIds = new LinkedList<>();
        data.forEach(item -> {
            Long procedureId = item.getProcedureId();
            String auditorId = item.getAuditorId();

            procedureIds.add(procedureId);
            auditorIds.add(auditorId);
        });

        // key: Id
        // value: 名称
        List<AuditProcedureBaseInfoDTO> list = this.procedureService.findExistsBaseInfoIds(
            procedureIds);
        Map<Long, String> procedureIdAndNameMap;
        if (CollUtil.isNotEmpty(list)) {
            procedureIdAndNameMap = list.stream().collect(
                Collectors.toMap(AuditProcedureBaseInfoDTO::getId,
                    AuditProcedureBaseInfoDTO::getName));
        } else {
            procedureIdAndNameMap = Collections.emptyMap();
        }

        List<OrgObjectDTO> orgObjects = this.orgService.findOrgObjectsByOrgIds(auditorIds);
        Map<String, String> orgIdAndNameMap;
        if (CollUtil.isNotEmpty(orgObjects)) {
            orgIdAndNameMap = orgObjects.stream().collect(
                Collectors.toMap(OrgObjectDTO::getOrgId,
                    OrgObjectDTO::getOrgName));
        } else {
            orgIdAndNameMap = Collections.emptyMap();
        }

        data.forEach(item -> {
            Long procedureId = item.getProcedureId();
            String auditorId = item.getAuditorId();

            item.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId));
            item.setName(MapUtils.getString(procedureIdAndNameMap, procedureId));
        });

        return data;
    }
}
