package com.sinitek.bnzg.audit.project.procedure.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpChangeParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpFullDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpSaveOrSubmitWorkParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureExecutionListResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmBatchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpConfirmSingleParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpExecutionSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureExecutionService;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.procedure.support.AuditPpFullDetailDTOFormat;
import com.sinitek.bnzg.audit.project.procedure.support.AuditProjectProcedureExecutionConfirmResultFormat;
import com.sinitek.bnzg.audit.project.procedure.support.AuditProjectProcedureExecutionSearchResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目审计程序 Controller
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@RestController
@RequestMapping("/frontend/api/audit/project/procedure/execution")
@Api(value = "/frontend/api/audit/project/procedure/execution", tags = "审计系统-项目审计程序")
public class ProjectProcedureExecutionController {

    @Autowired
    private IAuditProjectProcedureService auditProjectProcedureService;

    @Autowired
    private IAuditProjectProcedureExecutionService ppExecutionService;

    @Autowired
    private AuditProjectProcedureExecutionSearchResultFormat auditProjectProcedureSearchResultFormat;

    @Autowired
    private AuditProjectProcedureExecutionConfirmResultFormat auditProjectProcedureExecutionConfirmResultFormat;

    @Autowired
    private AuditPpFullDetailDTOFormat auditPpFullDetailDTOFormat;

    @Autowired
    private IPpApproveService ppApproveService;

    @ApiOperation(value = "审计工作-保存项目程序信息")
    @PostMapping("/save-work-info")
    public RequestResult<Void> save(
        @Validated @RequestBody AuditPpSaveOrSubmitWorkParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.auditProjectProcedureService.saveWork(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "审计工作-提交项目程序信息")
    @PostMapping("/submit-work-info")
    public RequestResult<Void> submitWorkInfo(
        @Validated @RequestBody AuditPpSaveOrSubmitWorkParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.auditProjectProcedureService.submitWork(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "审计实施-项目程序变更")
    @PostMapping("/change")
    public RequestResult<Void> change(
        @Validated @RequestBody AuditPpChangeParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        Boolean copyAttachmentFlag = param.getCopyAttachmentFlag();
        if (Objects.isNull(copyAttachmentFlag)) {
            param.setCopyAttachmentFlag(true);
        }
        this.auditProjectProcedureService.changeProjectProcedure(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "取消变更")
    @PostMapping("/cancel-change")
    public RequestResult<Void> cancelChange(
        @RequestBody @Validated AuditPpChangeParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.auditProjectProcedureService.cancelChangeProjectProcedure(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "审计程序提交审批(外层表格单个或多个提交)")
    @PostMapping("/submit")
    public RequestResult<Void> submit(@RequestBody @Validated PpApproveCreateParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.ppApproveService.create(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "审计实施-项目程序风险点查询")
    @PostMapping("/search")
    public TableResult<AuditProjectProcedureExecutionListResultDTO> list(
        @RequestBody PpExecutionSearchParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        IPage<AuditProjectProcedureExecutionListResultDTO> result = this.ppExecutionService.searchPpExecutionList(
            param);
        return param.build(result, this.auditProjectProcedureSearchResultFormat);
    }

    @ApiOperation(value = "提交前获取审计程序确认信息")
    @PostMapping("/list-procedure-confirm")
    public RequestResult<List<PpConfirmResultDTO>> findPpConfirmInfo(
        @RequestBody @Validated PpConfirmBatchParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        List<PpConfirmResultDTO> result = this.ppApproveService.findPpConfirmResult(param);
        return new RequestResult<>(
            this.auditProjectProcedureExecutionConfirmResultFormat.format(result));
    }

    @ApiOperation(value = "提交前获取审计程序确认信息")
    @PostMapping("/load-procedure-confirm")
    public RequestResult<PpConfirmResultDTO> getPpConfirmInfo(
        @RequestBody @Validated PpConfirmSingleParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        PpConfirmResultDTO result = this.ppApproveService.loadPpConfirmResult(param);
        if (Objects.nonNull(result)) {
            List<PpConfirmResultDTO> list = this.auditProjectProcedureExecutionConfirmResultFormat.format(
                Collections.singletonList(result));
            return new RequestResult<>(list.get(0));
        }
        return new RequestResult<>(null);
    }

    /**
     * 检查项目审计程序状态,如果没有风险点则项目审计程序必须审批通过
     * 检查风险点状态,风险点状态全为审批不通过或完成
     */
    @ApiOperation(value = "检查审计实施能否完成")
    @GetMapping("/check-status")
    public RequestResult<AuditCheckResultDTO> checkStatus(
        @RequestParam("projectId") Long projectId) {
        AuditCheckResultDTO checkResult = this.ppExecutionService.checkCanFinish(
            projectId);
        return new RequestResult<>(checkResult);
    }

    @ApiOperation(value = "根据项目审计程序id加载项目审计程序详情")
    @GetMapping("/detail")
    public RequestResult<AuditPpFullDetailDTO> loadDetailByPpId(
        @RequestParam("id") Long id) {
        AuditPpFullDetailDTO result = this.ppExecutionService.loadDetailById(id);
        if (Objects.isNull(result)) {
            return RequestResult.fail(AuditPpMessageCodeConstant.CANT_OPERATE_BECAUSEOF_PROJECT_PROCEDURE);
        }
        this.auditPpFullDetailDTOFormat.format(Collections.singletonList(result));
        return new RequestResult<>(result);
    }
}
