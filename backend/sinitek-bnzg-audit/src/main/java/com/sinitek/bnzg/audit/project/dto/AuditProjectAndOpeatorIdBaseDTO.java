package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024-10-10 11:29
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AuditProjectAndOpeatorIdBaseDTO implements IAuditProjectAndOpeatorId {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;
}
