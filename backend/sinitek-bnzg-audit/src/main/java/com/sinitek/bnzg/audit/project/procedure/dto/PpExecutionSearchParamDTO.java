package com.sinitek.bnzg.audit.project.procedure.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计文档 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "审计文档历史分页查询DTO")
public class PpExecutionSearchParamDTO extends PageDataParam {

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;
}
