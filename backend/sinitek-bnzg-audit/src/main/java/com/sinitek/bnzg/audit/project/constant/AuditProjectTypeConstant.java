package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目进度
 *
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectTypeConstant {

    /**
     * 计划内
     */
    public static final int WITHIN_PLAN = 1;

    /**
     * 计划外
     */
    public static final int OUTOF_PLAN = 2;

}
