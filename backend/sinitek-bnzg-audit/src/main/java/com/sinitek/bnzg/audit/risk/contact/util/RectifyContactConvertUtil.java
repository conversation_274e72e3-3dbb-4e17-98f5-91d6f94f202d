package com.sinitek.bnzg.audit.risk.contact.util;

import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactBaseDTO;
import com.sinitek.bnzg.audit.risk.contact.entity.RectifyContact;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 09/11/2024 17:26
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RectifyContactConvertUtil {

    public static RectifyContactBaseDTO makeEntity2BaseDTO(RectifyContact entity) {
        return LcConvertUtil.convert(entity, RectifyContactBaseDTO::new);
    }

}
