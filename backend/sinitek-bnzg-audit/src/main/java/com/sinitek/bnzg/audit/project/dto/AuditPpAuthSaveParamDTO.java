package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("授权用户保存参数")
public class AuditPpAuthSaveParamDTO implements IAuditProjectAndOpeatorId {

    @NotNull(message = "项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotNull(message = "审计程序不能为空")
    @ApiModelProperty("程序id")
    private Long procedureId;

    @ApiModelProperty("查看")
    private List<String> viewers;

    @ApiModelProperty("审阅")
    private List<String> approvers;

    @ApiModelProperty("编辑")
    private List<String> editors;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;
}
