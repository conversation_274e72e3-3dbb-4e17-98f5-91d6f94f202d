<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.risk.statistics.mapper.AuditRiskStatisticsMapper">

    <select id="searchRiskStatistics"
            resultType="com.sinitek.bnzg.audit.risk.statistics.po.AuditRiskStatisticsReasultPO">
        select
            apl.name as plan_name,
            ap.name as project_name,
            ar.name as risk_name,
            apd.name as procedure_name,
            ar.id as risk_id,
            ar.project_id,
            ap.plan_id,
            ar.procedure_id,
            apl.year as audit_year,
            ar.type as risk_type,
            ar.level as risk_level,
            ar.inner_category,
            ar.auditor_id,
            ar.audit_date,
            ar.rectify_state,
            ar.req_rectify_date,
            ar.act_rectify_date,
            ar.resp_dept_sug_type,
            ar.resp_dept_suggestion,
            ar.first_catalog,
            ar.repeat_flag,
            ar.description
        from
            audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
                left join audit_procedure apd
                          on apd.id = ar.procedure_id
                              and apd.remove_flag = 0
        where ar.status = 100
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase != 7
        <include refid="commonQueryConditions"/>
        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
        order by ar.audit_date desc
        </if>
    </select>

    <select id="findAuditYear"
            resultType="java.lang.Integer">
        select distinct year
          from audit_plan
         where remove_flag = 0
         group by year
         order by year desc
    </select>

    <select id="findRiskStatisticsChart"
            resultType="com.sinitek.bnzg.audit.risk.statistics.po.RiskStatisticsChartReasultPO">
        select
            ar.inner_category, COUNT(*) AS category_count
        from
            audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
        where ar.status = 100
        and ar.thread_latest_flag = 1
        and ar.remove_flag = 0
        and ap.project_phase != 7
        <include refid="commonQueryConditions"/>
        GROUP BY ar.inner_category;
    </select>

    <select id="findAuditFindRectifyChart"
            resultType="com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartReasultPO">
        select
            ap.id as project_id,
            ap.name as project_name,
            apl.name as plan_name,
            COUNT(*) AS total_risk_count,
            SUM(CASE WHEN ar.rectify_state = 3 THEN 1 ELSE 0 END) AS finish_risk_count
        from
            audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
        where ar.status = 100
        and ar.thread_latest_flag = 1
        and ar.remove_flag = 0
        and ap.project_phase != 7
        <include refid="commonQueryConditions"/>
        GROUP BY
            ap.id, ap.name;
    </select>

    <select id="searchAuditFindRectifyChartDetail"
            resultType="com.sinitek.bnzg.audit.risk.statistics.po.AuditRiskStatisticsReasultPO">
        select
          apl.name as plan_ame,
          ap.name as project_name,
          ar.name as risk_name,
          apd.name as procedure_name,
          ar.id as risk_id,
          ar.project_id,
          ap.plan_id,
          ar.procedure_id,
          apl.year as audit_year,
          ar.type as risk_type,
          ar.level as risk_level,
          ar.inner_category,
          ar.auditor_id,
          ar.audit_date,
          ar.rectify_state,
          ar.req_rectify_date,
          ar.act_rectify_date
        from audit_risk ar
             left join audit_project ap
                    on ar.project_id = ap.id
                   and ap.remove_flag = 0
             left join audit_plan apl
                    on apl.id = ap.plan_id
                   and apl.remove_flag = 0
             left join audit_procedure apd
                    on apd.id = ar.procedure_id
                   and apd.remove_flag = 0
        where ar.status = 100
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase != 7
        <include refid="commonQueryConditions"/>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.rectifyStates)">
            and ar.rectify_state in
            <foreach collection="param.rectifyStates" index="index" item="rectifyState" open="(" separator="," close=")">
                #{rectifyState}
            </foreach>
        </if>
        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
        order by ar.audit_date desc
        </if>
    </select>

    <sql id="commonQueryConditions">
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.projectIds)">
            <!-- 项目Id查询 -->
            and ap.id in
            <foreach collection="param.projectIds" index="index" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.projectNames)">
            <!-- 项目名称和计划名称查询合并 -->
            and
            <foreach collection="param.projectNames" index="index" item="projectName" open="(" separator="or" close=")">
                ap.name LIKE CONCAT('%', #{projectName}, '%') ESCAPE '/'
                or apl.name LIKE CONCAT('%', #{projectName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.riskNames)">
            <!-- 风险名称查询 -->
            and
            <foreach collection="param.riskNames" index="index" item="riskName" open="(" separator="or" close=")">
                ar.name LIKE CONCAT('%', #{riskName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.riskType)">
            and ar.type in
            <foreach collection="param.riskType" index="index" item="risk" open="(" separator="," close=")">
                #{risk}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.riskLevel)">
            and ar.level in
            <foreach collection="param.riskLevel" index="index" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.firstCatalog)">
            and ar.first_catalog in
            <foreach collection="param.firstCatalog" index="index" item="catalog" open="(" separator="," close=")">
                #{catalog}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.innerCategory)">
            and ar.inner_category in
            <foreach collection="param.innerCategory" index="index" item="inner" open="(" separator="," close=")">
                #{inner}
            </foreach>
        </if>
        <if test="@cn.hutool.core.util.ObjectUtil@isNotEmpty(param.auditStartDate)">
            and ar.audit_date  <![CDATA[ >= ]]> #{param.auditStartDate}
        </if>
        <if test="@cn.hutool.core.util.ObjectUtil@isNotEmpty(param.auditEndDate)">
            and ar.audit_date  <![CDATA[ <= ]]> #{param.auditEndDate}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.auditYears)">
            and apl.year in
            <foreach collection="param.auditYears" index="index" item="auditYear" open="(" separator="," close=")">
                #{auditYear}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.respDeptSugTypes)">
            and ar.resp_dept_sug_type in
            <foreach collection="param.respDeptSugTypes" index="index" item="respDeptSugType" open="(" separator="," close=")">
                #{respDeptSugType}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.rectifyStates)">
            and ar.rectify_state in
            <foreach collection="param.rectifyStates" index="index" item="rectifyState" open="(" separator="," close=")">
                #{rectifyState}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.respDeptIds)">
            <!-- 责任部门Id查询 -->
            AND ar.id IN (select distinct risk_id from audit_risk_resp_dept where resp_dept_id IN
            <foreach collection="param.respDeptIds" index="index" item="respDeptId" open="(" separator="," close=")">
                #{respDeptId}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.businessUnits)">
            <!-- 业务部门查询 -->
            AND apd.id IN (select distinct p_id from audit_procedure_bus_unit_mv where org_id IN
            <foreach collection="param.businessUnits" index="index" item="businessUnit" open="(" separator="," close=")">
                #{businessUnit}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.util.ObjectUtil@isNotEmpty(param.repeatFlag)">
            and ar.repeat_flag = #{param.repeatFlag}
        </if>
    </sql>
</mapper>
