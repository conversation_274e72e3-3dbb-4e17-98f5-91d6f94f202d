package com.sinitek.bnzg.audit.project.procedure.approve.log.result.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditPpApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.service.IAuditPpApprvResultChangeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpApprvResultChangeEventListener<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, Integer> {

    @Autowired
    private IAuditPpApprvResultChangeLogService logService;

    @Override
    protected String getEventName() {
        return "项目审计程序审批结果变动";
    }

    @Override
    protected IAuditPpApprvResultChangeLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditPpApprvResultChangeEvent.class, fallbackExecution = true)
    public void listen(
        AuditPpApprvResultChangeEvent<E> event) {
        this.doListen(event);
    }
}
