package com.sinitek.bnzg.audit.project.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.entity.AuditProject;
import com.sinitek.bnzg.audit.project.mapper.AuditProjectMapper;
import com.sinitek.bnzg.audit.project.po.AuditProjectQueryParamPO;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchParamPO;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchResultPO;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Slf4j
@Service
public class AuditProjectDAO extends ServiceImpl<AuditProjectMapper, AuditProject> {

    public List<AuditProject> findExistsProjectByPlanIds(Collection<Long> planIds) {
        if (CollUtil.isNotEmpty(planIds)) {
            LambdaQueryWrapper<AuditProject> queryWrapper = Wrappers.lambdaQuery(
                AuditProject.class);
            queryWrapper.in(AuditProject::getPlanId, planIds);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

    public void updateRegulatoryTimeById(Long id, Date regulatoryTime) {
        LambdaUpdateWrapper<AuditProject> updateWrapper = Wrappers.lambdaUpdate(
            AuditProject.class);
        updateWrapper.eq(AuditProject::getId, id);
        updateWrapper.set(AuditProject::getRegulatoryTime, regulatoryTime);
        this.baseMapper.update(null, updateWrapper);

    }

    public List<AuditProject> findAllAuditProject() {
        LambdaQueryWrapper<AuditProject> lambdaQuery = Wrappers.lambdaQuery(AuditProject.class);
        lambdaQuery.eq(AuditProject::getRemoveFlag, CommonBooleanEnum.FALSE.getValue());
        return this.list(lambdaQuery);
    }

    public List<AuditProject> findAuditProjectByNames(Collection<String> names) {
        if (CollUtil.isNotEmpty(names)) {
            LambdaQueryWrapper<AuditProject> lambdaQuery = Wrappers.lambdaQuery(AuditProject.class);
            lambdaQuery.in(AuditProject::getName, names);
            return this.list(lambdaQuery);
        }
        return Collections.emptyList();
    }

    public List<AuditProject> findAuditProjectByNamesAndPlanId(Collection<String> names,
        Long planId) {
        if (CollUtil.isNotEmpty(names)) {
            LambdaQueryWrapper<AuditProject> lambdaQuery = Wrappers.lambdaQuery(AuditProject.class);
            lambdaQuery.in(AuditProject::getName, names);
            lambdaQuery.in(AuditProject::getPlanId, planId);
            return this.list(lambdaQuery);
        }
        return Collections.emptyList();
    }

    public List<AuditProject> listAuditProject(AuditProjectQueryParamPO po) {
        if (Objects.nonNull(po)) {
            List<Long> planIds = po.getPlanIds();
            List<String> projectNames = po.getProjectNames();

            LambdaQueryWrapper<AuditProject> lambdaQuery = Wrappers.lambdaQuery(AuditProject.class);
            lambdaQuery.in(AuditProject::getPlanId, planIds);
            lambdaQuery.or(mapper -> {
                if (CollUtil.isNotEmpty(projectNames)) {
                    projectNames.forEach(item -> {
                        lambdaQuery.like(AuditProject::getName, item);
                    });
                }
            });
            lambdaQuery.eq(AuditProject::getRemoveFlag, CommonBooleanEnum.FALSE.getValue());
            return this.list(lambdaQuery);
        }
        return Collections.emptyList();
    }

    public IPage<AuditProjectSearchResultPO> search(
        Page<AuditProjectSearchResultPO> page, AuditProjectSearchParamPO param) {
        return this.baseMapper.search(page, param);
    }

    public Integer loadProjectAuditYearById(Long projectId) {
        return this.baseMapper.loadProjectAuditYearById(projectId);
    }

}
