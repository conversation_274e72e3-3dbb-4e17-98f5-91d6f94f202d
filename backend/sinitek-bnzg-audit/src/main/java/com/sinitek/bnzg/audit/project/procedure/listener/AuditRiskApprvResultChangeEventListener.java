package com.sinitek.bnzg.audit.project.procedure.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditRiskApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditRiskStatusUpdateParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskExecutionService;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRiskApprvResultChangeEventListener {

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    @Autowired
    private IRiskAccountabilityService accountabilityService;

    @Autowired
    private IAuditRiskExecutionService riskExecutionService;

    /**
     * 监听风险点审批
     * 1.把审批结果,审批反馈写回风险点
     * 2.如果风险点为审批通过则需要将整改状态置为待反馈
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditRiskApprvResultChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        AuditRiskApprvResultChangeEvent<T> event) {
        T source = event.getSource();
        log.info(
            "监听风险点审批把审批结果,审批反馈写回风险点,如果风险点为审批通过则需要将整改状态置为待反馈");
        if (source instanceof RecordChangeLogAddParamDTO) {
            RecordChangeLogAddParamDTO<Integer> data = ((RecordChangeLogAddParamDTO<Integer>) source);
            this.syncRiskData(Collections.singleton(data.getForeignKey()), data.getOperatorId(),
                data.getOpTime());
        } else {
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> data = ((AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source);
            Collection<Long> foreignKeys = data.getForeignKeys();
            Long oneKey = null;
            if (CollUtil.isNotEmpty(foreignKeys)) {
                oneKey = foreignKeys.iterator().next();
            }
            this.syncRiskData(data.getForeignKeys(), data.getOperatorId(oneKey),
                data.getOpTime(oneKey));
        }
    }

    private void syncRiskData(Collection<Long> keys, String gloablOperator, Date globalOpTime) {
        List<RiskApproveResultBaseInfoDTO> list = this.riskApproveResultService.findExistByIds(
            keys);

        List<RiskApproveResultBaseInfoDTO> needSyncDataList = list.stream().filter(item -> {
            Integer approveResult = item.getApproveResult();
            return Objects.equals(approveResult, AuditRiskApproveResultConstant.APPROVED)
                || Objects.equals(approveResult, AuditRiskApproveResultConstant.NOT_APPROVED);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(needSyncDataList)) {
            List<Long> riskIds = new LinkedList<>();
            // key:风险点id,value:新状态
            Map<Long, Integer> idAndStatusMap = new HashMap<>(list.size());
            // key:风险点id,value:审批人
            Map<Long, String> idAndApproverIdMap = new HashMap<>(list.size());
            // key:风险点id,value:审批时间
            Map<Long, Date> idAndApproveTimeMap = new HashMap<>(list.size());
            // key:项目审计程序id,value:审批反馈
            Map<Long, String> idAndApproveRemarkMap = new HashMap<>(list.size());

            list.forEach(item -> {
                Long riskId = item.getRiskId();
                Integer approveResult = item.getApproveResult();
                String operatorId = item.getOperatorId();
                Date opTime = item.getOpTime();
                String approveRemark = item.getApproveRemark();

                riskIds.add(riskId);
                if (Objects.equals(approveResult, AuditRiskApproveResultConstant.APPROVED)) {
                    idAndStatusMap.put(riskId, AuditRiskStatusConstant.APPROVED);
                } else {
                    idAndStatusMap.put(riskId, AuditRiskStatusConstant.NOT_APPROVED);
                }
                idAndApproverIdMap.put(riskId, operatorId);
                idAndApproveTimeMap.put(riskId, opTime);
                idAndApproveRemarkMap.put(riskId, approveRemark);
            });

            log.warn("风险点审批结果 {} 提交,同步风险点 {} 审批数据",
                keys, riskIds);

            // 1,2
            this.riskExecutionService.updateAfterApprove(
                AuditRiskStatusUpdateParamDTO.builder()
                    .riskIds(riskIds)
                    .idAndStatusMap(idAndStatusMap)
                    .idAndApproveRemarkMap(idAndApproveRemarkMap)
                    .idAndApproverIdMap(idAndApproverIdMap)
                    .idAndApproveTimeMap(idAndApproveTimeMap)
                    .operatorId(gloablOperator)
                    .opTime(globalOpTime)
                    .build());

        } else {
            log.info("风险点审批结果 {} 下经过滤不存在须同步的风险点审批数据,无需同步数据",
                keys);
        }
    }

}
