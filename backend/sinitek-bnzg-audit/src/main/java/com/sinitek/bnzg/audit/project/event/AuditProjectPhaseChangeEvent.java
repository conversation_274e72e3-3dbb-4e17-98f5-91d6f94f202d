package com.sinitek.bnzg.audit.project.event;

import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:12
 */
@ApiModel("审计项目阶段变动事件")
public class AuditProjectPhaseChangeEvent extends
    SiniCubeEvent<AuditProjectPhaseChangeEventSourceDTO> {

    public AuditProjectPhaseChangeEvent(AuditProjectPhaseChangeEventSourceDTO source) {
        super(source);
    }
}
