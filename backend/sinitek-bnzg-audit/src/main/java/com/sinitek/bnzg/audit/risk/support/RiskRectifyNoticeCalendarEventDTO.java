package com.sinitek.bnzg.audit.risk.support;

import com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant;
import com.sinitek.sirm.calendar.annotation.CalendarEventDefinition;
import com.sinitek.sirm.calendar.dto.BaseCalendarEventDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * Date  2022/3/23
 */
@CalendarEventDefinition(code = AuditRiskConstant.RISK_RECTIFY_NOTICE_CALENDAR_EVENT, name = "风险点整改提醒日程", color = "#bd6758")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskRectifyNoticeCalendarEventDTO extends BaseCalendarEventDTO {

}
