package com.sinitek.bnzg.audit.stage.service;

import com.sinitek.bnzg.audit.stage.dto.BatchGenerateStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import java.util.Collection;
import java.util.List;

/**
 * 阶段实例 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-15
 */
public interface IStageExampleService {

    Long generateStageExample(GenerateStageExampleParamDTO param);

    List<StageExampleDTO> batchGenerateStageExamples(BatchGenerateStageExampleParamDTO param);

    List<StageExampleDTO> findByProjectIds(Collection<Long> projectIds);

    StageExampleDTO getStageExampleById(Long id);
}
