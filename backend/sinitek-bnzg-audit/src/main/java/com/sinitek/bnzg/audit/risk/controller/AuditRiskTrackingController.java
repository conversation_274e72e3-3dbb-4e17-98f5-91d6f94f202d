package com.sinitek.bnzg.audit.risk.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactLoadResultDTO;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.dto.RectifyResultLoadResultDTO;
import com.sinitek.bnzg.audit.risk.dto.RectifyResultSaveParamDTO;
import com.sinitek.bnzg.audit.risk.log.rectify.dealy.dto.DelyRectifyDataLoadResultDTO;
import com.sinitek.bnzg.audit.risk.log.rectify.dealy.dto.DelyRectifyDataSaveParamDTO;
import com.sinitek.bnzg.audit.risk.log.rectify.dealy.dto.DelyRectifyDataSearchParamDTO;
import com.sinitek.bnzg.audit.risk.log.rectify.dealy.dto.DelyRectifyDataSearchResultDTO;
import com.sinitek.bnzg.audit.risk.log.rectify.dealy.service.IAuditRectifyDealyChangeLogService;
import com.sinitek.bnzg.audit.risk.log.rectify.dealy.support.DelyRectifyDataSearchResultFormat;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskTrackingService;
import com.sinitek.bnzg.audit.risk.support.AuditRiskSearchResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目风险点 Controller
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@RestController
@RequestMapping("/frontend/api/audit/project/risk/tracking")
@Api(value = "/frontend/api/audit/project/risk/tracking", tags = "审计系统-项目风险点-整改跟踪")
public class AuditRiskTrackingController {

    @Autowired
    private IRectifyContactService rectifyContactService;

    @Autowired
    private IAuditRiskTrackingService auditRiskTrackingService;

    @Autowired
    private IAuditRectifyDealyChangeLogService auditRectifyDealyChangeLogService;

    @Autowired
    private DelyRectifyDataSearchResultFormat delyRectifyDataSearchResultFormatter;

    @Autowired
    private AuditRiskSearchResultFormat<AuditRiskSearchResultDTO> format;

    @ApiOperation(value = "整改联系人-保存相关部门相关人员")
    @PostMapping("/save-rectify-contact")
    public RequestResult<Void> saveRectifyContact(
        @RequestBody @Validated RectifyContactEditParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.auditRiskTrackingService.saveRelevantPeopleAndDepartment(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "加载整改联系人")
    @GetMapping("/load-rectify-contact")
    public RequestResult<RectifyContactLoadResultDTO> loadRectifyContact(
        @RequestParam("riskId") Long riskId) {
        RectifyContactLoadResultDTO dto = this.rectifyContactService.getContactByRiskId(riskId);
        return new RequestResult<>(dto);
    }

    @ApiOperation(value = "保存整改结果")
    @PostMapping("/save-rectify-result")
    public RequestResult<Void> saveRectifyResult(
        @RequestBody @Validated RectifyResultSaveParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.auditRiskTrackingService.saveRectifyResult(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "加载整改结果")
    @GetMapping("/load-rectify-result")
    public RequestResult<RectifyResultLoadResultDTO> loadRectifyResult(
        @RequestParam("riskId") Long riskId) {
        return new RequestResult<>(this.auditRiskTrackingService.loadByRiskId(riskId));
    }

    @ApiOperation(value = "保存延期数据")
    @PostMapping("/save-dely-rectify-data")
    public RequestResult<Void> saveDelyRectifyData(
        @RequestBody @Validated DelyRectifyDataSaveParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.auditRectifyDealyChangeLogService.saveDelyRectifyData(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "加载延期数据")
    @GetMapping("/load-dely-rectify-data")
    public RequestResult<DelyRectifyDataLoadResultDTO> loadDelyRectifyData(
        @RequestParam("riskId") Long riskId) {
        return new RequestResult<>(this.auditRectifyDealyChangeLogService.loadByRiskId(riskId));
    }

    @ApiOperation(value = "查询延期数据")
    @PostMapping("/list-dely-history")
    public TableResult<DelyRectifyDataSearchResultDTO> saveDelyRectifyData(
        @RequestBody @Validated DelyRectifyDataSearchParamDTO param) {
        IPage<DelyRectifyDataSearchResultDTO> pageData = this.auditRectifyDealyChangeLogService.searchDelyHistory(
            param);
        return param.build(pageData, this.delyRectifyDataSearchResultFormatter);
    }

    @ApiOperation(value = "查询项目风险点分页列表")
    @PostMapping("/search")
    public TableResult<AuditRiskSearchResultDTO> search(@RequestBody AuditRiskSearchParamDTO dto) {
        IPage<AuditRiskSearchResultDTO> resultPage = this.auditRiskTrackingService.search(
            dto);
        return dto.build(resultPage, format);
    }

    @ApiOperation(value = "检查能否完成整改跟踪(如果风险点整改状态存在`整改中`或`延期整改`的数据，则不允许完成)")
    @GetMapping("/check-can-finish")
    public RequestResult<AuditCheckResultDTO> checkCanFinish(
        @RequestParam("projectId") Long projectId) {
        return new RequestResult<>(this.auditRiskTrackingService.checkCanFinish(projectId));
    }
}
