package com.sinitek.bnzg.audit.risk.accountability.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.event.AuditProjectPhaseChangeEvent;
import com.sinitek.bnzg.audit.risk.accountability.dto.DeleteAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class AuditProjectPhaseStopEventListenerInRiskAc {

    @Autowired
    private IAuditRiskService riskService;

    @Autowired
    private IRiskAccountabilityService riskAccountabilityService;

    /**
     * 监听项目阶段终止事件
     *
     * 清理该项目下所有风险点的问责管理数据
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditProjectPhaseChangeEvent.class, fallbackExecution = true)
    public void listen(AuditProjectPhaseChangeEvent event) {
        AuditProjectPhaseChangeEventSourceDTO source = event.getSource();
        Long projectId = source.getProjectId();
        String opOrgId = source.getOperatorId();
        Date opTime = source.getOpTime();
        Integer oldProjectPhase = source.getOldProjectPhase();
        Integer newProjectPhase = source.getNewProjectPhase();

        if (Objects.equals(newProjectPhase, AuditProjectPhaseConstant.STOP)) {
            log.info(
                "监听到审计项目[{}]阶段终止事件,操作人[{}],操作时间[{}],旧阶段[{}] => 新阶段[{}],清理该项目下所有风险点的问责管理数据",
                projectId, opOrgId, opTime, oldProjectPhase, newProjectPhase
            );

            List<AuditRiskBaseInfoDTO> existRisks = this.riskService.findExistRisksByProjectId(
                projectId);
            if (CollUtil.isNotEmpty(existRisks)) {
                List<Long> riskIds = existRisks.stream().map(AuditRiskBaseInfoDTO::getId)
                    .collect(Collectors.toList());

                List<AuditRiskAc> acList = this.riskAccountabilityService.findAuditRiskAcByRiskIds(
                    riskIds);
                if (CollUtil.isNotEmpty(acList)) {
                    List<Long> acIdList = acList.stream().map(AuditRiskAc::getId)
                        .collect(Collectors.toList());
                    log.info(
                        "项目[{}]终止时,根据风险点id[{}]获取到问责管理数据[{}]需被删除",
                        projectId,
                        riskIds, acIdList);
                    this.riskAccountabilityService.deleteByIdList(
                        DeleteAuditRiskAcParamDTO.builder()
                            .ids(acIdList)
                            .operatorId(opOrgId)
                            .opTime(opTime)
                            .opRemark("项目中止")
                            .build());
                } else {
                    log.info(
                        "项目[{}]终止时,根据风险点id[{}]获取到问责管理数据为空",
                        projectId,
                        riskIds);
                }
            } else {
                log.info("项目[{}]终止时,根据项目id获取到风险点数据为空", projectId);
            }

        }


    }

}
