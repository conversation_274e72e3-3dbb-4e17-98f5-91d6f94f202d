package com.sinitek.bnzg.audit.risk.accountability.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/11/15 10:32
 */


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目风险点问责情况列表参数PO")
public class AuditRiskAcSearchParamPO extends PageDataParam {

    @ApiModelProperty(value = "审计计划")
    private List<Long> planIds;

    @ApiModelProperty("审计项目")
    private List<String> projectNameList;

    @ApiModelProperty("审计程序")
    private List<String> procedureNameList;

    @ApiModelProperty("风险点")
    private List<String> riskNameList;

    @ApiModelProperty("责任部门")
    private List<String> respDeptIds;

    @ApiModelProperty("责任人")
    private List<String> respManIds;

}

