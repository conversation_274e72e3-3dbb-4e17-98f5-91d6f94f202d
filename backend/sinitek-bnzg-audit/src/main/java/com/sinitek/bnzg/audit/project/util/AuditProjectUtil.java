package com.sinitek.bnzg.audit.project.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectDetailDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchResultDTO;
import com.sinitek.bnzg.audit.project.entity.AuditProject;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchParamPO;
import com.sinitek.bnzg.audit.project.po.AuditProjectSearchResultPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectUtil {

    private static final String DEFAULT_SORT_BY_AUDIT_YEAR_ORDER_NAME = "auditYear";

    public static AuditProjectDetailDTO makeEntity2DetailDTO(AuditProject source) {
        if (Objects.nonNull(source)) {
            AuditProjectDetailDTO info = new AuditProjectDetailDTO();
            BeanUtils.copyProperties(source, info);
            return info;
        }
        return null;
    }

    public static AuditProjectInfoDTO makeEntity2InfoDTO(AuditProject source) {
        if (Objects.nonNull(source)) {
            AuditProjectInfoDTO info = new AuditProjectInfoDTO();
            BeanUtils.copyProperties(source, info);
            return info;
        }
        return null;
    }

    public static boolean isPhaseInvalidForOperation(Integer projectPhase) {
        return projectPhase.equals(AuditProjectPhaseConstant.TRACKING)
            || projectPhase.equals(AuditProjectPhaseConstant.STOP)
            || projectPhase.equals(AuditProjectPhaseConstant.CLOSE);
    }

    public static AuditProjectSearchParamPO makeSearchParamDTO2PO(AuditProjectSearchParamDTO dto) {
        String projectName = dto.getProjectName();
        String planName = dto.getPlanName();
        List<Integer> projectPhases = dto.getProjectPhases();

        AuditProjectSearchParamPO po = new AuditProjectSearchParamPO();

        List<String> projectNames = CommonStringUtil.toSearchStrList(projectName);
        if (CollUtil.isNotEmpty(projectNames)) {
            po.setProjectNames(projectNames);
        }
        List<String> planNames = CommonStringUtil.toSearchStrList(planName);
        if (CollUtil.isNotEmpty(planNames)) {
            po.setPlanNames(planNames);
        }
        if (CollUtil.isNotEmpty(projectPhases)) {
            po.setProjectPhases(projectPhases);
        }
        List<Integer> auditYears = dto.getAuditYears();
        if (CollUtil.isNotEmpty(auditYears)) {
            po.setAuditYears(auditYears);
        }

        String orderName = dto.getOrderName();
        if (Objects.equals(DEFAULT_SORT_BY_AUDIT_YEAR_ORDER_NAME, orderName)) {
            // 如果使用 auditYear 作为排序条件,则修改逻辑
            po.setOrderName(null);
            po.setUseAuditYearSortFlag(true);
        } else {
            po.setOrderName(dto.getOrderName());
            po.setUseAuditYearSortFlag(false);
        }

        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());

        return po;
    }

    public static AuditProjectSearchResultDTO makeSearchResultPO2DTO(
        AuditProjectSearchResultPO po) {
        return LcConvertUtil.convert(po, AuditProjectSearchResultDTO::new);
    }
}
