package com.sinitek.bnzg.audit.project.log.phase.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.bnzg.log.entity.AbstractRecordChangeLogEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计程序库状态日志 Entity
 *
 * <AUTHOR>
 * date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_project_phase_log")
@ApiModel(description = "项目进度变动日志")
public class AuditProjectPhaseLog extends AbstractRecordChangeLogEntity<Integer> {

    @ApiModelProperty("项目id")
    private Long projectId;

}