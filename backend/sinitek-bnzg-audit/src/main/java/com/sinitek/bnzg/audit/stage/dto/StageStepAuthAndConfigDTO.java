package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-06-16 16:10:21
 */

@Data
@NoArgsConstructor
@ApiModel("具体某一步骤的权限和配置")
@EqualsAndHashCode
public class StageStepAuthAndConfigDTO {

    @ApiModelProperty("当前阶段步骤权限")
    private StageStepPermissionDTO permission;

}
