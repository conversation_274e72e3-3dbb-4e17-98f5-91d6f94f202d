package com.sinitek.bnzg.audit.stage.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/15/2024 11:08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStepConstant {

    /**
     * 审计准备
     */
    public static final int AUDIT_PREPARING = 1;

    /**
     * 审计实施
     */
    public static final int AUDIT_EXECUTION = 2;

    /**
     * 审计报告
     */
    public static final int AUDIT_REPORT = 3;

    /**
     * 整改跟踪
     */
    public static final int RECTIFICATION_TRACKING = 4;
}
