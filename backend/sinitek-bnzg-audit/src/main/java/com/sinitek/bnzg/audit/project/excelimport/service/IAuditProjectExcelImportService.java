package com.sinitek.bnzg.audit.project.excelimport.service;

import com.sinitek.bnzg.audit.common.dto.AuditCommonImportParamDTO;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectExcelImportWrapperDTO;

/**
 * <AUTHOR>
 * @date 2024-12-26 15:32
 */
public interface IAuditProjectExcelImportService {

    ProjectExcelImportWrapperDTO importAuditProject(AuditCommonImportParamDTO param);

    ProjectExcelImportWrapperDTO readProjectImportExcel(String fileName, String filePath);

    void saveProject(ProjectExcelImportWrapperDTO data);

}
