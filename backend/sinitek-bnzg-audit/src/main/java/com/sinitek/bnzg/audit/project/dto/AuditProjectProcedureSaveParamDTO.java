package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "项目程序保存参数")
public class AuditProjectProcedureSaveParamDTO implements IAuditProjectAndOpeatorId {

    @NotNull(message = "所属项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotEmpty(message = "项目下审计程序不能为空")
    @ApiModelProperty("程序id(集合)")
    private List<Long> procedureIdList;

    @ApiModelProperty(value = "检查权限(true:检查;false:不需要检查)")
    private Boolean checkAuthFlag;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
