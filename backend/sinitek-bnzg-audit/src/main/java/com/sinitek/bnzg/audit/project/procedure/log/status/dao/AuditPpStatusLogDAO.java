package com.sinitek.bnzg.audit.project.procedure.log.status.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.log.status.entity.AuditPpStatusLog;
import com.sinitek.bnzg.audit.project.procedure.log.status.mapper.AuditPpStatusLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class AuditPpStatusLogDAO extends
    ServiceImpl<AuditPpStatusLogMapper, AuditPpStatusLog> {

    public List<AuditPpStatusLog> findByRiskId(Long ppId) {
        LambdaQueryWrapper<AuditPpStatusLog> queryWrapper = Wrappers.lambdaQuery(
            AuditPpStatusLog.class);
        queryWrapper.eq(AuditPpStatusLog::getPpId, ppId);
        return this.list(queryWrapper);
    }

}
