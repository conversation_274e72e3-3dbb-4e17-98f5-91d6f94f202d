package com.sinitek.bnzg.audit.risk.accountability.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/11/15 14:44
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "项目风险点问责情况新增/录入参数DTO")
public class SaveOrEditAuditRiskAcParamDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("审议阶段")
    private String reviewStage;

    @ApiModelProperty("审计复议")
    private String auditReconcile;

    @ApiModelProperty("处罚执行")
    private String punishExecution;

    @ApiModelProperty("审计调查结果附件")
    private UploadDTO investigationResultUpload;

    @ApiModelProperty("审计调查结果附件")
    private UploadDTO investigationResultUpload2;

    @ApiModelProperty("审议记录附件")
    private UploadDTO reviewRecordUpload;

    @ApiModelProperty("审议记录附件")
    private UploadDTO reviewRecordUpload2;

    @ApiModelProperty("处分意见")
    private UploadDTO punishOpinionUpload;

    @ApiModelProperty("处分意见")
    private UploadDTO punishOpinionUpload2;

    @ApiModelProperty("处罚告知书")
    private UploadDTO punishNoticeUpload;

    @ApiModelProperty("复议申请文件")
    private UploadDTO reconsiderationUpload;

    @ApiModelProperty("处罚决定书")
    private UploadDTO punishDecisionUpload;

    @ApiModelProperty("处罚落实文件")
    private UploadDTO implementationUpload;

    @ApiModelProperty("公司党委会审议结果")
    private UploadDTO companyReviewResultUpload;

    @ApiModelProperty("公司党委会审议结果")
    private UploadDTO companyReviewResultUpload2;

    @ApiModelProperty("审议开始日期")
    private Date reviewStartDate;

    @ApiModelProperty("审议结束日期")
    private Date reviewEndDate;

    @ApiModelProperty("公司党委会审议日期")
    private Date companyReviewDate;

    @ApiModelProperty("处罚告知书下发日期")
    private Date punishNoticeDate;

    @ApiModelProperty("审计复议申请日期")
    private Date reconcileApplyDate;

    @ApiModelProperty("处罚决定书下发日期")
    private Date punishDecisionDate;

    @ApiModelProperty("处罚落实日期")
    private Date punishImplementDate;

    @ApiModelProperty("审议开始日期(二次审议)")
    private Date reviewStartDate2;

    @ApiModelProperty("审议结束日期(二次审议)")
    private Date reviewEndDate2;

    @ApiModelProperty("公司党委会审议日期(二次审议)")
    private Date companyReviewDate2;

    @ApiModelProperty("审议阶段(二次审议)")
    private String reviewStage2;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;
}
