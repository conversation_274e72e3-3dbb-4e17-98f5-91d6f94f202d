package com.sinitek.bnzg.audit.risk.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/29/2024 10:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskStatusConstant {

    /**
     * 未审计
     */
    public static final int NOT_AUDIT = 1;

    /**
     * 待审核
     */
    public static final int DRAFT = 0;

    /**
     * 审批中
     */
    public static final int APPROVING = 10;

    /**
     * 审核不通过
     */
    public static final int NOT_APPROVED = 50;

    /**
     * 审批通过
     */
    public static final int APPROVED = 100;
}