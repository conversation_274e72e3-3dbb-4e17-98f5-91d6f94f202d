package com.sinitek.bnzg.audit.risk.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 09/10/2024 17:45
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "风险点审批")
public class RiskApproveDetailDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("项目id")
    private Long projectId;

}
