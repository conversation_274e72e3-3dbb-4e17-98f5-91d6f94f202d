package com.sinitek.bnzg.audit.risk.statistics.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.generator.Generator;
import cn.hutool.core.util.ObjectUtil;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartDetailParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.RiskStatisticsChartParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.RiskStatisticsChartReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartDetailParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditFindRectifyChartReasultPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditRiskStatisticsParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.AuditRiskStatisticsReasultPO;
import com.sinitek.bnzg.audit.risk.statistics.po.RiskStatisticsChartParamPO;
import com.sinitek.bnzg.audit.risk.statistics.po.RiskStatisticsChartReasultPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date：2024/9/20 17:32
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskStatisticsUtil {

    private static <E extends AuditRiskStatisticsParamDTO, T extends AuditRiskStatisticsParamPO> T doMakeSearchParamDTO2PO(
        E dto, Generator<T> generator) {
        List<Long> projectIds = dto.getProjectIds();
        String projectName = dto.getProjectName();
        String riskName = dto.getRiskName();
        List<Integer> riskType = dto.getRiskType();
        List<Integer> riskLevel = dto.getRiskLevel();
        List<Integer> firstCatalog = dto.getFirstCatalog();
        List<String> innerCategory = dto.getInnerCategory();
        Date auditStartDate = dto.getAuditStartDate();
        Date auditEndDate = dto.getAuditEndDate();
        List<Integer> auditYears = dto.getAuditYears();
        List<Integer> rectifyStates = dto.getRectifyStates();
        List<Integer> respDeptSugTypes = dto.getRespDeptSugTypes();
        List<String> respDeptIds = dto.getRespDeptIds();
        Integer repeatFlag = dto.getRepeatFlag();
        List<String> businessUnits = dto.getBusinessUnits();

        T po = generator.next();

        if (CollUtil.isNotEmpty(projectIds)) {
            po.setProjectIds(projectIds);
        }
        List<String> projectNames = CommonStringUtil.toSearchStrList(projectName);
        if (CollUtil.isNotEmpty(projectNames)) {
            po.setProjectNames(projectNames);
        }
        List<String> riskNames = CommonStringUtil.toSearchStrList(riskName);
        if (CollUtil.isNotEmpty(riskNames)) {
            po.setRiskNames(riskNames);
        }
        if (CollUtil.isNotEmpty(riskType)) {
            po.setRiskType(riskType);
        }
        if (CollUtil.isNotEmpty(riskLevel)) {
            po.setRiskLevel(riskLevel);
        }
        if (CollUtil.isNotEmpty(firstCatalog)) {
            po.setFirstCatalog(firstCatalog);
        }
        if (CollUtil.isNotEmpty(innerCategory)) {
            po.setInnerCategory(innerCategory);
        }
        if (ObjectUtil.isNotEmpty(auditStartDate)) {
            po.setAuditStartDate(auditStartDate);
        }
        if (ObjectUtil.isNotEmpty(auditEndDate)) {
            po.setAuditEndDate(dto.getAuditEndDate());
        }
        if (CollUtil.isNotEmpty(auditYears)) {
            po.setAuditYears(auditYears);
        }
        if (CollUtil.isNotEmpty(rectifyStates)) {
            po.setRectifyStates(rectifyStates);
        }

        if (CollUtil.isNotEmpty(respDeptSugTypes)) {
            po.setRespDeptSugTypes(respDeptSugTypes);
        }
        if (CollUtil.isNotEmpty(respDeptIds)) {
            po.setRespDeptIds(respDeptIds);
        }
        if (CollUtil.isNotEmpty(businessUnits)) {
            po.setBusinessUnits(businessUnits);
        }
        if (ObjectUtil.isNotEmpty(repeatFlag)) {
            po.setRepeatFlag(repeatFlag);
        }

        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());
        return po;
    }

    public static AuditRiskStatisticsParamPO makeAuditRiskStatisticsParamPO2DTO(
        AuditRiskStatisticsParamDTO dto) {
        return doMakeSearchParamDTO2PO(dto, AuditRiskStatisticsParamPO::new);
    }

    public static AuditRiskStatisticsReasultDTO makeAuditRiskStatisticsReasultPO2DTO(
        AuditRiskStatisticsReasultPO po) {
        return LcConvertUtil.convert(po, AuditRiskStatisticsReasultDTO::new);
    }

    public static RiskStatisticsChartParamPO makeRiskStatisticsChartParamDTO2PO(
        RiskStatisticsChartParamDTO dto) {
        return doMakeSearchParamDTO2PO(dto, RiskStatisticsChartParamPO::new);
    }

    public static AuditFindRectifyChartParamPO makeAuditFindRectifyChartParamDTO2PO(
        AuditFindRectifyChartParamDTO dto) {
        return doMakeSearchParamDTO2PO(dto, AuditFindRectifyChartParamPO::new);
    }

    public static AuditFindRectifyChartDetailParamPO makeAuditFindRectifyChartDetailParamDTO2PO(
        AuditFindRectifyChartDetailParamDTO dto) {
        return doMakeSearchParamDTO2PO(dto,
            AuditFindRectifyChartDetailParamPO::new);
    }

    public static AuditFindRectifyChartReasultDTO makeAuditFindRectifyChartReasultPO2DTO(
        AuditFindRectifyChartReasultPO po) {
        return LcConvertUtil.convert(po, AuditFindRectifyChartReasultDTO::new);
    }

    public static RiskStatisticsChartReasultDTO makeRiskStatisticsChartReasultPO2DTO(
        RiskStatisticsChartReasultPO po) {
        return LcConvertUtil.convert(po, RiskStatisticsChartReasultDTO::new);
    }

}
