package com.sinitek.bnzg.audit.project.procedure.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureDetailDTO;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目程序详情")
public class AuditPpFullDetailDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("审计程序id")
    private Long procedureId;

    @ApiModelProperty("执行情况")
    private String execution;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态名")
    private String statusName;

    @ApiModelProperty("审计人")
    private String auditorId;

    @ApiModelProperty("审计人名")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计日期")
    private Date auditDate;

    @ApiModelProperty("审批人")
    private String approverId;

    @ApiModelProperty("审批人名")
    private String approverName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date approveTime;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    @ApiModelProperty("审计程序详情")
    private AuditProcedureDetailDTO procedureDetail;
}
