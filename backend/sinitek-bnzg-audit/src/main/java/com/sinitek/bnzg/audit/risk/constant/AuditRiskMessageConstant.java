package com.sinitek.bnzg.audit.risk.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目风险点 MessageCode
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskMessageConstant {

    /**
     * 名称
     */
    public static final String NAME_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.name_can_not_exceed";
    /**
     * 描述
     */
    public static final String DESCRIPTION_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.description_can_not_exceed";
    /**
     * 制度依据不能超过500个字符
     */
    public static final String SYS_BASIS_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.sys_basis_can_not_exceed";
    /**
     * 内部分类不能超过10个字符
     */
    public static final String INNERCATEGORY_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.innercategory_can_not_exceed";
    /**
     * 审计建议不能超过500个字符
     */
    public static final String AUDITSUGGESTION_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.auditsuggestion_can_not_exceed";
    /**
     * 责任部门建议不能超过500个字符
     */
    public static final String RESPDEPTSUGGESTION_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.respdeptsuggestion_can_not_exceed";
    /**
     * 整改反馈不能超过500个字符
     */
    public static final String RECTIFYFEEDBACK_CAN_NOT_EXCEED = "com.sinitek.bnzg.audit.risk.rectifyfeedback_can_not_exceed";

    /**
     * 风险点[{0}]不是草稿状态,无法删除
     */
    public static final String CANT_DELETE_BECAUSE_OF_NOT_DRAFT = "9902009001";

    /**
     * 风险点不存在
     */
    public static final String RISK_DATA_NOT_EXISTS = "9902009002";

    /**
     * 当前风险点不为草稿状态,无法编辑
     */
    public static final String CANT_EDIT_NOT_DRAFT = "9902009003";

    /**
     * 当前风险点不为审批通过,审批不通过状态,无法变更
     */
    public static final String NOT_APPROVED_AND_NOT_APPROVED_CANT_CHANGE = "9902009004";

    /**
     * 当前风险点不为最新数据,无法变更
     */
    public static final String NOT_THREAD_LATEST_CANT_CHANGE = "9902009005";

    /**
     * 当前风险点未审批通过,无法保存审计建议
     */
    public static final String CANT_SAVE_SUGGESTION_ON_NOT_APPROVED = "9902009006";

    /**
     * 9902009007=当前风险点不为草稿状态,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_NOT_DRAFT = "9902009007";

    /**
     * 9902009008=当前风险点不为最新数据,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_NOT_THREAD_LATEST = "9902009008";

    /**
     * 9902009009=当前风险点不存在变更前数据,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_NOT_BEFORE_DATA = "9902009009";

    /**
     * 传入的内部分类调整对比数据为空
     */
    public static final String BEFORE_FILTER_INNER_CATEGORY_WARNING = "9902011001";

    /**
     * 过滤传入的内部分类后没有找到可更新的内部分类
     */
    public static final String AFTER_FILTER_INNER_CATEGORY_WARNING = "9902011002";

    /**
     * 没有通过当前审计年度找到风险点
     */
    public static final String INNER_CATEGORY_BY_AUDITYEAR = "9902011003";
}
