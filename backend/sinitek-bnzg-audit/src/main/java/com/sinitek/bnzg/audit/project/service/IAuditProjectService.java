package com.sinitek.bnzg.audit.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.dto.AuditProjectDetailDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectQueryParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditReStartProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditStartProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditStopProjectParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditWordFileParamDTO;
import com.sinitek.bnzg.audit.project.dto.UpdateProjectPhaseParamDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:45
 */
public interface IAuditProjectService {

    /**
     * 项目重开
     */
    void reStart(AuditReStartProjectParamDTO param);

    /**
     * 启动项目
     */
    void start(AuditStartProjectParamDTO param);

    void stop(AuditStopProjectParamDTO param);

    /**
     * 根据审计计划复制项目
     */
    void copyCascadeByPlanId(Long oldPlanId, Long newPlanId, String opOrgId);

    void updateProjectPhase(UpdateProjectPhaseParamDTO param);

    AuditProjectDetailDTO loadExistsProjectInfoById(Long id);

    AuditProjectInfoDTO getExistsProjectInfoById(Long id);

    List<AuditProjectInfoDTO> findExistsProjctByPlanId(Long planId);

    List<AuditProjectInfoDTO> findExistsProjctByPlanIds(Collection<Long> planIds);

    List<AuditProjectInfoDTO> findExistsProjctByIds(Collection<Long> ids);

    UploadFileDTO generateWordFile(AuditWordFileParamDTO param) throws Exception;

    void updateRegulatoryTimeById(Long id, Date regulatoryTime);

    List<AuditProjectInfoDTO> findAllAuditProject();

    List<AuditProjectInfoDTO> listAuditProject(AuditProjectQueryParamDTO param);

    List<AuditProjectInfoDTO> findExistsProjectByNames(Collection<String> names);

    List<AuditProjectInfoDTO> findExistsProjectByNamesAndPlanId(Collection<String> names,
        Long planId);

    IPage<AuditProjectSearchResultDTO> search(AuditProjectSearchParamDTO param);

    Integer loadProjectAuditYearById(Long projectId);
}
