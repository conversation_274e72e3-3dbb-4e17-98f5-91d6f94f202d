package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("项目启动参数")
public class AuditStartProjectParamDTO {

    @NotNull(message = "被启动项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目负责人")
    private List<String> orgIds;

    @Valid
    @ApiModelProperty("项目成员(集合)")
    private List<AuditProjectMemberSaveParamDTO> memberList;

    @ApiModelProperty("操作人(前端不需要传值)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;
}
