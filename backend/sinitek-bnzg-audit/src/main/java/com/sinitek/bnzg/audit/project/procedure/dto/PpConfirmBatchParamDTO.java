package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "项目审计程序批量确认参数DTO")
public class PpConfirmBatchParamDTO {

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotEmpty(message = "项目审计程序id不能为空")
    @ApiModelProperty("项目审计程序id")
    private List<Long> ids;
    
    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
