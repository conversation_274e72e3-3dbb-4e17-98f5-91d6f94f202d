package com.sinitek.bnzg.audit.project.log.phase.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.project.log.phase.dao.AuditProjectPhaseLogDAO;
import com.sinitek.bnzg.audit.project.log.phase.entity.AuditProjectPhaseLog;
import com.sinitek.bnzg.audit.project.log.phase.service.IAuditProjectPhaseLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class AuditProjectPhaseLogServiceImpl extends
    AbstractReecordChangeLogService<AuditProjectPhaseLog, Integer> implements
    IAuditProjectPhaseLogService {

    @Autowired
    private AuditProjectPhaseLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<AuditProjectPhaseLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected AuditProjectPhaseLog generateNewOne() {
        return new AuditProjectPhaseLog();
    }

    @Override
    protected void handlerForeignKey(AuditProjectPhaseLog entity, Long foreignKey) {
        entity.setProjectId(foreignKey);
    }
}
