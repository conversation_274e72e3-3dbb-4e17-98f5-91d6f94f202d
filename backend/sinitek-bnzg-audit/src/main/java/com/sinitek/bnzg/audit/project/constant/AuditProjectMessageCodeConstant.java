package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectMessageCodeConstant {

    /**
     * 当前项目不存在
     */
    public static final String CURRENT_PROJECT_NOT_EXISTS = "9902003001";

    /**
     * 当前项目进度为[{0}]无法启动
     */
    public static final String CNAT_START_BECAUSEOF_CURRENT_PHASE = "9902003002";

    /**
     * 当前项目进度为[{0}]无法重开
     */
    public static final String CNAT_RESTART_BECAUSEOF_CURRENT_PHASE = "9902003030";

    /**
     * 9902003031=当前项目审计程序非最新数据,无法变更
     */
    public static final String CANT_CHANGE_BECAUSEOF_NOT_LATEST = "9902003031";

    /**
     * 存在阶段已启动的项目,无法删除
     */
    public static final String CANT_DELETE_BECAUSEOF_ILL_PROJECT_PHASE = "9902003003";

    /**
     * 保存项目审计程序时,没有新数据保存
     */
    public static final String NO_NEW_DATA_SAVE_ON_SAVE_PROJECT_PROCEDURE = "9902003004";

    /**
     * [{0}]已经是项目负责人,无法保存新角色
     */
    public static final String CANT_SAVE_BECAUSE_ALREADY_OWNER = "9902003005";

    /**
     * 该项目下,被删除的审计程序存在风险点,无法删除
     */
    public static final String CANT_DELETE_BECAUSE_EXISTS_RISK = "9902003006";

    /**
     * 当前审计项目未配置项目负责人,无法保存审计程序
     */
    public static final String CANT_SAVE_PROJECT_PROCEDURE_NO_OWNER = "9902003007";

    /**
     * 当前登录人不是当前审计项目的负责人,无法保存审计程序
     */
    public static final String CANT_SAVE_PROJECT_PROCEDURE_NOT_OWNER = "9902003008";

    /**
     * 当前审计项目未配置项目负责人,无法保存项目成员
     */
    public static final String CANT_SAVE_PROJECT_MEMBER_NO_OWNER = "9902003009";

    /**
     * 当前登录人不是当前审计项目的负责人,无法保存项目成员
     */
    public static final String CANT_SAVE_PROJECT_MEMBER_NOT_OWNER = "9902003010";

    /**
     * 当前审计项目未配置项目负责人,无法保存项目成员权限
     */
    public static final String CANT_SAVE_PROJECT_MEMBER_AUTH_NO_OWNER = "9902003011";

    /**
     * 当前登录人不是当前审计项目的负责人,无法保存项目成员权限
     */
    public static final String CANT_SAVE_PROJECT_MEMBER_AUTH_NOT_OWNER = "9902003012";

    /**
     * 当前审计项目未配置相关角色,无法查看项目数据
     */
    public static final String CANT_VIEW_PROJECT_DATA_NO_ROLER = "9902003013";

    /**
     * 当前登录人不属于当前项目的相关角色人员,无法查看项目数据
     */
    public static final String CANT_VIEW_PROJECT_DATA_NOT_BELONG_ANY_ROLE = "9902003014";


    /**
     * 当前审计项目项目进度为[{0}]状态不可编辑
     */
    public static final String CANT_EDIT_BECAUSEOF_CURRENT_PHASE = "9902003015";

    /**
     * 当前审计项目项目进度为[{0}],无法设置团队成员
     */
    public static final String CANT_SAVE_PROJECT_MEMBER_BECAUSEOF_CURRENT_PHASE = "9902003016";

    /**
     * 当前审计项目项目进度为[{0}],无法设置审计程序
     */
    public static final String CANT_SAVE_PROJECT_PROCEDURE_BECAUSEOF_CURRENT_PHASE = "9902003017";
    /**
     * 当前审计项目项目进度为[{0}],无法删除审计程序
     */
    public static final String CANT_DELETE_PROJECT_PROCEDURE_BECAUSEOF_CURRENT_PHASE = "9902003018";

    /**
     * 实际完成日期不能超过要求完成整改日期
     */
    public static String ACTUAL_COMPLETE_DATE_GREATER_THAN_REQUIRED_COMPLETE_DATE = "9902003019";

    /**
     * 目前状态已经是整改完成，无法再次进行整改反馈操作
     */
    public static String RISK_RECTIFY_STATE_ALREADY_FINISH = "9902003020";

    /**
     * 目前状态已经是整改完成，无法再次进行整改延期操作
     */
    public static String RISK_RECTIFY_STATE_ALREADY_FINISH_DELAY = "9902003021";

    /**
     * 延期日期与预计整改完成日期不能相等
     */
    public static String DELEY_DATE_CANT_EQUAL_DB_DATE = "9902003028";

    /**
     * 当前审计程序存在风险点无法变更
     */
    public static String CANT_CHANGE_BECAUSE_OF_EXISTS_RISK = "9902003029";

    /**
     * 目前状态已经是整改完成，无法再次保存整改联系人
     */
    public static String RISK_RECTIFY_STATE_ALREADY_FINISH_CONTACT = "9902003022";

    /**
     * 目前状态已经是整改延期，无法再次进行整改反馈操作
     */
    public static String RISK_RECTIFY_STATE_ALREADY_DELAY = "9902003023";

    /**
     * 当前文档类型[{0}]模板不存在，无法生成文档
     */
    public static String AUDIT_REPORT_TEMPLATE_NOT_EXISTS = "9902007013";

    /**
     * 当前文档类型模板存在[{0}]个
     */
    public static String AUDIT_DOC_TYPE_TEMPLATE_EXISTS = "9902007014";

    /**
     * 当前项目[{0}]生成审计报告失败
     */
    public static String AUDIT_REPORT_GENERATE_FAIL = "9902007015";

    /**
     * 文档同步时,审计项目不存在
     */
    public static String SYNC_DOC_FAILURE_BECAUSE_PROJECT_NOT_EXISTS = "9902008006001";

}
