package com.sinitek.bnzg.audit.project.procedure.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditRiskApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.risk.accountability.dto.DeleteAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRiskApprvResultChangeEventListener2 {

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    @Autowired
    private IRiskAccountabilityService accountabilityService;

    /**
     * 监听项目审计程序审批批次提交
     *
     * 如果风险点审批不通过,删除问责数据
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditRiskApprvResultChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen4DeleteAc(
        AuditRiskApprvResultChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            RecordChangeLogAddParamDTO<Integer> data = ((RecordChangeLogAddParamDTO<Integer>) source);
            this.deleteAcData(Collections.singleton(data.getForeignKey()), data.getOperatorId(),
                data.getOpTime());
        } else {
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> data = ((AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source);
            Collection<Long> foreignKeys = data.getForeignKeys();
            Long oneKey = null;
            if (CollUtil.isNotEmpty(foreignKeys)) {
                oneKey = foreignKeys.iterator().next();
            }
            this.deleteAcData(data.getForeignKeys(), data.getOperatorId(oneKey),
                data.getOpTime(oneKey));
        }
    }

    private void deleteAcData(Collection<Long> keys, String gloablOperator, Date globalOpTime) {
        List<RiskApproveResultBaseInfoDTO> riskApproveResultList = this.riskApproveResultService.findExistByIds(
            keys);

        if (CollUtil.isNotEmpty(riskApproveResultList)) {
            List<RiskApproveResultBaseInfoDTO> needDeleteRiskAcList = riskApproveResultList.stream()
                .filter(
                    item -> Objects.equals(AuditRiskApproveResultConstant.NOT_APPROVED,
                        item.getApproveResult())).collect(
                    Collectors.toList());
            if (CollUtil.isNotEmpty(needDeleteRiskAcList)) {
                List<Long> needDeleteRiskIds = needDeleteRiskAcList.stream()
                    .map(RiskApproveResultBaseInfoDTO::getRiskId).collect(
                        Collectors.toList());

                List<AuditRiskAc> needDeleteDataList = this.accountabilityService.findAuditRiskAcByRiskIds(
                    needDeleteRiskIds);

                if (CollUtil.isNotEmpty(needDeleteDataList)) {
                    List<Long> needDeleteIdList = needDeleteDataList.stream()
                        .map(AuditRiskAc::getId)
                        .collect(Collectors.toList());
                    log.info(
                        "监听风险点审批结果[{}]提交,涉及风险点,有审批不通过的风险点id数据 {},需删除审批不通过风险点的问责数据 {}",
                        keys, needDeleteRiskIds, needDeleteIdList);
                    this.accountabilityService.deleteByIdList(DeleteAuditRiskAcParamDTO.builder()
                        .ids(needDeleteIdList)
                        .opRemark("风险点审批不通过,删除关联问责数据")
                        .operatorId(gloablOperator)
                        .opTime(globalOpTime)
                        .build());
                } else {
                    log.info(
                        "监听风险点审批结果[{}]提交,涉及风险点,有审批不通过的风险点id数据 {},但根据id没有找到对应问责数据,无需删除审批不通过风险点的问责数据",
                        keys, needDeleteRiskIds);
                }
            } else {
                log.info(
                    "监听风险点审批结果[{}]提交,涉及风险点,但没有审批不通过的数据,无需删除审批不通过风险点的问责数据",
                    keys);
            }

        } else {
            log.info(
                "监听风险点审批结果[{}]提交,不涉及风险点,无需删除审批不通过风险点的问责数据",
                keys);
        }
    }

}
