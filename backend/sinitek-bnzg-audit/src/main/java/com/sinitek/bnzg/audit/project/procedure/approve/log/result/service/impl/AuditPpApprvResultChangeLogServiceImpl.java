package com.sinitek.bnzg.audit.project.procedure.approve.log.result.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.dao.AuditPpApprvResultLogDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.entity.AuditPpApprvResultLog;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.service.IAuditPpApprvResultChangeLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class AuditPpApprvResultChangeLogServiceImpl extends
    AbstractReecordChangeLogService<AuditPpApprvResultLog, Integer> implements
    IAuditPpApprvResultChangeLogService {

    @Autowired
    private AuditPpApprvResultLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<AuditPpApprvResultLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected AuditPpApprvResultLog generateNewOne() {
        return new AuditPpApprvResultLog();
    }

    @Override
    protected void handlerForeignKey(AuditPpApprvResultLog entity, Long foreignKey) {
        entity.setApprvResultId(foreignKey);
    }
}
