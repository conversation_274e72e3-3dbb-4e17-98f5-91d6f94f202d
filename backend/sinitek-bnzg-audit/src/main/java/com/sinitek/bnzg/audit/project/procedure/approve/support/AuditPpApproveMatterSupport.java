package com.sinitek.bnzg.audit.project.procedure.approve.support;

import com.sinitek.bnzg.audit.project.procedure.approve.properties.AuditPpApproveGlobalProperties;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditPpApproveConstant;
import com.sinitek.sirm.workflow.support.claim.matterentity.AbstractMatterSupport;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 处理产品阶段待办任务
 *
 * <AUTHOR>
 * Date: 2023/8/23
 */
@Slf4j
@Component(AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY)
public class AuditPpApproveMatterSupport extends AbstractMatterSupport {

    @Autowired
    private AuditPpApproveGlobalProperties properties;

    @Override
    public boolean checkTask(Long sourceid, String sourcename) {
        return true;
    }

    @Override
    public String getShowUrl(Long sourceid, String sourcename) {
        return this.getCommonProcessUrl(sourceid, sourcename);
    }

    @Override
    public String getActionUrl(Long sourceid, String sourcename) {
        return this.getCommonProcessUrl(sourceid, sourcename);
    }

    private String getCommonProcessUrl(Long sourceId, String sourceName) {
        String riskApproveProcessUrl = this.properties.getApproveProcessUrl();
        String result = String.format(riskApproveProcessUrl, sourceId, "项目审计程序审批");
        log.info("sourceId: {},sourceName: {} 处理地址: {}", sourceId, sourceName, result);
        return result;
    }

    @Override
    public Map<String, String> getRemarksMap() {
        return new HashMap<>();
    }

    @Override
    public String getSourceEntity() {
        return "";
    }

    @Override
    public String getLinkBriefUrl(Long sourceid, String sourcename) {
        return "";
    }

    @Override
    public List<Map<String, String>> getFinishedOperations(Long sourceid, String sourcename) {
        return Collections.emptyList();
    }

    @Override
    public String terminateExampleTask(Long sourceid) {
        return "";
    }
}
