package com.sinitek.bnzg.audit.stage.service.impl;

import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.CANT_GET_STAGE_EXAMPLE_BY_PROJECT_ID;
import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.STAGE_EXAMPLE_ALREADY_EXISTS;
import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.STAGE_EXAMPLE_NOT_EXISTS;
import static com.sinitek.bnzg.audit.stage.constant.StageStepConstant.AUDIT_EXECUTION;
import static com.sinitek.bnzg.audit.stage.constant.StageStepConstant.AUDIT_PREPARING;
import static com.sinitek.bnzg.audit.stage.constant.StageStepConstant.AUDIT_REPORT;
import static com.sinitek.bnzg.audit.stage.constant.StageStepConstant.RECTIFICATION_TRACKING;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.stage.constant.StageStatusConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepSortConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dao.StageExampleDAO;
import com.sinitek.bnzg.audit.stage.dto.BatchGenerateStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.BatchGenerateStageStepExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateAuditStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageExample4ImportParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageStepExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.ReStartAuditStageParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDetailLoadParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleResultDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepDefInfoDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleResultDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.entity.StageExample;
import com.sinitek.bnzg.audit.stage.enumation.StageStatusEnum;
import com.sinitek.bnzg.audit.stage.enumation.StageStepEnum;
import com.sinitek.bnzg.audit.stage.enumation.StageStepStatusEnum;
import com.sinitek.bnzg.audit.stage.service.IAuditStageExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageExampleStatusService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 08/15/2024 13:10
 */
@Slf4j
@Service
public class AuditStageExampleServiceImpl implements IAuditStageExampleService {

    private static final int DEFAULT_STEP_NUM = 4;

    @Autowired
    private StageExampleDAO dao;

    @Autowired
    private IStageExampleService stageExampleService;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IStageExampleStatusService stageExampleStatusService;

    @Autowired
    private IStageStepExampleStatusService stageStepExampleStatusService;

    @Autowired
    private IOrgService orgService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateAuditStageExample(GenerateAuditStageExampleParamDTO param) {
        Long projectId = param.getProjectId();
        String opOrgId = param.getOpOrgId();
        Date opTime = param.getOpTime();
        Boolean publishEventFlag = param.getPublishEventFlag();

        List<StageExampleDTO> stageExamples = this.stageExampleService.findByProjectIds(
            Collections.singleton(projectId));
        if (CollUtil.isNotEmpty(stageExamples)) {
            log.error("当前项目[{}]所属阶段实例已存在[{}]条", projectId, stageExamples.size());
            throw new BussinessException(STAGE_EXAMPLE_ALREADY_EXISTS);
        }

        Long stageExampleId = this.stageExampleService.generateStageExample(param);

        List<StageStepDefInfoDTO> list = new LinkedList<>();

        // 审计准备
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(AUDIT_PREPARING)
            .sort(StageStepSortConstant.AUDIT_PREPARING_SORT)
            .status(StageStepStatusConstant.READY)
            .build());
        // 审计实施
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(AUDIT_EXECUTION)
            .sort(StageStepSortConstant.AUDIT_EXECUTION_SORT)
            .status(StageStepStatusConstant.READY)
            .build());
        // 审计报告
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(AUDIT_REPORT)
            .sort(StageStepSortConstant.AUDIT_REPORT_SORT)
            .status(StageStepStatusConstant.READY)
            .build());
        // 整改跟踪
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(RECTIFICATION_TRACKING)
            .status(StageStepStatusConstant.UN_START)
            .sort(StageStepSortConstant.RECTIFICATION_TRACKING_SORT)
            .build());

        GenerateStageStepExampleParamDTO generateStageStepExampleParam = GenerateStageStepExampleParamDTO.builder()
            .projectId(projectId)
            .stageExampleId(stageExampleId)
            .list(list)
            .opOrgId(opOrgId)
            .opTime(opTime)
            .publishEventFlag(publishEventFlag)
            .build();
        this.stageStepExampleService.generateStageStepExamples(generateStageStepExampleParam);

        return stageExampleId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateStageExample4Import(GenerateStageExample4ImportParamDTO param) {
        List<Long> projectIds = param.getProjectIds();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        if (CollUtil.isEmpty(projectIds)) {
            log.warn("导入项目生成阶段步骤时,传入项目[{}]为空,无需生成对应阶段", projectIds);
            return;
        }

        List<StageExampleDTO> existsedStageExamples = this.stageExampleService.findByProjectIds(
            projectIds);
        if (CollUtil.isNotEmpty(existsedStageExamples)) {
            log.error("导入项目生成阶段步骤时,当前项目[{}]所属阶段实例已存在[{}]条", projectIds,
                existsedStageExamples.size());
            throw new BussinessException(STAGE_EXAMPLE_ALREADY_EXISTS);
        }

        List<Long> distinctProjectIds = projectIds.stream().distinct().collect(Collectors.toList());

        if (!Objects.equals(projectIds.size(), distinctProjectIds.size())) {
            log.warn("导入项目生成阶段步骤时,传入项目[{}]经过滤为[{}],传入项目可能存在重复数据",
                projectIds, distinctProjectIds);
        } else {
            log.info("导入项目生成阶段步骤时,为传入项目[{}]生成阶段示例", distinctProjectIds);
        }

        List<StageExampleDTO> stageExamples = this.stageExampleService.batchGenerateStageExamples(
            BatchGenerateStageExampleParamDTO.builder()
                .projectIds(distinctProjectIds)
                // 抛出事件
                .publishEventFlag(true)
                // 所有步骤均为已完成状态
                .stageExampleStatus(StageStatusConstant.FINISHED)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

        List<Long> needGenerateStepExampleProjectIds = new LinkedList<>();
        Map<Long, Long> projectIdAndstageExampleIdMap = new HashMap<>(stageExamples.size());
        Map<Long, List<StageStepDefInfoDTO>> projectIdAndStepsMap = new HashMap<>(
            stageExamples.size() * DEFAULT_STEP_NUM);

        if (CollUtil.isNotEmpty(stageExamples)) {
            stageExamples.forEach(stageExample -> {
                Long id = stageExample.getId();
                Long projectId = stageExample.getProjectId();

                needGenerateStepExampleProjectIds.add(projectId);
                projectIdAndstageExampleIdMap.put(projectId, id);
                List<StageStepDefInfoDTO> stepDefList = this.generateBatchStepDefList(
                    StageStepStatusConstant.FINISHED);
                projectIdAndStepsMap.put(projectId, stepDefList);
            });

            this.stageStepExampleService.batchGenerateStageStepExamples(
                BatchGenerateStageStepExampleParamDTO.builder()
                    .projectIds(needGenerateStepExampleProjectIds)
                    .projectIdAndstageExampleIdMap(projectIdAndstageExampleIdMap)
                    .projectIdAndStepsMap(projectIdAndStepsMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .publishEventFlag(true)
                    .build());
        } else {
            log.warn("导入项目生成阶段步骤时,传入项目[{}]生成阶段示例为空", distinctProjectIds);
        }
    }

    private List<StageStepDefInfoDTO> generateBatchStepDefList(Integer stepStatus) {
        List<StageStepDefInfoDTO> list = new LinkedList<>();

        // 审计准备
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(AUDIT_PREPARING)
            .status(stepStatus)
            .sort(StageStepSortConstant.AUDIT_PREPARING_SORT)
            .build());
        // 审计实施
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(AUDIT_EXECUTION)
            .status(stepStatus)
            .sort(StageStepSortConstant.AUDIT_EXECUTION_SORT)
            .build());
        // 审计报告
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(AUDIT_REPORT)
            .status(stepStatus)
            .sort(StageStepSortConstant.AUDIT_REPORT_SORT)
            .build());
        // 整改跟踪
        list.add(StageStepDefInfoDTO.builder()
            .stepValue(RECTIFICATION_TRACKING)
            .status(stepStatus)
            .sort(StageStepSortConstant.RECTIFICATION_TRACKING_SORT)
            .build());

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reStartAuditStage(ReStartAuditStageParamDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        List<StageExampleDTO> stageExamples = this.stageExampleService.findByProjectIds(
            Collections.singleton(projectId));
        if (CollUtil.isEmpty(stageExamples)) {
            // 不存在阶段实例
            log.error("当前项目[{}]所属阶段实例不存在,无法重开", projectId);
            throw new BussinessException(STAGE_EXAMPLE_NOT_EXISTS);
        }
        StageExampleDTO stageExample = stageExamples.get(0);

        Long stageExampleId = stageExample.getId();

        // 项目阶段实例更新为进行中
        this.stageExampleStatusService.changeStageExampleStatus(
            StageExampleStatusChangeParamDTO.builder()
                .ids(Collections.singletonList(stageExampleId))
                .newStatus(StageStatusConstant.PROCESSING)
                .opOrgId(operatorId)
                .opTime(opTime)
                .opRemark("项目重开")
                .publishEventFlag(true)
                .force(true)
                .build());

        List<StageStepExampleDTO> stepExamples = this.stageStepExampleService.findByStageExampleIds(
            Collections.singleton(stageExampleId));

        List<Long> needUpdateAsProcessingIds = new LinkedList<>();
        List<Long> needUpdateAsReadyIds = new LinkedList<>();
        List<Long> needUpdateAsUnStartIds = new LinkedList<>();

        for (StageStepExampleDTO stepExample : stepExamples) {
            Long id = stepExample.getId();
            Integer stepValue = stepExample.getStepValue();

            if (Objects.equals(AUDIT_PREPARING, stepValue)) {
                needUpdateAsProcessingIds.add(id);
            } else if (Objects.equals(AUDIT_EXECUTION, stepValue)
                || Objects.equals(AUDIT_REPORT, stepValue)) {
                needUpdateAsReadyIds.add(id);
            } else {
                needUpdateAsUnStartIds.add(id);
            }
        }

        // 步骤状态重置
        this.stageStepExampleStatusService.updateStatus(
            StageStepExampleStatusChangeParamDTO.builder()
                .ids(needUpdateAsProcessingIds)
                .newStatus(StageStepStatusConstant.PROCESSING)
                .opOrgId(operatorId)
                .opTime(opTime)
                .opRemark("项目重开")
                .publishEventFlag(true)
                .force(true)
                .build());

        this.stageStepExampleStatusService.updateStatus(
            StageStepExampleStatusChangeParamDTO.builder()
                .ids(needUpdateAsReadyIds)
                .newStatus(StageStepStatusConstant.READY)
                .opOrgId(operatorId)
                .opTime(opTime)
                .opRemark("项目重开")
                .publishEventFlag(true)
                .force(true)
                .build());

        this.stageStepExampleStatusService.updateStatus(
            StageStepExampleStatusChangeParamDTO.builder()
                .ids(needUpdateAsProcessingIds)
                .newStatus(StageStepStatusConstant.PROCESSING)
                .opOrgId(operatorId)
                .opTime(opTime)
                .opRemark("项目重开")
                .publishEventFlag(true)
                .force(true)
                .build());

        this.stageStepExampleStatusService.updateStatus(
            StageStepExampleStatusChangeParamDTO.builder()
                .ids(needUpdateAsUnStartIds)
                .newStatus(StageStepStatusConstant.UN_START)
                .opOrgId(operatorId)
                .opTime(opTime)
                .opRemark("项目重开")
                .publishEventFlag(true)
                .force(true)
                .build());
    }

    @Override
    public StageExampleResultDTO loadDetail(StageExampleDetailLoadParamDTO param) {
        Long projectId = param.getProjectId();
        StageExample stageExample = this.dao.getExampleByProjectId(projectId);
        if (Objects.nonNull(stageExample)) {
            Long stageExampleId = stageExample.getId();
            Integer stageExampleStatus = stageExample.getStatus();

            List<StageStepExampleDTO> stepExamples = this.stageStepExampleService.findByStageExampleIds(
                Collections.singleton(stageExampleId));
            List<StageStepExampleResultDTO> steps;
            if (CollUtil.isNotEmpty(stepExamples)) {
                List<String> orgIds = new LinkedList<>();
                stepExamples.forEach(item -> {
                    String starterId = item.getStarterId();
                    String enderId = item.getEnderId();
                    String terminatorId = item.getTerminatorId();
                    orgIds.add(starterId);
                    orgIds.add(enderId);
                    orgIds.add(terminatorId);
                });
                // key: orgId
                // value: 名称
                List<String> filterOrgIds = orgIds.stream().distinct().filter(Objects::nonNull)
                    .collect(Collectors.toList());
                Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
                    filterOrgIds);
                // key: 阶段步骤值
                // value: 阶段步骤名
                Map<Integer, String> stepValueAndNameMap = StageStepEnum.getValueAndNameMap();
                // key: 阶段步骤状态值
                // value: 阶段步骤状态名
                Map<Integer, String> stepStatusValueAndNameMap = StageStepStatusEnum.getValueAndNameMap();
                steps = stepExamples.stream().map(item -> {
                    StageStepExampleResultDTO result = new StageStepExampleResultDTO();

                    result.setId(item.getId());
                    result.setStageExampleId(item.getStageExampleId());
                    result.setSort(item.getSort());

                    Integer stepValue = item.getStepValue();
                    result.setStepValue(stepValue);
                    result.setStepName(MapUtils.getString(stepValueAndNameMap, stepValue));

                    Integer status = item.getStatus();
                    result.setStatus(status);
                    result.setStatusName(MapUtils.getString(stepStatusValueAndNameMap, status));

                    String starterId = item.getStarterId();
                    result.setStarterId(starterId);
                    result.setStarterName(MapUtils.getString(orgIdAndNameMap, starterId));
                    result.setStartTime(item.getStartTime());

                    String enderId = item.getEnderId();
                    result.setEnderId(enderId);
                    result.setEnderName(MapUtils.getString(orgIdAndNameMap, enderId));
                    result.setEndTime(item.getEndTime());

                    String terminatorId = item.getTerminatorId();
                    result.setTerminatorId(terminatorId);
                    result.setTerminatorName(MapUtils.getString(orgIdAndNameMap, terminatorId));
                    result.setTerminatedTime(item.getTerminatedTime());

                    return result;
                }).collect(Collectors.toList());
            } else {
                log.warn("根据阶段实例id[{}]获取到步骤实例数据为空", stageExampleId);
                steps = Collections.emptyList();
            }

            StageExampleResultDTO result = new StageExampleResultDTO();
            result.setId(stageExampleId);
            result.setStatus(stageExampleStatus);
            result.setStatusName(
                StageStatusEnum.getByValue(stageExampleStatus).getName());
            result.setStepList(steps);

            return result;
        } else {
            log.error("根据项目id[{}]无法获取对应的阶段实例", projectId);
            throw new BussinessException(CANT_GET_STAGE_EXAMPLE_BY_PROJECT_ID);
        }
    }
}
