package com.sinitek.bnzg.audit.project.procedure.approve.log.status.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.event.AuditPpApprvStatusChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.service.IAuditPpApprvStatusChangeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpApprvStatusChangeEventListener<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, Integer> {

    @Autowired
    private IAuditPpApprvStatusChangeLogService logService;

    @Override
    protected String getEventName() {
        return "审计程序审批状态变动";
    }

    @Override
    protected IAuditPpApprvStatusChangeLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditPpApprvStatusChangeEvent.class, fallbackExecution = true)
    public void listen(
        AuditPpApprvStatusChangeEvent<E> event) {
        this.doListen(event);
    }
}
