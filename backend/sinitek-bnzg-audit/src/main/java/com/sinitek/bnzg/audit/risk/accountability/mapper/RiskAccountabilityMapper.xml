<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.risk.accountability.mapper.RiskAccountabilityMapper">

    <select id="searchRiskAccountability" resultType="com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchResultPO">
        select
            apl.name as planName,
            ap.name as projectName,
            ar.name as riskName,
            apd.name as procedureName,
            ar.id as riskId,
            ar.project_id,
            ap.plan_id,
            ar.procedure_id,
            arac.id,
            arac.review_stage,
            arac.audit_reconcile,
            arac.punish_execution,
            arac.review_start_date,
            arac.review_end_date,
            arac.company_review_date,
            arac.punish_notice_date,
            arac.reconcile_apply_date,
            arac.punish_decision_date,
            arac.punish_implement_date,
            arac.review_start_date2,
            arac.review_end_date2,
            arac.company_review_date2,
            arac.review_stage2,
            arac.createtimestamp
        from
            audit_risk_ac arac
                join audit_risk ar
                          on arac.risk_id = ar.id
                              and ar.status = 100
                              and ar.resp_dept_sug_type = 1
                              and ar.thread_latest_flag = 1
                              and ar.remove_flag = 0
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
                left join audit_procedure apd
                          on apd.id = ar.procedure_id
                              and apd.remove_flag = 0
        where
            arac.remove_flag = 0
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.projectNameList)">
            <!-- 项目名称查询 -->
            and
            <foreach collection="param.projectNameList" index="index" item="projectName" open="(" separator="or" close=")">
                ap.name LIKE CONCAT('%', #{projectName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.planIds)">
            <!-- 计划Id查询 -->
            AND apl.id in
            <foreach collection="param.planIds" index="index" item="planId" open="(" separator="," close=")">
                #{planId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.procedureNameList)">
            <!-- 程序名称查询 -->
            and
            <foreach collection="param.procedureNameList" index="index" item="procedureName" open="(" separator="or" close=")">
                apd.name LIKE CONCAT('%', #{procedureName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.respDeptIds)">
            <!-- 责任部门Id查询 -->
            AND ar.id IN (select distinct risk_id from audit_risk_resp_dept where resp_dept_id IN
            <foreach collection="param.respDeptIds" index="index" item="respDeptId" open="(" separator="," close=")">
                #{respDeptId}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.respManIds)">
            <!-- 责任人Id查询 -->
            AND ar.id IN (select distinct risk_id from audit_risk_resp_man where resp_man_id  IN
            <foreach collection="param.respManIds" index="index" item="respManId" open="(" separator="," close=")">
                #{respManId}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.riskNameList)">
            <!-- 风险点名称查询 -->
            AND
            <foreach collection="param.riskNameList" index="index" item="riskName" open="(" separator="or" close=")">
                ar.name LIKE CONCAT('%', #{riskName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
        order by arac.createtimestamp desc
        </if>
    </select>


    <select id="loadDetail" resultType="com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchResultPO">
        select
            apl.name as planName,
            ap.name as projectName,
            ar.name as riskName,
            apd.name as procedureName,
            ar.id as riskId,
            ar.project_id,
            ap.plan_id,
            ar.procedure_id,
            arac.id,
            arac.review_stage,
            arac.audit_reconcile,
            arac.punish_execution,
            arac.review_start_date,
            arac.review_end_date,
            arac.company_review_date,
            arac.punish_notice_date,
            arac.reconcile_apply_date,
            arac.punish_decision_date,
            arac.punish_implement_date,
            arac.review_start_date2,
            arac.review_end_date2,
            arac.company_review_date2,
            arac.review_stage2,
            arac.createtimestamp
        from
            audit_risk_ac arac
                left join audit_risk ar
                          on arac.risk_id = ar.id
                              and ar.status = 100
                              and ar.resp_dept_sug_type = 1
                              and ar.thread_latest_flag = 1
                              and ar.remove_flag = 0
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
                left join audit_procedure apd
                          on apd.id = ar.procedure_id
                              and apd.remove_flag = 0
        where
            arac.remove_flag = 0
          AND arac.risk_id = #{riskId}
    </select>

    <delete id="deleteByIds">
        update audit_risk_ac
        set remove_flag = 1,
        remover_id = #{operatorId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
