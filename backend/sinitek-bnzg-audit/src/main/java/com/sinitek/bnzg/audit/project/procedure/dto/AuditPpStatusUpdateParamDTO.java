package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * date 2024-08-28
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(value = "项目审计程序状态更新参数")
public class AuditPpStatusUpdateParamDTO {

    @ApiModelProperty(value = "项目审计程序ids")
    private List<Long> ppIds;

    @ApiModelProperty(value = "map,key:项目审计程序id,value:新状态")
    private Map<Long, Integer> idAndStatusMap;

    @ApiModelProperty(value = "map,key:项目审计程序id,value:审批人")
    private Map<Long, String> idAndApproverIdMap;

    @ApiModelProperty(value = "map,key:项目审计程序id,value:审批时间")
    private Map<Long, Date> idAndApproveTimeMap;

    @ApiModelProperty(value = "map,key:项目审计程序id,value:审批反馈")
    private Map<Long, String> idAndApproveRemarkMap;

    @ApiModelProperty(value = "操作人,后端自动生成,提交人")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成,提交人")
    private Date opTime;
}
