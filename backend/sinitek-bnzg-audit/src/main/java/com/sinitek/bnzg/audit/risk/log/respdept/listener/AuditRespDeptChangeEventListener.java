package com.sinitek.bnzg.audit.risk.log.respdept.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import com.sinitek.bnzg.audit.risk.log.respdept.event.AuditRiskRespDeptChangeEvent;
import com.sinitek.bnzg.audit.risk.log.respdept.service.IAuditRespDeptChangeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 12/06/2024 13:02
 */
@Slf4j
@Component
public class AuditRespDeptChangeEventListener<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, String> {

    @Autowired
    private IAuditRespDeptChangeLogService logService;

    @Override
    protected String getEventName() {
        return "责任部门变动日志";
    }

    @Override
    protected IAuditRespDeptChangeLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditRiskRespDeptChangeEvent.class, fallbackExecution = true)
    public void listen(
            AuditRiskRespDeptChangeEvent<E> event) {
        this.doListen(event);
    }
}
