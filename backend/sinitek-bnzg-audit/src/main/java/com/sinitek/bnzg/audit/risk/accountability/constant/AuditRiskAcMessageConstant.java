package com.sinitek.bnzg.audit.risk.accountability.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date：2024/11/15 15:11
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskAcMessageConstant {

    /**
     * 当前风险点已有问责管理数据
     */

    public static final String AUDIT_RISK_AC_EXISTS = "**********";

    /**
     * 当前风险点未审批通过，无法添加问责管理数据
     */
    public static final String AUDIT_RISK_AC_NOT_FINISH = "**********";

    /**
     * 当前风险点{}责任部门意见不同意，无法添加问责管理数据
     */
    public static final String AUDIT_RISK_AC_RESP_DEPT_NOT_AGREE = "**********";

    /**
     * 未选择风险点，无法添加问责管理数据
     */
    public static final String AUDIT_RISK_AC_NOT_SELECT_RISK = "**********";
}
