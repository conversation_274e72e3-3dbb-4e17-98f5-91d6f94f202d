package com.sinitek.bnzg.audit.project.procedure.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpFullDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpStatusUpdateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureExecutionListResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpExecutionSearchParamDTO;

/**
 * <AUTHOR>
 * @date 08/08/2024 09:54
 */
public interface IAuditProjectProcedureExecutionService {

    /**
     * 审批后更新
     */
    void updateAfterApprove(AuditPpStatusUpdateParamDTO param);

    AuditCheckResultDTO checkCanFinish(Long projectId);

    IPage<AuditProjectProcedureExecutionListResultDTO> searchPpExecutionList(
        PpExecutionSearchParamDTO param);

    AuditPpFullDetailDTO loadDetailById(Long id);
}
