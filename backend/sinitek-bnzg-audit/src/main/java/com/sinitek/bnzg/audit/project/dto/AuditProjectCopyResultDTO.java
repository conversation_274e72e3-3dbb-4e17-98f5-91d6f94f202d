package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目复制结果")
public class AuditProjectCopyResultDTO {

    /**
     * key: 旧项目id
     * value: 新项目id
     */
    @ApiModelProperty("项目id Map")
    private Map<Long, Long> projectIdMap;

    @ApiModelProperty("当前操作人")
    private String operatorId;

    @ApiModelProperty(value = "操作时间")
    private Date opTime;

}
