package com.sinitek.bnzg.audit.stage.enumation;

import com.sinitek.bnzg.audit.stage.constant.StageStatusConstant;
import java.util.Objects;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:15
 */
@Slf4j
@Getter
public enum StageStatusEnum {
    READY(StageStatusConstant.READY_NAME, StageStatusConstant.READY),
    PROCESSING(StageStatusConstant.PROCESSING_NAME, StageStatusConstant.PROCESSING),
    FINISHED(StageStatusConstant.FINISHED_NAME, StageStatusConstant.FINISHED),
    TERMINATE(StageStatusConstant.TERMINATE_NAME, StageStatusConstant.TERMINATE),
    UN_KNOWN("", -99);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    StageStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static StageStatusEnum getByValue(Integer value) {
        for (StageStatusEnum item : values()) {
            if (Objects.equals(item.getValue(), value)) {
                return item;
            }
        }
        log.warn("根据[{}]获取阶段状态,没有匹配的数据", value);
        return UN_KNOWN;
    }
}
