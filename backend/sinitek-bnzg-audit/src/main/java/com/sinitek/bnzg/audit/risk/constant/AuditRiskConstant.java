package com.sinitek.bnzg.audit.risk.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/29/2024 10:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskConstant {

    public static final String DEFAULT_SOURCE_NAME = "AUDIT_RISK";

    public static final String AUDIT_RISK_EXPECTED_FINISH_CALENDAR = "AUDIT_RISK_EXPECTED_FINISH";

    public static final String RISK_RECTIFY_NOTICE_CALENDAR_EVENT = "CE9999-01";

    public static final String RISK_RECTIFY_CHANGE_CALENDAR_EVENT = "CE9999-02";

    //问题描述附件description
    public static final Integer DESCRIPTION_UPLOAD_TYPE  = 1;

    //整改反馈附件
    public static final Integer RECTIFY_RESULT_UPLOAD_TYPE  = 0;
}
