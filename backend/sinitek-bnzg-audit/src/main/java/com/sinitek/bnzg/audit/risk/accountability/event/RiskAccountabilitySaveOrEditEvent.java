package com.sinitek.bnzg.audit.risk.accountability.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;

/**
 * <AUTHOR>
 * @Date：2024/11/15 15:57
 */
public class RiskAccountabilitySaveOrEditEvent <T extends AbstractRecordChangeLogAddParamBaseDTO> extends
        SiniCubeEvent<T> {
    public RiskAccountabilitySaveOrEditEvent(T source) {
        super(source);
    }

}
