package com.sinitek.bnzg.audit.risk.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/29/2024 10:40
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskRespDeeptSuggestTypeConstant {

    /**
     * 反对
     */
    public static final int DISAGREE = 0;

    /**
     * 同意
     */
    public static final int AGREE = 1;

    /**
     * 其他建议
     */
    public static final int OTHER = 2;

}