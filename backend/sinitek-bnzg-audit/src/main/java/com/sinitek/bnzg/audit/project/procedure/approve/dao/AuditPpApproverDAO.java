package com.sinitek.bnzg.audit.project.procedure.approve.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApprover;
import com.sinitek.bnzg.audit.project.procedure.approve.mapper.AuditPpApproverMapper;
import java.util.LinkedList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风险点审批操作人
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Slf4j
@Component
public class AuditPpApproverDAO extends ServiceImpl<AuditPpApproverMapper, AuditPpApprover> {

    public void saveApprovers(Long approveId, List<String> approverIds) {
        if (CollUtil.isNotEmpty(approverIds)) {
            List<AuditPpApprover> approvers = new LinkedList<>();
            for (int i = 0; i < approverIds.size(); i++) {
                String opOrgId = approverIds.get(i);
                AuditPpApprover approver = new AuditPpApprover();

                approver.setApproveId(approveId);
                approver.setOpOrgid(opOrgId);
                approver.setSort(i);

                approvers.add(approver);
            }

            this.saveBatch(approvers);
        }
    }

    public List<AuditPpApprover> findByApproveId(Long approveId) {
        LambdaQueryWrapper<AuditPpApprover> queryWrapper = Wrappers.lambdaQuery(
            AuditPpApprover.class);
        queryWrapper.eq(AuditPpApprover::getApproveId, approveId);
        queryWrapper.orderByAsc(AuditPpApprover::getSort);
        return this.list(queryWrapper);
    }

}
