package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 项目审计程序审批-返回DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目审计程序审批-查询结果DTO")
public class PpApproveResultSearchResultDTO extends PpApproveResultSearchResultBaseDTO {

    @ApiModelProperty("项目审计程序id")
    private Long ppId;
    
    @ApiModelProperty(value = "执行情况")
    private String execution;

    @ApiModelProperty(value = "风险点")
    private List<PpRiskApproveResultSearchResultDTO> children;

}
