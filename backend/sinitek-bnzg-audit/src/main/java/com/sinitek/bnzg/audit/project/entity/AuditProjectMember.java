package com.sinitek.bnzg.audit.project.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_project_member")
@ApiModel(description = "项目成员")
public class AuditProjectMember extends BaseEntity {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("人员OrgId")
    private String orgId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("角色")
    private Integer role;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;
}
