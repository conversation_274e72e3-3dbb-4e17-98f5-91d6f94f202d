package com.sinitek.bnzg.audit.project.procedure.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.event.AuditPpApprvStatusChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditPpApproveTodoTaskService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.sirm.common.event.annotation.SiniCubeEventListener;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpApprvStatusCreateEventListener {

    @Autowired
    private IAuditPpApproveTodoTaskService ppApproveTodoTaskService;

    /**
     * 项目审计程序 审批创建后:
     * 1.创建代办任务
     *
     * 为了防止创建失败,该行为为同步
     */
    @SiniCubeEventListener
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        AuditPpApprvStatusChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            log.info("监听到 单条 项目审计程序审批任务创建 事件,数据: {}",
                JsonUtil.toJsonString(event));
            RecordChangeLogAddParamDTO<Integer> singleData = (RecordChangeLogAddParamDTO<Integer>) source;
            this.dealSingle(singleData);
        } else {
            log.info("监听到 项目审计程序审批任务创建 批量事件,数据: {}",
                JsonUtil.toJsonString(event));
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source;
            Collection<Long> foreignKeys = batchParam.getForeignKeys();
            for (Long foreignKey : foreignKeys) {
                this.dealSingle(RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(foreignKey)
                    .newValue(batchParam.getNewValue(foreignKey))
                    .oldValue(batchParam.getOldValue(foreignKey))
                    .remark(batchParam.getRemark(foreignKey))
                    .operatorId(batchParam.getOperatorId(foreignKey))
                    .opTime(batchParam.getOpTime(foreignKey))
                    .build());
            }
        }
    }

    private void dealSingle(RecordChangeLogAddParamDTO<Integer> source) {
        Integer newStatus = source.getNewValue();
        Integer oldStatus = source.getOldValue();
        if (Objects.equals(newStatus, AuditRiskApproveStatusConstant.NOT_SUBMIT)
            && Objects.isNull(oldStatus)) {
            Long foreignKey = source.getForeignKey();
            String operatorId = source.getOperatorId();
            Date opTime = source.getOpTime();

            log.info("监听到 项目审计程序审批创建[{}]事件,开始生成代办任务", foreignKey);

            this.ppApproveTodoTaskService.create(PpApproveTodoTaskCreateParamDTO.builder()
                .ppApproveId(foreignKey)
                .operatorId(operatorId)
                .opTime(opTime)
                .build());
        }
    }
}
