package com.sinitek.bnzg.audit.risk.controller;

import com.sinitek.bnzg.audit.risk.service.IAuditRiskRectifyTraceNoticeService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目风险点 Controller
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@RestController
@RequestMapping("/frontend/api/audit/project/risk/remind")
@Api(value = "/frontend/api/audit/project/risk/remind", tags = "审计系统-项目风险点提醒")
public class AuditRiskRemindController {

    @Autowired
    private IAuditRiskRectifyTraceNoticeService riskRectifyTraceNoticeService;

    @ApiOperation(value = "整改状态变动提醒")
    @GetMapping("/rectify-status-change-reminder")
    public RequestResult<Void> rectifyStatusChangeReminder(
        @ApiParam("riskId(风险点id)") @Param("riskId") Long riskId) {
        this.riskRectifyTraceNoticeService.saveRectifyChangeNotice(riskId,
            CurrentUserFactory.getOrgId());
        return RequestResult.success();
    }

    @ApiOperation(value = "删除整改状态变动提醒日程")
    @GetMapping("/delete-rectify-status-change-reminder-calendar")
    public RequestResult<Void> deleteRectifyStatusChangeReminderCalendar(
        @ApiParam("riskId(风险点id)") @Param("riskId") Long riskId) {
        this.riskRectifyTraceNoticeService.deleteRectifyChangeCalendar(riskId,
            CurrentUserFactory.getOrgId());
        return RequestResult.success();
    }

    @ApiOperation(value = "取消整改状态变动提醒")
    @GetMapping("/delete-rectify-status-change-reminder")
    public RequestResult<Void> deleteRectifyStatusChangeReminder(
        @ApiParam("riskId(风险点id)") @Param("riskId") Long riskId) {
        this.riskRectifyTraceNoticeService.deleteRectifyChangeNotice(riskId,
            CurrentUserFactory.getOrgId());
        return RequestResult.success();
    }

    @ApiOperation(value = "整改提醒")
    @GetMapping("/deferral-reminder")
    public RequestResult<Void> deferralReminder(
        @ApiParam("riskId(风险点id)") @Param("riskId") Long riskId) {
        this.riskRectifyTraceNoticeService.saveRectifyTraceNotice(riskId,
            CurrentUserFactory.getOrgId());
        return RequestResult.success();
    }

    @ApiOperation(value = "删除整改提醒日程")
    @GetMapping("/delete-deferral-reminder-calendar")
    public RequestResult<Void> deleteDeferralReminderCalendar(
        @ApiParam("riskId(风险点id)") @Param("riskId") Long riskId) {
        this.riskRectifyTraceNoticeService.deleteRectifyTraceCalendar(riskId,
            CurrentUserFactory.getOrgId());
        return RequestResult.success();
    }

    @ApiOperation(value = "取消提醒")
    @GetMapping("/delete-reminder")
    public RequestResult<Void> deleteReminder(
        @ApiParam("riskId(风险点id)") @Param("riskId") Long riskId) {
        this.riskRectifyTraceNoticeService.deleteRectifyTraceNotice(riskId,
            CurrentUserFactory.getOrgId());
        return RequestResult.success();
    }
}
