package com.sinitek.bnzg.audit.risk.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "风险点审批-提交参数DTO")
public class RiskApproveSubmitParamDTO {

    @NotNull(message = "审批id不能为空")
    @ApiModelProperty("审批id")
    private Long approveId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
