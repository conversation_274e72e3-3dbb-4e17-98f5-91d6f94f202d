package com.sinitek.bnzg.audit.project.procedure.approve.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计程序审批结果 Entity
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_pp_approve_result")
@ApiModel(description = "项目审计程序审批结果")
public class AuditPpApproveResult extends BaseAuditEntity {

    /**
     * 审计程序审批id
     */
    @ApiModelProperty("审计程序审批id")
    private Long approveId;

    /**
     * 项目审计程序id
     */
    @ApiModelProperty("项目审计程序id")
    private Long ppId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("审计程序id")
    private Long procedureId;
    
    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批反馈")
    private String approveRemark;


    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date opTime;

    @ApiModelProperty("顺序")
    private Integer sort;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人
     */
    @ApiModelProperty("删除人")
    private String removerId;

}
