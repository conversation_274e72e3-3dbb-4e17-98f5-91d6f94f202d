package com.sinitek.bnzg.audit.project.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.entity.AuditProjectMember;
import com.sinitek.bnzg.audit.project.mapper.AuditProjectMemberMapper;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Slf4j
@Service
public class AuditProjectMemberDAO extends
    ServiceImpl<AuditProjectMemberMapper, AuditProjectMember> {

    public List<AuditProjectMember> findExistsMemberByProjectIds(Collection<Long> projectIds,
        Integer role) {
        return this.findExistsMembers(projectIds, Collections.singleton(role));
    }

    public List<AuditProjectMember> findExistsMembers(Collection<Long> projectIds,
        Collection<Integer> roles) {
        if (CollUtil.isNotEmpty(projectIds) && CollUtil.isNotEmpty(roles)) {
            LambdaQueryWrapper<AuditProjectMember> queryWrapper = Wrappers.lambdaQuery(
                AuditProjectMember.class);
            queryWrapper.in(AuditProjectMember::getProjectId, projectIds);
            queryWrapper.in(AuditProjectMember::getRole, roles);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

    public List<AuditProjectMember> findExistsMemberRoles(Collection<Long> projectIds,
        Collection<String> orgIds) {
        if (CollUtil.isNotEmpty(projectIds) && CollUtil.isNotEmpty(orgIds)) {
            LambdaQueryWrapper<AuditProjectMember> queryWrapper = Wrappers.lambdaQuery(
                AuditProjectMember.class);
            queryWrapper.in(AuditProjectMember::getProjectId, projectIds);
            queryWrapper.in(AuditProjectMember::getOrgId, orgIds);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

    public List<AuditProjectMember> findExistsMemberByProjectIds(Collection<Long> projectIds) {
        if (CollUtil.isNotEmpty(projectIds)) {
            LambdaQueryWrapper<AuditProjectMember> queryWrapper = Wrappers.lambdaQuery(
                AuditProjectMember.class);
            queryWrapper.in(AuditProjectMember::getProjectId, projectIds);
            queryWrapper.eq(AuditProjectMember::getRemoveFlag, CommonBooleanEnum.FALSE.getValue());
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

}
