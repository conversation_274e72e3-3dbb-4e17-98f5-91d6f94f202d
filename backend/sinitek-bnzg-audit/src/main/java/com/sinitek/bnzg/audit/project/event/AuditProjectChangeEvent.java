package com.sinitek.bnzg.audit.project.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 12/02/2024 13:52
 */
@ApiModel("审计项目变动事件")
public class AuditProjectChangeEvent <T extends AbstractRecordChangeLogAddParamBaseDTO> extends
        SiniCubeEvent<T> {
    public AuditProjectChangeEvent(T source) {
        super(source);
    }

}
