package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel(description = "项目成员追加参数")
public class AuditProjectMember4ImportSaveParamWrapperDTO {

    @Valid
    @ApiModelProperty("项目成员(集合)")
    private List<AuditProjectMember4ImportSaveParamDTO> list;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;


}
