package com.sinitek.bnzg.audit.project.log.phase.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.log.phase.entity.AuditProjectPhaseLog;
import com.sinitek.bnzg.audit.project.log.phase.mapper.AuditProjectPhaseLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class AuditProjectPhaseLogDAO extends
    ServiceImpl<AuditProjectPhaseLogMapper, AuditProjectPhaseLog> {

    public List<AuditProjectPhaseLog> findByProjectId(Long projectId) {
        LambdaQueryWrapper<AuditProjectPhaseLog> queryWrapper = Wrappers.lambdaQuery(
            AuditProjectPhaseLog.class);
        queryWrapper.eq(AuditProjectPhaseLog::getProjectId, projectId);
        return this.list(queryWrapper);
    }

}
