package com.sinitek.bnzg.audit.project.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditProjectProcedureSearchResultFormat implements
    ITableResultFormat<AuditProjectProcedureInfoDTO> {

    @Autowired
    private IAuditProcedureService procedureService;

    @Override
    public List<AuditProjectProcedureInfoDTO> format(List<AuditProjectProcedureInfoDTO> data) {

        List<Long> procedureIds = new LinkedList<>();
        data.forEach(item -> {
            procedureIds.add(item.getProcedureId());
        });
        // key: Id
        // value: 名称
        List<AuditProcedureBaseInfoDTO> list = this.procedureService.findExistsBaseInfoIds(
            procedureIds);
        Map<Long, String> procedureIdAndNameMap;
        if (CollUtil.isNotEmpty(list)) {
            procedureIdAndNameMap = list.stream().collect(
                Collectors.toMap(AuditProcedureBaseInfoDTO::getId,
                    AuditProcedureBaseInfoDTO::getName));
        } else {
            procedureIdAndNameMap = Collections.emptyMap();
        }

        data.forEach(item -> {
            Long procedureId = item.getProcedureId();

            item.setProcedureName(MapUtils.getString(procedureIdAndNameMap, procedureId));
        });

        return data;
    }
}
