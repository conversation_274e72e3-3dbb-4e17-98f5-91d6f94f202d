package com.sinitek.bnzg.audit.project.excelimport.service.impl;

import static com.sinitek.bnzg.audit.lib.excelimport.constant.AuditLibraryExcelImportMessageCodeConstant.ONLY_SUPPORT_SINGLE_EXCEL_FILE;
import static com.sinitek.bnzg.audit.lib.excelimport.constant.AuditLibraryExcelImportMessageCodeConstant.UPLOAD_FILE_IS_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.AUDIT_PLAN_NOT_FOUND;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.AUDIT_PROGRAM_NOT_FOUND;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.AUDIT_PROJECT_INSTANCE_NOT_FOUND;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.DATA_TO_SAVE_IS_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.DEFAULT_AUDIT_PROGRAM_IS_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.DEFAULT_AUDIT_PROGRAM_LIBRARY_IS_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.DEFAULT_PROJECT_MEMBER_IS_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.DEFAULT_PROJECT_OWNER_IS_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.ENUM_NAME_NOT_UNIQUE;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.FINDING_TYPE_EXCEL_DATA_NOT_MATCH;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.FIRST_CATALOG_EXCEL_DATA_NOT_MATCH;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.PROJECT_CODE_CANT_BE_EMPTY;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.PROJECT_CODE_NOT_YEAR;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.READ_EXCEL_FILE_FAILED;
import static com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportMessageCodeConstant.RISK_LEVEL_EXCEL_DATA_NOT_MATCH;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.sinitek.bnzg.audit.common.dto.AuditCommonImportParamDTO;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.constant.AuditPlanStatusConstant;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanModelDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectRoleConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectTypeConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMember4ImportSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMember4ImportSaveParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectModelDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureBatchSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureBatchSaveParamDTO.BatchSaveParamItem;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.excelimport.constant.AuditProjectExcelImportConstant;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectExcelImportWrapperDTO;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectRiskExcelParseDTO;
import com.sinitek.bnzg.audit.project.excelimport.service.IAuditProjectExcelImportService;
import com.sinitek.bnzg.audit.project.excelimport.support.ProjectRiskExcelImportListener;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRespDeeptSuggestTypeConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBatchSaveParamDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageExample4ImportParamDTO;
import com.sinitek.bnzg.audit.stage.service.IAuditStageExampleService;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.lowcode.common.service.ILcAttachmentService;
import com.sinitek.sirm.lowcode.sdk.service.IModelOperateSDKService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.io.File;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-12-26 15:32
 */
@Slf4j
@Service
public class AuditProjectExcelImportServiceImpl implements IAuditProjectExcelImportService {

    private static final int YEAR_START_POSITION = 0;

    private static final int YEAR_END_POSITION = 4;

    // 默认分割数,一般为审计系统使用人员数量
    private static final int DEFAULT_SPLIT_NUM = 2;

    @Autowired
    private IModelOperateSDKService lcSDKService;

    @Autowired
    private ILcAttachmentService lcAttachmentService;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private IAuditPlanService planService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProjectMemberService projectMemberService;

    @Autowired
    private IAuditStageExampleService auditStageExampleService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IAuditProjectProcedureService projectProcedureService;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Override
    public ProjectExcelImportWrapperDTO importAuditProject(AuditCommonImportParamDTO param) {
        UploadDTO upload = param.getUpload();
        String operatorId = param.getOperatorId();

        List<UploadFileDTO> uploadFileList = upload.getUploadFileList();
        if (CollUtil.isEmpty(uploadFileList)) {
            log.error("操作人 {} 上传文件为空,无法解析审计程序库excel", operatorId);
            throw new BussinessException(UPLOAD_FILE_IS_EMPTY);
        }

        int size = uploadFileList.size();
        if (size != 1) {
            log.error("操作人 {} 上传文件个数为 {},当前仅支持上传单个excel文件", operatorId, size);
            throw new BussinessException(ONLY_SUPPORT_SINGLE_EXCEL_FILE);
        }

        UploadFileDTO uploadFileDTO = uploadFileList.get(0);
        String name = uploadFileDTO.getName();
        File file = this.lcAttachmentService.getFileByUploadFile(uploadFileDTO);
        return this.doReadProcedureImportExcel(name, file);
    }

    @Override
    public ProjectExcelImportWrapperDTO readProjectImportExcel(String fileName,
        String fileStorePath) {
        File excelFile = new File(fileStorePath);
        return this.doReadProcedureImportExcel(fileName, excelFile);
    }

    private ProjectExcelImportWrapperDTO doReadProcedureImportExcel(String fileName,
        File excelFile) {
        List<ProjectRiskExcelParseDTO> list = this.parseExcel4ProcedureData(excelFile);

        // key: 类型值字符串
        // value: 名称
        // 风险类型
        Map<String, String> riskTypeMap = this.getEnumNameAndValueMap(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_TYPE);
        // 风险级别
        Map<String, String> riskLevelMap = this.getEnumNameAndValueMap(
            AuditRiskEnumConstant.DEFAULT_CATALOG,
            AuditRiskEnumConstant.RISK_LEVEL);
        // 一级分类
        Map<String, String> firstCatalogMap = this.getEnumNameAndValueMap(
            AuditRiskEnumConstant.DEFAULT_CATALOG,
            AuditRiskEnumConstant.FIRST_CATALOG);

        list.forEach(item -> {
            String serialno = item.getSerialno();
            String projectCode = item.getProjectCode();

            if (StringUtils.isBlank(projectCode)) {
                log.error("excel {} serialno {} 项目编码为空", fileName, serialno);
                throw new BussinessException(PROJECT_CODE_CANT_BE_EMPTY, serialno);
            }

            String yearStr = projectCode.substring(YEAR_START_POSITION, YEAR_END_POSITION);
            Integer auditYear;
            try {
                DateUtils.parseDate(yearStr, "yyyy");
                auditYear = Integer.parseInt(yearStr);
            } catch (Exception e) {
                log.error("excel {} serialno {} 项目编码 {} 前4位解析为年份报错, {}", fileName,
                    serialno, projectCode, e.getMessage(), e);
                throw new BussinessException(PROJECT_CODE_NOT_YEAR, serialno, projectCode);
            }

            // 前4位为所属审计年份
            item.setAuditYear(auditYear);
            // 计划名称根据审计年份命名,例如`2019年审计计划`
            item.setAuditPlanName(String.format("%s年审计计划", auditYear));
            // 审计期 默认为所属审计年份前一年的年初(1.1)-年尾(12.31)
            item.setAuditPeriodStartDate(DateUtil.parse(String.format("%s-01-01", auditYear),
                GlobalConstant.TIME_FORMAT_TEN));
            item.setAuditPeriodEndDate(DateUtil.parse(String.format("%s-12-31", auditYear),
                GlobalConstant.TIME_FORMAT_TEN));
            // 风险点类型
            this.setEnumValue(item, ProjectRiskExcelParseDTO::getAuditFindingType, riskTypeMap,
                item::setAuditFindingTypeValue, FINDING_TYPE_EXCEL_DATA_NOT_MATCH);
            // 风险级别
            this.setEnumValue(item, ProjectRiskExcelParseDTO::getRiskLevel, riskLevelMap,
                item::setRiskLevelValue, RISK_LEVEL_EXCEL_DATA_NOT_MATCH);
            // 一级分类
            this.setEnumValue(item, ProjectRiskExcelParseDTO::getFirstLevelClassification,
                firstCatalogMap,
                item::setFirstLevelClassificationValue, FIRST_CATALOG_EXCEL_DATA_NOT_MATCH);
        });

        return ProjectExcelImportWrapperDTO.builder()
            .fileName(fileName)
            .risks(list)
            .build();
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<String, String> getEnumNameAndValueMap(String catalog, String type) {
        Map<String, String> map = this.enumService.getSirmEnumByCataLogAndType(catalog, type);
        if (CollUtil.isNotEmpty(map)) {
            try {
                return map.entrySet().stream()
                    .collect(Collectors.toMap(Entry::getValue, Entry::getKey));
            } catch (Exception e) {
                log.error("枚举名称和值映射转换异常, catalog: {}, type: {}, map: {}, errmsg: {}",
                    catalog, type, map, e.getMessage(), e);
                throw new BussinessException(ENUM_NAME_NOT_UNIQUE, catalog, type);
            }
        }
        return Collections.emptyMap();
    }

    private void setEnumValue(ProjectRiskExcelParseDTO data,
        Function<ProjectRiskExcelParseDTO, String> enumNameGetter,
        Map<String, String> enumNameAndStrValueMap,
        Consumer<Integer> enumValueConsumer, String errorCode) {

        String serialno = data.getSerialno();
        String enumName = StringUtils.trim(enumNameGetter.apply(data));
        Integer enumValue = MapUtils.getInteger(enumNameAndStrValueMap, enumName,
            null);
        if (Objects.isNull(enumValue)) {
            log.error("根据枚举名称 [{}] 在 [{}] 中匹配不到枚举值", enumName,
                enumNameAndStrValueMap);
            throw new BussinessException(errorCode, serialno, enumName);
        }
        enumValueConsumer.accept(enumValue);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProject(ProjectExcelImportWrapperDTO data) {
        String fileName = data.getFileName();
        String operatorId = data.getOperatorId();
        Date opTime = data.getOpTime();
        List<ProjectRiskExcelParseDTO> risks = data.getRisks();

        if (CollUtil.isEmpty(risks)) {
            log.info("操作人 {} 导入 {} 数据为空", operatorId, fileName);
            throw new BussinessException(DATA_TO_SAVE_IS_EMPTY);
        }

        String defaultLibIdStr = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IMPORT_PROJECT_DEFAULT_LIBRARY);
        Long defaultLibId = Long.valueOf(defaultLibIdStr);
        if (IdUtil.isDataId(defaultLibId)) {
            log.info("导入项目对应审计程序库为 {}", defaultLibId);
        } else {
            log.error("导入项目对应审计程序库为空, 字符串:{},long:{}", defaultLibIdStr,
                defaultLibId);
            throw new BussinessException(DEFAULT_AUDIT_PROGRAM_LIBRARY_IS_EMPTY);
        }

        String defaultMembersStr = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IMPORT_PROJECT_DEFAULT_MEMBERS);
        List<String> defaultMembers;
        if (StringUtils.isNotBlank(defaultMembersStr)) {
            defaultMembers = Arrays.stream(defaultMembersStr.split(",")).distinct()
                .collect(
                    Collectors.toList());
        } else {
            defaultMembers = null;
        }
        if (Objects.isNull(defaultMembers) || CollUtil.isEmpty(defaultMembers)) {
            log.error("默认项目成员不能为空,系统配置 {},{} 不能为空",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.IMPORT_PROJECT_DEFAULT_MEMBERS);
            throw new RuntimeException(DEFAULT_PROJECT_MEMBER_IS_EMPTY);
        }

        Map<String, String> auditorAndApproverMap = new HashMap<>(defaultMembers.size());
        for (int i = 0; i < defaultMembers.size(); i++) {
            String auditor = defaultMembers.get(i);
            int nextIndex = i + 1;
            if (nextIndex >= defaultMembers.size()) {
                nextIndex = 0;
            }
            String approver = defaultMembers.get(nextIndex);
            auditorAndApproverMap.put(auditor, approver);
        }

        List<AuditProcedureBaseInfoDTO> procedures = this.procedureService.findExistsBaseInfoByLibId(
            defaultLibId);
        if (CollUtil.isEmpty(procedures)) {
            log.error("导入项目对应审计程序库[{}]中的审计程序为空", defaultLibId);
            throw new BussinessException(DEFAULT_AUDIT_PROGRAM_IS_EMPTY);
        }

        log.info("操作人 {} 导入 {} 共 {} 条数据", operatorId, fileName, risks.size());

        List<String> auditPlanNames = new LinkedList<>();
        Map<String, Integer> auditPlanNameAndYearMap = new HashMap<>(
            risks.size() / DEFAULT_SPLIT_NUM);
        List<String> auditProjectNames = new LinkedList<>();
        Map<String, String> auditProjectNameAndPlanNameMap = new HashMap<>(
            risks.size() / DEFAULT_SPLIT_NUM);

        risks.forEach(item -> {
            String auditPlanName = item.getAuditPlanName();
            Integer auditYear = item.getAuditYear();
            String projectName = item.getProjectName();

            Integer planYearInMap = MapUtils.getInteger(auditPlanNameAndYearMap, auditPlanName);
            if (Objects.isNull(planYearInMap)) {
                auditPlanNames.add(auditPlanName);
                auditPlanNameAndYearMap.put(auditPlanName, auditYear);
            }

            String planNameInMap = MapUtils.getString(auditProjectNameAndPlanNameMap, projectName);
            if (Objects.isNull(planNameInMap)) {
                auditProjectNames.add(projectName);
                auditProjectNameAndPlanNameMap.put(projectName, auditPlanName);
            }
        });

        // key: 计划名称,value: 计划id
        Map<String, Long> planNameAndIdMap = this.saveOrGetPlan(fileName, operatorId,
            auditPlanNames,
            auditPlanNameAndYearMap);

        // key: 项目名称,value: 项目id
        Map<String, Long> projectNameAndIdMap = this.saveOrGetProject(fileName, operatorId, opTime,
            auditProjectNames, auditProjectNameAndPlanNameMap, planNameAndIdMap, defaultMembers,
            auditPlanNameAndYearMap);

        Collection<Long> projectIds = projectNameAndIdMap.values();
        // 保存项目审计程序
        this.saveProjectProcedure(projectIds, procedures, operatorId, opTime, defaultMembers,
            auditorAndApproverMap);
        // 项目审计程序
        List<AuditProjectProcedureInfoDTO> projectProcedures = this.projectProcedureService.findByProjectIds(
            projectIds);

        // key:由产品id,审计程序id构成唯一key
        // value: 项目审计程序数据
        Map<String, AuditProjectProcedureInfoDTO> uKeyAndPpDataMap = new HashMap<>(
            procedures.size());
        projectProcedures.forEach(projectProcedure -> {
            Long projectId = projectProcedure.getProjectId();
            Long procedureId = projectProcedure.getProcedureId();
            String uniqueKey = this.getPorjectIdAndProcedureIdUniqueKey(projectId, procedureId);

            uKeyAndPpDataMap.put(uniqueKey, projectProcedure);
        });

        // 保存风险点
        this.saveRisk(procedures, risks, projectNameAndIdMap, uKeyAndPpDataMap,
            auditorAndApproverMap, defaultMembers, operatorId, opTime);

    }

    private void saveRisk(List<AuditProcedureBaseInfoDTO> procedures,
        List<ProjectRiskExcelParseDTO> risks,
        Map<String, Long> projectNameAndIdMap,
        Map<String, AuditProjectProcedureInfoDTO> uKeyAndPpDataMap,
        Map<String, String> auditorAndApproverMap,
        List<String> defaultMembers, String operatorId, Date opTime) {
        List<AuditRisk> needSaveRisks = new LinkedList<>();
        int procedureSize = procedures.size();
        for (int i = 0; i < risks.size(); i++) {
            ProjectRiskExcelParseDTO projectRiskExcelParseData = risks.get(i);
            String projectName = projectRiskExcelParseData.getProjectName();
            Long realProjectId = projectNameAndIdMap.get(projectName);

            if (IdUtil.isNotDataId(realProjectId)) {
                log.error("导入数据时,项目[{}]在[{}]中不存在对应项目数据", projectName,
                    projectNameAndIdMap);
                throw new BussinessException(AUDIT_PROJECT_INSTANCE_NOT_FOUND, projectName);
            }

            AuditProcedureBaseInfoDTO procedure = procedures.get(
                ((i + 1) % procedureSize));

            AuditRisk realRisk = new AuditRisk();

            // 风险点名称: 审计发现标题
            realRisk.setName(projectRiskExcelParseData.getAuditFindingTitle());
            // 描述: 问题描述
            realRisk.setDescription(projectRiskExcelParseData.getProblemDescription());
            // 类型: 审计发现类型
            realRisk.setType(projectRiskExcelParseData.getAuditFindingTypeValue());
            // 风险级别
            realRisk.setLevel(projectRiskExcelParseData.getRiskLevelValue());
            // 一级分类
            realRisk.setFirstCatalog(projectRiskExcelParseData.getFirstLevelClassificationValue());
            // 二级分类
            realRisk.setInnerCategory(projectRiskExcelParseData.getSecondLevelClassification());
            // 审计建议: 管理建议
            realRisk.setAuditSuggestion(projectRiskExcelParseData.getManagementSuggestion());
            // 责任部门意见类型: 均为同意
            realRisk.setRespDeptSugType(AuditRiskRespDeeptSuggestTypeConstant.AGREE);
            // 责任部门意见反馈: 管理答复
            realRisk.setRespDeptSuggestion(projectRiskExcelParseData.getManagementResponse());
            // 责任人: 均为空
            // 责任部门: 均为空
            // 是否重复发生: 均为空

            Long procedureId = procedure.getId();
            // 产品id
            realRisk.setProjectId(realProjectId);
            // 审计程序id
            realRisk.setProcedureId(procedureId);
            String uniqueKey = this.getPorjectIdAndProcedureIdUniqueKey(
                realProjectId, procedureId);
            AuditProjectProcedureInfoDTO ppData = MapUtils.getObject(uKeyAndPpDataMap, uniqueKey);
            if (Objects.isNull(ppData)) {
                log.warn("项目[{}]审计程序[{}]未找到对应项目审计程序数据", realProjectId,
                    procedureId);
                throw new BussinessException(AUDIT_PROGRAM_NOT_FOUND,
                    projectRiskExcelParseData.getProjectName(), procedure.getName());
            }
            String auditorId = ppData.getAuditorId();
            // 审计人
            realRisk.setAuditorId(auditorId);

            // 要求整改日期: 整改实施计划日期 2020-12-31 00:00:00.000
            String rectificationPlanDateStr = projectRiskExcelParseData.getRectificationPlanDate();
            if (StringUtils.isNotBlank(rectificationPlanDateStr)) {
                Date rectificationPlanDate = DateUtil.parse(rectificationPlanDateStr,
                    GlobalConstant.TIME_FORMAT_THIRTEEN);
                realRisk.setReqRectifyDate(rectificationPlanDate);
            }

            // 实际整改日期: 整改实施实际日期 2019-12-31 15:20:37.000
            String rectificationActualDateStr = projectRiskExcelParseData.getRectificationActualDate();
            if (StringUtils.isNotBlank(rectificationActualDateStr)) {
                Date rectificationActualDate = DateUtil.parse(rectificationActualDateStr,
                    GlobalConstant.TIME_FORMAT_THIRTEEN);
                realRisk.setActRectifyDate(rectificationActualDate);
            }

            // 整改状态: 均为 已完成
            realRisk.setRectifyState(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH);

            // 审批人
            String approver = auditorAndApproverMap.get(auditorId);
            if (StringUtils.isBlank(approver)) {
                log.warn("审计人[{}]在项目成员列表中[{}]对应审批人为空[{}]", auditorId,
                    defaultMembers, approver);
            }
            realRisk.setApproverId(approver);
            // 审批状态: 均为 已审批
            realRisk.setStatus(AuditRiskStatusConstant.APPROVED);
            // 均为最新数据
            realRisk.setThreadLatestFlag(CommonBooleanEnum.TRUE.getValue());

            needSaveRisks.add(realRisk);
        }

        this.auditRiskService.batchSave(AuditRiskBatchSaveParamDTO.builder()
            .list(needSaveRisks)
            .operatorId(operatorId)
            .opTime(opTime)
            .build());
    }

    private String getPorjectIdAndProcedureIdUniqueKey(Long projectId, Long procedureId) {
        return String.format("%s-%s", projectId, procedureId);
    }

    private void saveProjectProcedure(Collection<Long> projectIds,
        List<AuditProcedureBaseInfoDTO> procedures, String operatorId, Date opTime,
        List<String> defaultMembers, Map<String, String> auditorAndApproverMap) {
        List<BatchSaveParamItem> needSaveList = new LinkedList<>();
        // 每一个审计程序分配一个人处理
        int memberSize = defaultMembers.size();
        int procedureSize = procedures.size();
        // key:程序id,value:对应处理人
        Map<Long, String> procedureIdAndOrgId = new HashMap<>(procedureSize);
        for (int i = 0; i < procedures.size(); i++) {
            AuditProcedureBaseInfoDTO procedure = procedures.get(i);
            int index = (i + 1) % memberSize;
            Long id = procedure.getId();
            String orgId = defaultMembers.get(index);
            procedureIdAndOrgId.put(id, orgId);
        }
        List<Long> procedureIds = procedures.stream().map(AuditProcedureBaseInfoDTO::getId)
            .collect(Collectors.toList());
        projectIds.forEach(projectId -> needSaveList.add(BatchSaveParamItem.builder()
            .projectId(projectId)
            .procedureIdList(procedureIds)
            .build()));
        this.projectProcedureService.batchSave(AuditProjectProcedureBatchSaveParamDTO.builder()
            .list(needSaveList)
            .procedureIdAndOrgId(procedureIdAndOrgId)
            .auditorAndApproverMap(auditorAndApproverMap)
            .operatorId(operatorId)
            .opTime(opTime)
            .build());
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<String, Long> saveOrGetProject(String fileName,
        String operatorId, Date opTime, List<String> distinctProjectNames,
        Map<String, String> auditProjectNameAndPlanNameMap,
        Map<String, Long> planNameAndIdMap, List<String> defaultMembers,
        Map<String, Integer> auditPlanNameAndYearMap) {
        Map<String, Long> projectNameAndIdMap = new HashMap<>(distinctProjectNames.size());

        log.info("操作人 {} 导入 {} 中共有 {} 个审计项目, {}", operatorId, fileName,
            distinctProjectNames.size(), distinctProjectNames);

        String defaultOwner = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IMPORT_PROJECT_DEFAULT_OWNER);
        if (StringUtils.isBlank(defaultOwner)) {
            log.error("默认项目所有人不能为空,系统配置 {},{} 不能为空",
                BnzgSettingConstant.DEFAULT_MODULE,
                BnzgSettingConstant.IMPORT_PROJECT_DEFAULT_OWNER);
            throw new RuntimeException(DEFAULT_PROJECT_OWNER_IS_EMPTY);
        }

        List<AuditProjectInfoDTO> projects = this.projectService.findExistsProjectByNames(
            distinctProjectNames);
        if (CollUtil.isEmpty(projects)) {
            projects.forEach(project -> {
                Long id = project.getId();
                String name = project.getName();
                projectNameAndIdMap.put(name, id);
                log.info("根据名称 {} 查询到 id 为 {} 的审计项目", name, id);
            });
        }

        List<String> needSaveProjectNames = distinctProjectNames.stream().filter(projectName -> {
            Long id = projectNameAndIdMap.get(projectName);
            return Objects.isNull(id);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(needSaveProjectNames)) {
            log.info("操作人 {} 导入 {} 中有 {} 个审计项目需要新增, {}", operatorId, fileName,
                needSaveProjectNames.size(), needSaveProjectNames);

            List<AuditProjectModelDTO> needSaveProjects = needSaveProjectNames.stream()
                .map(projectName -> {
                    AuditProjectModelDTO project = new AuditProjectModelDTO();
                    project.setName(projectName);

                    String planName = MapUtils.getString(auditProjectNameAndPlanNameMap,
                        projectName);
                    Long planId = MapUtils.getLong(planNameAndIdMap, planName);
                    if (IdUtil.isNotDataId(planId)) {
                        log.info("新增审计项目 {} 对应的审计计划 {} id不存在", projectName,
                            planName);
                        throw new BussinessException(AUDIT_PLAN_NOT_FOUND, projectName, planName);
                    }
                    project.setPlanId(planId);

                    // 审计期: 默认为所属审计年份前一年的年初(1.1)-年尾(12.31)
                    Integer year = MapUtils.getInteger(auditPlanNameAndYearMap, planName);
                    project.setPeriodStartDate(DateUtil.parse(String.format("%s-01-01", year),
                        GlobalConstant.TIME_FORMAT_TEN));
                    project.setPeriodEndDate(DateUtil.parse(String.format("%s-12-31", year),
                        GlobalConstant.TIME_FORMAT_TEN));
                    // 项目进度: 已关闭
                    project.setProjectPhase(AuditProjectPhaseConstant.CLOSE);
                    // 项目类型: 计划内
                    project.setProjectType(AuditProjectTypeConstant.WITHIN_PLAN);
                    // 是否报送均为否
                    project.setRegulatoryFlag(CommonBooleanEnum.FALSE.getValue());

                    return project;
                }).collect(Collectors.toList());

            RequestResult<Void> saveProjectResutl = this.lcSDKService.saveOrUpdateBatch(
                needSaveProjects);
            if (saveProjectResutl.isSuccess()) {
                List<Long> newGenerateProjectIds = new LinkedList<>();
                List<AuditProjectMember4ImportSaveParamDTO> list = new LinkedList<>();
                needSaveProjects.forEach(project -> {
                    Long projectId = project.getId();
                    newGenerateProjectIds.add(projectId);

                    String name = project.getName();
                    projectNameAndIdMap.put(name, projectId);
                    log.info("新增审计项目 {} 的 id 为 {}", name, projectId);

                    // 项目负责人
                    list.add(AuditProjectMember4ImportSaveParamDTO.builder()
                        .projectId(projectId)
                        .role(AuditProjectRoleConstant.OWNER)
                        .orgId(defaultOwner)
                        .remark("历史项目导入")
                        .build());
                    // 项目成员
                    defaultMembers.forEach(member -> {
                        list.add(AuditProjectMember4ImportSaveParamDTO.builder()
                            .projectId(projectId)
                            .role(AuditProjectRoleConstant.APPROVE_AND_EDIT)
                            .orgId(member)
                            .remark("历史项目导入")
                            .build());
                    });

                });

                // 保存项目负责人,保存项目成员
                AuditProjectMember4ImportSaveParamWrapperDTO param = new AuditProjectMember4ImportSaveParamWrapperDTO();
                param.setOperatorId(operatorId);
                param.setOpTime(opTime);
                param.setList(list);
                this.projectMemberService.saveMembers4Import(param);

                // 开启项目流程
                this.auditStageExampleService.generateStageExample4Import(
                    GenerateStageExample4ImportParamDTO.builder()
                        .projectIds(newGenerateProjectIds)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .build());
            } else {
                throw new BussinessException(saveProjectResutl.getMessage());
            }
        } else {
            log.info("操作人 {} 导入 {} 中没有需要新增的审计项目", operatorId, fileName);
        }

        return projectNameAndIdMap;
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<String, Long> saveOrGetPlan(String fileName,
        String operatorId, List<String> distinctPlanNames,
        Map<String, Integer> auditPlanNameAndYearMap) {
        Map<String, Long> planNameAndIdMap = new HashMap<>(distinctPlanNames.size());

        log.info("操作人 {} 导入 {} 中共有 {} 个审计计划, {}", operatorId, fileName,
            distinctPlanNames.size(), distinctPlanNames);

        List<AuditPlanInfoDTO> plans = this.planService.findExistsPlanByNames(distinctPlanNames);
        if (CollUtil.isEmpty(plans)) {
            plans.forEach(plan -> {
                Long id = plan.getId();
                String name = plan.getName();
                planNameAndIdMap.put(name, id);
                log.info("根据名称 {} 查询到 id 为 {} 的审计计划", name, id);
            });
        }

        List<String> needSavePlanNames = distinctPlanNames.stream().filter(planName -> {
            Long id = planNameAndIdMap.get(planName);
            return Objects.isNull(id);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(needSavePlanNames)) {
            log.info("操作人 {} 导入 {} 中有 {} 个审计计划需要新增, {}", operatorId, fileName,
                needSavePlanNames.size(), needSavePlanNames);

            List<AuditPlanModelDTO> needSavePlans = needSavePlanNames.stream().map(planName -> {
                AuditPlanModelDTO plan = new AuditPlanModelDTO();
                plan.setName(planName);

                Integer year = MapUtils.getInteger(auditPlanNameAndYearMap, planName);
                plan.setYear(year);
                plan.setStatus(AuditPlanStatusConstant.END);
                return plan;
            }).collect(Collectors.toList());

            RequestResult<Void> savePlanResutl = this.lcSDKService.saveOrUpdateBatch(needSavePlans);
            if (savePlanResutl.isSuccess()) {
                needSavePlans.forEach(plan -> {
                    Long id = plan.getId();
                    String name = plan.getName();
                    planNameAndIdMap.put(name, id);
                    log.info("新增审计计划 {} 的 id 为 {}", name, id);
                });
            } else {
                throw new BussinessException(savePlanResutl.getMessage());
            }
        } else {
            log.info("操作人 {} 导入 {} 中没有需要新增的审计计划", operatorId, fileName);
        }

        return planNameAndIdMap;
    }

    private List<ProjectRiskExcelParseDTO> parseExcel4ProcedureData(File excelFile) {
        String filePath = FileUtil.getCanonicalPath(excelFile);

        ProjectRiskExcelImportListener listener = new ProjectRiskExcelImportListener();
        ExcelReaderBuilder readerBuilder = EasyExcelFactory.read(excelFile,
            ProjectRiskExcelParseDTO.class,
            listener);
        try (ExcelReader reader = readerBuilder.headRowNumber(
                AuditProjectExcelImportConstant.HEADER_ROW_NUMBER).extraRead(CellExtraTypeEnum.MERGE)
            .build()) {
            List<ReadSheet> readSheets = reader.excelExecutor().sheetList();

            ReadSheet readSheet1 = readSheets.get(0);
            log.info("sheetName: {}, sheetNo: {} 需要读取", readSheet1.getSheetName(),
                readSheet1.getSheetNo());

            reader.read(readSheet1);

            return listener.getAllData();
        } catch (Exception e) {
            log.error("读取审计项目风险点 excel文件 {} 失败,{}", filePath, e.getMessage(), e);
            if (e instanceof BussinessException) {
                throw e;
            } else {
                throw new BussinessException(READ_EXCEL_FILE_FAILED);
            }
        }
    }
}
