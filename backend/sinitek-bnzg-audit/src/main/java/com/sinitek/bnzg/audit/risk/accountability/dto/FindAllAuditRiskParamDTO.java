package com.sinitek.bnzg.audit.risk.accountability.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/11/26 14:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目风险点问责情况查询参数DTO")
public class FindAllAuditRiskParamDTO extends PageDataParam {

    @ApiModelProperty(value = "审计计划")
    private List<Long> planIds;

    @ApiModelProperty("审计项目")
    private String projectName;

    @ApiModelProperty("审计程序")
    private String procedureName;

    @ApiModelProperty("风险点id")
    private Long riskId;

}
