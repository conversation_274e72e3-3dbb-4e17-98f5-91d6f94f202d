package com.sinitek.bnzg.audit.project.procedure.approve.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024-12-02 21:18
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "项目审计程序审批结果创建参数")
public class AuditPpApproveResultCreateParamPO {

    @ApiModelProperty("审批id")
    private Long approveId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目审计程序id")
    List<Long> ppIds;

    @ApiModelProperty("项目审计程序id和审计程序idMap")
    Map<Long, Long> ppIdAndProcedureIdMap;

    @ApiModelProperty("项目审计程序id和状态Map")
    Map<Long, Integer> ppidAndApproveResultMap;

}
