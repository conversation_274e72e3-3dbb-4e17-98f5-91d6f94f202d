package com.sinitek.bnzg.audit.risk.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/9/25 15:31
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "审计发现及整改进度表-返回DTO")
public class AuditFindRectifyChartReasultDTO {

    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "发现的风险点个数")
    private Integer totalRiskCount;

    @ApiModelProperty(value = "整改完成的风险点个数")
    private Integer finishRiskCount;

    @ApiModelProperty("计划和项目名称")
    private String planAndProjectName;
}
