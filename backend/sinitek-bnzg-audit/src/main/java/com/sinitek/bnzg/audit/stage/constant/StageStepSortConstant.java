package com.sinitek.bnzg.audit.stage.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/15/2024 11:08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStepSortConstant {

    /**
     * 审计准备
     */
    public static final int AUDIT_PREPARING_SORT = 1;

    /**
     * 审计实施
     */
    public static final int AUDIT_EXECUTION_SORT = 2;

    /**
     * 审计报告
     */
    public static final int AUDIT_REPORT_SORT = 3;

    /**
     * 整改跟踪
     */
    public static final int RECTIFICATION_TRACKING_SORT = 4;
}
