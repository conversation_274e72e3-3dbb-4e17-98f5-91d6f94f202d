package com.sinitek.bnzg.audit.risk.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.common.constant.EnumConstant;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditRiskSearchResultFormat<T extends AuditRiskBaseDTO> implements
    ITableResultFormat<T> {

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IRectifyContactService rectifyContactService;

    @Autowired
    private IAuditPlanService planService;

    @Override
    public List<T> format(List<T> data) {

        if (CollUtil.isEmpty(data)) {
            return data;
        }

        List<Long> ids = data.stream().map(T::getId).collect(Collectors.toList());

        // key: 风险点id,value: 整改联系人
        Map<Long, List<String>> contactMap = this.rectifyContactService.getContactMap(ids);

        List<String> orgIds = new LinkedList<>();
        List<Long> projectIds = new LinkedList<>();
        List<Long> procedureIds = new LinkedList<>();

        boolean isAuditRiskDetailDTO = data.get(0) instanceof AuditRiskDetailDTO;

        data.forEach(item -> {
            Long id = item.getId();
            List<String> respManIds = item.getRespManId();
            List<String> respDeptIds = item.getRespDeptId();
            if (CollUtil.isNotEmpty(respManIds)) {
                orgIds.addAll(respManIds);
            }
            if (CollUtil.isNotEmpty(respDeptIds)) {
                orgIds.addAll(respDeptIds);
            }

            List<String> contacts = contactMap.get(id);
            orgIds.add(item.getAuditorId());
            orgIds.add(item.getApproverId());

            item.setRectifyContactIds(contacts);
            if (CollUtil.isNotEmpty(item.getRectifyContactIds())) {
                orgIds.addAll(item.getRectifyContactIds());
            }

            projectIds.add(item.getProjectId());

            procedureIds.add(item.getProcedureId());
        });
        // key: orgId
        // value: 名称
        Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
            orgIds.stream().distinct().filter(Objects::nonNull).collect(Collectors.toList()));

        List<AuditProjectInfoDTO> projects = this.projectService.findExistsProjctByIds(
            projectIds);

        Map<Long, String> projectIdAndNameMap;
        Map<Long, Long> projectIdAndPlanIdMap;
        List<Long> planIds = new LinkedList<>();
        if (CollUtil.isNotEmpty(projects)) {
            projectIdAndNameMap = new HashMap<>(projects.size());
            projectIdAndPlanIdMap = new HashMap<>(projects.size());

            projects.forEach(item -> {
                Long id = item.getId();
                Long planId = item.getPlanId();
                String name = item.getName();

                projectIdAndNameMap.put(id, name);
                projectIdAndPlanIdMap.put(id, planId);
                planIds.add(planId);
            });

        } else {
            projectIdAndNameMap = Collections.emptyMap();
            projectIdAndPlanIdMap = Collections.emptyMap();
        }

        Map<Long, String> planIdAndNameMap;
        if (isAuditRiskDetailDTO) {
            List<AuditPlanInfoDTO> plans = this.planService.findExistsInfoByIds(planIds);
            if (CollUtil.isNotEmpty(plans)) {
                planIdAndNameMap = new HashMap<>(plans.size());
                plans.forEach(item -> {
                    Long id = item.getId();
                    String name = item.getName();
                    planIdAndNameMap.put(id, name);
                });
            } else {
                planIdAndNameMap = Collections.emptyMap();
            }
        } else {
            planIdAndNameMap = Collections.emptyMap();
        }

        Map<Long, String> procedureIdAndNameMap = this.getProcedureMap(
            procedureIds.stream().distinct().collect(Collectors.toList()));

        // key: 类型值字符串
        // value: 名称
        // 风险点状态
        Map<String, String> riskStatusMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_STATUS);
        // 风险类型
        Map<String, String> riskTypeMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_TYPE);
        // 风险级别
        Map<String, String> riskLevelMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_LEVEL);
        // 责任部门建议类型
        Map<String, String> respDeptSuggestTypeMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RESP_DEPT_SUGGEST_TYPE);
        // 整改状态
        Map<String, String> rectifyStatusMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RECTIFY_STATUS);
        //制度方向
        Map<String, String> firstCatalogValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.FIRST_CATALOG);
        //是否重复发生
        Map<String, String> repeatFlagValueAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            EnumConstant.COMMON, EnumConstant.BOOLEAN_TYPE);

        data.forEach(item -> {
            item.setProjectName(MapUtils.getString(projectIdAndNameMap, item.getProjectId()));
            item.setProcedureName(MapUtils.getString(procedureIdAndNameMap, item.getProcedureId()));

            item.setTypeName(MapUtils.getString(riskTypeMap, String.valueOf(item.getType())));
            item.setLevelName(MapUtils.getString(riskLevelMap, String.valueOf(item.getLevel())));
            item.setRespDeptSugTypeName(MapUtils.getString(respDeptSuggestTypeMap,
                String.valueOf(item.getRespDeptSugType())));

            String respDeptNames = Optional.ofNullable(item.getRespDeptId())
                .orElse(Collections.emptyList())
                .stream()
                .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(","));
            item.setRespDeptName(respDeptNames);

            String respManNames = Optional.ofNullable(item.getRespManId())
                .orElse(Collections.emptyList())
                .stream()
                .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(","));
            item.setRespManName(respManNames);

            item.setAuditorName(MapUtils.getString(orgIdAndNameMap, item.getAuditorId()));
            item.setApproverName(MapUtils.getString(orgIdAndNameMap, item.getApproverId()));

            item.setRectifyStateName(
                MapUtils.getString(rectifyStatusMap, String.valueOf(item.getRectifyState())));
            item.setStatusName(MapUtils.getString(riskStatusMap, String.valueOf(item.getStatus())));
            item.setFirstCatalogName(
                MapUtils.getString(firstCatalogValueAndNameMap,
                    String.valueOf(item.getFirstCatalog())));
            // 整改联系人
            List<String> contacts = item.getRectifyContactIds();
            if (CollUtil.isNotEmpty(contacts)) {
                String contactNames = contacts.stream()
                    .map(contact -> MapUtils.getString(orgIdAndNameMap, contact))
                    .collect(Collectors.joining(","));
                item.setRectifyContactNames(contactNames);
            }
            item.setRepeatFlagName(MapUtils.getString(repeatFlagValueAndNameMap,
                String.valueOf(item.getRepeatFlag())));

            if (isAuditRiskDetailDTO) {
                AuditRiskDetailDTO dto = (AuditRiskDetailDTO) item;
                dto.setRiskName(dto.getName());

                Long projectId = dto.getProjectId();
                Long planId = MapUtils.getLong(projectIdAndPlanIdMap, projectId);
                String planName = MapUtils.getString(planIdAndNameMap, planId);
                dto.setPlanId(planId);
                dto.setPlanName(planName);
            }
        });

        return data;
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<Long, String> getProcedureMap(Collection<Long> ids) {
        List<AuditProcedureBaseInfoDTO> projects = this.procedureService.findExistsBaseInfoIds(
            ids);
        if (CollUtil.isNotEmpty(projects)) {
            return projects.stream().collect(
                Collectors.toMap(AuditProcedureBaseInfoDTO::getId,
                    AuditProcedureBaseInfoDTO::getName));
        } else {
            return Collections.emptyMap();
        }
    }
}
