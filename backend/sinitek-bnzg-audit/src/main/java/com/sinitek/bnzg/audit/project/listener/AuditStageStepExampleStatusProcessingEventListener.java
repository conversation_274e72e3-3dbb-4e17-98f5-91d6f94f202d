package com.sinitek.bnzg.audit.project.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.UpdateProjectPhaseParamDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.log.status.step.event.StageStepExampleStatusChangeEvent;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.util.StageStepUtil;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class AuditStageStepExampleStatusProcessingEventListener {

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IAuditProjectService projectService;

    /**
     * 步骤实例状态变化事件
     *
     * 这里只关心 审计准备,审计实施,审计报告 进行中 事件,更新项目进度
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = StageStepExampleStatusChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        StageStepExampleStatusChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            this.dealSingleData((RecordChangeLogAddParamDTO) source);
        } else if (source instanceof AbstractRecordChangeLogBatchAddBaseParamDTO) {
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> data = (AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source;
            Collection<Long> foreignKeys = data.getForeignKeys();
            for (Long foreignKey : foreignKeys) {
                this.dealSingleData(RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(foreignKey)
                    .remark(data.getRemark(foreignKey))
                    .newValue(data.getNewValue(foreignKey))
                    .oldValue(data.getOldValue(foreignKey))
                    .operatorId(data.getOperatorId(foreignKey))
                    .opTime(data.getOpTime(foreignKey))
                    .build());
            }
        }
    }


    private void dealSingleData(RecordChangeLogAddParamDTO<Integer> source) {
        Integer newValue = source.getNewValue();
        if (this.isNeedDealStageStepStatus(newValue)) {
            Long foreignKey = source.getForeignKey();
            Integer oldValue = source.getOldValue();
            String operatorId = source.getOperatorId();
            Date opTime = source.getOpTime();
            String remark = source.getRemark();

            log.info(
                "监听到审计步骤[{}] 进行中事件,操作人[{}],操作时间[{}],旧状态[{}] => 新状态[{}],备注[{}]",
                foreignKey, operatorId, opTime, oldValue, newValue, remark
            );

            List<StageStepExampleDTO> stageStepExamples = this.stageStepExampleService.findByIds(
                Collections.singleton(foreignKey));
            if (CollUtil.isNotEmpty(stageStepExamples)) {
                StageStepExampleDTO stageStepExample = stageStepExamples.get(0);

                // 更新项目阶段
                this.updateProjectPhase(stageStepExample, operatorId, opTime);
            } else {
                log.warn("根据阶段步骤实例id[{}]无法获取阶段步骤实例数据", foreignKey);
            }

        }
    }

    /**
     * 更新项目阶段
     */
    private void updateProjectPhase(StageStepExampleDTO stageStepExample, String operatorId,
        Date opTime) {
        Long stageStepExampleId = stageStepExample.getId();
        Long projectId = stageStepExample.getProjectId();

        Integer stepValue = stageStepExample.getStepValue();
        Integer calcProjectPhase = StageStepUtil.getProjectPhaseByStepValue(
            stepValue);

        AuditProjectInfoDTO project = this.projectService.getExistsProjectInfoById(
            projectId);
        if (Objects.nonNull(project)) {
            Integer projectPhaseInProject = project.getProjectPhase();
            if (Objects.nonNull(calcProjectPhase)) {
                if (projectPhaseInProject < calcProjectPhase) {
                    this.projectService.updateProjectPhase(UpdateProjectPhaseParamDTO.builder()
                        .projectId(projectId)
                        .newProjectPhase(calcProjectPhase)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark("阶段步骤变为进行中后自动更新产品阶段")
                        .build());
                } else {
                    log.warn(
                        "产品[{}]步骤实例[{}]进行中对应产品进度[{}]小于当前产品进度[{}],无需更新产品进度",
                        projectId, stageStepExampleId, calcProjectPhase, projectPhaseInProject);
                }
            } else {
                log.warn("步骤[{}]完成后获取到对应产品进度为null,无法继续更新产品阶段",
                    stepValue);
            }
        } else {
            log.warn(
                "根据项目id[{}]无法获取项目信息,无法在阶段步骤实例[{}]更新为进行中后更新项目解读",
                projectId, stageStepExampleId);
        }


    }

    private boolean isNeedDealStageStepStatus(Integer status) {
        return Objects.equals(StageStepStatusConstant.PROCESSING, status);
    }
}
