package com.sinitek.bnzg.audit.risk.approve.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "风险点审批结果-分页查询PO")
public class RiskApproveResultSearchParamPO extends PageDataParam {

    @ApiModelProperty(value = "审批id")
    private Long approveId;

}
