package com.sinitek.bnzg.audit.project.procedure.service;

import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskFinishParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskProcessingParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskTerminateParamDTO;

/**
 * <AUTHOR>
 * @date 09/09/2024 16:22
 */
public interface IAuditPpApproveTodoTaskService {

    void create(PpApproveTodoTaskCreateParamDTO param);

    void processing(PpApproveTodoTaskProcessingParamDTO param);

    void finish(PpApproveTodoTaskFinishParamDTO param);

    void terminate(PpApproveTodoTaskTerminateParamDTO param);

}
