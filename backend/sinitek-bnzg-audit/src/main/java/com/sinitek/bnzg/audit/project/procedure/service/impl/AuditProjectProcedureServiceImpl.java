package com.sinitek.bnzg.audit.project.procedure.service.impl;

import static com.sinitek.bnzg.audit.doc.type.constant.DocTypeMessageCodeConstant.DOC_ATTACHMENT_COPY_FAILURE_ON_DOC_SAVE;
import static com.sinitek.bnzg.audit.lib.constant.AuditLibraryMessageCodeConstant.CANT_DELETE_BECAUSE_OF_PP_HAS_NOT_DRAFT_STATUS;
import static com.sinitek.bnzg.audit.lib.constant.AuditLibraryMessageCodeConstant.CANT_DELETE_BECAUSE_OF_RISK_EXISTS;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_CHANGE_BECAUSEOF_NOT_LATEST;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_SAVE_PROJECT_PROCEDURE_BECAUSEOF_CURRENT_PHASE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_SAVE_PROJECT_PROCEDURE_NOT_OWNER;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_SAVE_PROJECT_PROCEDURE_NO_OWNER;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.NO_NEW_DATA_SAVE_ON_SAVE_PROJECT_PROCEDURE;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_OPERATE_BECAUSEOF_NOT_LATEST;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_OPERATE_BECAUSEOF_PROJECT;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_OPERATE_BECAUSEOF_PROJECT_STOP;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_SAVE_BECAUSE_NOT_DRAFT;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.CANT_SAVE_BECAUSE_NO_DATA;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant.NO_DATA_NEED_SUBMIT;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpUploadConstant.INVESTIGATION_RESULT_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpUploadConstant.PUNISH_NOTICE_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpUploadConstant.PUNISH_OPINION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditProjectProcedureConstant.DEFAULT_SOURCE_NAME;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.lib.entity.AuditGroup;
import com.sinitek.bnzg.audit.lib.service.IAuditGroupService;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.constant.AuditProjectProcedureModelConstant;
import com.sinitek.bnzg.audit.project.dao.AuditProjectDAO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectCopyResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureBatchSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureBatchSaveParamDTO.BatchSaveParamItem;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureSearchParamDTO;
import com.sinitek.bnzg.audit.project.entity.AuditProject;
import com.sinitek.bnzg.audit.project.enumation.AuditProjectPhaseEnum;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditPpHomeTypeConstant;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditPpMessageCodeConstant;
import com.sinitek.bnzg.audit.project.procedure.constant.ProcedureAndRiskTypeConstant;
import com.sinitek.bnzg.audit.project.procedure.dao.AuditProjectProcedureDAO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpChangeParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeStatisticsResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpSaveOrSubmitWorkParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureDeleteParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchParamOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchResultOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.DeletePpAfterProcedureDeleteParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpExecutionSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure;
import com.sinitek.bnzg.audit.project.procedure.log.status.util.AuditPpStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchParamOnProjectConfigPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectCountInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.procedure.util.AuditPpChangeEventUtil;
import com.sinitek.bnzg.audit.project.procedure.util.AuditPpConvertUtil;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditProjectRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStepStatusUtil;
import com.sinitek.bnzg.common.constant.AuditModelAuditOpTypeConstant;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.lowcode.audit.constant.LcDataAuditOpTypeConstant;
import com.sinitek.sirm.lowcode.audit.dto.DataAuditEventSourceDTO;
import com.sinitek.sirm.lowcode.audit.dto.LcAuditDataDTO;
import com.sinitek.sirm.lowcode.audit.event.DataAuditEventDTO;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import com.sinitek.sirm.lowcode.common.model.constant.LcModelConstant;
import com.sinitek.sirm.lowcode.model.base.LcBaseModel;
import com.sinitek.sirm.lowcode.model.constant.LcSearchConditionOpConstant;
import com.sinitek.sirm.lowcode.model.dto.LcSearchConditionDTO;
import com.sinitek.sirm.lowcode.sdk.service.IModelOperateSDKService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 08/08/2024 09:54
 */
@Slf4j
@Service
public class AuditProjectProcedureServiceImpl implements IAuditProjectProcedureService {

    @Autowired
    private AuditProjectProcedureDAO dao;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IAuditGroupService auditGroupService;

    @Autowired
    private IModelOperateSDKService sdkService;

    @Autowired
    private AuditProjectDAO auditProjectDAO;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IPpApproveService ppApproveService;

    @Autowired
    private IStageStepExampleStatusService stepExampleStatusService;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IEnumService enumService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWork(AuditPpSaveOrSubmitWorkParamDTO param) {
        Long id = param.getId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditProjectProcedure currentValue = this.dao.getById(id);
        if (Objects.isNull(currentValue)) {
            log.error("根据项目审计程序id {} 查不到相关数据,无法保存", id);
            throw new BussinessException(CANT_SAVE_BECAUSE_NO_DATA);
        }

        Integer threadLatestFlag = currentValue.getThreadLatestFlag();
        if (Objects.equals(threadLatestFlag, CommonBooleanEnum.FALSE.getValue())) {
            log.error("当前数据[{}]非最新,操作人[{}]无法保存", id, operatorId);
            throw new BussinessException(CANT_OPERATE_BECAUSEOF_NOT_LATEST, "保存");
        }

        Long projectId = currentValue.getProjectId();
        Integer status = currentValue.getStatus();

        // 如果当前审计程序为审批通过状态,则变更保存为草稿状态
        // 如果当前审计程序为审批不通过状态,则变更保存为草稿状态

        boolean isChangeSave = false;
        if (Objects.equals(status, AuditRiskStatusConstant.APPROVED)
            || Objects.equals(status, AuditRiskStatusConstant.NOT_APPROVED)) {
            Long newId = this.changeProjectProcedure(AuditPpChangeParamDTO.builder()
                .id(id)
                .operatorId(operatorId)
                .copyAttachmentFlag(false)
                .opTime(opTime)
                .build());
            log.info("项目审计程序{}保存时为审批通过或不通过状态,自动变更为{}", id, newId);
            param.setId(newId);
            isChangeSave = true;
        }

        this.doSave(param, "审计实施-保存", isChangeSave);

        this.makeStepAsProgressing(projectId, param.getOperatorId(), param.getOpTime(),
            "审计实施保存审计工作");
    }

    private AuditProjectProcedure doSave(AuditPpSaveOrSubmitWorkParamDTO param, String opRemark,
        boolean isChangeSave) {
        Long id = param.getId();

        AuditProjectProcedure currentValue = this.dao.getById(id);
        AuditProjectProcedure oldValue = JsonUtil.jsonCopy(currentValue,
            AuditProjectProcedure.class);

        Integer status = currentValue.getStatus();
        if (Objects.equals(status, AuditRiskStatusConstant.APPROVING)) {
            log.error("项目审计程序 {} 为审批中无法保存", id);
            throw new BussinessException(CANT_SAVE_BECAUSE_NOT_DRAFT);
        }

        String execution = param.getExecution();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        currentValue.setStatus(AuditRiskStatusConstant.DRAFT);
        currentValue.setExecution(execution);
        currentValue.setAuditorId(operatorId);
        currentValue.setAuditDate(opTime);
        this.dao.updateById(currentValue);

        // 保存证明材料
        UploadDTO upload = param.getUpload();
        if (Objects.nonNull(upload)) {
            upload.setType(INVESTIGATION_RESULT_UPLOAD_TYPE);
            if (isChangeSave) {
                this.copyUpload2New(upload, id, DEFAULT_SOURCE_NAME,
                    INVESTIGATION_RESULT_UPLOAD_TYPE);
            } else {
                AttachmentUtils.saveAttachmentList(upload, id,
                    DEFAULT_SOURCE_NAME);
            }
        }

        //保存访谈笔录
        UploadDTO interviewTranscript = param.getInterviewTranscript();
        if (Objects.nonNull(interviewTranscript)) {
            interviewTranscript.setType(PUNISH_OPINION_UPLOAD_TYPE);
            if (isChangeSave) {
                this.copyUpload2New(interviewTranscript, id, DEFAULT_SOURCE_NAME,
                    PUNISH_OPINION_UPLOAD_TYPE);
            } else {
                AttachmentUtils.saveAttachmentList(interviewTranscript, id,
                    DEFAULT_SOURCE_NAME);
            }
        }

        //调查问卷
        UploadDTO questionnaire = param.getQuestionnaire();
        if (Objects.nonNull(questionnaire)) {
            questionnaire.setType(PUNISH_NOTICE_UPLOAD_TYPE);
            if (isChangeSave) {
                this.copyUpload2New(questionnaire, id, DEFAULT_SOURCE_NAME,
                    PUNISH_NOTICE_UPLOAD_TYPE);
            } else {
                AttachmentUtils.saveAttachmentList(questionnaire, id,
                    DEFAULT_SOURCE_NAME);
            }
        }

        // 发布 项目审计程序 变动时间
        AuditPpChangeEventUtil.publish(
            RecordChangeLogAddParamDTO.<AuditProjectProcedure>builder()
                .foreignKey(id)
                .oldValue(oldValue)
                .newValue(currentValue)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark(opRemark)
                .build());

        return currentValue;
    }

    private void copyUpload2New(UploadDTO upload, Long attachmentSourceId,
        String attachmentSourceEntity, Integer attachmentType) {
        List<UploadFileDTO> uploadFileList = upload.getUploadFileList();
        List<Attachment> attachments = new LinkedList<>();
        List<UploadFileDTO> filtedUploadFileList = uploadFileList.stream().map(item -> {
            // 如果是已上传文件,需要复制一份
            // 避免保存后出现旧版本附件丢失问题
            String id = item.getId();
            if (StringUtils.isNotBlank(id)) {
                // 复制一份
                Attachment attachment = AttachmentUtils.copyAttachmentByEncId(id);
                if (Objects.isNull(attachment)) {
                    log.error("根据 attachment id[{}]获取到的Attachment为空", id);
                    throw new BussinessException(DOC_ATTACHMENT_COPY_FAILURE_ON_DOC_SAVE);
                } else {
                    attachment.setObjId(null);
                    attachment.setType(attachmentType);
                    attachments.add(attachment);
                }

                // 返回null,后面会过滤掉
                return null;
            } else {
                item.setType(attachmentType);
                return item;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(filtedUploadFileList)) {
            log.info("attachment sourceId:[{}],sourceEntity:[{}]共有[{}]条新上传文件需要保存",
                attachmentSourceId, attachmentSourceEntity,
                filtedUploadFileList.size());
            upload.setUploadFileList(filtedUploadFileList);
            upload.setRemoveFileList(Collections.emptyList());
            AttachmentUtils.saveAttachmentList(upload, attachmentSourceId, attachmentSourceEntity);
        }

        if (CollUtil.isNotEmpty(attachments)) {
            log.info("attachment sourceId:[{}],sourceEntity:[{}]共有[{}]条已上传文件需要转存",
                attachmentSourceId,
                attachmentSourceEntity,
                attachments.size());
            this.attachmentService.saveAttachmentList(attachmentSourceId,
                attachmentSourceEntity,
                attachments);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitWork(AuditPpSaveOrSubmitWorkParamDTO param) {
        Long id = param.getId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditProjectProcedure currentValue = this.dao.getById(id);
        if (Objects.isNull(currentValue)) {
            log.error("根据项目审计程序id {} 查不到相关数据,无法提交", id);
            throw new BussinessException(CANT_SAVE_BECAUSE_NO_DATA);
        }

        Integer threadLatestFlag = currentValue.getThreadLatestFlag();
        if (Objects.equals(threadLatestFlag, CommonBooleanEnum.FALSE.getValue())) {
            log.error("当前数据[{}]非最新,操作人[{}]无法提交", id, operatorId);
            throw new BussinessException(CANT_OPERATE_BECAUSEOF_NOT_LATEST, "提交");
        }

        Long projectId = currentValue.getProjectId();
        Long procedureId = currentValue.getProcedureId();
        Integer status = currentValue.getStatus();

        // 项目终止项目审计程序不能提交,否则会产生代办
        AuditProjectInfoDTO existsProjectInfo = this.projectService.getExistsProjectInfoById(
            projectId);
        if (Objects.nonNull(existsProjectInfo)) {
            Integer projectPhase = existsProjectInfo.getProjectPhase();
            if (Objects.equals(projectPhase, AuditProjectPhaseConstant.STOP)) {
                log.error("当前项目[{}]已终止,无法提交", projectId);
                throw new BussinessException(CANT_OPERATE_BECAUSEOF_PROJECT_STOP);
            }
        } else {
            log.error("根据项目id {} 查不到相关数据,无法提交", projectId);
            throw new BussinessException(CANT_OPERATE_BECAUSEOF_PROJECT);
        }

        boolean isChangeSave = false;
        boolean isNeedSave = true;
        // 如果当前审计程序为审批通过状态
        //
        //   -检查数据是否有修改,如果有修改则变更提交为审批中状态
        //
        //   - 如果没有修改，则查询风险点是否有需要提交审批的数据,如果也没有则抛出异常,没有需要提交的审计程序与风险点
        if (Objects.equals(status, AuditRiskStatusConstant.APPROVED)) {
            String oldExecution = currentValue.getExecution();
            String execution = param.getExecution();
            UploadDTO upload = param.getUpload();
            UploadDTO interviewTranscript = param.getInterviewTranscript();
            UploadDTO questionnaire = param.getQuestionnaire();
            if (!Objects.equals(oldExecution, execution)
                || this.isUploadChange(upload)
                || this.isUploadChange(interviewTranscript)
                || this.isUploadChange(questionnaire)) {
                Long newId = this.changeProjectProcedure(AuditPpChangeParamDTO.builder()
                    .id(id)
                    .operatorId(operatorId)
                    .copyAttachmentFlag(false)
                    .opTime(opTime)
                    .build());
                log.info("项目审计程序{}提交时为通过状态且存在数据变动,自动变更为{}", id,
                    newId);
                param.setId(newId);
                isChangeSave = true;
            } else {
                // 不存在数据变动,需要看风险点是否有需要的数据
                List<AuditRiskBaseInfoDTO> risks = this.auditRiskService.findProjectRiskInfoByProjectIdAndProcedureIds(
                    projectId, Collections.singletonList(procedureId));
                if (CollUtil.isEmpty(risks)) {
                    // 不存在风险点
                    log.error(
                        "项目审计程序{}提交时为通过状态且没有变动也没有风险点,无法提交",
                        id);
                    throw new BussinessException(NO_DATA_NEED_SUBMIT);
                } else {
                    // 存在风险点,需要看风险点是否有需要提交的数据
                    List<AuditRiskBaseInfoDTO> filtedRisks = risks.stream()
                        .filter(item -> Objects.equals(item.getStatus(),
                            AuditRiskStatusConstant.DRAFT)).collect(Collectors.toList());
                    if (CollUtil.isEmpty(filtedRisks)) {
                        // 不存在需要提交的风险点
                        log.error(
                            "项目审计程序{}提交时为通过状态且没有变动也没有需要提交的风险点,无法提交",
                            id);
                        throw new BussinessException(NO_DATA_NEED_SUBMIT);
                    } else {
                        isNeedSave = false;
                        log.info(
                            "项目审计程序{}提交时为通过状态数据未发生变化但风险点需要提交,直接提交",
                            id);
                    }
                }
            }
        }

        // 如果当前审计程序为审批不通过状态,则变更提交为审批中状态,同时提交其下风险点
        if (Objects.equals(status, AuditRiskStatusConstant.NOT_APPROVED)) {
            Long newId = this.changeProjectProcedure(AuditPpChangeParamDTO.builder()
                .id(id)
                .operatorId(operatorId)
                .copyAttachmentFlag(false)
                .opTime(opTime)
                .build());
            log.info("项目审计程序{}提交时为不通过状态,自动变更为{}", id, newId);
            param.setId(newId);
            isChangeSave = true;
        }

        // 如果当前为审批中状态,则不保存项目审计程序,直接提交
        if (Objects.equals(status, AuditRiskStatusConstant.APPROVING)) {
            isNeedSave = false;
        }

        if (isNeedSave) {
            this.doSave(param, "审计实施-提交保存", isChangeSave);
        }

        id = param.getId();

        // 后提交
        this.ppApproveService.create(PpApproveCreateParamDTO.builder()
            .projectId(projectId)
            .ids(Collections.singletonList(id))
            .operatorId(operatorId)
            .opTime(opTime)
            .build());

        this.makeStepAsProgressing(projectId, operatorId, opTime,
            "审计实施提交审计工作");
    }

    private boolean isUploadChange(UploadDTO uploadDTO) {
        if (Objects.isNull(uploadDTO)) {
            // 为null代表没变动
            return false;
        }

        List<UploadFileDTO> removeFileList = uploadDTO.getRemoveFileList();
        // 如果存在附件删除,则视为数据变动
        if (CollUtil.isNotEmpty(removeFileList)) {
            return true;
        }

        List<UploadFileDTO> uploadFileList = uploadDTO.getUploadFileList();
        // 如果存在附件新上传,则视为数据变动
        return uploadFileList.stream().anyMatch(item -> Objects.isNull(item.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AuditProjectProcedureSaveParamDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        log.info("[{}]保存项目程序[{}]", operatorId, param.getProjectId());

        AuditProject auditProject = auditProjectDAO.getById(projectId);
        if (ObjectUtils.isNotEmpty(auditProject)) {
            Integer projectPhase = auditProject.getProjectPhase();
            AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(projectPhase);
            if (AuditProjectUtil.isPhaseInvalidForOperation(projectPhase)) {
                log.error("当前项目[{}]的项目进度为[{}],无法设置审计程序", projectId,
                    phaseEnum.getName());
                throw new BussinessException(CANT_SAVE_PROJECT_PROCEDURE_BECAUSEOF_CURRENT_PHASE,
                    phaseEnum.getName());
            }
        }

        Boolean checkAuthFlag = param.getCheckAuthFlag();

        if (Objects.equals(Boolean.TRUE, checkAuthFlag)) {
            // 检查是否为当前项目负责人
            AuditProjectCheckUtil.checkProjectOwner(param, "保存审计程序",
                CANT_SAVE_PROJECT_PROCEDURE_NOT_OWNER, CANT_SAVE_PROJECT_PROCEDURE_NO_OWNER);
        }

        List<Long> procedureIdList = param.getProcedureIdList();

        List<AuditProjectProcedure> allExistsPocedure = this.dao.findThreadLatestByProjectId(
            projectId);
        // key:审计程序id value:存在标识默认为true
        Map<Long, AuditProjectProcedure> procedureExistsMap = new HashMap<>(
            allExistsPocedure.size());
        if (CollUtil.isNotEmpty(allExistsPocedure)) {
            allExistsPocedure.forEach(item -> {
                Long procedureId = item.getProcedureId();
                AuditProjectProcedure cacheData = procedureExistsMap.get(procedureId);
                if (Objects.isNull(cacheData)) {
                    procedureExistsMap.put(procedureId, item);
                } else {
                    // 如果存在多条记录，则取未逻辑删除的数据
                    if (Objects.equals(CommonBooleanEnum.FALSE.getValue(), item.getRemoveFlag())) {
                        procedureExistsMap.put(procedureId, item);
                    }
                }
            });
        }
        List<Long> needSaveProcedureIdList = procedureIdList.stream()
            .filter(procedureId -> {
                AuditProjectProcedure item = procedureExistsMap.get(procedureId);
                if (Objects.nonNull(item)) {
                    return Objects.equals(CommonBooleanEnum.TRUE.getValue(), item.getRemoveFlag());
                } else {
                    return true;
                }
            }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(needSaveProcedureIdList)) {
            List<AuditProjectProcedure> needSaveList = needSaveProcedureIdList.stream()
                .map(procedureId -> {
                    AuditProjectProcedure item = procedureExistsMap.get(procedureId);

                    AuditProjectProcedure entity = new AuditProjectProcedure();
                    entity.setProjectId(projectId);
                    entity.setProcedureId(procedureId);
                    entity.setStatus(AuditRiskStatusConstant.NOT_AUDIT);
                    if (Objects.nonNull(item)) {
                        entity.setThreadId(item.getThreadId());
                    } else {
                        entity.setThreadId(null);
                    }
                    entity.setThreadLatestFlag(CommonBooleanEnum.TRUE.getValue());
                    return entity;
                }).collect(Collectors.toList());
            this.dao.saveBatch(needSaveList);

            List<Long> needSaveIds = new LinkedList<>();
            Map<Long, AuditProjectProcedure> idAndNewDataMap = new LinkedHashMap<>();
            Map<Long, Integer> idAndNewStatusMap = new LinkedHashMap<>();

            List<AuditProjectProcedure> needUpdateThreadIdList = new LinkedList<>();
            needSaveList.forEach(item -> {
                Long id = item.getId();
                Long threadId = item.getThreadId();

                needSaveIds.add(id);
                idAndNewDataMap.put(id, item);
                idAndNewStatusMap.put(id, item.getStatus());
                if (Objects.isNull(threadId)) {
                    item.setThreadId(id);
                    needUpdateThreadIdList.add(item);
                }
            });

            if (CollUtil.isNotEmpty(needUpdateThreadIdList)) {
                this.dao.updateBatchById(needUpdateThreadIdList);
            }

            AuditPpStatusChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(needSaveIds)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(idAndNewStatusMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审计实施-添加项目审计程序")
                    .build());

            AuditPpChangeEventUtil.publish(
                RecordChangeLogBatchAddParam2DTO.<AuditProjectProcedure>builder()
                    .foreignKeys(needSaveIds)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(idAndNewDataMap)
                    .remark("添加项目审计程序")
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .build());
        } else {
            log.error("保存项目[{}]审计程序 {} 时,当前数据都已存在,没有新数据保存", projectId,
                procedureIdList);
            throw new BussinessException(NO_NEW_DATA_SAVE_ON_SAVE_PROJECT_PROCEDURE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(AuditProjectProcedureBatchSaveParamDTO param) {
        List<BatchSaveParamItem> list = param.getList();
        Map<Long, String> procedureIdAndOrgId = param.getProcedureIdAndOrgId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        log.info("[{}]批量保存[{}]条项目程序", operatorId, list.size());

        List<Long> projectIds = new LinkedList<>();
        Map<Long, List<Long>> projectIdAndProcedureIdMap = new HashMap<>(list.size());
        list.forEach(item -> {
            Long projectId = item.getProjectId();
            List<Long> procedureIdList = item.getProcedureIdList();

            projectIds.add(projectId);
            projectIdAndProcedureIdMap.put(projectId, procedureIdList);
        });

        List<AuditProjectProcedure> allExistsPocedure = this.dao.findThreadLatestByProjectIds(
            projectIds);
        // key:审计项目id-审计程序id value:存在标识默认为true
        Map<String, AuditProjectProcedure> procedureExistsMap = new HashMap<>(
            allExistsPocedure.size());
        if (CollUtil.isNotEmpty(allExistsPocedure)) {
            allExistsPocedure.forEach(item -> {
                Long projectId = item.getProjectId();
                Long procedureId = item.getProcedureId();
                String uniqueKey = this.getUniqueKey(projectId, procedureId);
                AuditProjectProcedure cacheData = procedureExistsMap.get(uniqueKey);
                if (Objects.isNull(cacheData)) {
                    procedureExistsMap.put(uniqueKey, item);
                } else {
                    // 如果存在多条记录，则取未逻辑删除的数据
                    if (Objects.equals(CommonBooleanEnum.FALSE.getValue(), item.getRemoveFlag())) {
                        procedureExistsMap.put(uniqueKey, item);
                    }
                }
            });
        }

        List<AuditProjectProcedure> needSaveList = new LinkedList<>();
        projectIdAndProcedureIdMap.forEach((projectId, procedureIdList) -> {
            List<Long> needSaveProcedureIdList = procedureIdList.stream()
                .filter(procedureId -> {
                    String uniqueKey = this.getUniqueKey(projectId, procedureId);
                    AuditProjectProcedure item = procedureExistsMap.get(uniqueKey);
                    if (Objects.nonNull(item)) {
                        return Objects.equals(CommonBooleanEnum.TRUE.getValue(),
                            item.getRemoveFlag());
                    } else {
                        return true;
                    }
                }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(needSaveProcedureIdList)) {
                Map<String, String> auditorAndApproverMap = param.getAuditorAndApproverMap();
                needSaveProcedureIdList.forEach(procedureId -> {
                    String uniqueKey = this.getUniqueKey(projectId, procedureId);
                    AuditProjectProcedure item = procedureExistsMap.get(uniqueKey);

                    String auditorId = MapUtils.getString(procedureIdAndOrgId, procedureId);

                    AuditProjectProcedure entity = new AuditProjectProcedure();
                    entity.setProjectId(projectId);
                    entity.setProcedureId(procedureId);
                    // 批量导入默认为审批通过数据
                    entity.setStatus(AuditRiskStatusConstant.APPROVED);
                    // 审计人
                    entity.setAuditorId(auditorId);
                    // 审批人
                    String approverId = MapUtils.getString(auditorAndApproverMap, auditorId);
                    entity.setApproverId(approverId);
                    entity.setApproveRemark("历史数据导入");
                    if (Objects.nonNull(item)) {
                        entity.setThreadId(item.getThreadId());
                    } else {
                        entity.setThreadId(null);
                    }
                    entity.setThreadLatestFlag(CommonBooleanEnum.TRUE.getValue());
                    needSaveList.add(entity);
                });
            }
        });

        if (CollUtil.isNotEmpty(needSaveList)) {
            this.dao.saveBatch(needSaveList);

            List<Long> needSaveIds = new LinkedList<>();
            Map<Long, AuditProjectProcedure> idAndNewDataMap = new LinkedHashMap<>();
            Map<Long, Integer> idAndNewStatusMap = new LinkedHashMap<>();

            List<AuditProjectProcedure> needUpdateThreadIdList = new LinkedList<>();
            needSaveList.forEach(item -> {
                Long id = item.getId();
                Long threadId = item.getThreadId();

                needSaveIds.add(id);
                idAndNewDataMap.put(id, item);
                idAndNewStatusMap.put(id, item.getStatus());
                if (Objects.isNull(threadId)) {
                    item.setThreadId(id);
                    needUpdateThreadIdList.add(item);
                }
            });

            if (CollUtil.isNotEmpty(needUpdateThreadIdList)) {
                this.dao.updateBatchById(needUpdateThreadIdList);
            }

            AuditPpStatusChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(needSaveIds)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(idAndNewStatusMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审计实施-添加项目审计程序")
                    .build());

            AuditPpChangeEventUtil.publish(
                RecordChangeLogBatchAddParam2DTO.<AuditProjectProcedure>builder()
                    .foreignKeys(needSaveIds)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(idAndNewDataMap)
                    .remark("添加项目审计程序")
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .build());
        } else {
            log.warn("保存项目审计程序时,当前数据[{}]都已存在,没有新数据保存",
                projectIdAndProcedureIdMap);
        }
    }

    private String getUniqueKey(Long projectId, Long proceudreId) {
        return String.format("%s-%s", projectId, proceudreId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyCascadeByProject(AuditProjectCopyResultDTO param) {
        Map<Long, Long> projectIdMap = param.getProjectIdMap();
        Date opTime = param.getOpTime();
        String operatorId = param.getOperatorId();

        Set<Long> oldProjectIds = projectIdMap.keySet();

        List<AuditProjectProcedure> procedures = this.dao.findExistsThreadLatestByProjectId(
            oldProjectIds);
        if (CollUtil.isNotEmpty(procedures)) {
            List<AuditProjectProcedure> needSaveList = procedures.stream().map(item -> {
                Long oldProjectId = item.getProjectId();
                Long newProjectId = projectIdMap.get(oldProjectId);

                AuditProjectProcedure procedure = new AuditProjectProcedure();
                procedure.setProjectId(newProjectId);
                procedure.setProcedureId(item.getProcedureId());
                procedure.setThreadLatestFlag(CommonBooleanEnum.TRUE.getValue());
                procedure.setStatus(AuditRiskStatusConstant.NOT_AUDIT);
                return procedure;
            }).collect(Collectors.toList());

            log.info("复制项目 {} 下项目审计程序共 [{}] 条数据", oldProjectIds,
                needSaveList.size());

            this.dao.saveBatch(needSaveList);

            needSaveList.forEach(item -> {
                Long id = item.getId();
                Long threadId = item.getThreadId();

                if (Objects.isNull(threadId)) {
                    item.setThreadId(id);
                }
            });
            this.dao.updateBatchById(needSaveList);

            // 复制项目审计程序时状态为null,因此这里就不再抛出事件
            this.publishAuditDataCopyEvent(needSaveList, operatorId, opTime);
        } else {
            log.warn("根据项目id {} 复制项目审计程序时,不存在审计程序数据", oldProjectIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(AuditProjectProcedureDeleteParamDTO param) {
        Collection<Long> ids = param.getIds();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        List<AuditProjectProcedure> ppInfos = this.dao.listByIds(ids);
        if (CollUtil.isNotEmpty(ppInfos)) {
            List<AuditProjectProcedure> cantDeletePps = ppInfos.stream().filter(item -> {
                Integer status = item.getStatus();
                return !Objects.equals(status, AuditRiskStatusConstant.NOT_AUDIT)
                    && !Objects.equals(status, AuditRiskStatusConstant.DRAFT);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cantDeletePps)) {
                List<Long> hasStatusPpIds = cantDeletePps.stream()
                    .map(AuditProjectProcedure::getProcedureId)
                    .collect(
                        Collectors.toList());
                log.error("审计程序 {} 在项目中不为未审计或草稿状态,无法删除",
                    hasStatusPpIds);
                throw new BussinessException(CANT_DELETE_BECAUSE_OF_PP_HAS_NOT_DRAFT_STATUS);
            } else {
                this.dao.logicDeletePp(ids, operatorId);

                this.publishAuditDataDeleteEvent(ppInfos, operatorId, opTime);
            }
            AuditProjectProcedure procedure = ppInfos.get(0);
            Long projectId = procedure.getProjectId();
            List<Long> procedureIds = ppInfos.stream()
                .map(AuditProjectProcedure::getProcedureId)
                .collect(Collectors.toList());
            // 项目审计程序风险点
            List<AuditProjectRiskRefCountDTO> riskRefCounts = this.auditRiskService.findProjectRiskRefCountByProjectIdAndProcedureIds(
                projectId, procedureIds);
            if (CollUtil.isNotEmpty(riskRefCounts)) {
                List<Long> riskyProcedureIds = riskRefCounts.stream()
                    .filter(riskRefCount -> riskRefCount.getRefCount() > 0)
                    .map(AuditProjectRiskRefCountDTO::getProcedureId)
                    .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(riskyProcedureIds)) {
                    log.error("以下审计程序存在风险点，无法删除: {}", riskyProcedureIds);
                    throw new BussinessException(CANT_DELETE_BECAUSE_OF_RISK_EXISTS);
                }
            }

        } else {
            log.info("根据项目审计程序id {} 没有找到关联的项目审计程序数据,无需删除", ids);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectprocedureAfterProcedureDelete(
        DeletePpAfterProcedureDeleteParamDTO param) {
        Collection<Long> procedureIds = param.getProcedureIds();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        List<AuditProjectProcedure> ppInfos = this.dao.findExistsThreadLatestByProcedureIds(
            procedureIds);
        if (CollUtil.isNotEmpty(ppInfos)) {
            List<AuditProjectProcedure> cantDeletePps = ppInfos.stream().filter(item -> {
                Integer status = item.getStatus();
                return Objects.nonNull(status) && !Objects.equals(status,
                    AuditRiskStatusConstant.DRAFT);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cantDeletePps)) {
                List<Long> hasStatusPpIds = cantDeletePps.stream()
                    .map(AuditProjectProcedure::getProcedureId)
                    .collect(
                        Collectors.toList());
                log.error("审计程序 {} 在项目中存在非空状态或草稿状态数据,无法删除",
                    hasStatusPpIds);
                throw new BussinessException(CANT_DELETE_BECAUSE_OF_PP_HAS_NOT_DRAFT_STATUS);
            } else {
                List<Long> ids = ppInfos.stream().map(AuditProjectProcedure::getId)
                    .collect(Collectors.toList());
                this.dao.logicDeletePp(ids, operatorId);

                this.publishAuditDataDeleteEvent(ppInfos, operatorId, opTime);
            }
        } else {
            log.info("根据审计程序id {} 没有找到关联的项目审计程序数据,无需删除", procedureIds);
        }
    }

    private void publishAuditDataCopyEvent(List<AuditProjectProcedure> allPps, String operatorId,
        Date opTime) {
        List<LcAuditDataDTO<?>> data = allPps.stream().map(item -> {
            LcAuditDataDTO<AuditProjectProcedure> auditData = new LcAuditDataDTO<>();
            auditData.setNewData(item);
            auditData.setId(item.getId());
            auditData.setModelCode(AuditProjectProcedureModelConstant.MODEL_CODE);
            auditData.setOpType(AuditModelAuditOpTypeConstant.COPY);
            return auditData;
        }).collect(Collectors.toList());

        DataAuditEventSourceDTO sourceEvent = new DataAuditEventSourceDTO();
        sourceEvent.setRemark("复制");
        sourceEvent.setOpDate(opTime);
        sourceEvent.setOpOrgId(operatorId);
        sourceEvent.setTraceSeq(UUID.fastUUID().toString(true));
        sourceEvent.setData(data);
        DataAuditEventDTO event = new DataAuditEventDTO(sourceEvent);
        LcEventUtil.publishEvent(event);
    }

    private void publishAuditDataDeleteEvent(List<AuditProjectProcedure> allPps, String operatorId,
        Date opTime) {
        List<LcAuditDataDTO<?>> data = allPps.stream().map(item -> {
            LcAuditDataDTO<AuditProjectProcedure> auditData = new LcAuditDataDTO<>();
            auditData.setNewData(item);
            auditData.setId(item.getId());
            auditData.setModelCode(AuditProjectProcedureModelConstant.MODEL_CODE);
            auditData.setOpType(LcDataAuditOpTypeConstant.DELETE);
            return auditData;
        }).collect(Collectors.toList());

        DataAuditEventSourceDTO sourceEvent = new DataAuditEventSourceDTO();
        sourceEvent.setRemark("审计程序删除后删除项目审计程序");
        sourceEvent.setOpDate(opTime);
        sourceEvent.setOpOrgId(operatorId);
        sourceEvent.setTraceSeq(UUID.fastUUID().toString(true));
        sourceEvent.setData(data);
        DataAuditEventDTO event = new DataAuditEventDTO(sourceEvent);
        LcEventUtil.publishEvent(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long changeProjectProcedure(AuditPpChangeParamDTO param) {
        Long ppId = param.getId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        Boolean copyAttachmentFlag = param.getCopyAttachmentFlag();

        AuditProjectProcedure newValue = this.dao.getById(ppId);

        if (Objects.isNull(newValue)) {
            log.error(
                "操作人 {} 尝试变更 项目审计程序 时,根据 项目审计程序id {} 无法获取项目审计程序数据",
                operatorId, ppId);
            throw new BussinessException(
                AuditPpMessageCodeConstant.CANT_CHANGE_BECAUSE_DATA_NOT_EXISTS);
        }

        Long projectId = newValue.getProjectId();

        AuditProjectProcedure oldValue = JsonUtil.jsonCopy(newValue,
            AuditProjectProcedure.class);
        AuditProjectProcedure oldAfterChangeValue = JsonUtil.jsonCopy(newValue,
            AuditProjectProcedure.class);

        // 只有最新数据才允许变更
        Integer threadLatestFlag = newValue.getThreadLatestFlag();
        if (!Objects.equals(LcModelConstant.THREAD_LATEST_FLAG, threadLatestFlag)) {
            log.info("项目审计程序[id:{},project:{},procedureId:{}]非最新无法变更", ppId,
                newValue.getProjectId(), newValue.getProcedureId());
            throw new BussinessException(CANT_CHANGE_BECAUSEOF_NOT_LATEST);
        }

        // 只有审批通过或不通过才能变更
        Integer status = newValue.getStatus();
        if (!Objects.equals(AuditRiskStatusConstant.NOT_APPROVED, status)
            && !Objects.equals(AuditRiskStatusConstant.APPROVED, status)) {
            log.error(
                "操作人 {} 尝试变更 项目审计程序 时,根据 项目审计程序id {} 对应状态为 {} 无法变更",
                operatorId, ppId, status);
            throw new BussinessException(AuditPpMessageCodeConstant.CANT_CHANGE_BECAUSE_ILL_STATUS);
        }

        newValue.setId(null);
        newValue.setStatus(AuditRiskStatusConstant.DRAFT);
        newValue.setThreadLatestFlag(CommonBooleanEnum.TRUE.getValue());
        newValue.setApproverId(null);
        newValue.setApproveTime(null);
        newValue.setApproveRemark(null);

        this.dao.save(newValue);

        oldAfterChangeValue.setThreadLatestFlag(CommonBooleanEnum.FALSE.getValue());
        this.dao.updateById(oldAfterChangeValue);

        Long newPpId = newValue.getId();
        AuditPpStatusChangeEventPublishUtil.publishEvent(
            RecordChangeLogAddParamDTO.<Integer>builder()
                .foreignKey(newPpId)
                .oldValue(null)
                .newValue(newValue.getStatus())
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("审计实施-项目审计程序变更")
                .build());

        Long oldPpId = oldValue.getId();

        if (Objects.equals(copyAttachmentFlag, Boolean.TRUE)) {
            List<Attachment> allAttachments = this.attachmentService.findAttachmentList(
                DEFAULT_SOURCE_NAME, oldPpId);
            if (CollUtil.isNotEmpty(allAttachments)) {
                List<Long> objids = allAttachments.stream().map(Attachment::getObjId)
                    .collect(Collectors.toList());
                this.attachmentService.copyAttachment(objids, DEFAULT_SOURCE_NAME, newPpId);
                log.info("项目审计程序变更,复制附件 {} -> {}", oldPpId, newPpId);
            }
        } else {
            log.info("项目审计程序变更,不复制附件 {} -> {}", oldPpId, newPpId);
        }

        List<Long> foreignKeys = new LinkedList<>();
        foreignKeys.add(newPpId);
        foreignKeys.add(oldPpId);

        Map<Long, AuditProjectProcedure> oldValueMap = new LinkedHashMap<>();
        oldValueMap.put(oldPpId, oldValue);
        oldValueMap.put(newPpId, null);

        Map<Long, AuditProjectProcedure> newValueMap = new LinkedHashMap<>();
        newValueMap.put(oldPpId, oldAfterChangeValue);
        newValueMap.put(newPpId, newValue);

        AuditPpChangeEventUtil.publish(
            RecordChangeLogBatchAddParam2DTO.<AuditProjectProcedure>builder()
                .foreignKeys(foreignKeys)
                .oldValueMap(oldValueMap)
                .newValueMap(newValueMap)
                .remark("审计实施-项目审计程序变更")
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

        this.makeStepAsProgressing(projectId, param.getOperatorId(), param.getOpTime(),
            "审计实施变更项目审计程序");

        return newPpId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelChangeProjectProcedure(AuditPpChangeParamDTO param) {
        Long ppId = param.getId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditProjectProcedure newValue = this.dao.getById(ppId);
        if (Objects.isNull(newValue)) {
            log.error(
                "操作人 {} 尝试取消变更 项目审计程序 时,根据 项目审计程序id {} 无法获取项目审计程序数据",
                operatorId, ppId);
            throw new BussinessException(
                AuditPpMessageCodeConstant.CANT_CANCEL_CHANGE_BECAUSEOF_NOT_EXISTS);
        }

        Integer threadLatestFlag = newValue.getThreadLatestFlag();
        if (!Objects.equals(LcModelConstant.THREAD_LATEST_FLAG, threadLatestFlag)) {
            log.error(
                "操作人 {} 尝试取消变更 项目审计程序 时,根据 项目审计程序id {} 对应线程最新标识为 {},非最新标识 无法取消变更",
                operatorId, ppId, threadLatestFlag);
            throw new BussinessException(
                AuditPpMessageCodeConstant.CANT_CANCEL_CHANGE_BECAUSEOF_NOT_LATEST);
        }

        // 只有审批通过或不通过才能变更
        Integer status = newValue.getStatus();
        if (!Objects.equals(AuditRiskStatusConstant.DRAFT, status)) {
            log.error(
                "操作人 {} 尝试变更 项目审计程序 时,根据 项目审计程序id {} 对应状态为 {},非草稿状态 无法变更",
                operatorId, ppId, status);
            throw new BussinessException(
                AuditPpMessageCodeConstant.CANT_CANCEL_CHANGE_BECAUSEOF_NOT_DRAFT);
        }

        Long threadId = newValue.getThreadId();

        // 根据当前操作项目审计程序获取非最新但审批通过/审批不通过的创建时间最近的数据作为变更前数据
        AuditProjectProcedure latestOne = this.dao.getLatestOne(ppId, threadId);
        if (Objects.isNull(latestOne)) {
            log.error(
                "操作人 {} 尝试取消变更 项目审计程序 时,根据 项目审计程序id {} 线索id {} 无法获取变更前数据",
                operatorId, ppId, threadId);
            throw new BussinessException(
                AuditPpMessageCodeConstant.CANT_CANCEL_CHANGE_BECAUSEOF_NO_PRE_DATA);
        }

        AuditProjectProcedure newValueCopy = JsonUtil.jsonCopy(newValue,
            AuditProjectProcedure.class);
        AuditProjectProcedure latestOneCopy = JsonUtil.jsonCopy(latestOne,
            AuditProjectProcedure.class);

        // 当前操作的项目审计程序变更为非最新且已删除数据,变更前数据变更为最新且未删除数据
        newValue.setThreadLatestFlag(LcModelConstant.NOT_THREAD_LATEST_FLAG);
        this.dao.updateById(newValue);

        // 后删除
        this.dao.logicDeletePp(Collections.singletonList(ppId), operatorId);

        latestOne.setThreadLatestFlag(LcModelConstant.THREAD_LATEST_FLAG);
        this.dao.updateById(latestOne);

        Long latestOneId = latestOne.getId();

        List<Long> foreignKeys = new LinkedList<>();
        foreignKeys.add(ppId);
        foreignKeys.add(latestOneId);

        Map<Long, AuditProjectProcedure> oldValueMap = new LinkedHashMap<>();
        oldValueMap.put(ppId, newValueCopy);
        oldValueMap.put(latestOneId, latestOneCopy);

        Map<Long, AuditProjectProcedure> newValueMap = new LinkedHashMap<>();
        newValueMap.put(ppId, null);
        newValueMap.put(latestOneId, latestOne);

        AuditPpChangeEventUtil.publish(
            RecordChangeLogBatchAddParam2DTO.<AuditProjectProcedure>builder()
                .foreignKeys(foreignKeys)
                .oldValueMap(oldValueMap)
                .newValueMap(newValueMap)
                .remark("审计实施-项目审计程序变更")
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

    }

    @Override
    @SuppressWarnings("squid:ReturnMapCheck")
    public Map<Long, Integer> getLibRefCountInfo(Collection<Long> libIds) {
        List<LibRefProjectCountInfoPO> libRefInfoList = this.dao.findLibRefCountInfo(libIds);
        if (CollUtil.isNotEmpty(libRefInfoList)) {
            return libRefInfoList.stream().collect(
                Collectors.toMap(LibRefProjectCountInfoPO::getLibId,
                    LibRefProjectCountInfoPO::getRefCount));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<LibRefProjectInfoSearchResultDTO> findLibRefInfo(Collection<Long> libIds) {
        List<LibRefProjectInfoSearchResultPO> pos = this.dao.findLibRefInfo(libIds);
        if (CollUtil.isNotEmpty(pos)) {
            List<Long> projectIds = pos.stream().map(LibRefProjectInfoSearchResultPO::getProjectId)
                .collect(Collectors.toList());
            List<AuditProjectInfoDTO> projects = this.projectService.findExistsProjctByIds(
                projectIds);
            // key: projectId
            // value: name
            Map<Long, String> projectIdAndNameMap = projects.stream().collect(
                Collectors.toMap(AuditProjectInfoDTO::getId, AuditProjectInfoDTO::getName));
            return pos.stream().map(item -> {
                LibRefProjectInfoSearchResultDTO dto = new LibRefProjectInfoSearchResultDTO();
                BeanUtils.copyProperties(item, dto);
                Long projectId = dto.getProjectId();
                dto.setProjectName(MapUtils.getString(projectIdAndNameMap, projectId));
                return dto;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public IPage<LibRefProjectInfoSearchResultDTO> searchLibRefInfo(
        LibRefProjectInfoSearchParamDTO param) {
        IPage<LibRefProjectInfoSearchResultPO> result = this.dao.searchLibRefInfo(
            AuditPpConvertUtil.makeSearchParamDTO2PO(param));
        return result.convert(AuditPpConvertUtil::makePO2DTO);
    }

    @Override
    public TableResult<LcBaseModel> search(AuditProjectProcedureSearchParamDTO param) {
        Boolean nestedQueryFlag = param.getNestedQueryFlag();
        Long groupId = param.getGroupId();
        Long projectId = param.getProjectId();

        List<Long> groupIdsInCondition = new LinkedList<>();
        groupIdsInCondition.add(groupId);
        if (Objects.equals(nestedQueryFlag, Boolean.TRUE)) {
            // 嵌套查询
            List<AuditGroup> groups = this.auditGroupService.findAllExistsChildrenNodeById(
                groupId);
            if (CollUtil.isNotEmpty(groups)) {

                List<Long> groupIds = groups.stream().map(AuditGroup::getId)
                    .collect(Collectors.toList());
                groupIdsInCondition.addAll(groupIds);
            }
        }

        log.info("项目审计程序权限配置,审计程序查询追加条件 groupid in {}", groupIdsInCondition);
        LcSearchConditionDTO condition = param.getCondition();
        List<LcSearchConditionDTO> andConditons = condition.getAnd();
        LcSearchConditionDTO groupIdCondition = LcSearchConditionDTO.builder().field(
                AuditProjectProcedureModelConstant.GROUP_ID_SEARCH_PARAM_NAME)
            .op(LcSearchConditionOpConstant.IN)
            .value(groupIdsInCondition).build();

        LcSearchConditionDTO projectIdCondition = LcSearchConditionDTO.builder().field(
                AuditProjectProcedureModelConstant.PROJECT_ID_NAME).op(LcSearchConditionOpConstant.EQ)
            .value(projectId).build();

        if (Objects.isNull(andConditons)) {
            condition.setAnd(CollUtil.toList(groupIdCondition, projectIdCondition));
        } else {
            andConditons.add(groupIdCondition);
            andConditons.add(projectIdCondition);
        }

        return this.sdkService.search(param, AuditProjectProcedureDTO::new);
    }

    @Override
    public IPage<AuditProjectProcedureSearchResultOnProjectConfigDTO> searchOnProjectConfig(
        AuditProjectProcedureSearchParamOnProjectConfigDTO param) {
        AuditProjectProcedureSearchParamOnProjectConfigPO paramPO = AuditPpConvertUtil.makeSearchParamDTO2PO(
            param);
        IPage<AuditProjectProcedureSearchResultPO> pageResult = this.dao.searchOnProjectConfig(
            paramPO);
        return pageResult.convert(AuditPpConvertUtil::makePO2ProjectConfigDTO);
    }

    @Override
    public List<AuditProjectProcedureInfoDTO> findByProjectId(Long projectId) {
        List<AuditProjectProcedure> procedures = this.dao.findExistsThreadLatestByProjectId(
            Collections.singleton(projectId));
        return this.makeEntity2Info(procedures);
    }

    @Override
    public List<AuditProjectProcedureInfoDTO> findByProjectIds(Collection<Long> projectIds) {
        List<AuditProjectProcedure> procedures = this.dao.findExistsThreadLatestByProjectId(
            projectIds);
        return this.makeEntity2Info(procedures);
    }

    @Override
    public List<AuditProjectProcedureInfoDTO> findExistsThreadLatestByProcedureIds(
        Collection<Long> procedureIds) {
        List<AuditProjectProcedure> procedures = this.dao.findExistsThreadLatestByProcedureIds(
            procedureIds);
        return this.makeEntity2Info(procedures);
    }

    @Override
    public IPage<AuditProjectProcedureInfoDTO> searchExecutionList(
        PpExecutionSearchParamDTO param) {
        IPage<AuditProjectProcedureInfoPO> result = this.dao.searchExecutionList(
            param.buildPage(), AuditPpConvertUtil.makeSearchParamDTO2PO(param));
        return result.convert(AuditPpConvertUtil::makePO2DTO);
    }

    @Override
    public List<AuditProjectProcedureInfoDTO> findByIds(Collection<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            List<AuditProjectProcedure> procedures = this.dao.listByIds(ids);
            return this.makeEntity2Info(procedures);
        }
        return Collections.emptyList();
    }

    @Override
    public AuditProjectProcedureInfoDTO getById(Long id) {
        AuditProjectProcedure procedure = this.dao.getById(id);
        if (Objects.isNull(procedure)) {
            return null;
        }
        List<AuditProjectProcedureInfoDTO> list = this.makeEntity2Info(
            Collections.singletonList(procedure));
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public AuditPpHomeStatisticsResultDTO countHomeProcedure(String orgId) {
        AuditPpHomeStatisticsResultDTO resultDTO = new AuditPpHomeStatisticsResultDTO();
        Integer waitApprovePpCount = this.dao.countWaitApproveProjectProcedure(orgId);
        resultDTO.setWaitApprovePpCount(waitApprovePpCount);
        Integer waitHandlePpCount = this.dao.countWaitHandleProjectProcedure(orgId);
        resultDTO.setWaitHandlePpCount(waitHandlePpCount);
        Integer notPassPpCount = this.dao.countNotPassPpCount(orgId);
        resultDTO.setNotPassPpCount(notPassPpCount);
        return resultDTO;
    }

    @Override
    public IPage<AuditPpHomeSearchResultDTO> searchHomeProcedure(AuditPpHomeSearchParamDTO param) {
        Integer type = param.getType();
        AuditPpHomeSearchParamPO paramPO = AuditPpConvertUtil.makeSearchParamDTO2PO(param);
        IPage<AuditPpHomeSearchResultPO> result = this.dao.searchHomeProcedure(paramPO);
        List<AuditPpHomeSearchResultPO> records = result.getRecords();

        // 获取待审批数据时，sql没有查询planId,需要在这里进行填充
        if (Objects.equals(AuditPpHomeTypeConstant.WAIT_APPROVE, type) && CollUtil.isNotEmpty(
            records)) {
            List<Long> projectIdList = new LinkedList<>();
            records.forEach(item -> projectIdList.add(item.getProjectId()));

            List<AuditProjectInfoDTO> projectInfoDTOList = this.projectService.findExistsProjctByIds(
                projectIdList);

            // key: 审计项目id
            // value: 审计计划id
            Map<Long, Long> projectIdAndPlanIdMap;

            if (CollUtil.isNotEmpty(projectInfoDTOList)) {
                projectIdAndPlanIdMap = projectInfoDTOList.stream()
                    .collect(Collectors.toMap(AuditProjectInfoDTO::getId,
                        AuditProjectInfoDTO::getPlanId));
            } else {
                projectIdAndPlanIdMap = Collections.emptyMap();
            }

            List<AuditPpHomeSearchResultPO> newRecords = records.stream().map(item -> {
                Long planId = MapUtils.getLong(projectIdAndPlanIdMap, item.getProjectId(), null);
                item.setPlanId(planId);
                return item;
            }).collect(Collectors.toList());

            result.setRecords(newRecords);
        }

        // 生成type字段，用于标识数据是审计程序还是风险点
        if (CollUtil.isNotEmpty(records)) {
            List<AuditPpHomeSearchResultPO> newRecords = records.stream()
                .map(po -> {
                    Long riskId = po.getRiskId();
                    if (ObjectUtils.isNotEmpty(riskId)) {
                        po.setType(ProcedureAndRiskTypeConstant.RISK_TYPE);
                    } else {
                        po.setType(ProcedureAndRiskTypeConstant.PROCEDURE_TYPE);
                    }
                    return po;
                })
                .collect(Collectors.toList());

            result.setRecords(newRecords);
        }

        return result.convert(AuditPpConvertUtil::makePO2DTO);
    }

    private List<AuditProjectProcedureInfoDTO> makeEntity2Info(
        List<AuditProjectProcedure> procedures) {
        if (CollUtil.isNotEmpty(procedures)) {
            return procedures.stream().map(item -> {
                Long id = item.getId();
                Long projectId = item.getProjectId();
                Long procedureId = item.getProcedureId();
                Integer status = item.getStatus();
                String auditorId = item.getAuditorId();
                Date auditDate = item.getAuditDate();
                String execution = item.getExecution();
                String approverId = item.getApproverId();
                Date approveTime = item.getApproveTime();
                String approveRemark = item.getApproveRemark();

                return AuditProjectProcedureInfoDTO.builder()
                    .id(id)
                    .projectId(projectId)
                    .procedureId(procedureId)
                    .status(status)
                    .auditorId(auditorId)
                    .auditDate(auditDate)
                    .execution(execution)
                    .approverId(approverId)
                    .approveTime(approveTime)
                    .approveRemark(approveRemark)
                    .build();
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private void makeStepAsProgressing(Long projectId, String operatorId, Date opTime,
        String remark) {
        // 审计实施
        StageStepExampleDTO stepExample = this.stageStepExampleService.getByProjectIdAndStepValue(
            projectId, StageStepConstant.AUDIT_EXECUTION);
        if (Objects.isNull(stepExample)) {
            log.warn("项目[{}]审计程序 {} 后,审计实施阶段步骤实例不存在, 不需要变为进行中",
                projectId,
                remark);
            return;
        }
        Long id = stepExample.getId();
        Integer status = stepExample.getStatus();

        if (StageStepStatusUtil.checkCanChangeAsProcessiongStatus(status)) {
            this.stepExampleStatusService.updateStatus(
                StageStepExampleStatusChangeParamDTO.builder()
                    .ids(Collections.singletonList(id))
                    .newStatus(StageStepStatusConstant.PROCESSING)
                    .opOrgId(operatorId)
                    .opTime(opTime)
                    .opRemark(remark)
                    .publishEventFlag(true)
                    .build());
            log.info("项目[{}]审计程序{}后,审计实施阶段步骤实例 {} 状态为 {} 变为进行中", projectId,
                remark, id, status);
        } else {
            log.info("项目[{}]审计程序{}后,审计实施阶段步骤实例 {} 状态为 {} 无需变为进行中",
                projectId,
                remark, id, status);
        }

    }
}
