package com.sinitek.bnzg.audit.project.procedure.dao;

import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpHomeTypeConstant.WAIT_APPROVE;
import static com.sinitek.bnzg.audit.project.procedure.constant.AuditPpHomeTypeConstant.WAIT_HANDLE;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure;
import com.sinitek.bnzg.audit.project.procedure.mapper.AuditPpExecutionMapper;
import com.sinitek.bnzg.audit.project.procedure.mapper.AuditProjectProcedureMapper;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureExecutionListResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchParamOnProjectConfigPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectCountInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.PpExecutionSearchParamPO;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Slf4j
@Service
public class AuditProjectProcedureDAO extends
    ServiceImpl<AuditProjectProcedureMapper, AuditProjectProcedure> {

    @Autowired
    private AuditPpExecutionMapper ppExecutionMapper;

    public void logicDeletePp(Collection<Long> ids, String operatorId) {
        if (CollUtil.isNotEmpty(ids)) {
            this.baseMapper.logicDeletePp(ids, operatorId);
        }
    }

    public IPage<AuditProjectProcedureExecutionListResultPO> searchExecutionList(
        PpExecutionSearchParamPO param) {
        return this.ppExecutionMapper.searchExecutionList(param.buildPage(), param);
    }

    public IPage<AuditProjectProcedureInfoPO> searchExecutionList(
        Page<AuditProjectProcedureInfoPO> page, PpExecutionSearchParamPO param) {
        return this.baseMapper.searchExecutionList(page, param);
    }

    public List<AuditProjectProcedure> findThreadLatestByProjectId(Long projectId) {
        return this.baseMapper.findByProjectId(projectId);
    }

    public List<AuditProjectProcedure> findThreadLatestByProjectIds(Collection<Long> projectIds) {
        if (CollUtil.isNotEmpty(projectIds)) {
            return this.baseMapper.findByProjectIds(projectIds);
        }
        return Collections.emptyList();
    }

    public List<AuditProjectProcedure> findExistsThreadLatestByProjectId(
        Collection<Long> projectIds) {
        if (CollUtil.isNotEmpty(projectIds)) {
            LambdaQueryWrapper<AuditProjectProcedure> queryWrapper = Wrappers.lambdaQuery(
                AuditProjectProcedure.class);
            queryWrapper.in(AuditProjectProcedure::getProjectId, projectIds);
            queryWrapper.eq(AuditProjectProcedure::getThreadLatestFlag,
                CommonBooleanEnum.TRUE.getValue());
            queryWrapper.orderByAsc(AuditProjectProcedure::getId);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

    public List<AuditProjectProcedure> findExistsThreadLatestByProcedureIds(
        Collection<Long> procedureIds) {
        if (CollUtil.isNotEmpty(procedureIds)) {
            LambdaQueryWrapper<AuditProjectProcedure> queryWrapper = Wrappers.lambdaQuery(
                AuditProjectProcedure.class);
            queryWrapper.in(AuditProjectProcedure::getProcedureId, procedureIds);
            queryWrapper.eq(AuditProjectProcedure::getThreadLatestFlag,
                CommonBooleanEnum.TRUE.getValue());
            queryWrapper.orderByAsc(AuditProjectProcedure::getId);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

    public List<LibRefProjectCountInfoPO> findLibRefCountInfo(Collection<Long> libIds) {
        if (CollUtil.isNotEmpty(libIds)) {
            return this.baseMapper.findLibRefCountInfo(libIds);
        }
        return Collections.emptyList();
    }

    public List<LibRefProjectInfoSearchResultPO> findLibRefInfo(Collection<Long> libIds) {
        if (CollUtil.isNotEmpty(libIds)) {
            return this.baseMapper.findLibRefInfo(libIds);
        }
        return Collections.emptyList();
    }

    public IPage<LibRefProjectInfoSearchResultPO> searchLibRefInfo(
        LibRefProjectInfoSearchParamPO param) {
        return this.baseMapper.searchLibRefInfo(param.buildPage(), param);
    }

    public IPage<AuditProjectProcedureSearchResultPO> searchOnProjectConfig(
        AuditProjectProcedureSearchParamOnProjectConfigPO param) {
        return this.baseMapper.searchOnProjectConfig(param.buildPage(), param);
    }

    public AuditProjectProcedure getLatestOne(Long id, Long threadId) {
        return this.baseMapper.getLatestOne(id, threadId);
    }

    public Integer countWaitApproveProjectProcedure(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return 0;
        }
        return this.baseMapper.countWaitApproveProjectProcedure(orgId);
    }

    public Integer countWaitHandleProjectProcedure(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return 0;
        }
        return this.baseMapper.countWaitHandleProjectProcedure(orgId);
    }

    public Integer countNotPassPpCount(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return 0;
        }
        return this.baseMapper.countNotPassPpCount(orgId);
    }

    public IPage<AuditPpHomeSearchResultPO> searchHomeProcedure(AuditPpHomeSearchParamPO paramPO) {
        Integer type = paramPO.getType();
        if (Objects.equals(WAIT_HANDLE, type)) {
            return this.baseMapper.searchHomeWaitHandleProcedure(paramPO.buildPage(), paramPO);
        } else if (Objects.equals(WAIT_APPROVE, type)) {
            return this.baseMapper.searchHomeWaitApproveProcedure(paramPO.buildPage(), paramPO);
        } else {
            return this.baseMapper.searchHomeNotPassData(paramPO.buildPage(), paramPO);
        }
    }
}
