package com.sinitek.bnzg.audit.risk.accountability.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <AUTHOR>
 * @Date：2024/11/15 11:14
 */
public interface RiskAccountabilityMapper extends BaseMapper<AuditRiskAc> {


    IPage<AuditRiskAcSearchResultPO> searchRiskAccountability(
            Page<AuditRiskAcSearchResultPO> page, @Param("param") AuditRiskAcSearchParamPO param);

    AuditRiskAcSearchResultPO loadDetail(@Param("riskId") Long riskId);


    int deleteByIds(@Param("ids") Collection<Long> ids, @Param("operatorId") String operatorId);

}
