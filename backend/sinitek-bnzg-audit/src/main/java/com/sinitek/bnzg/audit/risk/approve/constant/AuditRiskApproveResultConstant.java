package com.sinitek.bnzg.audit.risk.approve.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目风险点审批 MessageCode
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskApproveResultConstant {

    /**
     * 无需审批
     */
    public static final int NO_NEED = 1;

    /**
     * 待审批
     */
    public static final int DRAFT = 10;

    /**
     * 通过
     */
    public static final int APPROVED = 100;

    /**
     * 不通过
     */
    public static final int NOT_APPROVED = 50;
}
