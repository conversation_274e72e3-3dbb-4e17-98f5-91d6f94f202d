package com.sinitek.bnzg.audit.common.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * <AUTHOR>
 * @date 11/23/2022 1:09 PM
 */
@Configuration
@EnableSwagger2WebMvc
public class AuditSwagger2Config {

    @Bean
    public Docket createBnzgAuditRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
            .groupName("百年资管合规审计平台-审计系统")
            .apiInfo(bnzgAuditApiInfo())
            .select()
            //此包路径下的类，才生成接口文档
            .apis(RequestHandlerSelectors.basePackage("com.sinitek.bnzg.audit"))
            //加了ApiOperation注解的类，才生成接口文档
            .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
            .paths(PathSelectors.any())
            .build();
    }

    private ApiInfo bnzgAuditApiInfo() {
        return new ApiInfoBuilder()
            .title("百年资管合规审计平台 审计系统 APIs")
            .build();
    }
}
