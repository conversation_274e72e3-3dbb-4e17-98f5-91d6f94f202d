<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.project.procedure.mapper.AuditPpExecutionMapper">

  <select id="searchExecutionList" resultType="com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureExecutionListResultPO">
      select 0 data_type,
             pp.id,
             pp.project_id,
             pp.procedure_id,
             p.name,
             pp.status,
             pp.auditor_id,
             pp.audit_date,
             pp.approve_remark
        from audit_project_procedure pp
        join audit_procedure p
          on pp.procedure_id = p.id
         and p.remove_flag = 0
       where pp.remove_flag = 0
         and pp.thread_latest_flag = 1
         and pp.project_id = #{param.projectId}
     <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
        order by pp.createtimestamp asc,pp.id asc
     </if>
  </select>
</mapper>
