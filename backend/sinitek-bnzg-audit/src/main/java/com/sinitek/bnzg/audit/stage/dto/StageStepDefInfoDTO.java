package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:34
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("步骤定义信息")
public class StageStepDefInfoDTO {

    @ApiModelProperty("阶段步骤值")
    private Integer stepValue;

    @ApiModelProperty("阶段步骤状态")
    private Integer status;

    @ApiModelProperty("顺序")
    private Integer sort;

}
