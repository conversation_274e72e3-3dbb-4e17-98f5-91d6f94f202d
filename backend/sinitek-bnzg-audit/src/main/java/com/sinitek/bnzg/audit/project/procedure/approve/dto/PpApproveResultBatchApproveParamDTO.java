package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 项目审计程序审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "项目审计程序批量审批参数DTO")
public class PpApproveResultBatchApproveParamDTO extends PpOrRiskApproveResultApproveBaseParamDTO {

    @ApiModelProperty("项目审计程序审批结果id")
    private List<Long> ppApproveResultIds;

}
