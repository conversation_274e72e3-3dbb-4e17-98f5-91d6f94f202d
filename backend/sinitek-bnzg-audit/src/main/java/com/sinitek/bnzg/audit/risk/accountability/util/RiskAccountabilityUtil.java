package com.sinitek.bnzg.audit.risk.accountability.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcDetailDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.AuditRiskAcSearchResultPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date：2024/11/15 13:24
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RiskAccountabilityUtil {

    public static AuditRiskAcSearchParamPO makeSearchParamDTO2PO(AuditRiskAcSearchParamDTO dto) {
        List<Long> planIds = dto.getPlanIds();
        List<String> respDeptIds = dto.getRespDeptIds();
        List<String> respManIds = dto.getRespManIds();

        List<String> projectNameList = CommonStringUtil.toSearchStrList(dto.getProjectName());
        List<String> riskNameList = CommonStringUtil.toSearchStrList(dto.getRiskName());
        List<String> procedureNameList = CommonStringUtil.toSearchStrList(dto.getProcedureName());

        AuditRiskAcSearchParamPO po = new AuditRiskAcSearchParamPO();

        if (CollUtil.isNotEmpty(planIds)) {
            po.setPlanIds(planIds);
        }
        if (CollUtil.isNotEmpty(projectNameList)) {
            po.setProjectNameList(projectNameList);
        }
        if (CollUtil.isNotEmpty(riskNameList)) {
            po.setRiskNameList(riskNameList);
        }
        if (CollUtil.isNotEmpty(procedureNameList)) {
            po.setProcedureNameList(procedureNameList);
        }
        if (CollUtil.isNotEmpty(respDeptIds)) {
            po.setRespDeptIds(respDeptIds);
        }
        if (CollUtil.isNotEmpty(respManIds)) {
            po.setRespManIds(respManIds);
        }
        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());
        return po;
    }

    public static AuditRiskAcSearchResultDTO makeSearchResultPO2DTO(AuditRiskAcSearchResultPO po) {
        return LcConvertUtil.convert(po, AuditRiskAcSearchResultDTO::new);
    }

    public static AuditRiskAcDetailDTO makeEntity2DetailDTO(AuditRiskAc source) {
        return LcConvertUtil.convert(source, AuditRiskAcDetailDTO::new);
    }
}
