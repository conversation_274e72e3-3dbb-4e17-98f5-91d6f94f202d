package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@ApiModel("项目审计程序首页查询 - 返回DTO")
public class AuditPpHomeSearchResultDTO {

    @ApiModelProperty(value = "项目审计程序id")
    private Long id;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "审计程序id")
    private Long procedureId;

    @ApiModelProperty(value = "审计程序名称")
    private String procedureName;

    @ApiModelProperty(value = "风险点id")
    private Long riskId;

    @ApiModelProperty(value = "风险点名称")
    private String riskName;

    @ApiModelProperty(value = "状态(当riskId为空时，状态为审计程序状态。否则为风险点状态)")
    private Integer status;

    @ApiModelProperty(value = "状态名")
    private String statusName;

    @ApiModelProperty(value = "审批反馈")
    private String approveRemark;

    @ApiModelProperty(value = "类型")
    private Integer type;

}
