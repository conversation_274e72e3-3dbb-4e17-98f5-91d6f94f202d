package com.sinitek.bnzg.audit.risk.service.impl;

import static com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcMessageConstant.AUDIT_RISK_AC_EXISTS;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.AFTER_FILTER_INNER_CATEGORY_WARNING;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.BEFORE_FILTER_INNER_CATEGORY_WARNING;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.INNER_CATEGORY_BY_AUDITYEAR;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskResultPO;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.dto.AdjustInnerCategoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditProcedureRiskRefBaseDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditProjectRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBatchSaveParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryResultDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.dto.InnerCategoryListParamDTO;
import com.sinitek.bnzg.audit.risk.dto.UpdateInnerCategoryCompareParamDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import com.sinitek.bnzg.audit.risk.log.rectify.state.util.AuditRiskRectifyStateChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.log.status.util.AuditRiskStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.po.AuditProjectRiskRefCountPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskDataResultPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryResultPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskRefCountPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespManService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.risk.support.AuditRiskSearchResultFormat;
import com.sinitek.bnzg.audit.risk.util.AuditRiskChangeEventUtil;
import com.sinitek.bnzg.audit.risk.util.AuditRiskConvertUtil;
import com.sinitek.bnzg.audit.risk.util.AuditRiskHistoryUtil;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目风险点 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@Slf4j
@Service
public class AuditRiskServiceImpl implements IAuditRiskService {

    @Autowired
    private AuditRiskDAO dao;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IAuditRiskRespDeptService deptService;

    @Autowired
    private IAuditRiskRespManService respManService;

    @Autowired
    private IRiskAccountabilityService riskAccountabilityService;

    @Autowired
    private IEnumService enumService;

    @Autowired
    private AuditRiskSearchResultFormat<AuditRiskDetailDTO> format;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(AuditRiskBatchSaveParamDTO param) {
        List<AuditRisk> list = param.getList();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        if (CollUtil.isNotEmpty(list)) {
            this.dao.saveBatch(list);

            List<Long> ids = new LinkedList<>();
            Map<Long, Integer> idAndStatusMap = new HashMap<>(list.size());
            Map<Long, AuditRisk> idAndDataMap = new HashMap<>(list.size());
            Map<Long, Integer> idAndRectifyStatusMap = new HashMap<>(list.size());

            List<AuditRisk> needUpdateThreadList = new LinkedList<>();
            list.forEach(item -> {
                Long id = item.getId();
                Long threadId = item.getThreadId();
                Integer status = item.getStatus();
                Integer rectifyState = item.getRectifyState();

                ids.add(id);
                idAndStatusMap.put(id, status);
                idAndDataMap.put(id, item);
                idAndRectifyStatusMap.put(id, rectifyState);

                if (Objects.isNull(threadId)) {
                    item.setThreadId(item.getId());
                    needUpdateThreadList.add(item);
                }
            });
            if (CollUtil.isNotEmpty(needUpdateThreadList)) {
                this.dao.updateBatchById(needUpdateThreadList);
            }

            AuditRiskStatusChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(ids)
                    .oldValueMap(null)
                    .newValueMap(idAndStatusMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("风险点批量新增")
                    .build());

            AuditRiskChangeEventUtil.publish(RecordChangeLogBatchAddParam2DTO.<AuditRisk>builder()
                .foreignKeys(ids)
                .oldValueMap(Collections.emptyMap())
                .newValueMap(idAndDataMap)
                .remark("风险点批量新增")
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

            AuditRiskRectifyStateChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(ids)
                    .oldValueMap(null)
                    .newValueMap(idAndRectifyStatusMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("风险点批量新增")
                    .build());
        } else {
            log.warn("操作人 {} 批量保存风险点时,传入风险点数据为空", operatorId);
        }
    }

    @Override
    public IPage<AuditRiskSearchResultDTO> search(AuditRiskSearchParamDTO searchDTO) {
        AuditRiskSearchParamPO param = AuditRiskConvertUtil.makeSearchParamDTO2PO(
            searchDTO);
        IPage<AuditRiskSearchResultPO> result = this.dao.searchInExecution(param);
        return result.convert(AuditRiskConvertUtil::makeSearchResultPO2DTO);
    }

    @Override
    public AuditRiskDetailDTO loadDetail(Long id) {
        List<String> deptIds = this.deptService.findByRiskId(id).stream()
            .map(AuditRiskRespDept::getRespDeptId)
            .collect(Collectors.toList());
        List<String> respManIds = this.respManService.findByRiskId(id).stream()
            .map(AuditRiskRespMan::getRespManId)
            .collect(Collectors.toList());
        AuditRiskDetailDTO auditRiskDetailDTO = new AuditRiskDetailDTO();
        auditRiskDetailDTO.setRespDeptId(deptIds);
        auditRiskDetailDTO.setRespManId(respManIds);
        AuditRisk entity = this.dao.getById(id);
        BeanUtils.copyProperties(entity, auditRiskDetailDTO);

        return auditRiskDetailDTO;
    }

    @Override
    public List<AuditRiskBaseInfoDTO> findExistsByIds(Collection<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            List<AuditRisk> risks = this.dao.listByIds(ids);
            if (CollUtil.isNotEmpty(risks)) {
                return risks.stream().map(AuditRiskConvertUtil::makeEntity2BaseInfoDTO).collect(
                    Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditRiskRefCountDTO> findRiskRefCountByProcedureIds(
        Collection<Long> procedureIds) {
        if (CollUtil.isNotEmpty(procedureIds)) {
            List<AuditRiskRefCountPO> list = this.dao.findRiskRefCountByProcedureIds(
                procedureIds);
            if (CollUtil.isNotEmpty(list)) {
                return list.stream().map(AuditRiskConvertUtil::makeRiskRefCountPO2DTO)
                    .collect(Collectors.toList());
            } else {
                log.info("审计程序 {} 不存在风险点", procedureIds);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditProjectRiskRefCountDTO> findProjectRiskRefCountByProjectIdAndProcedureIds(
        Long projectId, Collection<Long> procedureIds) {
        if (CollUtil.isNotEmpty(procedureIds)) {
            List<AuditProjectRiskRefCountPO> list = this.dao.findProjectRiskRefCountByProjectIdAndProcedureIds(
                projectId, procedureIds);
            if (CollUtil.isNotEmpty(list)) {
                return list.stream().map(AuditRiskConvertUtil::makeProjectRiskRefCountPO2DTO)
                    .collect(
                        Collectors.toList());
            } else {
                log.info("项目 {} 审计程序 {} 不存在风险点", projectId, procedureIds);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditRiskBaseInfoDTO> findProjectRiskInfoByProjectIdAndProcedureIds(Long projectId,
        Collection<Long> procedureIds) {
        List<AuditRisk> result = this.dao.findProjectRiskInfoByProjectIdAndProcedureIds(
            projectId, procedureIds);
        return result.stream().map(AuditRiskConvertUtil::makeEntity2BaseInfoDTO)
            .collect(Collectors.toList());
    }

    @Override
    public List<String> findInnerCategory() {
        List<String> result = new LinkedList<>();
        String categoryStr = SettingUtils.getStringValue(BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.RISK_INNER_CATEGORY);
        if (StringUtils.isNotBlank(categoryStr)) {
            List<String> categorysInSetting = Arrays.stream(categoryStr.split("[,， ]"))
                .map(StringUtils::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(categorysInSetting)) {
                result.addAll(categorysInSetting);
            }
        }
        List<String> allInnerCategory = this.dao.findAllInnerCategory();
        if (CollUtil.isNotEmpty(allInnerCategory)) {
            allInnerCategory.forEach(item -> {
                String trimStr = item.trim();
                if (StringUtils.isNotBlank(trimStr) && !result.contains(trimStr)) {
                    result.add(trimStr);
                }
            });
        }
        return result;
    }

    @Override
    public List<String> findInnerCategoryByYear(InnerCategoryListParamDTO param) {
        List<String> innerCategoryByYear = this.dao.findInnerCategoryByYear(param);
        return innerCategoryByYear.stream()
            .filter(Objects::nonNull)
            .map(String::trim)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchInnerCategory(AdjustInnerCategoryParamDTO param) {
        Integer auditYear = param.getAuditYear();
        List<UpdateInnerCategoryCompareParamDTO> compareList = param.getList();

        log.info("传入审计年度[{}],内部分类数据[{}]", auditYear, compareList);

        if (CollUtil.isEmpty(compareList)) {
            log.error("传入的内部分类调整对比数据为空");
            throw new BussinessException(BEFORE_FILTER_INNER_CATEGORY_WARNING);
        }

        // key: 旧内部分类
        // value: 新内部分类
        Map<String, String> oldInnerCategoryAndParamMap = compareList.stream()
            .filter(paramDTO -> StringUtils.isNotBlank(paramDTO.getOldInnerCategory())
                && StringUtils.isNotBlank(paramDTO.getNewInnerCategory())
                && !paramDTO.getOldInnerCategory().equals(paramDTO.getNewInnerCategory()))
            .collect(Collectors.toMap(UpdateInnerCategoryCompareParamDTO::getOldInnerCategory,
                UpdateInnerCategoryCompareParamDTO::getNewInnerCategory));

        if (CollUtil.isEmpty(oldInnerCategoryAndParamMap)) {
            log.error("传入审计年度[{}],需要更新的内部分类数据[{}]筛选后为空", auditYear,
                compareList);
            throw new BussinessException(AFTER_FILTER_INNER_CATEGORY_WARNING);
        }

        List<AuditRisk> auditRisks = this.dao.findAuditRiskByYear(auditYear);
        if (CollUtil.isEmpty(auditRisks)) {
            log.error("没有通过当前审计年度[{}]找到风险点", auditYear);
            throw new BussinessException(INNER_CATEGORY_BY_AUDITYEAR);
        }

        // key: 风险点id,value: 旧风险点数据
        Map<Long, AuditRisk> idAndOldDataMap = new HashMap<>(auditRisks.size());
        // key: 风险点id,value: 新风险点数据
        Map<Long, AuditRisk> idAndNewDataMap = new HashMap<>(auditRisks.size());
        ArrayList<AuditRisk> needUpdateRisks = new ArrayList<>();
        if (CollUtil.isNotEmpty(auditRisks)) {
            auditRisks.forEach(auditRisk -> {
                Long id = auditRisk.getId();
                String innerCategory = auditRisk.getInnerCategory();

                String newInnerCategory = oldInnerCategoryAndParamMap.get(innerCategory);
                if (StringUtils.isNotBlank(newInnerCategory)) {

                    AuditRisk oldData = JsonUtil.jsonCopy(auditRisk, AuditRisk.class);

                    auditRisk.setInnerCategory(newInnerCategory);

                    needUpdateRisks.add(auditRisk);
                    idAndOldDataMap.put(id, oldData);
                    idAndNewDataMap.put(id, auditRisk);

                } else {
                    log.debug("风险点[{}]内部分类[{}]无更新", auditRisk,
                        auditRisk.getInnerCategory());
                }
            });

            if (CollUtil.isNotEmpty(needUpdateRisks)) {
                this.dao.saveOrUpdateBatch(needUpdateRisks);

                // 风险点数据变动
                AuditRiskChangeEventUtil.publish(
                    RecordChangeLogBatchAddParam2DTO.<AuditRisk>builder()
                        .foreignKeys(needUpdateRisks.stream().map(AuditRisk::getId)
                            .collect(Collectors.toList()))
                        .oldValueMap(idAndOldDataMap)
                        .newValueMap(idAndNewDataMap)
                        .operatorId(CurrentUserFactory.getOrgId())
                        .opTime(new Date())
                        .remark("风险点查询统计-风险分类调整")
                        .build());
            } else {
                log.info("当前审计年度[{}],需要更新的风险点数据为空", auditYear);
            }

        }
    }

    @Override
    public List<AuditProcedureRiskRefBaseDTO> findProcedureRiskRefByProcedureIds(
        Collection<Long> procedureIds) {
        if (CollUtil.isNotEmpty(procedureIds)) {
            return LcConvertUtil.convert(
                this.dao.findProcedureRiskRefByProcedureIds(procedureIds),
                AuditProcedureRiskRefBaseDTO::new);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditRiskHistoryResultDTO> findRiskHistory(AuditRiskHistoryParamDTO param) {
        AuditRiskHistoryParamPO paramPO = AuditRiskHistoryUtil.makeSearchParamDTO2PO(param);
        List<AuditRiskHistoryResultPO> list = this.dao.findRiskHistory(paramPO);
        // 一级分类
        Map<String, String> firstCatalogMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.FIRST_CATALOG);

        for (AuditRiskHistoryResultPO resultPO : list) {
            List<String> respDeptIds = this.deptService.findByRiskId(resultPO.getRiskId()).stream()
                .map(AuditRiskRespDept::getRespDeptId)
                .collect(Collectors.toList());
            List<String> respManIds = this.respManService.findByRiskId(resultPO.getRiskId())
                .stream()
                .map(AuditRiskRespMan::getRespManId)
                .collect(Collectors.toList());
            List<String> orgIds = new LinkedList<>();
            orgIds.addAll(respManIds);
            orgIds.addAll(respDeptIds);

            Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(orgIds);

            resultPO.setRespManId(respManIds);
            resultPO.setRespDeptId(respDeptIds);
            String respDeptNames = Optional.ofNullable(resultPO.getRespDeptId())
                .orElse(Collections.emptyList())
                .stream()
                .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                .filter(name -> StringUtils.isNotBlank(name))
                .collect(Collectors.joining(","));
            resultPO.setRespDeptName(respDeptNames);
            String respManNames = Optional.ofNullable(resultPO.getRespManId())
                .orElse(Collections.emptyList())
                .stream()
                .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
            resultPO.setRespManName(respManNames);

            resultPO.setFirstCatalogName(MapUtils.getString(firstCatalogMap,
                String.valueOf(resultPO.getFirstCatalog())));
        }

        return list.stream().map(AuditRiskHistoryUtil::makeResultPO2DTO)
            .collect(Collectors.toList());
    }

    @Override
    public List<FindAllAuditRiskResultDTO> findAllauditRisk(FindAllAuditRiskParamDTO param) {
        FindAllAuditRiskParamPO riskParam = AuditRiskConvertUtil.makeSearchParamDTO3PO(param);
        List<FindAllAuditRiskResultPO> risks = this.dao.findAllauditRisk(riskParam);
        risks.stream()
            .filter(Objects::nonNull)
            .forEach(risk -> {
                // 设置责任人 ID
                List<AuditRiskRespMan> respManList = respManService.findByRiskId(risk.getRiskId());
                if (CollectionUtils.isNotEmpty(respManList)) {
                    List<String> respManIdList = respManList.stream()
                        .map(AuditRiskRespMan::getRespManId)
                        .collect(Collectors.toList());
                    risk.setRespManIds(respManIdList);
                }

                // 设置责任部门 ID
                List<AuditRiskRespDept> respDeptList = deptService.findByRiskId(risk.getRiskId());
                if (CollectionUtils.isNotEmpty(respDeptList)) {
                    List<String> respDeptIdList = respDeptList.stream()
                        .map(AuditRiskRespDept::getRespDeptId)
                        .collect(Collectors.toList());
                    risk.setRespDeptIds(respDeptIdList);
                }
                List<String> orgIds = new LinkedList<>();
                List<String> respManIds = risk.getRespManIds();
                if (CollectionUtils.isNotEmpty(respManIds)) {
                    orgIds.addAll(respManIds);
                }
                List<String> respDeptIds = risk.getRespDeptIds();
                if (CollectionUtils.isNotEmpty(respDeptIds)) {
                    orgIds.addAll(respDeptIds);
                }

                Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
                    orgIds);

                String respDeptNames = Optional.ofNullable(risk.getRespDeptIds())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                    .filter(name -> StringUtils.isNotBlank(name))
                    .collect(Collectors.joining(","));
                risk.setRespDeptName(respDeptNames);
                String respManNames = Optional.ofNullable(risk.getRespManIds())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));
                risk.setRespManName(respManNames);
            });
        return risks.stream().map(AuditRiskConvertUtil::makeEntity3BaseInfoDTO)
            .collect(Collectors.toList());
    }

    @Override
    public AuditRiskDetailDTO getRiskByIdWithAcCheck(Long id) {
        AuditRiskAc riskAc = this.riskAccountabilityService.getAuditRiskAcByRiskId(id);
        if (!Objects.isNull(riskAc)) {
            log.error("当前风险点{}已存在问责管理数据,请前往问责结果管理查看", id);
            throw new BussinessException(AUDIT_RISK_AC_EXISTS);
        }
        AuditRiskDataResultPO risk = this.dao.getRiskById(id);
        if (Objects.nonNull(risk)) {
            List<AuditRiskRespMan> respManList = respManService.findByRiskId(id);
            if (CollectionUtils.isNotEmpty(respManList)) {
                List<String> respManIdList = respManList.stream()
                    .map(AuditRiskRespMan::getRespManId)
                    .collect(Collectors.toList());
                risk.setRespManId(respManIdList);
            }

            // 设置责任部门 ID
            List<AuditRiskRespDept> respDeptList = deptService.findByRiskId(id);
            if (CollectionUtils.isNotEmpty(respDeptList)) {
                List<String> respDeptIdList = respDeptList.stream()
                    .map(AuditRiskRespDept::getRespDeptId)
                    .collect(Collectors.toList());
                risk.setRespDeptId(respDeptIdList);
            }

            return AuditRiskConvertUtil.makeEntity3BaseInfoDTO(risk);
        }
        return null;
    }

    @Override
    public AuditRiskDetailDTO getFormatedData(Long riskId) {
        AuditRiskDetailDTO result = this.loadDetail(riskId);
        if (Objects.nonNull(result)) {
            result.setRiskName(result.getName());
            result.setPlanId(result.getPlanId());
            this.format.format(Collections.singletonList(result));
        }
        return result;
    }

    @Override
    public AuditRiskBaseInfoDTO getById(Long id) {
        AuditRisk risk = this.dao.getById(id);
        return AuditRiskConvertUtil.makeEntity2BaseInfoDTO(risk);
    }

    @Override
    public List<AuditRiskBaseInfoDTO> findExistRisksByProjectId(Long projectId) {
        if (Objects.nonNull(projectId)) {
            List<AuditRisk> risks = this.dao.findExistRisksByProjectId(projectId);
            if (CollUtil.isNotEmpty(risks)) {
                return risks.stream().map(AuditRiskConvertUtil::makeEntity2BaseInfoDTO).collect(
                    Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditRiskBaseInfoDTO> findExistRisksByProjectIds(Collection<Long> projectIds) {
        if (CollUtil.isNotEmpty(projectIds)) {
            List<AuditRisk> risks = this.dao.findExistRisksByProjectIds(projectIds);
            if (CollUtil.isNotEmpty(risks)) {
                return risks.stream().map(AuditRiskConvertUtil::makeEntity2BaseInfoDTO).collect(
                    Collectors.toList());
            }
        }
        return Collections.emptyList();
    }
}
