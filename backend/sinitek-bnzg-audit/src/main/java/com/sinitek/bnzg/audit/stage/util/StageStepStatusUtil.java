package com.sinitek.bnzg.audit.stage.util;


import static com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant.FINISHED;
import static com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant.PROCESSING;
import static com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant.READY;
import static com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant.TERMINATE;
import static com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant.UN_START;

import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 7/11/2023 1:01 PM
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStepStatusUtil {

    /**
     * 检查阶段步骤状态能否转换
     *
     * @param oldStatus 原阶段步骤状态
     * @param newStatus 现阶段步骤状态
     * @return boolean true: 继续执行，false: 不再继续执行
     */
    public static boolean checkCanChangeStatus(Integer oldStatus, Integer newStatus) {
        if (Objects.isNull(newStatus)) {
            log.warn(
                "检查阶段步骤状态能否从[oldStatus:{}]变为[{newStatus:{}}]时传入新状态为null,返回false",
                oldStatus,
                newStatus);
            return false;
        }
        switch (newStatus) {
            case READY:
                return checkCanChangeAsReadyStatus(oldStatus);
            case PROCESSING:
                return checkCanChangeAsProcessiongStatus(oldStatus);
            case FINISHED:
                return checkCanChangeAsFinishedStatus(oldStatus);
            case TERMINATE:
                return checkCanChangeAsTerminatedStatus(oldStatus);
            default:
                log.warn(
                    "检查阶段步骤状态能否从[oldStatus:{}]变为[{newStatus:{}}]时未处理旧状态,返回false",
                    oldStatus,
                    newStatus);
                return false;
        }
    }

    /**
     * 检查能否变成允许状态
     */
    public static boolean checkCanChangeAsReadyStatus(Integer oldStatus) {
        if (Objects.equals(READY, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前已经为 就绪 状态,返回false,不再继续");
            return false;
        }
        // 只有 不存在状态 或 不允许状态 才能转为 允许状态
        if (Objects.isNull(oldStatus)
            || Objects.equals(UN_START, oldStatus)) {
            log.info("当前状态[{}]满足变为 就绪 状态的条件", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不满足变为 就绪 状态的条件,返回false,不再继续", oldStatus);
        return false;
    }

    /**
     * 检查能否变成处理中状态
     */
    public static boolean checkCanChangeAsProcessiongStatus(Integer oldStatus) {
        if (Objects.equals(PROCESSING, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前已经为 处理中 状态,返回false,不再继续");
            return false;
        }
        // 只有 允许状态 才能转为 处理中状态
        if (Objects.equals(READY, oldStatus)) {
            log.info("当前状态[{}]满足变为 处理中 状态的条件", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不满足变为 处理中 状态的条件,返回false,不再继续", oldStatus);
        return false;
    }

    /**
     * 检查能否变成完成状态
     */
    public static boolean checkCanChangeAsFinishedStatus(Integer oldStatus) {
        if (Objects.equals(FINISHED, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前已经为 完成 状态,返回false,不再继续");
            return false;
        }
        // 只有 允许或处理中 才能转为 完成状态
        if (Objects.equals(READY, oldStatus)
            || Objects.equals(PROCESSING, oldStatus)) {
            log.info("当前状态[{}]满足变为 完成 状态的条件", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不满足变为 完成 状态的条件,返回false,不再继续", oldStatus);
        return false;
    }

    /**
     * 检查能否变成已终止状态
     */
    public static boolean checkCanChangeAsTerminatedStatus(Integer oldStatus) {
        if (Objects.equals(TERMINATE, oldStatus)) {
            // 如果当前就是该状态则不再继续
            log.warn("当前已经为 已终止 状态,返回false,不再继续");
            return false;
        }
        // 只有 允许或处理中 才能转为 已终止状态
        if (Objects.equals(READY, oldStatus)
            || Objects.equals(PROCESSING, oldStatus)) {
            log.info("当前状态[{}]满足变为 已终止 状态的条件", oldStatus);
            return true;
        }

        log.warn("当前状态[{}]不满足变为 已终止 状态的条件,返回false,不再继续", oldStatus);
        return false;
    }

}
