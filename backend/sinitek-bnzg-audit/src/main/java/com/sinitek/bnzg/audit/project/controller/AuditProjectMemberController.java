package com.sinitek.bnzg.audit.project.controller;

import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberSaveParamWrapperDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 07/29/2024 14:27
 */
@Slf4j
@RestController
@RequestMapping("/frontend/api/audit/project/member")
@Api(value = "/frontend/api/audit/project/member", tags = "审计系统-项目成员管理")
public class AuditProjectMemberController {

    @Autowired
    private IAuditProjectMemberService projectMemberService;

    @ApiOperation(value = "保存项目成员")
    @PostMapping(path = "/save-or-update")
    public void saveProjectProcedure(
        @Validated @RequestBody AuditProjectMemberSaveParamWrapperDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOpTime(new Date());
        param.setCheckAuthFlag(true);
        this.projectMemberService.saveMembersWithoutOwners(param);
    }

}
