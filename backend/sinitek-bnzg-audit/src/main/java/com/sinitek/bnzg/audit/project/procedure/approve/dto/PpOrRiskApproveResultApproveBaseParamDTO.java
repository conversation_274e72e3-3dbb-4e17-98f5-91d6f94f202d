package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 项目审计程序审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode
@ApiModel(description = "项目审计程序或风险点审批基础参数DTO")
public class PpOrRiskApproveResultApproveBaseParamDTO {

    @NotNull(message = "审批结果不能为空")
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;
}
