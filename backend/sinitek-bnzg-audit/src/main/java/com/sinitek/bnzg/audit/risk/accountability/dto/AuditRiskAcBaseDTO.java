package com.sinitek.bnzg.audit.risk.accountability.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date：2024/11/15 10:32
 */


@Data
@EqualsAndHashCode
@ApiModel(value = "项目风险点问责情况基本DTO")
public class AuditRiskAcBaseDTO  {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("审议阶段")
    private String reviewStage;

    @ApiModelProperty("审计复议")
    private String auditReconcile;

    @ApiModelProperty("处罚执行")
    private String punishExecution;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审议开始日期")
    private Date reviewStartDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审议结束日期")
    private Date reviewEndDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("公司党委会审议日期")
    private Date companyReviewDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("处罚告知书下发日期")
    private Date punishNoticeDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计复议申请日期")
    private Date reconcileApplyDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("处罚决定书下发日期")
    private Date punishDecisionDate;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("处罚落实日期")
    private Date punishImplementDate;

}

