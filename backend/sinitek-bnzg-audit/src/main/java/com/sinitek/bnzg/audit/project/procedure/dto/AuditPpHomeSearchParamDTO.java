package com.sinitek.bnzg.audit.project.procedure.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@ApiModel("项目审计程序首页查询 - 参数DTO")
@EqualsAndHashCode(callSuper = true)
public class AuditPpHomeSearchParamDTO extends PageDataParam {

    @ApiModelProperty(value = "查询类型 1-待处理审计程序 2-待审批审计程序 3-未审批通过数据")
    @NotNull(message = "查询类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "当前登录人,后端自动生成")
    private String currentOrgId;

}
