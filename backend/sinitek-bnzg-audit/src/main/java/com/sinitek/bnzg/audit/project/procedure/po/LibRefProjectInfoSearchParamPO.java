package com.sinitek.bnzg.audit.project.procedure.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collection;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/13/2024 11:24
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("被审计项目引用的审计程序库信息")
public class LibRefProjectInfoSearchParamPO extends PageDataParam {

    @ApiModelProperty("审计程序库id")
    private Collection<Long> libIds;

}
