package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 12/05/2024 14:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "审计项目列查询DTO")
public class AuditProjectSearchParamDTO extends PageDataParam {

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目进度")
    private List<Integer> projectPhases;

    @ApiModelProperty("审计年度")
    private List<Integer> auditYears;

}
