package com.sinitek.bnzg.audit.project.util;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.audit.project.event.AuditProjectChangeEvent;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectChangeEventUtil {
    public static <T extends AbstractRecordChangeLogAddParamBaseDTO> void publish(
            T source) {
        LcEventUtil.publishEvent(new AuditProjectChangeEvent<>(source));
    }
}
