package com.sinitek.bnzg.audit.project.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_project")
@ApiModel(description = "审计项目实体")
public class AuditProject extends BaseEntity {

    public static final String ENTITY_NAME = "AUDIT_PROJECT";

    /**
     * 计划id
     */
    @ApiModelProperty("计划id")
    private Long planId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private Date endDate;
    /*
     * 审计期开始日期
     */
    @ApiModelProperty("审计期开始日期")
    private Date periodStartDate;

    /*
     * 审计期结束日期
     */
    @ApiModelProperty("审计期结束日期")
    private Date periodEndDate;
    /**
     * 项目进度
     */
    @ApiModelProperty("项目进度")
    private Integer projectPhase;

    /**
     * 启动日期
     */
    @ApiModelProperty("启动日期")
    private Date startupDate;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 上报时间
     */
    @ApiModelProperty("上报时间")
    private Date regulatoryTime;


    /**
     * 是否上报
     */
    @ApiModelProperty("是否上报")
    private Integer regulatoryFlag;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 现场审计开始日期
     */
    @ApiModelProperty("现场审计开始日期")
    private Date localAuditStartDate;

    /**
     * 现场审计结束日期
     */
    @ApiModelProperty("现场审计结束日期")
    private Date localAuditEndDate;

    /**
     * 征求意见开始日期
     */
    @ApiModelProperty("征求意见开始日期")
    private Date consultationStartDate;

    /**
     * 征求意见结束日期
     */
    @ApiModelProperty("征求意见结束日期")
    private Date consultationEndDate;

    /**
     * 审计报告完成日期
     */
    @ApiModelProperty("审计报告完成日期")
    private Date reportCompleteDate;

    /**
     * 项目类型
     */
    @ApiModelProperty("项目类型")
    private Integer projectType;
}
