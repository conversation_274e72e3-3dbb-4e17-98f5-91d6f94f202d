package com.sinitek.bnzg.audit.project.excelimport.support;

import static com.sinitek.bnzg.audit.lib.excelimport.constant.AuditLibraryExcelImportMessageCodeConstant.DATA_EXCEPTION;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.sinitek.bnzg.audit.project.excelimport.dto.ProjectRiskExcelParseDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.LinkedList;
import java.util.List;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-12-25 15:59
 */
@Slf4j
@NoArgsConstructor
public class ProjectRiskExcelImportListener implements ReadListener<ProjectRiskExcelParseDTO> {
    
    // 数据
    private List<ProjectRiskExcelParseDTO> list = new LinkedList<>();

    @Override
    public void invoke(ProjectRiskExcelParseDTO data, AnalysisContext context) {
        if (log.isDebugEnabled()) {
            log.debug("解析到一条数据:{}", JsonUtil.toJsonString(data));
        }

        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ReadSheetHolder readSheetHolder = context.readSheetHolder();
        ReadSheet readSheet = readSheetHolder.getReadSheet();
        String sheetName = readSheet.getSheetName();
        Integer sheetNo = readSheet.getSheetNo();
        log.info("sheetName: {},sheetNo: {} 解析完成", sheetName, sheetNo);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            ReadSheetHolder readSheetHolder = context.readSheetHolder();
            ReadSheet readSheet = readSheetHolder.getReadSheet();
            String sheetName = readSheet.getSheetName();
            Integer sheetNo = readSheet.getSheetNo();

            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            Integer rowIndex = excelDataConvertException.getRowIndex();
            Integer columnIndex = excelDataConvertException.getColumnIndex();
            String errMsg = String.format("第[%s]个名为[%s]sheet中第[%s]行，第[%s]列解析异常",
                sheetNo + 1, sheetName, rowIndex + 1, columnIndex + 1);
            log.error("sheetName: {},sheetNo: {},第{}行，第{}列解析异常", sheetName, sheetNo + 1,
                rowIndex + 1, columnIndex + 1);
            throw new BussinessException(DATA_EXCEPTION, errMsg);
        } else {
            throw exception;
        }
    }

    public List<ProjectRiskExcelParseDTO> getAllData() {
        return list;
    }
}
