package com.sinitek.bnzg.audit.project.procedure.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditPpMessageCodeConstant {

    /**
     * 数据不存在无法变更
     */
    public static final String CANT_CHANGE_BECAUSE_DATA_NOT_EXISTS = "9902003024";

    /**
     * 仅状态为审批通过,不通过才能变更
     */
    public static final String CANT_CHANGE_BECAUSE_ILL_STATUS = "9902003025";

    /**
     * 项目审计程序数据为空,无法保存
     */
    public static final String CANT_SAVE_BECAUSE_NO_DATA = "9902003026";

    /**
     * 项目审计程序为审批中状态,无法保存
     */
    public static final String CANT_SAVE_BECAUSE_NOT_DRAFT = "9902003027";

    /**
     * 9902003032=当前数据不存在,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_BECAUSEOF_NOT_EXISTS = "9902003032";

    /**
     * 9902003033=当前项目审计程序非最新数据,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_BECAUSEOF_NOT_LATEST = "9902003033";

    /**
     * 9902003034=当前项目审计程序非草稿状态,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_BECAUSEOF_NOT_DRAFT = "9902003034";

    /**
     * 9902003035=当前项目审计程序不存在变更前数据,无法取消变更
     */
    public static final String CANT_CANCEL_CHANGE_BECAUSEOF_NO_PRE_DATA = "9902003035";

    /**
     * 9902003036=当前项目审计程序不存在需要提交的数据
     */
    public static final String NO_DATA_NEED_SUBMIT = "9902003036";

    /**
     * 9902003037=当前项目进度为[{0}]无法关闭
     */
    public static final String CANT_CLOSE_BECAUSEOF_PROGRESS = "9902003037";

    /**
     * 9902003038=当前审计项目未配置项目负责人,无法终止项目
     */
    public static final String CANT_STOP_BECAUSEOF_NO_PROJECT_LEADER = "9902003038";

    /**
     * 9902003039=当前登录人不是当前审计项目的负责人,无法终止项目
     */
    public static final String CANT_STOP_BECAUSEOF_NOT_PROJECT_LEADER = "9902003039";

    /**
     * 9902003040=审计项目不存在,无法操作
     */
    public static final String CANT_OPERATE_BECAUSEOF_PROJECT = "9902003040";

    /**
     * 9902003041=审计项目已终止,无法操作
     */
    public static final String CANT_OPERATE_BECAUSEOF_PROJECT_STOP = "9902003041";

    /**
     * 9902003042=项目审计程序不存在,无法操作
     */
    public static final String CANT_OPERATE_BECAUSEOF_PROJECT_PROCEDURE = "9902003042";

    /**
     * 9902003043=当前项目进度为[{0}]无法终止
     */
    public static final String CANT_STOP_BECAUSEOF_PROGRESS = "9902003043";

    /**
     * 9902003044=当前数据非最新,无法{0}
     */
    public static final String CANT_OPERATE_BECAUSEOF_NOT_LATEST = "9902003044";
}
