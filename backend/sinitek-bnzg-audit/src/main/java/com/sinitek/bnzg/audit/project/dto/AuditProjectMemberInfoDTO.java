package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/09/2024 14:41
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("项目成员信息")
public class AuditProjectMemberInfoDTO {

    @ApiModelProperty("orgId")
    private String orgId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("角色")
    private Integer role;
}
