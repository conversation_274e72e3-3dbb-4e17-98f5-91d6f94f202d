package com.sinitek.bnzg.audit.risk.contact.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 整改联系人 Entity
 *
 * <AUTHOR>
 * date 2024-08-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_rectify_contact")
@ApiModel(description = "整改联系人实体")
public class RectifyContact extends BaseEntity {

    /**
     * 风险点id
     */
    @ApiModelProperty("风险点id")
    private Long riskId;

    /**
     * 人员orgId
     */
    @ApiModelProperty("人员orgId")
    private String orgId;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    private Integer sort;

}
