package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:09
 */
@Data
@Slf4j
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目阶段变动事件")
public class AuditProjectPhaseChangeEventSourceDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("操作人")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date opTime;

    @ApiModelProperty("旧项目阶段")
    private Integer oldProjectPhase;

    @ApiModelProperty("新项目阶段")
    private Integer newProjectPhase;

    @ApiModelProperty("备注")
    private String remark;
}
