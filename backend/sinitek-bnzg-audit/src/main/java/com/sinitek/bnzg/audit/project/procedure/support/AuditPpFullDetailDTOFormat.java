package com.sinitek.bnzg.audit.project.procedure.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpFullDetailDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditPpFullDetailDTOFormat implements
    ITableResultFormat<AuditPpFullDetailDTO> {

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Override
    public List<AuditPpFullDetailDTO> format(
        List<AuditPpFullDetailDTO> data) {

        List<String> orgIds = new LinkedList<>();
        data.forEach(item -> {
            String auditorId = item.getAuditorId();
            String approverId = item.getApproverId();

            orgIds.add(auditorId);
            orgIds.add(approverId);
        });

        // key: 类型值字符串
        // value: 名称
        // 风险点状态
        Map<String, String> riskStatusMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_STATUS);

        List<OrgObjectDTO> orgObjects = this.orgService.findOrgObjectsByOrgIds(orgIds);
        Map<String, String> orgIdAndNameMap;
        if (CollUtil.isNotEmpty(orgObjects)) {
            orgIdAndNameMap = orgObjects.stream().collect(
                Collectors.toMap(OrgObjectDTO::getOrgId,
                    OrgObjectDTO::getOrgName));
        } else {
            orgIdAndNameMap = Collections.emptyMap();
        }

        data.forEach(item -> {
            String auditorId = item.getAuditorId();
            String approverId = item.getApproverId();
            Integer status = item.getStatus();

            item.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId));
            item.setApproverName(MapUtils.getString(orgIdAndNameMap, approverId));
            item.setStatusName(MapUtils.getString(riskStatusMap, String.valueOf(status), ""));
        });

        return data;
    }
}
