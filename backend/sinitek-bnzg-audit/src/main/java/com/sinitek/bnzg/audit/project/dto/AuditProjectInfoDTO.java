package com.sinitek.bnzg.audit.project.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "审计项目")
public class AuditProjectInfoDTO {

    @ApiModelProperty("主键")
    private Long id;

    /**
     * 计划id
     */
    @ApiModelProperty("计划id")
    private Long planId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private Date endDate;

    /**
     * 项目进度
     */
    @ApiModelProperty("项目进度")
    private Integer projectPhase;

    /**
     * 启动日期
     */
    @ApiModelProperty("启动日期")
    private Date startupDate;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /*
     * 审计期开始日期
     */
    @ApiModelProperty("审计期开始日期")
    private Date periodStartDate;

    /*
     * 审计期结束日期
     */
    @ApiModelProperty("审计期结束日期")
    private Date periodEndDate;

    /**
     * 上报时间
     */
    @ApiModelProperty("上报时间")
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    private Date regulatoryTime;


    /**
     * 是否上报
     */
    @ApiModelProperty("是否上报")
    private Integer regulatoryFlag;
}
