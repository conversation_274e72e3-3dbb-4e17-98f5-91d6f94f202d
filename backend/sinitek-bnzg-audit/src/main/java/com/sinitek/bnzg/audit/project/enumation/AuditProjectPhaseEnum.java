package com.sinitek.bnzg.audit.project.enumation;

import java.util.Objects;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:15
 */
@Slf4j
@Getter
public enum AuditProjectPhaseEnum {
    INIT("待启动", 0),
    READY("审计准备", 1),
    IMPL("审计实施", 2),
    SUGGEST("征求意见", 3),
    REPORT("审计报告", 4),
    TRACKING("整改跟踪", 5),
    CLOSE("已关闭", 6),
    STOP("已中止", 7);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    AuditProjectPhaseEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static AuditProjectPhaseEnum getByValue(Integer value) {
        for (AuditProjectPhaseEnum item : values()) {
            if (Objects.equals(item.getValue(), value)) {
                return item;
            }
        }
        log.warn("根据[{}]获取项目进度,没有匹配的数据", value);
        return STOP;
    }
}
