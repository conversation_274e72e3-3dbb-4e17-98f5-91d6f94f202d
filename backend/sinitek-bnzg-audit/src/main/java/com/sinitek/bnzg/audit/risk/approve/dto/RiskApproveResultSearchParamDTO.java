package com.sinitek.bnzg.audit.risk.approve.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "风险点审批结果-分页查询DTO")
public class RiskApproveResultSearchParamDTO extends PageDataParam {

    @NotNull(message = "审批id不能为空")
    @ApiModelProperty(value = "审批id")
    private Long approveId;

}
