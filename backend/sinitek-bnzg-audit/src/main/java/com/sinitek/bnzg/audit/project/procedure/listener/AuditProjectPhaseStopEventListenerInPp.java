package com.sinitek.bnzg.audit.project.procedure.listener;

import com.sinitek.bnzg.audit.project.constant.AuditProjectPhaseConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectPhaseChangeEventSourceDTO;
import com.sinitek.bnzg.audit.project.event.AuditProjectPhaseChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveTerminateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class AuditProjectPhaseStopEventListenerInPp {

    @Autowired
    private IPpApproveService ppApproveService;

    /**
     * 监听项目阶段终止事件
     *
     * 清理该项目下所有在途流程
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditProjectPhaseChangeEvent.class, fallbackExecution = true)
    public void listen(AuditProjectPhaseChangeEvent event) {
        AuditProjectPhaseChangeEventSourceDTO source = event.getSource();
        Long projectId = source.getProjectId();
        String opOrgId = source.getOperatorId();
        Date opTime = source.getOpTime();
        Integer oldProjectPhase = source.getOldProjectPhase();
        Integer newProjectPhase = source.getNewProjectPhase();

        if (Objects.equals(newProjectPhase, AuditProjectPhaseConstant.STOP)) {
            log.info(
                "监听到审计项目[{}]阶段终止事件,操作人[{}],操作时间[{}],旧阶段[{}] => 新阶段[{}],清理该项目下所有在途流程",
                projectId, opOrgId, opTime, oldProjectPhase, newProjectPhase
            );

            this.ppApproveService.terminate(PpApproveTerminateParamDTO.builder()
                .projectId(projectId)
                .operatorId(opOrgId)
                .opTime(opTime)
                .opRemark("项目终止")
                .build());
        }


    }

}
