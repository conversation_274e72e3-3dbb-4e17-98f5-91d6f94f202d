package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "项目成员保存参数")
public class AuditProjectMember4ImportSaveParamDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("成员id")
    private String orgId;

    @ApiModelProperty("角色")
    private Integer role;

    @ApiModelProperty("备注")
    private String remark;

}
