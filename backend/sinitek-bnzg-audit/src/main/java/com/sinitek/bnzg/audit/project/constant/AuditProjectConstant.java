package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectConstant {

    public static final String SOURCE_NAME = "AUDIT_PROJECT";

    /**
     * 项目启动材料
     */
    public static final int PROJECT_START_ATTACHMENT_TYPE = 0;

    /**
     * 负责人OrgId
     */
    public static final String OWNER_ORGID = "ownerOrgIds";

    /**
     * 负责人名称
     */
    public static final String OWNER_NAME = "ownerNames";

    /**
     * 风险点
     */
    public static final String RISKS = "risks";

    /**
     * 审计组成员
     */
    public static final String AUDIT_GROUP_MEMBERS = "auditGroupMembers";

    /**
     * 审计报告
     */
    public static final String AUDIT_REPORT_NAME = "审计报告";


    /**
     * 制度建设
     */
    public static final Integer BUILD_RISKS = 1;

    /**
     * 制度执行
     */
    public static final Integer EXECUTE_RISKS = 2;
}
