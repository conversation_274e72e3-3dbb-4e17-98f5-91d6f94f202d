package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目程序校验结果")
public class AuditCheckResultDTO {

    @ApiModelProperty("校验结果")
    private Boolean result;

    @ApiModelProperty(value = "错误消息(当校验结果为false时,返回错误消息)")
    private String msg;
}
