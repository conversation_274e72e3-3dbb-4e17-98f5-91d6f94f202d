package com.sinitek.bnzg.audit.project.procedure.approve.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.mapper.AuditPpApproveResultMapper;
import com.sinitek.bnzg.audit.project.procedure.approve.po.AuditPpApproveResultCreateParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchResultPO;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 风险点审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Slf4j
@Component
public class AuditPpApproveResultDAO extends
    ServiceImpl<AuditPpApproveResultMapper, AuditPpApproveResult> {

    public List<AuditPpApproveResult> create(AuditPpApproveResultCreateParamPO param) {

        Long approveId = param.getApproveId();
        Long projectId = param.getProjectId();
        List<Long> ppIds = param.getPpIds();
        Map<Long, Long> ppIdAndProcedureIdMap = param.getPpIdAndProcedureIdMap();
        Map<Long, Integer> ppidAndApproveResultMap = param.getPpidAndApproveResultMap();

        List<AuditPpApproveResult> list = new LinkedList<>();
        for (int i = 0; i < ppIds.size(); i++) {
            Long ppId = ppIds.get(i);
            Long procedureId = MapUtils.getLong(ppIdAndProcedureIdMap, ppId);
            Integer approveResult = MapUtils.getInteger(ppidAndApproveResultMap, ppId);

            AuditPpApproveResult entity = new AuditPpApproveResult();
            entity.setApproveId(approveId);
            entity.setProjectId(projectId);
            entity.setProcedureId(procedureId);
            entity.setPpId(ppId);
            entity.setApproveResult(approveResult);
            entity.setSort(i);

            list.add(entity);
        }
        this.saveBatch(list);
        return list;
    }

    public IPage<PpApproveResultSearchResultPO> search(PpApproveResultSearchParamPO param) {
        Page<PpApproveResultSearchParamPO> page = param.buildPage();
        return this.baseMapper.search(page, param);
    }

    public List<AuditPpApproveResult> findExistByApproveId(Long approveId) {
        LambdaQueryWrapper<AuditPpApproveResult> query = Wrappers.lambdaQuery(
            AuditPpApproveResult.class);
        query.eq(AuditPpApproveResult::getApproveId, approveId);
        return this.list(query);
    }

    public List<AuditPpApproveResult> findApprovingByPpId(Long ppId) {
        LambdaQueryWrapper<AuditPpApproveResult> query = Wrappers.lambdaQuery(
            AuditPpApproveResult.class);
        query.eq(AuditPpApproveResult::getPpId, ppId);
        query.eq(AuditPpApproveResult::getApproveResult, AuditRiskApproveResultConstant.DRAFT);
        return this.list(query);
    }
}
