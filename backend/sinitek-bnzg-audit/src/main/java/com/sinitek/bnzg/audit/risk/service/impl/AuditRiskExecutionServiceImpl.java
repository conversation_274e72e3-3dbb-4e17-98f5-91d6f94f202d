package com.sinitek.bnzg.audit.risk.service.impl;

import static com.sinitek.bnzg.audit.doc.type.constant.DocTypeMessageCodeConstant.DOC_ATTACHMENT_COPY_FAILURE_ON_DOC_SAVE;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant.DESCRIPTION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.CANT_CANCEL_CHANGE_NOT_BEFORE_DATA;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.CANT_CANCEL_CHANGE_NOT_DRAFT;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.CANT_CANCEL_CHANGE_NOT_THREAD_LATEST;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.CANT_DELETE_BECAUSE_OF_NOT_DRAFT;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.CANT_EDIT_NOT_DRAFT;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.NOT_APPROVED_AND_NOT_APPROVED_CANT_CHANGE;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.NOT_THREAD_LATEST_CANT_CHANGE;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditRiskStatusUpdateParamDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.accountability.util.RiskAccountabilityEventUtil;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRespDeeptSuggestTypeConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.audit.risk.contact.dao.RectifyContactDAO;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.contact.entity.RectifyContact;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskChangeOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDeleteOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSaveOrEditOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import com.sinitek.bnzg.audit.risk.log.rectify.contact.util.AuditRectifyContactChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.log.rectify.state.util.AuditRiskRectifyStateChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.log.respdept.util.AuditRiskRespDeptChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.log.respman.util.AuditRespManChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.log.status.util.AuditRiskStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskExecutionService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRectifyTraceNoticeService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespManService;
import com.sinitek.bnzg.audit.risk.util.AuditRiskChangeEventUtil;
import com.sinitek.bnzg.audit.risk.util.AuditRiskConvertUtil;
import com.sinitek.bnzg.audit.risk.util.AuditRiskStatusUtil;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStepStatusUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam4DTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.lowcode.common.model.constant.LcModelConstant;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;


/**
 * 项目风险点 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@Slf4j
@Service
public class AuditRiskExecutionServiceImpl implements IAuditRiskExecutionService {

    @Autowired
    private AuditRiskDAO dao;

    @Autowired
    private IRectifyContactService contactService;

    @Autowired
    private RectifyContactDAO contactDao;

    @Autowired
    private IAuditRiskRectifyTraceNoticeService auditRiskRectifyTraceNoticeService;

    @Autowired
    private IAuditRiskRespDeptService deptService;

    @Autowired
    private IAuditRiskRespManService respManService;

    @Autowired
    private IRiskAccountabilityService accountabilityService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IStageStepExampleStatusService stepExampleStatusService;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IAuditProjectService projectService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveOrEdit(AuditRiskSaveOrEditOnExecutionParamDTO param) {
        Long id = param.getId();
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        List<String> respDeptIds = param.getRespDeptId();
        List<String> respManIds = param.getRespManId();

        // 是否为变更保存
        boolean isChangeSave = false;

        if (IdUtil.isNotDataId(id)) {
            AuditRisk entity = this.save(param, null);

            id = entity.getId();

            this.deptService.saveBatchRespDept(id, respDeptIds);

            this.respManService.saveBatchRespMan(id, respManIds);

            AuditRiskStatusChangeEventPublishUtil.publishEvent(RecordChangeLogAddParamDTO.builder()
                .foreignKey(id)
                .oldValue(null)
                .newValue(entity.getStatus())
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("审计实施-新增风险点")
                .build());

            AuditRiskChangeEventUtil.publish(RecordChangeLogAddParamDTO.builder()
                .foreignKey(id)
                .newValue(entity)
                .oldValue(null)
                .remark("审计实施-新增")
                .operatorId(operatorId)
                .opTime(opTime)
                .build());
        } else {
            AuditRisk riskDbData = this.dao.getById(id);
            if (Objects.isNull(riskDbData)) {
                log.error("编辑风险点时,{} 对应数据不存在", id);
                throw new BussinessException(AuditRiskMessageConstant.RISK_DATA_NOT_EXISTS);
            }
            Integer status = riskDbData.getStatus();
            if (Objects.equals(status, AuditRiskStatusConstant.APPROVED)
                || Objects.equals(status, AuditRiskStatusConstant.NOT_APPROVED)) {
                // 根据数据状态判断,如果是审批通过/不通过,则变更保存
                Long newId = this.change(AuditRiskChangeOnExecutionParamDTO.builder()
                    .id(id)
                    .operatorId(operatorId)
                    .copyAttachmentFlag(false)
                    .opTime(opTime)
                    .build());
                log.info("当前风险点[{}]为审批通过或不通过,保存时自动变更为{}", id, newId);
                id = newId;
                isChangeSave = true;
            }

            param.setId(id);
            Tuple2<AuditRisk, AuditRisk> result = this.edit(param);

            AuditRisk newValue = result.getT1();
            AuditRisk oldValue = result.getT2();
            id = newValue.getId();
            this.deptService.saveBatchRespDept(id, respDeptIds);
            this.respManService.saveBatchRespMan(id, respManIds);

            AuditRiskChangeEventUtil.publish(RecordChangeLogAddParamDTO.builder()
                .foreignKey(id)
                .newValue(newValue)
                .oldValue(oldValue)
                .remark("审计实施-编辑")
                .operatorId(operatorId)
                .opTime(opTime)
                .build());
        }

        // 整改联系人
        List<String> rectifyContactIds = param.getRectifyContactIds();
        this.contactService.saveContacts(RectifyContactEditParamDTO.builder()
            .riskId(id)
            .contacts(rectifyContactIds)
            .operatorId(operatorId)
            .opTime(opTime)
            .build());

        UploadDTO upload = param.getUpload();
        if (ObjectUtil.isNotEmpty(upload)) {
            upload.setType(DESCRIPTION_UPLOAD_TYPE);
            if (isChangeSave) {
                // 变更保存
                // 已删除附件需要移除
                upload.setRemoveFileList(Collections.emptyList());
                this.copyUpload2New(upload, id, AuditRiskConstant.DEFAULT_SOURCE_NAME,
                    DESCRIPTION_UPLOAD_TYPE);
            } else {
                AttachmentUtils.saveAttachmentList(upload, id,
                    AuditRiskConstant.DEFAULT_SOURCE_NAME);
            }
        }

        this.makeStepAsProgressing(projectId, operatorId, opTime, "风险点新增或编辑");

        return id;
    }

    private void copyUpload2New(UploadDTO upload, Long attachmentSourceId,
        String attachmentSourceEntity, Integer attachmentType) {
        List<UploadFileDTO> uploadFileList = upload.getUploadFileList();
        List<Attachment> attachments = new LinkedList<>();
        List<UploadFileDTO> filtedUploadFileList = uploadFileList.stream().map(item -> {
            // 如果是已上传文件,需要复制一份
            // 避免保存后出现旧版本附件丢失问题
            String id = item.getId();
            if (StringUtils.isNotBlank(id)) {
                // 复制一份
                Attachment attachment = AttachmentUtils.copyAttachmentByEncId(id);
                if (Objects.isNull(attachment)) {
                    log.error("根据 attachment id[{}]获取到的Attachment为空", id);
                    throw new BussinessException(DOC_ATTACHMENT_COPY_FAILURE_ON_DOC_SAVE);
                } else {
                    attachment.setObjId(null);
                    attachment.setType(attachmentType);
                    attachments.add(attachment);
                }

                // 返回null,后面会过滤掉
                return null;
            } else {
                item.setType(attachmentType);
                return item;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(filtedUploadFileList)) {
            log.info("attachment sourceId:[{}],sourceEntity:[{}]共有[{}]条新上传文件需要保存",
                attachmentSourceId, attachmentSourceEntity,
                filtedUploadFileList.size());
            upload.setUploadFileList(filtedUploadFileList);
            upload.setRemoveFileList(Collections.emptyList());
            AttachmentUtils.saveAttachmentList(upload, attachmentSourceId, attachmentSourceEntity);
        }

        if (CollUtil.isNotEmpty(attachments)) {
            log.info("attachment sourceId:[{}],sourceEntity:[{}]共有[{}]条已上传文件需要转存",
                attachmentSourceId,
                attachmentSourceEntity,
                attachments.size());
            this.attachmentService.saveAttachmentList(attachmentSourceId,
                attachmentSourceEntity,
                attachments);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long change(AuditRiskChangeOnExecutionParamDTO param) {
        Long id = param.getId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        Boolean copyAttachmentFlag = param.getCopyAttachmentFlag();

        AuditRisk entity = this.dao.getById(id);

        if (Objects.isNull(entity)) {
            log.error("变更风险点时,{} 对应数据不存在", id);
            throw new BussinessException(AuditRiskMessageConstant.RISK_DATA_NOT_EXISTS);
        }

        Long projectId = entity.getProjectId();

        Integer status = entity.getStatus();
        if (!Objects.equals(AuditRiskStatusConstant.APPROVED, status)
            && !Objects.equals(AuditRiskStatusConstant.NOT_APPROVED, status)) {
            log.error("当前风险点[{}]审批状态为[{}],非审批通过,审批不通过状态无法变更", id,
                status);
            throw new BussinessException(NOT_APPROVED_AND_NOT_APPROVED_CANT_CHANGE);
        }

        Integer threadLatestFlag = entity.getThreadLatestFlag();
        if (!Objects.equals(threadLatestFlag, LcModelConstant.THREAD_LATEST_FLAG)) {
            log.error("当前风险点[{}]最新标识为[{}],非最新数据无法变更", id,
                threadLatestFlag);
            throw new BussinessException(NOT_THREAD_LATEST_CANT_CHANGE);
        }

        // 最新数据变成非最新
        entity.setThreadLatestFlag(LcModelConstant.NOT_THREAD_LATEST_FLAG);
        this.dao.updateById(entity);

        Long threadId = entity.getThreadId();

        AuditRiskSaveOrEditOnExecutionParamDTO saveParam = new AuditRiskSaveOrEditOnExecutionParamDTO();
        BeanUtils.copyProperties(entity, saveParam);
        saveParam.setOperatorId(operatorId);
        saveParam.setOpTime(opTime);

        AuditRisk newEntity = this.save(saveParam, innerEntity -> {
            innerEntity.setThreadId(threadId);
            innerEntity.setApproverId(null);
            innerEntity.setApproveTime(null);
            innerEntity.setApproveRemark(null);
        });
        Long newId = newEntity.getId();

        // 删除旧风险点提醒与日程
        this.deleteRelatedData(id, operatorId);

        AuditRiskStatusChangeEventPublishUtil.publishEvent(RecordChangeLogAddParamDTO.builder()
            .foreignKey(newId)
            .oldValue(null)
            .newValue(newEntity.getStatus())
            .operatorId(operatorId)
            .opTime(opTime)
            .remark("审计实施-风险点变更")
            .build());

        AuditRiskChangeEventUtil.publish(RecordChangeLogAddParamDTO.builder()
            .foreignKey(newId)
            .newValue(newEntity)
            .oldValue(null)
            .remark("审计实施-变更新增")
            .operatorId(operatorId)
            .opTime(opTime)
            .build());

        List<AuditRiskRespDept> respDepts = this.deptService.findByRiskId(id);
        if (CollectionUtils.isNotEmpty(respDepts)) {
            List<String> existsdeptOrgIds = respDepts.stream().map(AuditRiskRespDept::getRespDeptId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<AuditRiskRespDept> needSaveList = new LinkedList<>();
            respDepts.forEach(respDept -> {
                String respDeptId = respDept.getRespDeptId();
                if (StringUtils.isNotBlank(respDeptId)) {
                    respDept.setId(null);
                    respDept.setRiskId(newId);
                    needSaveList.add(respDept);
                }
            });
            if (CollUtil.isNotEmpty(needSaveList)) {
                log.info("本次风险点变更需保存[{}]条责任部门数据", needSaveList.size());
                this.deptService.saveOrUpdateBatch(respDepts);
                AuditRiskRespDeptChangeEventPublishUtil.publishEvent(
                    RecordChangeLogAddParamDTO.<String>builder()
                        .foreignKey(newId)
                        .oldValue(null)
                        .newValue(JsonUtil.toJsonString(existsdeptOrgIds))
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark("风险点变更-新增责任部门")
                        .build());
            } else {
                log.warn("本次风险点变更无需保存责任部门数据");
            }
        }

        List<AuditRiskRespMan> respManList = this.respManService.findByRiskId(id);
        if (CollectionUtils.isNotEmpty(respManList)) {
            List<String> existsResManOrgIds = respManList.stream()
                .map(AuditRiskRespMan::getRespManId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            List<AuditRiskRespMan> needSaveList = new LinkedList<>();
            respManList.forEach(respMan -> {
                String respManId = respMan.getRespManId();
                if (StringUtils.isNotBlank(respManId)) {
                    respMan.setId(null);
                    respMan.setRiskId(newId);
                    needSaveList.add(respMan);
                }
            });
            if (CollUtil.isNotEmpty(needSaveList)) {
                log.info("本次风险点变更需保存[{}]条责任人数据", needSaveList.size());
                this.respManService.saveOrUpdateBatch(respManList);

                AuditRespManChangeEventPublishUtil.publishEvent(
                    RecordChangeLogAddParamDTO.<String>builder()
                        .foreignKey(newId)
                        .oldValue(null)
                        .newValue(JsonUtil.toJsonString(existsResManOrgIds))
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark("风险点变更-新增责任人")
                        .build());
            } else {
                log.warn("本次风险点变更无需保存责任人数据");
            }

        }

        //问题描述附件
        if (Objects.equals(copyAttachmentFlag, Boolean.TRUE)) {
            List<Attachment> attachments = this.attachmentService.findAttachments(
                AuditRiskConstant.DEFAULT_SOURCE_NAME, DESCRIPTION_UPLOAD_TYPE,
                Collections.singletonList(id));
            if (CollUtil.isNotEmpty(attachments)) {
                List<Long> objids = attachments.stream().map(Attachment::getObjId)
                    .collect(Collectors.toList());
                this.attachmentService.copyAttachment(objids, AuditRiskConstant.DEFAULT_SOURCE_NAME,
                    newId);
                log.info("风险点[{}]变更[{}]-复制问题描述附件,附件id:{}", id, newId, objids);
            }
        } else {
            log.info("风险点[{}]变更[{}]-不复制问题描述附件", id, newId);
        }

        //整改联系人
        List<RectifyContact> rectifyContacts = this.contactDao.findByRiskId(id);
        if (CollectionUtils.isNotEmpty(rectifyContacts)) {
            List<String> contacts =
                rectifyContacts.stream().map(RectifyContact::getOrgId).collect(Collectors.toList());
            rectifyContacts.forEach(rectifyContact -> {
                rectifyContact.setId(null);
                rectifyContact.setRiskId(newId);
            });
            this.contactDao.saveOrUpdateBatch(rectifyContacts);

            AuditRectifyContactChangeEventPublishUtil.publishEvent(
                RecordChangeLogAddParamDTO.<String>builder()
                    .foreignKey(newId)
                    .oldValue(null)
                    .newValue(JsonUtil.toJsonString(contacts))
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("风险点变更-新增整改联系人")
                    .build());
        }

        //风险点变更后问责数据处理
        //查询旧风险点是否有问责数据
        AuditRiskAc riskAc = this.accountabilityService.getAuditRiskAcByRiskId(id);
        if (Objects.isNull(riskAc)) {
            log.info("当前风险点{}不存在问责管理数据,不进行问责数据处理", id);
        } else {
            accountabilityService.updateByRiskId(id, newId);
            AuditRiskAc newRiskAc = this.accountabilityService.getAuditRiskAcByRiskId(newId);
            RiskAccountabilityEventUtil.publish(RecordChangeLogAddParamDTO.builder()
                .foreignKey(riskAc.getId())
                .oldValue(riskAc)
                .newValue(newRiskAc)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("风险点变更-问责数据结果处理")
                .build());
        }

        this.makeStepAsProgressing(projectId, operatorId, opTime, "风险点变更");

        return newId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelChange(AuditRiskChangeOnExecutionParamDTO param) {
        Long id = param.getId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditRisk entity = this.dao.getById(id);

        if (Objects.isNull(entity)) {
            log.error("取消变更风险点时,{} 对应数据不存在", id);
            throw new BussinessException(AuditRiskMessageConstant.RISK_DATA_NOT_EXISTS);
        }

        Integer status = entity.getStatus();
        if (!Objects.equals(AuditRiskStatusConstant.DRAFT, status)) {
            log.error("当前风险点[{}]审批状态为[{}],非草稿状态无法取消变更", id,
                status);
            throw new BussinessException(CANT_CANCEL_CHANGE_NOT_DRAFT);
        }

        Integer threadLatestFlag = entity.getThreadLatestFlag();
        if (!Objects.equals(threadLatestFlag, LcModelConstant.THREAD_LATEST_FLAG)) {
            log.error("当前风险点[{}]最新标识为[{}],非最新数据无法变更", id,
                threadLatestFlag);
            throw new BussinessException(CANT_CANCEL_CHANGE_NOT_THREAD_LATEST);
        }

        Long threadId = entity.getThreadId();

        AuditRisk entityCopy = JsonUtil.jsonCopy(entity, AuditRisk.class);

        // - 根据当前操作风险点获取非最新但审批通过的更新时间最近的数据作为取消变更后数据
        AuditRisk latestOne = this.dao.getLatestOne(id, threadId);
        if (Objects.isNull(latestOne)) {
            log.error("当前风险点[id:{},threadId:{}]不存在变更前数据", id, threadId);
            throw new BussinessException(CANT_CANCEL_CHANGE_NOT_BEFORE_DATA);
        }

        AuditRisk latestOneCopy = JsonUtil.jsonCopy(latestOne, AuditRisk.class);

        // - 当前操作的风险点变更为非最新且已删除数据,变更前数据变更为最新且未删除数据
        entity.setThreadLatestFlag(LcModelConstant.NOT_THREAD_LATEST_FLAG);
        this.dao.updateById(entity);
        // 删除
        this.dao.deleteByIds(Collections.singletonList(entity.getId()), operatorId);

        // 删除旧风险点提醒与日程
        this.deleteRelatedData(id, operatorId);

        latestOne.setThreadLatestFlag(LcModelConstant.THREAD_LATEST_FLAG);
        this.dao.updateById(latestOne);

        Long latestOneId = latestOne.getId();
        // - 问责数据绑定的风险点变更为取消变更后数据
        AuditRiskAc riskAc = this.accountabilityService.getAuditRiskAcByRiskId(id);
        if (Objects.isNull(riskAc)) {
            log.info("当前风险点{}不存在问责管理数据,不进行问责数据处理", id);
        } else {
            accountabilityService.updateByRiskId(id, latestOneId);
            AuditRiskAc newRiskAc = this.accountabilityService.getAuditRiskAcByRiskId(latestOneId);
            RiskAccountabilityEventUtil.publish(RecordChangeLogAddParamDTO.builder()
                .foreignKey(riskAc.getId())
                .oldValue(riskAc)
                .newValue(newRiskAc)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("风险点变更-问责数据结果处理")
                .build());
        }

        Map<Long, AuditRisk> newValueMap = new HashMap<>();
        newValueMap.put(id, null);
        newValueMap.put(latestOneId, latestOne);

        Map<Long, AuditRisk> oldValueMap = new HashMap<>();
        oldValueMap.put(id, entityCopy);
        oldValueMap.put(latestOneId, latestOneCopy);

        AuditRiskChangeEventUtil.publish(RecordChangeLogBatchAddParam2DTO.<AuditRisk>builder()
            .foreignKeys(Arrays.asList(id, latestOneId))
            .newValueMap(newValueMap)
            .oldValueMap(oldValueMap)
            .remark("审计实施-取消变更")
            .operatorId(operatorId)
            .opTime(opTime)
            .build());
    }

    private void deleteRelatedData(Long riskId, String operatorId) {
        log.info("操作人[{}]变更风险点[{}]删除有关的提醒与日程", operatorId, riskId);
        this.auditRiskRectifyTraceNoticeService.deleteRectifyChangeNotice(riskId, operatorId);
        this.auditRiskRectifyTraceNoticeService.deleteRectifyTraceCalendar(riskId, operatorId);
        this.auditRiskRectifyTraceNoticeService.deleteRectifyChangeCalendar(riskId, operatorId);
        this.auditRiskRectifyTraceNoticeService.deleteRectifyTraceNotice(riskId, operatorId);
    }

    private AuditRisk save(AuditRiskSaveOrEditOnExecutionParamDTO param,
        Consumer<AuditRisk> riskConsumer) {
        String operatorId = param.getOperatorId();
        AuditRisk entity = AuditRiskConvertUtil.makeSaveOrEditDTO2Entity(param);
        entity.setId(null);

        // 审批状态
        entity.setStatus(AuditRiskStatusConstant.DRAFT);

        // 审计人
        entity.setAuditorId(operatorId);

        // 最新标识
        entity.setThreadLatestFlag(LcModelConstant.THREAD_LATEST_FLAG);

        if (Objects.nonNull(riskConsumer)) {
            riskConsumer.accept(entity);
        }

        this.dao.save(entity);

        Long id = entity.getId();
        Long threadId = entity.getThreadId();
        if (Objects.isNull(threadId)) {
            entity.setThreadId(id);
            this.dao.updateById(entity);
        }

        return entity;
    }

    /**
     * @return 1: 新数据/2: 旧数据
     */
    private Tuple2<AuditRisk, AuditRisk> edit(AuditRiskSaveOrEditOnExecutionParamDTO param) {
        Long id = param.getId();
        AuditRisk entity = this.dao.getById(id);
        AuditRisk oldData = JsonUtil.jsonCopy(entity, AuditRisk.class);
        if (Objects.isNull(entity)) {
            log.error("编辑风险点时,{} 对应数据不存在", id);
            throw new BussinessException(AuditRiskMessageConstant.RISK_DATA_NOT_EXISTS);
        }

        Integer status = entity.getStatus();
        if (!Objects.equals(AuditRiskStatusConstant.DRAFT, status)) {
            log.error("当前风险点[{}]审批状态为[{}],非草稿状态无法编辑", id, status);
            throw new BussinessException(CANT_EDIT_NOT_DRAFT);
        }

        // 审计人
        String operatorId = param.getOperatorId();
        entity.setAuditorId(operatorId);

        AuditRiskConvertUtil.makeSaveOrEditDTO2Entity(param, entity);

        this.dao.updateById(entity);

        return Tuples.of(entity, oldData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(AuditRiskDeleteOnExecutionParamDTO param) {
        List<Long> idList = param.getIds();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        if (CollUtil.isNotEmpty(idList)) {
            List<AuditRisk> auditRisks = this.dao.listByIds(idList);
            // 仅草稿状态才能删除
            List<AuditRisk> cantDeleteList = auditRisks.stream().filter(item -> {
                Integer status = item.getStatus();
                return !Objects.equals(AuditRiskStatusConstant.DRAFT, status);
            }).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(cantDeleteList)) {
                List<Long> ids = cantDeleteList.stream().map(AuditRisk::getId).collect(
                    Collectors.toList());
                String names = cantDeleteList.stream().map(AuditRisk::getName).collect(
                    Collectors.joining(","));
                log.error("风险点 {} {} 状态不为草稿无法删除",
                    ids, names);
                throw new BussinessException(CANT_DELETE_BECAUSE_OF_NOT_DRAFT, names);
            }

            this.dao.deleteByIds(idList, operatorId);

            Long projectId = auditRisks.get(0).getProjectId();

            // key: 风险点id,value: 风险点
            Map<Long, AuditRisk> oldValueMap = auditRisks.stream()
                .collect(Collectors.toMap(AuditRisk::getId, v -> v));
            AuditRiskChangeEventUtil.publish(RecordChangeLogBatchAddParam2DTO.<AuditRisk>builder()
                .foreignKeys(idList)
                .newValueMap(null)
                .oldValueMap(oldValueMap)
                .remark("审计实施-删除")
                .operatorId(operatorId)
                .opTime(opTime)
                .build());

            //删除相应的整改联系人
            List<RectifyContact> contacts = this.contactDao.findByRiskId(idList);
            if (CollUtil.isNotEmpty(contacts)) {

                Map<Long, RectifyContact> contactOldValueMap = contacts.stream()
                    .collect(Collectors.toMap(RectifyContact::getId, v -> v));

                this.contactDao.removeByIds(contacts.stream().
                    map(RectifyContact::getId).collect(Collectors.toList()));

                AuditRectifyContactChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam2DTO.<RectifyContact>builder()
                        .foreignKeys(idList)
                        .oldValueMap(contactOldValueMap)
                        .newValueMap(null)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark("删除风险点-删除整改联系人")
                        .build());
            }

            // 删除相应的责任人、责任部门
            List<AuditRiskRespMan> respManList = this.respManService.findByRiskIds(idList);
            if (CollUtil.isNotEmpty(respManList)) {
                Map<Long, AuditRiskRespMan> respManOldValueMap = respManList.stream()
                    .collect(Collectors.toMap(AuditRiskRespMan::getId, v -> v));

                this.respManService.removeByIds(respManList.stream()
                    .map(AuditRiskRespMan::getId).collect(Collectors.toList()));

                AuditRespManChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam2DTO.<AuditRiskRespMan>builder()
                        .foreignKeys(idList)
                        .oldValueMap(respManOldValueMap)
                        .newValueMap(null)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark("删除风险点-删除责任人")
                        .build());
            }

            List<AuditRiskRespDept> respDepts = this.deptService.findByRiskIds(idList);
            if (CollUtil.isNotEmpty(respDepts)) {
                Map<Long, AuditRiskRespDept> respDeptOldValueMap = respDepts.stream()
                    .collect(Collectors.toMap(AuditRiskRespDept::getId, v -> v));
                this.deptService.removeByIds(respDepts.stream().
                    map(AuditRiskRespDept::getId).collect(Collectors.toList()));

                AuditRiskRespDeptChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam2DTO.<AuditRiskRespDept>builder()
                        .foreignKeys(idList)
                        .oldValueMap(respDeptOldValueMap)
                        .newValueMap(null)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark("删除风险点-删除责任部门")
                        .build());
            }

            this.makeStepAsProgressing(projectId, operatorId, opTime, "风险点删除");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAfterApprove(AuditRiskStatusUpdateParamDTO param) {
        List<Long> riskIds = param.getRiskIds();
        Map<Long, Integer> idAndStatusMap = param.getIdAndStatusMap();
        Map<Long, String> idAndApproverIdMap = param.getIdAndApproverIdMap();
        Map<Long, Date> idAndApproveTimeMap = param.getIdAndApproveTimeMap();
        Map<Long, String> idAndApproveRemarkMap = param.getIdAndApproveRemarkMap();

        String operatorId = param.getOperatorId();

        if (CollUtil.isNotEmpty(riskIds)) {

            List<AuditRisk> auditRisks = this.dao.listByIds(riskIds);

            List<AuditRisk> needUpdateRisk = new LinkedList<>();

            List<Long> needUpdateIds = new LinkedList<>();
            Map<Long, Integer> idAndOldStatusMap = new HashMap<>(auditRisks.size());
            Map<Long, Integer> idAndNewStatusMap = new HashMap<>(auditRisks.size());

            Map<Long, AuditRisk> idAndOldDataMap = new HashMap<>(auditRisks.size());
            Map<Long, AuditRisk> idAndNewDataMap = new HashMap<>(auditRisks.size());

            // 需要更新整改状态的风险点数据
            List<Long> needUpdateRectifyStateIds = new LinkedList<>();
            Map<Long, Integer> idAndOldRectifyStateMap = new HashMap<>(auditRisks.size());
            Map<Long, Integer> idAndNewRectifyStateMap = new HashMap<>(auditRisks.size());

            // 需要生成提醒的id
            List<Long> needGenerateNoticeEmailIds = new LinkedList<>();

            auditRisks.forEach(item -> {
                Long id = item.getId();
                Integer oldStatus = item.getStatus();
                Integer respDeptSugType = item.getRespDeptSugType();
                Integer newStatus = idAndStatusMap.get(id);

                boolean canChange = AuditRiskStatusUtil.checkCanChangeStatus(oldStatus, newStatus);
                if (canChange) {
                    needUpdateIds.add(id);
                    idAndOldStatusMap.put(id, oldStatus);
                    idAndNewStatusMap.put(id, newStatus);

                    idAndOldDataMap.put(id, JsonUtil.jsonCopy(item, AuditRisk.class));

                    item.setStatus(newStatus);

                    // 整改状态
                    // 如果审批通过且责任部门意见类型为同意则需要将整改状态置为整改中
                    if (Objects.equals(AuditRiskStatusConstant.APPROVED, newStatus)
                        && Objects.equals(AuditRiskRespDeeptSuggestTypeConstant.AGREE,
                        respDeptSugType)) {

                        Integer oldRectifyState = item.getRectifyState();
                        Integer newRectifyState = AuditRiskRectifyStateConstant.IN_RECTIFICATION;

                        needGenerateNoticeEmailIds.add(id);

                        needUpdateRectifyStateIds.add(id);
                        idAndOldRectifyStateMap.put(id, oldRectifyState);
                        idAndNewRectifyStateMap.put(id, newRectifyState);

                        item.setRectifyState(newRectifyState);
                        log.info("风险点 {} 审批通过且责任部门意见类型为同意 整改状态 {} -> {}", id,
                            oldRectifyState, newRectifyState);
                        log.info(
                            "风险点 {} 审批通过且责任部门意见类型为同意 将会生成整改跟踪提醒/整改状态改变当天提醒",
                            id);
                    } else {
                        item.setRectifyState(null);
                    }

                    // 审批反馈
                    item.setApproveRemark(idAndApproveRemarkMap.get(id));
                    // 审批人
                    item.setApproverId(idAndApproverIdMap.get(id));
                    // 审批时间
                    item.setApproveTime(idAndApproveTimeMap.get(id));

                    idAndNewDataMap.put(id, item);

                    needUpdateRisk.add(item);
                } else {
                    log.warn("风险点[{}]当前状态[{}]无法更新成[{}]", id, oldStatus, newStatus);
                }
            });

            if (CollUtil.isNotEmpty(needUpdateRisk)) {
                this.dao.updateBatchById(needUpdateRisk);

                AuditRiskStatusChangeEventPublishUtil.publishEvent(
                    RecordChangeLogBatchAddParam4DTO.<Integer>builder()
                        .foreignKeys(needUpdateIds)
                        .oldValueMap(idAndOldStatusMap)
                        .newValueMap(idAndNewStatusMap)
                        .operatorIdMap(idAndApproverIdMap)
                        .opTimeMap(idAndApproveTimeMap)
                        .remark("审计实施-风险点审批")
                        .build());

                AuditRiskChangeEventUtil.publish(
                    RecordChangeLogBatchAddParam4DTO.<AuditRisk>builder()
                        .foreignKeys(needUpdateIds)
                        .oldValueMap(idAndOldDataMap)
                        .newValueMap(idAndNewDataMap)
                        .remark("审计实施-风险点审批")
                        .operatorIdMap(idAndApproverIdMap)
                        .opTimeMap(idAndApproveTimeMap)
                        .build());

                needGenerateNoticeEmailIds.forEach(riskId -> {
                    // 生成整改延期提醒
                    this.auditRiskRectifyTraceNoticeService.saveRectifyTraceNotice(riskId,
                        operatorId);
                    // 生成整改状态变动提醒
                    this.auditRiskRectifyTraceNoticeService.saveRectifyChangeNotice(riskId,
                        operatorId);
                });

                if (CollUtil.isNotEmpty(needUpdateRectifyStateIds)) {
                    // 风险点整改状态变动
                    AuditRiskRectifyStateChangeEventPublishUtil.publishEvent(
                        RecordChangeLogBatchAddParam4DTO.<Integer>builder()
                            .foreignKeys(needUpdateRectifyStateIds)
                            .oldValueMap(idAndOldRectifyStateMap)
                            .newValueMap(idAndNewRectifyStateMap)
                            .operatorIdMap(idAndApproverIdMap)
                            .opTimeMap(idAndApproveTimeMap)
                            .remark("风险点审批通过")
                            .build());
                }

            }
        } else {
            log.warn("操作人: {} 更新风险点状态时传入数据为空", operatorId);
        }
    }

    @Override
    public IPage<AuditRiskSearchResultDTO> search(AuditRiskSearchParamDTO searchDTO) {
        AuditRiskSearchParamPO param = AuditRiskConvertUtil.makeSearchParamDTO2PO(
            searchDTO);
        IPage<AuditRiskSearchResultPO> result = this.dao.searchInExecution(param);
        result.getRecords().stream()
            .filter(Objects::nonNull)
            .forEach(record -> {
                // 设置责任人 ID
                List<AuditRiskRespMan> respManList = respManService.findByRiskId(record.getId());
                if (CollectionUtils.isNotEmpty(respManList)) {
                    List<String> respManIdList = respManList.stream()
                        .map(AuditRiskRespMan::getRespManId)
                        .collect(Collectors.toList());
                    record.setRespManId(respManIdList);
                }

                // 设置责任部门 ID
                List<AuditRiskRespDept> respDeptList = deptService.findByRiskId(record.getId());
                if (CollectionUtils.isNotEmpty(respDeptList)) {
                    List<String> respDeptIdList = respDeptList.stream()
                        .map(AuditRiskRespDept::getRespDeptId)
                        .collect(Collectors.toList());
                    record.setRespDeptId(respDeptIdList);
                }

                List<RectifyContact> contactList = this.contactService.findByRiskId(record.getId());
                if (CollectionUtils.isNotEmpty(contactList)) {
                    List<String> contactOrgidList = contactList.stream().
                        map(RectifyContact::getOrgId)
                        .collect(Collectors.toList());
                    record.setRectifyContactIds(contactOrgidList);
                }
            });
        return result.convert(AuditRiskConvertUtil::makeSearchResultPO2DTO);
    }

    private void makeStepAsProgressing(Long projectId, String operatorId, Date opTime,
        String remark) {
        // 审计实施
        StageStepExampleDTO stepExample = this.stageStepExampleService.getByProjectIdAndStepValue(
            projectId, StageStepConstant.AUDIT_EXECUTION);
        if (Objects.isNull(stepExample)) {
            log.warn("项目[{}] 风险点 {} 后,审计实施阶段步骤实例不存在, 不需要变为进行中",
                projectId,
                remark);
            return;
        }
        Long id = stepExample.getId();
        Integer status = stepExample.getStatus();

        if (StageStepStatusUtil.checkCanChangeAsProcessiongStatus(status)) {
            this.stepExampleStatusService.updateStatus(
                StageStepExampleStatusChangeParamDTO.builder()
                    .ids(Collections.singletonList(id))
                    .newStatus(StageStepStatusConstant.PROCESSING)
                    .opOrgId(operatorId)
                    .opTime(opTime)
                    .opRemark(remark)
                    .publishEventFlag(true)
                    .build());
            log.info("项目[{}]风险点{}后,审计实施阶段步骤实例 {} 状态为 {} 变为进行中", projectId,
                remark, id, status);
        } else {
            log.info("项目[{}]风险点{}后,审计实施阶段步骤实例 {} 状态为 {} 无需变为进行中",
                projectId,
                remark, id, status);
        }

    }
}
