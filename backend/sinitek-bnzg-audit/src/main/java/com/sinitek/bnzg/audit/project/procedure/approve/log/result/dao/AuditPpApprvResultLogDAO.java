package com.sinitek.bnzg.audit.project.procedure.approve.log.result.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.entity.AuditPpApprvResultLog;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.mapper.AuditPpApprvResultLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class AuditPpApprvResultLogDAO extends
    ServiceImpl<AuditPpApprvResultLogMapper, AuditPpApprvResultLog> {

    public List<AuditPpApprvResultLog> findByApproveResultId(Long approveResultId) {
        LambdaQueryWrapper<AuditPpApprvResultLog> queryWrapper = Wrappers.lambdaQuery(
            AuditPpApprvResultLog.class);
        queryWrapper.eq(AuditPpApprvResultLog::getApprvResultId, approveResultId);
        return this.list(queryWrapper);
    }

}
