package com.sinitek.bnzg.audit.project.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 12/05/2024 14:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "审计项目列查询DTO")
public class AuditProjectSearchParamPO extends PageDataParam {

    @ApiModelProperty("计划名称")
    private List<String> planNames;

    @ApiModelProperty("项目名称")
    private List<String> projectNames;

    @ApiModelProperty("项目进度")
    private List<Integer> projectPhases;

    @ApiModelProperty("审计年度")
    private List<Integer> auditYears;

    @ApiModelProperty("使用审计年度排序标志")
    private Boolean useAuditYearSortFlag;
}
