package com.sinitek.bnzg.audit.stage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 阶段实例 Entity
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_stage_example")
@ApiModel(description = "阶段实例实体")
public class StageExample extends BaseEntity {

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 阶段实例状态
     */
    @ApiModelProperty("阶段实例状态")
    private Integer status;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;

    /**
     * 终止时间
     */
    @ApiModelProperty("终止时间")
    private Date terminatedTime;

    /**
     * 结束处理人
     */
    @ApiModelProperty("结束处理人")
    private String enderId;

    /**
     * 终止处理人
     */
    @ApiModelProperty("终止处理人")
    private String terminatorId;

}
