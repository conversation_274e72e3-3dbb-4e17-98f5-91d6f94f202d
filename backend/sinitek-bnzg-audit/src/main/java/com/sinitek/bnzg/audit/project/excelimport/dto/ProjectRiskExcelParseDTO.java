package com.sinitek.bnzg.audit.project.excelimport.dto;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:47
 */

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 审计项目风险点excel解析dto
 *
 * <AUTHOR>
 * @since 2024-07-29 13:47:40
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("审计项目风险点excel解析dto")
public class ProjectRiskExcelParseDTO {

    @ExcelProperty(value = "serialno")
    @ApiModelProperty("serialno")
    private String serialno;

    @ExcelProperty(value = "项目ID")
    @ApiModelProperty("项目ID")
    private String projectId;

    @ExcelProperty(value = "项目编码")
    @ApiModelProperty("项目编码")
    private String projectCode;

    @ExcelIgnore
    @ApiModelProperty("审计年份")
    private Integer auditYear;

    @ExcelIgnore
    @ApiModelProperty("审计计划名称")
    private String auditPlanName;

    @ExcelProperty(value = "项目名称")
    @ApiModelProperty("项目名称")
    private String projectName;

    @ExcelProperty(value = "审计发现标题")
    @ApiModelProperty("审计发现标题(名称)")
    private String auditFindingTitle;

    @ExcelProperty(value = "审计发现类型")
    @ApiModelProperty("审计发现类型")
    private String auditFindingType;

    @ExcelIgnore
    @ApiModelProperty("审计发现类型(类型值)")
    private Integer auditFindingTypeValue;

    @ExcelProperty(value = "一级分类")
    @ApiModelProperty("一级分类")
    private String firstLevelClassification;

    @ExcelIgnore
    @ApiModelProperty("一级分类值")
    private Integer firstLevelClassificationValue;

    @ExcelProperty(value = "二级分类")
    @ApiModelProperty("二级分类")
    private String secondLevelClassification;

    @ExcelProperty(value = "问题描述")
    @ApiModelProperty("问题描述(描述)")
    private String problemDescription;

    @ExcelProperty(value = "风险级别")
    @ApiModelProperty("风险级别(风险级别)")
    private String riskLevel;

    @ExcelIgnore
    @ApiModelProperty("风险级别(风险级别值)")
    private Integer riskLevelValue;

    @ExcelProperty(value = "管理建议")
    @ApiModelProperty("管理建议(审计建议)")
    private String managementSuggestion;

    @ExcelProperty(value = "管理答复")
    @ApiModelProperty("管理答复(责任部门意见反馈)")
    private String managementResponse;

    @ExcelProperty(value = "整改实施计划日期")
    @ApiModelProperty("整改实施计划日期(要求整改日期)")
    private String rectificationPlanDate;

    @ExcelProperty(value = "整改实施实际日期")
    @ApiModelProperty("整改实施实际日期(实际整改日期)")
    private String rectificationActualDate;

    @ExcelIgnore
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计期间开始日")
    private Date auditPeriodStartDate;

    @ExcelIgnore
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty("审计期间截至日")
    private Date auditPeriodEndDate;
}

