package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("批量保存项目程序权限参数")
public class AuditAllAuthOrgBatchSaveParamDTO implements IAuditProjectAndOpeatorId {

    @NotNull(message = "项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotEmpty(message = "审计程序不能为空")
    @ApiModelProperty("程序id")
    private List<Long> procedureIds;

    @NotNull(message = "角色不能为空")
    @ApiModelProperty("角色")
    private Integer role;

    @NotEmpty(message = "项目成员不能为空")
    @ApiModelProperty("项目成员")
    private List<String> members;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;
}
