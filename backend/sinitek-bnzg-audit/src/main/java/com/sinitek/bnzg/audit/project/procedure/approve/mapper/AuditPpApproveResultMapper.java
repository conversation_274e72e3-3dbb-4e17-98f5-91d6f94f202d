package com.sinitek.bnzg.audit.project.procedure.approve.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchResultPO;
import org.apache.ibatis.annotations.Param;

/**
 * 风险点审批结果 Mapper
 *
 * <AUTHOR>
 * date 2024-08-30
 */
public interface AuditPpApproveResultMapper extends BaseMapper<AuditPpApproveResult> {

    IPage<PpApproveResultSearchResultPO> search(
        Page<PpApproveResultSearchParamPO> page,
        @Param("param") PpApproveResultSearchParamPO param);

}
