package com.sinitek.bnzg.audit.project.procedure.util;

import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-11-13 21:48
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditPpStatusUtil {

    /**
     * 检查状态能否转换
     *
     * @param oldStatus 原阶段步骤状态
     * @param newStatus 现阶段步骤状态
     * @return boolean true: 继续执行，false: 不再继续执行
     */
    public static boolean checkCanChangeStatus(Integer oldStatus, Integer newStatus) {
        if (Objects.isNull(newStatus)) {
            return false;
        }
        switch (newStatus) {
            case AuditRiskStatusConstant.APPROVED:
                return checkCanChangeAsApprovedStatus(oldStatus);
            case AuditRiskStatusConstant.NOT_APPROVED:
                return checkCanChangeAsNotApprovedStatus(oldStatus);
            default:
                return false;
        }
    }

    /**
     * 检查能否变成 审批通过 状态
     */
    private static boolean checkCanChangeAsApprovedStatus(Integer oldStatus) {
        // 只有审批中才能变成审批通过状态
        return Objects.equals(AuditRiskStatusConstant.APPROVING, oldStatus);
    }

    /**
     * 检查能否变成 审批不通过 状态
     */
    private static boolean checkCanChangeAsNotApprovedStatus(Integer oldStatus) {
        // 只有审批中才能变成 审批不通过 状态
        return Objects.equals(AuditRiskStatusConstant.APPROVING, oldStatus);
    }
}
