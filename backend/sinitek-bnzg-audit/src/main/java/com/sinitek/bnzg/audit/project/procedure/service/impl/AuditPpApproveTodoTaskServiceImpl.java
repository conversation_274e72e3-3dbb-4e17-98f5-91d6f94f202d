package com.sinitek.bnzg.audit.project.procedure.service.impl;

import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.CANT_CREATE_TODO_TASK_NO_PLAN;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.CANT_CREATE_TODO_TASK_NO_PP;
import static com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant.CANT_CREATE_TODO_TASK_NO_PROJECT;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproverService;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditPpApproveConstant;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskFinishParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskProcessingParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskTerminateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditPpApproveTodoTaskService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.cloud.workflow.enumerate.WorkflowStepOwnerStatus;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.common.utils.ObjectUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.workflow.entity.WfExampletask;
import com.sinitek.sirm.workflow.service.IWfExampletaskService;
import com.sinitek.sirm.workflow.service.claim.ExampleTaskQuery;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 09/09/2024 16:22
 */
@Slf4j
@Service
public class AuditPpApproveTodoTaskServiceImpl implements IAuditPpApproveTodoTaskService {

    @Autowired
    private IWfExampletaskService wfExampletaskService;

    @Autowired
    private IPpApproveResultService ppApproveResultService;

    @Autowired
    private IPpApproverService ppApproverService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IAuditPlanService planService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IOrgService orgService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(PpApproveTodoTaskCreateParamDTO param) {
        Long ppApproveId = param.getPpApproveId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        ExampleTaskQuery query = new ExampleTaskQuery();
        query.setSourceId(ppApproveId);
        query.setSourceEntity(AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        List<WfExampletask> wfExampletasks = wfExampletaskService.findExampleTask(
            query);

        if (CollUtil.isEmpty(wfExampletasks)) {
            List<PpApproveResultBaseInfoDTO> ppApproveResultList = this.ppApproveResultService.findExistByApproveId(
                ppApproveId);
            if (CollUtil.isEmpty(ppApproveResultList)) {
                log.error("根据审批id {} 找不到对应的审计程序审批任务", ppApproveId);
                throw new BussinessException(CANT_CREATE_TODO_TASK_NO_PP);
            }
            boolean isSingle = ppApproveResultList.size() == 1;
            PpApproveResultBaseInfoDTO ppApproveResult = ppApproveResultList.get(0);
            Long ppId = ppApproveResult.getId();
            Long projectId = ppApproveResult.getProjectId();
            Long procedureId = ppApproveResult.getProcedureId();

            AuditProcedureBaseInfoDTO procedure = this.procedureService.getBaseInfoById(
                procedureId);
            String procedureName = procedure.getName();

            AuditProjectInfoDTO projectInfo = this.projectService.getExistsProjectInfoById(
                projectId);
            if (Objects.isNull(projectInfo)) {
                log.error("项目审计程序[{}]对应审计项目不存在", ppId);
                throw new BussinessException(CANT_CREATE_TODO_TASK_NO_PROJECT, procedureName);
            }
            String projectName = projectInfo.getName();

            Long planId = projectInfo.getPlanId();
            AuditPlanInfoDTO planInfo = this.planService.getInfoById(planId);
            if (Objects.isNull(planInfo)) {
                log.error("项目审计程序[{}]对应审计项目[{}]对应的计划不存在", ppId, projectId);
                throw new BussinessException(CANT_CREATE_TODO_TASK_NO_PLAN, procedureName);
            }

            String planName = planInfo.getName();

            List<String> approvers = this.ppApproverService.findApproversByApproveId(
                ppApproveId);

            if (CollUtil.isNotEmpty(approvers)) {
                Map<String, String> orgIdAndOrgNameMap = this.orgService.getOrgNameMapByOrgIdList(
                    Collections.singletonList(operatorId));
                String operatorName = MapUtils.getString(orgIdAndOrgNameMap, operatorId);
                String opTimeStr = DateUtil.format(opTime, GlobalConstant.TIME_FORMAT_THIRTEEN);

                approvers.forEach(approver -> {
                    // 生成待办任务
                    WfExampletask exampleTask = new WfExampletask();
                    exampleTask.setSourceEntity(
                        AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);// 实体对象名称
                    exampleTask.setSourceId(ppApproveId);

                    // 处理人id
                    exampleTask.setDealerId(approver);
                    // 任务发布人id
                    exampleTask.setOrginerId(operatorId);
                    // 待处理
                    exampleTask.setStatus(WorkflowStepOwnerStatus.WF_OWNER_PEND.getEnumItemValue());
                    exampleTask.setStartTime(opTime);
                    exampleTask.setRemarks("审计程序审批任务");
                    exampleTask.setProcessName("审计程序审批");

                    String processStepName;
                    if (isSingle) {
                        processStepName = String.format("【%s】审批", procedureName);
                    } else {
                        processStepName = "审计程序批量审批";
                    }

                    exampleTask.setProcessStepName(processStepName);
                    String format = String.format(
                        "请处理【%s】于【%s】提交的%s/%s/%s审批任务",
                        operatorName,
                        opTimeStr,
                        planName,
                        projectName,
                        isSingle ? procedureName : "审计程序");
                    exampleTask.setDescription(format);
                    // 优先级
                    exampleTask.setSort(0);
                    this.wfExampletaskService.saveWfExampleTask(exampleTask);
                });
            } else {
                log.warn("项目审计程序审批[{}]创建,不存在审批人,无法创建待办任务", ppApproveId);
            }
        } else {
            List<Long> wfTaskIds = wfExampletasks.stream().map(WfExampletask::getObjId)
                .collect(Collectors.toList());
            log.info("项目审计程序审批[{}]创建,存在待办任务 {} ,不再重复创建待办任务", ppApproveId,
                wfTaskIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processing(PpApproveTodoTaskProcessingParamDTO param) {
        Long ppApproveId = param.getPpApproveId();

        ExampleTaskQuery query = new ExampleTaskQuery();
        query.setSourceId(ppApproveId);
        query.setSourceEntity(AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        List<WfExampletask> wfExampletasks = wfExampletaskService.findExampleTask(
            query);

        if (CollUtil.isNotEmpty(wfExampletasks)) {
            int size = wfExampletasks.size();
            log.info(
                "根据sourceid:[{}],sourceEntity:[{}]找到多条[{}]待办任务",
                ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY, size);
            wfExampletasks.forEach(dto -> {
                Long objId = dto.getObjId();
                Integer oldStatus = dto.getStatus();
                if (Objects.equals(oldStatus,
                    WorkflowStepOwnerStatus.WF_OWNER_BEING.getEnumItemValue())) {
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为正在处理,无需更新",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId);
                } else {
                    WfExampletask exampleTask = new WfExampletask();
                    ObjectUtils.copyObject(dto, exampleTask);
                    // 正在处理
                    exampleTask.setStatus(
                        WorkflowStepOwnerStatus.WF_OWNER_BEING.getEnumItemValue());
                    wfExampletaskService.saveWfExampleTask(exampleTask);
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为[{}],当前项目审计程序审批结果已保存,将待办任务更新为正在处理",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId, oldStatus);
                }
            });
        } else {
            log.warn(
                "项目审计程序审批保存时,根据sourceid:[{}],sourceEntity:[{}]找不到对应的待办任务,无法将待办任务置为处理中",
                ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finish(PpApproveTodoTaskFinishParamDTO param) {
        Long ppApproveId = param.getPpApproveId();
        Date opTime = param.getOpTime();

        ExampleTaskQuery query = new ExampleTaskQuery();
        query.setSourceId(ppApproveId);
        query.setSourceEntity(AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        List<WfExampletask> wfExampletasks = wfExampletaskService.findExampleTask(
            query);

        if (CollUtil.isNotEmpty(wfExampletasks)) {
            int size = wfExampletasks.size();
            log.info(
                "根据sourceid:[{}],sourceEntity:[{}]找到多条[{}]待办任务",
                ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY, size);
            wfExampletasks.forEach(dto -> {
                Long objId = dto.getObjId();
                Integer oldStatus = dto.getStatus();
                if (Objects.equals(oldStatus,
                    WorkflowStepOwnerStatus.WF_OWNER_PROCESS.getEnumItemValue())) {
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为已完成,无需更新为完成",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId);
                } else if (Objects.equals(oldStatus,
                    WorkflowStepOwnerStatus.WF_OWNER_TERMINAT.getEnumItemValue())) {
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为已终止,无需更新为完成",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId);
                } else {
                    WfExampletask exampleTask = new WfExampletask();
                    ObjectUtils.copyObject(dto, exampleTask);
                    // 结束日期
                    exampleTask.setEndTime(opTime);
                    // 处理完成
                    exampleTask.setStatus(
                        WorkflowStepOwnerStatus.WF_OWNER_PROCESS.getEnumItemValue());
                    wfExampletaskService.saveWfExampleTask(exampleTask);
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为[{}],当前项目审计程序审批任务已提交,将待办任务更新为已完成",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId, oldStatus);
                }
            });
        } else {
            log.warn(
                "项目审计程序审批待办任务完成时,根据sourceid:[{}],sourceEntity:[{}]找不到对应的待办任务",
                ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminate(PpApproveTodoTaskTerminateParamDTO param) {
        Long ppApproveId = param.getPpApproveId();
        String opRemark = param.getOpRemark();

        ExampleTaskQuery query = new ExampleTaskQuery();
        query.setSourceId(ppApproveId);
        query.setSourceEntity(AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        List<WfExampletask> wfExampletasks = wfExampletaskService.findExampleTask(
            query);

        if (CollUtil.isNotEmpty(wfExampletasks)) {
            int size = wfExampletasks.size();
            log.info(
                "根据sourceid:[{}],sourceEntity:[{}]找到多条[{}]待办任务",
                ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY, size);
            wfExampletasks.forEach(dto -> {
                Long objId = dto.getObjId();
                Integer oldStatus = dto.getStatus();
                if (Objects.equals(oldStatus,
                    WorkflowStepOwnerStatus.WF_OWNER_PROCESS.getEnumItemValue())) {
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为已完成,无需更新为终止",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId);
                } else if (Objects.equals(oldStatus,
                    WorkflowStepOwnerStatus.WF_OWNER_TERMINAT.getEnumItemValue())) {
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为已终止,无需更新为终止",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId);
                } else {
                    this.wfExampletaskService.terminateTaskByTaskId(objId, opRemark);
                    log.info(
                        "根据sourceid:[{}],sourceEntity:[{}]找到待办任务[objId:{}],其状态为[{}],当前项目已终止,将待办任务更新为已终止",
                        ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY,
                        objId, oldStatus);
                }
            });
        } else {
            log.warn(
                "项目审计程序审批待办任务完成时,根据sourceid:[{}],sourceEntity:[{}]找不到对应的待办任务",
                ppApproveId, AuditPpApproveConstant.TODO_TASK_DEFAULT_SOURRCE_ENTITY);
        }
    }
}
