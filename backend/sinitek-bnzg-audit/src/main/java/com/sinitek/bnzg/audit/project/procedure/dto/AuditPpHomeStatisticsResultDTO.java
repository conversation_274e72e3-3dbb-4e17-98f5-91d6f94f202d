package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@ApiModel(value = "项目审计程序首页统计-返回DTO")
public class AuditPpHomeStatisticsResultDTO {

    @ApiModelProperty(value = "待审批审计程序")
    private Integer waitApprovePpCount;

    @ApiModelProperty(value = "待处理审计程序")
    private Integer waitHandlePpCount;

    @ApiModelProperty(value = "含有未审批通过数据的数量")
    private Integer notPassPpCount;
}
