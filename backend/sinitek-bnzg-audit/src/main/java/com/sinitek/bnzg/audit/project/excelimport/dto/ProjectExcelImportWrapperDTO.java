package com.sinitek.bnzg.audit.project.excelimport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024-12-26 17:17
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ApiModel("审计项目excel解析包装dto")
public class ProjectExcelImportWrapperDTO {

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("风险点")
    private List<ProjectRiskExcelParseDTO> risks;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;
}
