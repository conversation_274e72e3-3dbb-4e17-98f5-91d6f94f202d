package com.sinitek.bnzg.audit.stage.enumation;

import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:15
 */
@Slf4j
@Getter
public enum StageStepStatusEnum {
    UN_START(StageStepStatusConstant.UN_START_NAME, StageStepStatusConstant.UN_START),
    READY(StageStepStatusConstant.READY_NAME, StageStepStatusConstant.READY),
    PROCESSING(StageStepStatusConstant.PROCESSING_NAME, StageStepStatusConstant.PROCESSING),
    FINISHED(StageStepStatusConstant.FINISHED_NAME, StageStepStatusConstant.FINISHED),
    TERMINATE(StageStepStatusConstant.TERMINATE_NAME, StageStepStatusConstant.TERMINATE);

    /**
     * 名称
     */
    private final String name;

    /**
     * 值
     */
    private final Integer value;

    StageStepStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static Map<Integer, String> getValueAndNameMap() {
        return Arrays.stream(values()).collect(
            Collectors.toMap(StageStepStatusEnum::getValue, StageStepStatusEnum::getName));
    }
}
