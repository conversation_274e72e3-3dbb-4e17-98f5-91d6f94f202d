package com.sinitek.bnzg.audit.risk.contact.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 08/29/2024 17:16
 */
@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("整改联系人加载结果")
public class RectifyContactLoadResultDTO {

    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("整改联系人")
    private List<String> contacts;

    @ApiModelProperty(value = "责任人")
    private List<String> respManId;

    @ApiModelProperty(value = "责任部门")
    private List<String> respDeptId;

}
