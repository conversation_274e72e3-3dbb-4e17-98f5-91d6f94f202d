package com.sinitek.bnzg.audit.stage.log.status.step.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.stage.log.status.step.entity.StageStepExampleStatusLog;
import com.sinitek.bnzg.audit.stage.log.status.step.mapper.StageStepExampleStatusLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class StageStepExampleStatusLogDAO extends
    ServiceImpl<StageStepExampleStatusLogMapper, StageStepExampleStatusLog> {

    public List<StageStepExampleStatusLog> findByStageStepExampleId(Long stageStepExampleId) {
        LambdaQueryWrapper<StageStepExampleStatusLog> queryWrapper = Wrappers.lambdaQuery(
            StageStepExampleStatusLog.class);
        queryWrapper.eq(StageStepExampleStatusLog::getStageStepExampleId, stageStepExampleId);
        return this.list(queryWrapper);
    }

}
