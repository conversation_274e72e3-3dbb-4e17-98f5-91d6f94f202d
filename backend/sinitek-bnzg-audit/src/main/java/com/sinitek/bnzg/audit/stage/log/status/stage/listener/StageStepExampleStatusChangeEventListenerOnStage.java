package com.sinitek.bnzg.audit.stage.log.status.stage.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.stage.constant.StageStatusConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.log.status.step.event.StageStepExampleStatusChangeEvent;
import com.sinitek.bnzg.audit.stage.service.IStageExampleStatusService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class StageStepExampleStatusChangeEventListenerOnStage {

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IStageExampleStatusService stageExampleStatusService;

    /**
     * 监听阶段步骤状态变化事件,如果存在变为进行中或处理中的数据,需要改变对应的阶段实例状态
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = StageStepExampleStatusChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        StageStepExampleStatusChangeEvent<T> event) {
        T source = event.getSource();
        String opOrgId = null;
        Date opTime = null;

        // 变为处理中的阶段步骤实例id
        List<Long> changeAsProcessingStageStepExampleIds = new LinkedList<>();

        // 变为终止的阶段步骤实例id
        List<Long> changeAsTerminateStageStepExampleIds = new LinkedList<>();

        // 变为完成的阶段步骤实例id
        List<Long> changeAsFinishedStageStepExampleIds = new LinkedList<>();

        if (source instanceof RecordChangeLogAddParamDTO) {
            RecordChangeLogAddParamDTO<Integer> param = (RecordChangeLogAddParamDTO) source;
            Long foreignKey = param.getForeignKey();
            Integer newValue = param.getNewValue();

            if (Objects.equals(StageStepStatusConstant.PROCESSING, newValue)) {
                // 处理中
                changeAsProcessingStageStepExampleIds.add(foreignKey);
            } else if (Objects.equals(StageStepStatusConstant.TERMINATE, newValue)) {
                // 终止
                changeAsTerminateStageStepExampleIds.add(foreignKey);
            } else if (Objects.equals(StageStepStatusConstant.FINISHED, newValue)) {
                // 已完成
                changeAsFinishedStageStepExampleIds.add(foreignKey);
            }

            opOrgId = param.getOperatorId();
            opTime = param.getOpTime();

        } else {
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO) source;
            Collection<Long> foreignKeys = batchParam.getForeignKeys();
            if (CollUtil.isNotEmpty(foreignKeys)) {
                for (Long foreignKey : foreignKeys) {
                    Integer newValue = batchParam.getNewValue(foreignKey);

                    if (Objects.equals(StageStepStatusConstant.PROCESSING, newValue)) {
                        // 处理中
                        changeAsProcessingStageStepExampleIds.add(foreignKey);
                    } else if (Objects.equals(StageStepStatusConstant.TERMINATE, newValue)) {
                        // 终止
                        changeAsTerminateStageStepExampleIds.add(foreignKey);
                    } else if (Objects.equals(StageStepStatusConstant.FINISHED, newValue)) {
                        // 已完成
                        changeAsFinishedStageStepExampleIds.add(foreignKey);
                    }

                    if (StringUtils.isBlank(opOrgId)) {
                        opOrgId = batchParam.getOperatorId(foreignKey);
                    }

                    if (Objects.isNull(opTime)) {
                        opTime = batchParam.getOpTime(foreignKey);
                    }
                }
            }
        }

        List<Long> ids = new LinkedList<>();
        ids.addAll(changeAsProcessingStageStepExampleIds);
        ids.addAll(changeAsTerminateStageStepExampleIds);
        ids.addAll(changeAsFinishedStageStepExampleIds);

        if (CollUtil.isNotEmpty(ids)) {
            List<StageStepExampleDTO> stageStepExamples = this.stageStepExampleService.findByIds(
                ids);

            // key: 阶段步骤实例id
            // value: 阶段步骤实例
            Map<Long, StageStepExampleDTO> idAndStageStepExample = stageStepExamples.stream()
                .collect(Collectors.toMap(StageStepExampleDTO::getId, v -> v));

            if (CollUtil.isNotEmpty(changeAsProcessingStageStepExampleIds)) {
                List<Long> stageExampleIds = this.findStageExampleIds(
                    changeAsProcessingStageStepExampleIds, idAndStageStepExample);
                // 阶段步骤实例变为 处理中,其对应的阶段实例应该变成 处理中
                this.stageExampleStatusService.changeStageExampleStatus(
                    StageExampleStatusChangeParamDTO.builder()
                        .ids(stageExampleIds)
                        .newStatus(StageStatusConstant.PROCESSING)
                        .opOrgId(opOrgId)
                        .opTime(opTime)
                        .opRemark("阶段步骤实例变成处理中,阶段实例自动更新")
                        .publishEventFlag(true)
                        .build());
            }

            if (CollUtil.isNotEmpty(changeAsTerminateStageStepExampleIds)) {
                List<Long> stageExampleIds = this.findStageExampleIds(
                    changeAsTerminateStageStepExampleIds, idAndStageStepExample);
                // 阶段步骤实例变为 终止,其对应的阶段实例应该变成 终止
                this.stageExampleStatusService.changeStageExampleStatus(
                    StageExampleStatusChangeParamDTO.builder()
                        .ids(stageExampleIds)
                        .newStatus(StageStatusConstant.TERMINATE)
                        .opOrgId(opOrgId)
                        .opTime(opTime)
                        .opRemark("阶段步骤实例变成中止,阶段实例自动更新")
                        .publishEventFlag(true)
                        .build());
            }

            if (CollUtil.isNotEmpty(changeAsFinishedStageStepExampleIds)) {
                List<Long> stageExampleIds = this.findStageExampleIds(
                    changeAsFinishedStageStepExampleIds, idAndStageStepExample);
                // 阶段步骤实例变为 完成,检查阶段实例下所有步骤是否全部完成
                // 如果全部完成,阶段实例状态要变为已完成状态

                List<StageStepExampleDTO> allStageStepExamples = this.stageStepExampleService.findByStageExampleIds(
                    stageExampleIds);
                if (CollUtil.isNotEmpty(allStageStepExamples)) {

                    Map<Long, List<StageStepExampleDTO>> stageExampleIdAndStepExampleListMap = allStageStepExamples.stream()
                        .collect(Collectors.groupingBy(StageStepExampleDTO::getStageExampleId));

                    List<Long> needUpdateStageExampleIds = new LinkedList<>();
                    stageExampleIdAndStepExampleListMap.forEach((stageExampleId, stepExamples) -> {
                        boolean isAllFinished = stepExamples.stream().allMatch(
                            item -> Objects.equals(StageStepStatusConstant.FINISHED,
                                item.getStatus()));
                        if (isAllFinished) {
                            log.info("阶段实例[{}]下所有步骤已完成,自动完成", stageExampleId);
                            needUpdateStageExampleIds.add(stageExampleId);
                        }
                    });

                    if (CollUtil.isNotEmpty(needUpdateStageExampleIds)) {
                        this.stageExampleStatusService.changeStageExampleStatus(
                            StageExampleStatusChangeParamDTO.builder()
                                .ids(needUpdateStageExampleIds)
                                .newStatus(StageStatusConstant.FINISHED)
                                .opOrgId(opOrgId)
                                .opTime(opTime)
                                .opRemark("其下所有阶段步骤已完成,阶段实例自动更新")
                                .publishEventFlag(true)
                                .build());
                    }

                } else {
                    log.warn(
                        "阶段步骤实例状态完成后,stageExampleIds {} 检查是否需要变为完成状态,但是获取到的阶段下步骤实例数据为空,无法继续",
                        stageExampleIds);
                }
            }
        }
    }

    private List<Long> findStageExampleIds(List<Long> stageStepExampleIds,
        Map<Long, StageStepExampleDTO> idAndStageStepExample) {
        List<Long> stageExampleIds = new LinkedList<>();
        stageStepExampleIds.forEach(id -> {
            StageStepExampleDTO stageStepExample = idAndStageStepExample.get(id);
            if (Objects.nonNull(stageStepExample)) {
                Long stageExampleId = stageStepExample.getStageExampleId();
                stageExampleIds.add(stageExampleId);
            } else {
                log.warn("根据阶段步骤实例id[{}]找不到对应的阶段步骤实例数据", id);
            }
        });
        return stageExampleIds;
    }
}
