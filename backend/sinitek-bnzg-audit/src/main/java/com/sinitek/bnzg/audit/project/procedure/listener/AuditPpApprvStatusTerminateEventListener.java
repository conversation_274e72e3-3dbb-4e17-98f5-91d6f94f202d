package com.sinitek.bnzg.audit.project.procedure.listener;

import com.sinitek.bnzg.audit.project.procedure.approve.log.status.event.AuditPpApprvStatusChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.dto.PpApproveTodoTaskTerminateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditPpApproveTodoTaskService;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.event.annotation.SiniCubeEventListener;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpApprvStatusTerminateEventListener {

    @Autowired
    private IAuditPpApproveTodoTaskService ppApproveTodoTaskService;

    /**
     * 项目审计程序 审批终止后:
     * 1.终止代办任务
     */
    @SiniCubeEventListener
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        AuditPpApprvStatusChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            log.info("监听到 单条 项目审计程序审批任务 事件,数据: {}",
                JsonUtil.toJsonString(event));
            RecordChangeLogAddParamDTO<Integer> singleData = (RecordChangeLogAddParamDTO<Integer>) source;
            this.dealSingle(singleData);
        } else {
            log.info("监听到 项目审计程序审批任务 批量事件,数据: {}",
                JsonUtil.toJsonString(event));
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source;
            Collection<Long> foreignKeys = batchParam.getForeignKeys();
            for (Long foreignKey : foreignKeys) {
                this.dealSingle(RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(foreignKey)
                    .newValue(batchParam.getNewValue(foreignKey))
                    .oldValue(batchParam.getOldValue(foreignKey))
                    .remark(batchParam.getRemark(foreignKey))
                    .operatorId(batchParam.getOperatorId(foreignKey))
                    .opTime(batchParam.getOpTime(foreignKey))
                    .build());
            }
        }
    }

    private void dealSingle(RecordChangeLogAddParamDTO<Integer> source) {
        Integer newStatus = source.getNewValue();
        if (Objects.equals(newStatus, AuditRiskApproveStatusConstant.TERMINATE)) {
            Long foreignKey = source.getForeignKey();
            String operatorId = source.getOperatorId();
            Date opTime = source.getOpTime();
            String remark = source.getRemark();

            log.info("监听到 项目审计程序审批终止[{}]事件,开始终止代办任务", foreignKey);

            this.ppApproveTodoTaskService.terminate(PpApproveTodoTaskTerminateParamDTO.builder()
                .ppApproveId(foreignKey)
                .operatorId(operatorId)
                .opTime(opTime)
                .opRemark(remark)
                .build());
        }
    }
}
