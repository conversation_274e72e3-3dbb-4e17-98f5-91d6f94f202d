package com.sinitek.bnzg.audit.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectRoleConstant;
import com.sinitek.bnzg.audit.project.dto.AuditProjectAuthParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectAuthService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.stage.constant.StageStatusConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepAuthAndConfigDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepPermissionDTO;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-10-11 14:34
 */
@Slf4j
@Service
public class AuditProjectAuthServiceImpl implements IAuditProjectAuthService {

    @Autowired
    private IAuditProjectMemberService auditProjectMemberService;

    @Autowired
    private IOrgService orgService;

    @Override
    public StageStepAuthAndConfigDTO getAuth(AuditProjectAuthParamDTO param) {
        Long projectId = param.getProjectId();
        String orgId = param.getOrgId();
        Boolean debugFlag = param.getDebugFlag();
        boolean isDebug = Objects.equals(Boolean.TRUE, debugFlag);

        StageStepPermissionDTO permission = new StageStepPermissionDTO();
        permission.setOrgId(orgId);

        List<AuditProjectMemberInfoDTO> memberRoles = this.auditProjectMemberService.findMemberRoles(
            Collections.singleton(projectId), Collections.singleton(orgId));

        if (CollUtil.isNotEmpty(memberRoles)) {
            StageExampleDTO stageExample = param.getStageExample();
            StageStepExampleDTO stageStepExample = param.getStageStepExample();

            Long stageExampleId = stageExample.getId();
            Integer stageStatus = stageExample.getStatus();
            Long stageStepExampleId = stageStepExample.getId();
            Integer stepStatus = stageStepExample.getStatus();

            boolean canStageEdit = Objects.equals(StageStatusConstant.READY, stageStatus)
                || Objects.equals(StageStatusConstant.PROCESSING, stageStatus);
            boolean canStepEdit = Objects.equals(StageStepStatusConstant.READY, stepStatus)
                || Objects.equals(StageStepStatusConstant.PROCESSING, stepStatus);

            permission.setView(true);
            boolean hasEditRole = memberRoles.stream()
                .anyMatch(item -> Objects.equals(item.getRole(), AuditProjectRoleConstant.EDIT)
                    || Objects.equals(item.getRole(), AuditProjectRoleConstant.APPROVE_AND_EDIT));
            boolean hasOwnerRole = memberRoles.stream()
                .anyMatch(item -> Objects.equals(item.getRole(),
                    AuditProjectRoleConstant.OWNER));

            if (canStageEdit && canStepEdit) {
                permission.setManuallyFinish(hasOwnerRole);
                permission.setEdit(hasEditRole);
                log.info(
                    "项目[{}],阶段实例[{},状态{}]步骤实例[{},状态{}],当前操作人[{}],阶段实例是否可编辑[{}],步骤实例是否可编辑[{}],当前操作人能否编辑[{}]能否完成[{}]",
                    projectId, stageExampleId, stageStatus, stageStepExampleId, stepStatus, orgId,
                    canStageEdit, canStepEdit, hasEditRole, hasOwnerRole);
            } else {
                log.info(
                    "项目[{}],阶段实例[{},状态{}]步骤实例[{},状态{}],当前操作人[{}],阶段实例是否可编辑[{}],步骤实例是否可编辑[{}],由于阶段或步骤实例不允许编辑，所以当前操作人不能编辑不能完成",
                    projectId, stageExampleId, stageStatus, stageStepExampleId, stepStatus, orgId,
                    canStageEdit, canStepEdit);
            }

            if (isDebug) {
                log.info(
                    "当前用户[{}]在项目[{}]中是否拥有编辑角色[{}],阶段实例[{}]对应状态[{}],步骤实例[{}]状态[{}],编辑权限为[{}]",
                    orgId,
                    projectId, hasEditRole, stageExample.getId(), stageStatus,
                    stageStepExample.getId(),
                    stepStatus, permission.getEdit());

                log.info(
                    "当前用户[{}]在项目[{}]中是否拥有负责人角色[{}],阶段实例[{}]对应状态[{}],步骤实例[{}]状态[{}],编辑权限为[{}]",
                    orgId,
                    projectId, hasOwnerRole, stageExample.getId(), stageStatus,
                    stageStepExample.getId(),
                    stepStatus, permission.getManuallyFinish());
            }

        } else {
            List<String> roles = this.orgService.findRoleIdListByOrgId(orgId);
            if (CollUtil.isNotEmpty(roles)) {
                String globalViewerRoleId = SettingUtils.getStringValue(
                    BnzgSettingConstant.DEFAULT_MODULE,
                    BnzgSettingConstant.AUDIT_GLOBAL_VIEWER_ROLE);
                boolean isGlobalViewer = roles.stream()
                    .anyMatch(item -> Objects.equals(item, globalViewerRoleId));
                if (isGlobalViewer) {
                    permission.setView(true);
                    log.info("[{}]拥有全局查看角色[{}],允许查看[{}]项目", orgId, globalViewerRoleId,
                        projectId);
                } else {
                    permission.setView(false);
                }
            } else {
                permission.setView(false);
            }

            permission.setEdit(false);
            permission.setManuallyFinish(false);
            if (isDebug) {
                log.info("当前用户[{}]未在项目[{}]中拥有任何角色,所有权限为{}", orgId, projectId,
                    permission);
            }
        }

        StageStepAuthAndConfigDTO result = new StageStepAuthAndConfigDTO();
        result.setPermission(permission);
        return result;
    }
}
