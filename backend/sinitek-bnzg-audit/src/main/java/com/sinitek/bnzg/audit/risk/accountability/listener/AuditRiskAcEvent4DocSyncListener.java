package com.sinitek.bnzg.audit.risk.accountability.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.doc.constant.DocTypeConstant;
import com.sinitek.bnzg.audit.doc.dto.AttachmentDocSyncParamDTO;
import com.sinitek.bnzg.audit.doc.dto.DocumentBaseDTO;
import com.sinitek.bnzg.audit.doc.dto.DocumentDeleteParamDTO;
import com.sinitek.bnzg.audit.doc.service.IDocumentService;
import com.sinitek.bnzg.audit.risk.accountability.constant.AuditRiskAcConstant;
import com.sinitek.bnzg.audit.risk.accountability.entity.AuditRiskAc;
import com.sinitek.bnzg.audit.risk.accountability.event.RiskAccountabilitySaveOrEditEvent;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRiskAcEvent4DocSyncListener {

    @Autowired
    private IDocumentService documentService;

    @Autowired
    private IAuditRiskService auditRiskService;

    /**
     * 监听风险点改变事件,同步附件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = RiskAccountabilitySaveOrEditEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        RiskAccountabilitySaveOrEditEvent<T> event) {

        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            log.info("风险点问责管理文档同步痕事件监听器 监听到 风险点问责管理 事件,数据: {}",
                JsonUtil.toJsonString(event));
            this.handleSingleData((RecordChangeLogAddParamDTO) source);
        } else {
            log.info("风险点问责管理文档同步事件监听器 监听到 风险点问责管理 批量事件,数据: {}",
                JsonUtil.toJsonString(event));
            AbstractRecordChangeLogBatchAddBaseParamDTO<AuditRiskAc> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO) source;
            this.handleBatchData(batchParam);
        }
    }

    private void handleBatchData(AbstractRecordChangeLogBatchAddBaseParamDTO<AuditRiskAc> param) {
        Collection<Long> foreignKeys = param.getForeignKeys();
        if (CollUtil.isNotEmpty(foreignKeys)) {
            String operatorId = null;
            Date opTime = null;
            String remark = null;
            for (Long foreignKey : foreignKeys) {
                RecordChangeLogAddParamDTO<AuditRiskAc> data = new RecordChangeLogAddParamDTO<>();
                AuditRiskAc newValue = param.getNewValue(foreignKey);
                AuditRiskAc oldValue = param.getOldValue(foreignKey);

                if (StringUtils.isBlank(remark)) {
                    remark = param.getRemark(foreignKey);
                }
                if (StringUtils.isBlank(operatorId)) {
                    operatorId = param.getOperatorId(foreignKey);
                }
                if (Objects.isNull(opTime)) {
                    opTime = param.getOpTime(foreignKey);
                }

                data.setForeignKey(foreignKey);
                data.setNewValue(newValue);
                data.setOldValue(oldValue);
                data.setRemark(remark);
                data.setOperatorId(param.getOperatorId(foreignKey));
                data.setOpTime(param.getOpTime(foreignKey));

                this.syncDocData(data);
            }
        }
    }

    private void handleSingleData(RecordChangeLogAddParamDTO<AuditRiskAc> param) {
        this.syncDocData(param);
    }

    private void syncDocData(RecordChangeLogAddParamDTO<AuditRiskAc> param) {
        Long foreignKey = param.getForeignKey();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        AuditRiskAc oldValue = param.getOldValue();
        AuditRiskAc newValue = param.getNewValue();

        boolean isNeedDelete = false;
        boolean isNeedSync = false;
        Long riskId = null;
        if (Objects.nonNull(oldValue) && Objects.isNull(newValue)) {
            // 旧数据存在,新数据不存在 为删除
            riskId = oldValue.getRiskId();
            isNeedDelete = true;
        } else if (Objects.isNull(oldValue) && Objects.nonNull(newValue)) {
            // 旧数据不存在,新数据存在 为新增
            riskId = newValue.getRiskId();
            isNeedSync = true;
        } else if (Objects.nonNull(oldValue) && Objects.nonNull(newValue)) {
            // 新旧数据都存在为更新
            riskId = newValue.getRiskId();
            isNeedSync = true;
        }

        // 删除文档
        if (isNeedDelete) {
            List<DocumentBaseDTO> list = this.documentService.findThreadLatestExists(
                AuditRiskAcConstant.DEFAULT_SOURCE_NAME, foreignKey);
            if (CollUtil.isNotEmpty(list)) {
                log.info("当前问责数据[{}]已被删除,需要删除[{}]条关联的文档数据", foreignKey,
                    list.size());
                List<Long> needDeleteIds = list.stream().map(DocumentBaseDTO::getId)
                    .collect(Collectors.toList());
                this.documentService.deleteByIdsWithoutProjectPhaseCheck(
                    DocumentDeleteParamDTO.builder()
                        .ids(needDeleteIds)
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .build());
            } else {
                log.info("当前问责数据[{}]已被删除,没有关联的文档数据,无需删除文档数据",
                    foreignKey);
            }
        }
        // 同步文档
        if (isNeedSync) {
            AuditRiskBaseInfoDTO risk = this.auditRiskService.getById(riskId);
            Long projectId = risk.getProjectId();
            Long procedureId = risk.getProcedureId();

            AttachmentDocSyncParamDTO syncParam = new AttachmentDocSyncParamDTO();
            syncParam.setSourceId(foreignKey);
            syncParam.setSourceName(AuditRiskAcConstant.DEFAULT_SOURCE_NAME);
            syncParam.setRiskId(riskId);
            syncParam.setProcedureId(procedureId);
            syncParam.setProjectId(projectId);
            syncParam.setUploaderId(operatorId);
            syncParam.setOpTime(opTime);

            syncParam.addAttachmentTypeAndDocType(
                AuditRiskAcConstant.INVESTIGATION_RESULT_UPLOAD_TYPE,
                DocTypeConstant.INVESTIGATION_RESULT_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.REVIEW_RECORD_UPLOAD_TYPE,
                DocTypeConstant.REVIEW_RECORD_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.PUNISH_OPINION_UPLOAD_TYPE,
                DocTypeConstant.PUNISH_OPINION_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.PUNISH_NOTICE_UPLOAD_TYPE,
                DocTypeConstant.PUNISH_NOTICE_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.RECONSIDERATION_UPLOAD_TYPE,
                DocTypeConstant.RECONSIDERATION_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.PUNISH_DECISION_UPLOAD_TYPE,
                DocTypeConstant.PUNISH_DECISION_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.IMPLEMENTATION_UPLOAD_TYPE,
                DocTypeConstant.IMPLEMENTATION_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(
                AuditRiskAcConstant.COMPANY_REVIEW_RESULT_UPLOAD_TYPE,
                DocTypeConstant.COMPANY_REVIEW_RESULT_UPLOAD_TYPE);
            syncParam.addAttachmentTypeAndDocType(
                AuditRiskAcConstant.INVESTIGATION_RESULT_UPLOAD_TYPE2,
                DocTypeConstant.INVESTIGATION_RESULT_UPLOAD_TYPE2);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.REVIEW_RECORD_UPLOAD_TYPE2,
                DocTypeConstant.REVIEW_RECORD_UPLOAD_TYPE2);
            syncParam.addAttachmentTypeAndDocType(AuditRiskAcConstant.PUNISH_OPINION_UPLOAD_TYPE2,
                DocTypeConstant.PUNISH_OPINION_UPLOAD_TYPE2);
            syncParam.addAttachmentTypeAndDocType(
                AuditRiskAcConstant.COMPANY_REVIEW_RESULT_UPLOAD_TYPE2,
                DocTypeConstant.COMPANY_REVIEW_RESULT_UPLOAD_TYPE2);

            log.info("同步问责数据[{}]对应的文档数据", foreignKey);
            this.documentService.syncAndForceDeleteDoc(syncParam);
        }

    }

}
