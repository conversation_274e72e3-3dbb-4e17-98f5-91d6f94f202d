package com.sinitek.bnzg.audit.project.procedure.approve.log.result.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.entity.AuditRiskApprvResultLog;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.mapper.AuditRiskApprvResultLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class AuditRiskApprvResultLogDAO extends
    ServiceImpl<AuditRiskApprvResultLogMapper, AuditRiskApprvResultLog> {

    public List<AuditRiskApprvResultLog> findByApproveResultId(
        Long approveResultId) {
        LambdaQueryWrapper<AuditRiskApprvResultLog> queryWrapper = Wrappers.lambdaQuery(
            AuditRiskApprvResultLog.class);
        queryWrapper.eq(AuditRiskApprvResultLog::getApprvResultId, approveResultId);
        return this.list(queryWrapper);
    }

}
