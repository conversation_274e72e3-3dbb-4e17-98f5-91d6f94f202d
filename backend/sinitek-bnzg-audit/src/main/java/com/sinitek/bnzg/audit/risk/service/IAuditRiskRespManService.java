package com.sinitek.bnzg.audit.risk.service;


import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespMan;
import java.util.Collection;
import java.util.List;

/**
 * 项目风险点责任人 Service 接口
 *
 * <AUTHOR>
 * date 2024-11-08
 */
public interface IAuditRiskRespManService {

    void saveBatchRespMan(Long riskId, List<String> respManIds);

    List<AuditRiskRespMan> findByRiskId(Long riskId);

    void saveOrUpdateBatch(List<AuditRiskRespMan> respManList);

    void removeByIds(List<Long> ids);

    List<AuditRiskRespMan> findByRiskIds(Collection<Long> riskIds);

}
