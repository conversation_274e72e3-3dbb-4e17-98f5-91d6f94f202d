package com.sinitek.bnzg.audit.risk.service.impl;

import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.RISK_RECTIFY_STATE_ALREADY_FINISH;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskMessageConstant.RISK_DATA_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditCheckResultDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.dto.RectifyResultLoadResultDTO;
import com.sinitek.bnzg.audit.risk.dto.RectifyResultSaveParamDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.log.rectify.state.util.AuditRiskRectifyStateChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.log.respdept.service.IAuditRespDeptChangeLogService;
import com.sinitek.bnzg.audit.risk.log.respman.service.IAuditRespManChangeLogService;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRectifyTraceNoticeService;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskTrackingService;
import com.sinitek.bnzg.audit.risk.util.AuditRiskChangeEventUtil;
import com.sinitek.bnzg.audit.risk.util.AuditRiskConvertUtil;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStepStatusUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 09/03/2024 17:41
 */
@Slf4j
@Service
public class AuditRiskTrackingServiceImpl implements IAuditRiskTrackingService {

    @Autowired
    private AuditRiskDAO dao;

    @Autowired
    private IRectifyContactService contactService;

    @Autowired
    private IAuditRespManChangeLogService manChangeLogService;

    @Autowired
    private IAuditRespDeptChangeLogService deptChangeLogService;

    @Autowired
    private IAuditRiskRectifyTraceNoticeService auditRiskRectifyTraceNoticeService;

    @Autowired
    private IStageStepExampleStatusService stepExampleStatusService;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRectifyResult(RectifyResultSaveParamDTO param) {
        Long riskId = param.getRiskId();
        Date actRectifyDate = param.getActRectifyDate();
        String rectifyFeedback = param.getRectifyFeedback();
        Date reqRectifyDate = param.getReqRectifyDate();

        log.info("风险点[{}]预计整改完成时间[{}], 实际整改完成时间[{}], 整改反馈[{}]", riskId,
            reqRectifyDate, actRectifyDate, rectifyFeedback);

        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        AuditRisk risk = this.dao.getById(riskId);
        if (Objects.nonNull(risk)) {
            Long projectId = risk.getProjectId();

            AuditRisk oldData = JsonUtil.jsonCopy(risk, AuditRisk.class);
            Integer oldRectifyState = risk.getRectifyState();
            if (ObjectUtil.isNotEmpty(oldRectifyState) &&
                oldRectifyState.equals(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH)) {
                log.error("风险点[{}]目前已经是整改完成状态，无法再次进行整改反馈操作", riskId);
                throw new BussinessException(RISK_RECTIFY_STATE_ALREADY_FINISH);
            }
            risk.setActRectifyDate(actRectifyDate);
            risk.setRectifyFeedback(rectifyFeedback);
            risk.setRectifyState(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH);
            this.dao.updateById(risk);

            // 风险点数据变动
            AuditRiskChangeEventUtil.publish(RecordChangeLogAddParamDTO.builder()
                .foreignKey(riskId)
                .oldValue(oldData)
                .newValue(risk)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("整改追踪-保存整改结果")
                .build());

            // 风险点整改状态变动
            AuditRiskRectifyStateChangeEventPublishUtil.publishEvent(
                RecordChangeLogAddParamDTO.builder()
                    .foreignKey(riskId)
                    .oldValue(oldRectifyState)
                    .newValue(risk.getRectifyState())
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("整改追踪-保存整改结果")
                    .build());

            UploadDTO upload = param.getUpload();
            // 附件
            if (ObjectUtil.isNotEmpty(upload)) {
                AttachmentUtils.saveAttachmentList(upload, riskId,
                    AuditRiskConstant.DEFAULT_SOURCE_NAME);
            }

            this.makeStepAsProgressing(projectId, operatorId, opTime, "整改跟踪阶段保存整改结果");

        } else {
            log.error("根据风险点id {} 找不到对应的风险点数据", riskId);
            throw new BussinessException(RISK_DATA_NOT_EXISTS);
        }

    }

    @Override
    public RectifyResultLoadResultDTO loadByRiskId(Long riskId) {
        AuditRisk risk = this.dao.getById(riskId);
        if (Objects.nonNull(risk)) {
            RectifyResultLoadResultDTO result = new RectifyResultLoadResultDTO();

            result.setRiskId(riskId);
            result.setReqRectifyDate(risk.getReqRectifyDate());
            result.setActRectifyDate(risk.getActRectifyDate());
            result.setRectifyFeedback(risk.getRectifyFeedback());

            return result;
        } else {
            log.warn("根据风险点id {} 查不到风险点数据", riskId);
        }
        return null;
    }

    @Override
    public IPage<AuditRiskSearchResultDTO> search(AuditRiskSearchParamDTO searchDTO) {
        AuditRiskSearchParamPO param = AuditRiskConvertUtil.makeSearchParamDTO2PO(
            searchDTO);
        IPage<AuditRiskSearchResultPO> result = this.dao.searchInTracking(param);
        return result.convert(AuditRiskConvertUtil::makeSearchResultPO2DTO);
    }

    @Override
    public AuditCheckResultDTO checkCanFinish(Long projectId) {
        List<AuditRisk> risks = this.dao.findThreadLatestByProjectId(projectId);
        if (CollUtil.isNotEmpty(risks)) {
            // 存在整改状态的不允许完成
            List<AuditRisk> notRectifyedRisks = new LinkedList<>();
            risks.forEach(item -> {
                Long id = item.getId();
                Integer rectifyState = item.getRectifyState();
                if (Objects.nonNull(rectifyState)) {
                    if (Objects.equals(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH,
                        rectifyState)) {
                        log.debug("项目[{}]下风险点[{}]整改状态[{}]已完成,允许完成整改跟踪",
                            projectId, id, rectifyState);
                    } else {
                        log.info("项目[{}]下风险点[{}]整改状态[{}]未完成,不允许完成整改跟踪",
                            projectId, id, rectifyState);
                        notRectifyedRisks.add(item);
                    }
                } else {
                    log.debug("项目[{}]下风险点[{}]不存在整改状态[{}],允许完成整改跟踪", projectId,
                        id, rectifyState);
                }
            });

            if (CollUtil.isNotEmpty(notRectifyedRisks)) {
                log.warn("项目[{}]下风险点[{}]整改未完成,不允许完成整改跟踪", projectId,
                    notRectifyedRisks.stream().map(AuditRisk::getId).collect(
                        Collectors.toList()));
                String msg = String.format("风险点 %s 整改未完成",
                    notRectifyedRisks.stream().map(AuditRisk::getName)
                        .map(item -> String.format("【%s】", item)).collect(
                            Collectors.joining(",")));
                return AuditCheckResultDTO.builder().result(false).msg(msg).build();
            } else {
                return AuditCheckResultDTO.builder().result(true).build();
            }
        } else {
            log.info("项目[{}]不存在风险点,允许完成整改跟踪", projectId);
            // 不存在风险点
            return AuditCheckResultDTO.builder().result(true).build();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRelevantPeopleAndDepartment(RectifyContactEditParamDTO param) {
        Long riskId = param.getRiskId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        this.contactService.saveContacts(param);
        this.deptChangeLogService.saveAuditRespDept(param);
        this.manChangeLogService.saveAuditRespMan(param);

        // 生成整改延期提醒
        this.auditRiskRectifyTraceNoticeService.saveRectifyTraceNotice(riskId, operatorId);

        // 生成整改状态变动提醒
        this.auditRiskRectifyTraceNoticeService.saveRectifyChangeNotice(riskId, operatorId);

        AuditRisk risk = this.dao.getById(riskId);
        Long projectId = risk.getProjectId();

        this.makeStepAsProgressing(projectId, operatorId, opTime, "整改跟踪保存整改联系人");
    }

    private void makeStepAsProgressing(Long projectId, String operatorId, Date opTime,
        String remark) {
        // 审计实施
        StageStepExampleDTO stepExample = this.stageStepExampleService.getByProjectIdAndStepValue(
            projectId, StageStepConstant.RECTIFICATION_TRACKING);
        if (Objects.isNull(stepExample)) {
            log.warn("项目[{}] 风险点 {} 后,整改跟踪阶段步骤实例不存在, 不需要变为进行中",
                projectId,
                remark);
            return;
        }
        Long id = stepExample.getId();
        Integer status = stepExample.getStatus();

        if (StageStepStatusUtil.checkCanChangeAsProcessiongStatus(status)) {
            this.stepExampleStatusService.updateStatus(
                StageStepExampleStatusChangeParamDTO.builder()
                    .ids(Collections.singletonList(id))
                    .newStatus(StageStepStatusConstant.PROCESSING)
                    .opOrgId(operatorId)
                    .opTime(opTime)
                    .opRemark(remark)
                    .publishEventFlag(true)
                    .build());
            log.info("项目[{}]风险点{}后,整改跟踪阶段步骤实例 {} 状态为 {} 变为进行中", projectId,
                remark, id, status);
        } else {
            log.info("项目[{}]风险点{}后,整改跟踪阶段步骤实例 {} 状态为 {} 无需变为进行中",
                projectId,
                remark, id, status);
        }

    }
}
