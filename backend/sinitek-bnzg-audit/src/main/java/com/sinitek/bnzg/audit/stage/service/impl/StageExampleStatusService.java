package com.sinitek.bnzg.audit.stage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.bnzg.audit.stage.constant.StageStatusConstant;
import com.sinitek.bnzg.audit.stage.dao.StageExampleDAO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.entity.StageExample;
import com.sinitek.bnzg.audit.stage.log.status.stage.util.StageExampleStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.stage.service.IStageExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStatusUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 08/15/2024 16:29
 */
@Slf4j
@Service
public class StageExampleStatusService implements IStageExampleStatusService {

    @Autowired
    private StageExampleDAO dao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStageExampleStatus(StageExampleStatusChangeParamDTO param) {
        List<Long> ids = param.getIds();
        Integer newStatus = param.getNewStatus();
        String opOrgId = param.getOpOrgId();
        Date opTime = param.getOpTime();
        String opNote = param.getOpRemark();
        Boolean publishEventFlag = param.getPublishEventFlag();
        Boolean force = param.getForce();

        List<StageExample> stageExamples = this.dao.listByIds(ids);
        if (CollUtil.isNotEmpty(stageExamples)) {

            List<StageExample> needUpdateList;

            if (Objects.equals(Boolean.TRUE, force)) {
                needUpdateList = stageExamples;
                log.warn("操作人[{}]强制更新阶段实例[{}]状态,参数: {}", opOrgId, ids,
                    JsonUtil.toJsonString(param));
            } else {
                needUpdateList = stageExamples.stream().filter(item -> {
                    Integer status = item.getStatus();
                    return StageStatusUtil.checkCanChangeStatus(status, newStatus);
                }).collect(Collectors.toList());
            }

            if (CollUtil.isNotEmpty(needUpdateList)) {

                Map<Long, Integer> idAndOldStatusMap = new LinkedHashMap<>();
                Map<Long, Integer> idAndNewStatusMap = new LinkedHashMap<>();
                List<Long> needUpdateIds = new LinkedList<>();

                needUpdateList.forEach(item -> {
                    Long id = item.getId();
                    Integer oldStatus = item.getStatus();
                    Date endTime = item.getEndTime();
                    Date terminatedTime = item.getTerminatedTime();

                    item.setStatus(newStatus);

                    if (Objects.equals(StageStatusConstant.FINISHED, newStatus)) {
                        if (Objects.isNull(endTime)) {
                            item.setEnderId(opOrgId);
                            item.setEndTime(opTime);
                        } else {
                            log.info(
                                "操作人[{}]于[{}]完成阶段实例[{}],已存在历史完成时间[{}],不再更新完成时间",
                                opOrgId, opTime, endTime, id);
                        }
                    }
                    if (Objects.equals(StageStatusConstant.TERMINATE, newStatus)) {
                        if (Objects.isNull(terminatedTime)) {
                            item.setTerminatorId(opOrgId);
                            item.setTerminatedTime(opTime);
                        } else {
                            log.info(
                                "操作人[{}]于[{}]终止阶段实例[{}],已存在历史终止时间[{}],不再更新终止时间",
                                opOrgId, opTime, terminatedTime, id);
                        }
                    }

                    needUpdateIds.add(id);
                    idAndOldStatusMap.put(id, oldStatus);
                    idAndNewStatusMap.put(id, newStatus);
                });

                this.dao.updateBatchById(needUpdateList);

                if (Objects.equals(Boolean.TRUE, publishEventFlag)) {
                    StageExampleStatusChangeEventPublishUtil.publishEvent(
                        RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                            .foreignKeys(needUpdateIds)
                            .oldValueMap(idAndOldStatusMap)
                            .newValueMap(idAndNewStatusMap)
                            .operatorId(opOrgId)
                            .opTime(opTime)
                            .remark(opNote)
                            .build());
                } else {
                    log.info("当前无需发布阶段实例[{}]状态变动事件", needUpdateIds);
                }

            } else {
                log.info(
                    "根据阶段实例id[{}]获取到的阶段实例状态不满足变更为[{}]的条件,没有数据更新",
                    ids, newStatus);
            }

        } else {
            log.warn("根据阶段实例id[{}]获取到的阶段实例数据为空,无法继续更新", ids);
        }

    }
}
