package com.sinitek.bnzg.audit.project.procedure.approve.controller;

import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpAndRiskBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风险点审批 Controller
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@RestController
@RequestMapping("/frontend/api/audit/project/procedure/approve")
@Api(value = "/frontend/api/audit/project/procedure/approve", tags = "审计系统-项目审计程序-审批")
public class PpApproveController {

    @Autowired
    private IPpApproveService ppApproveService;

    @ApiOperation(value = "批量审批")
    @PostMapping("/batch-approve")
    public RequestResult<Void> batchApprove(
        @RequestBody @Validated PpAndRiskBatchApproveParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.ppApproveService.batchApprove(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "加载详情")
    @GetMapping("/detail")
    public RequestResult<PpApproveDetailDTO> loadDetail(@RequestParam("id") Long id) {
        return new RequestResult<>(this.ppApproveService.loadDetail(id));
    }

}
