package com.sinitek.bnzg.audit.stage.log.status.stage.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.stage.log.status.stage.dao.StageExampleStatusLogDAO;
import com.sinitek.bnzg.audit.stage.log.status.stage.entity.StageExampleStatusLog;
import com.sinitek.bnzg.audit.stage.log.status.stage.service.IStageExampleStatusLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class StageExampleStatusLogServiceImpl extends
    AbstractReecordChangeLogService<StageExampleStatusLog, Integer> implements
    IStageExampleStatusLogService {

    @Autowired
    private StageExampleStatusLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<StageExampleStatusLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected StageExampleStatusLog generateNewOne() {
        return new StageExampleStatusLog();
    }

    @Override
    protected void handlerForeignKey(StageExampleStatusLog entity, Long foreignKey) {
        entity.setStageExampleId(foreignKey);
    }
}
