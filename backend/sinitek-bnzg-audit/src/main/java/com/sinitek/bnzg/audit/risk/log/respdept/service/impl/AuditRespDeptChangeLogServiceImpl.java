package com.sinitek.bnzg.audit.risk.log.respdept.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.risk.contact.dto.RectifyContactEditParamDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.log.respdept.dao.AuditRespDeptLogDAO;
import com.sinitek.bnzg.audit.risk.log.respdept.entity.AuditRespDeptLog;
import com.sinitek.bnzg.audit.risk.log.respdept.service.IAuditRespDeptChangeLogService;
import com.sinitek.bnzg.audit.risk.log.respdept.util.AuditRiskRespDeptChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 12/06/2024 13:02
 */
@Slf4j
@Service
public class AuditRespDeptChangeLogServiceImpl extends
    AbstractReecordChangeLogService<AuditRespDeptLog, String> implements
        IAuditRespDeptChangeLogService {

    @Autowired
    private AuditRespDeptLogDAO dao;


    @Autowired
    private IAuditRiskRespDeptService deptService;

    @Override
    protected boolean saveBatch(Collection<AuditRespDeptLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected AuditRespDeptLog generateNewOne() {
        return new AuditRespDeptLog();
    }

    @Override
    protected void handlerForeignKey(AuditRespDeptLog entity, Long foreignKey) {
        entity.setRiskId(foreignKey);
    }

    @Override
    public void saveAuditRespDept(RectifyContactEditParamDTO param) {
        Long riskId = param.getRiskId();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();
        List<String> respDeptIds = param.getRespDeptId();

        List<AuditRiskRespDept> deptList = this.deptService.findByRiskId(riskId);
        List<String> existsdeptOrgIds = null;
        if (CollUtil.isNotEmpty(deptList)) {
            existsdeptOrgIds = deptList.stream().map(AuditRiskRespDept::getRespDeptId)
                    .collect(Collectors.toList());
            List<Long> existsIds = deptList.stream().map(AuditRiskRespDept::getId)
                    .collect(Collectors.toList());
            this.deptService.removeByIds(existsIds);
        }
        if (CollUtil.isNotEmpty(respDeptIds)) {
            this.deptService.saveBatchRespDept(riskId, respDeptIds);
        }
        if (Objects.isNull(existsdeptOrgIds) && CollUtil.isEmpty(respDeptIds)) {
            // 无变动
            return;
        }
        String respDeptremark = "整改跟踪-更新责任部门";
        if (Objects.isNull(existsdeptOrgIds)) {
            respDeptremark= "整改跟踪-新增责任部门";
        }

        AuditRiskRespDeptChangeEventPublishUtil.publishEvent(
                RecordChangeLogAddParamDTO.<String>builder()
                        .foreignKey(riskId)
                        .oldValue(JsonUtil.toJsonString(existsdeptOrgIds))
                        .newValue(JsonUtil.toJsonString(respDeptIds))
                        .operatorId(operatorId)
                        .opTime(opTime)
                        .remark(respDeptremark)
                        .build());
    }
}
