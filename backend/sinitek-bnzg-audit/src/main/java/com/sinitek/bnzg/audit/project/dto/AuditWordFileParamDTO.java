package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date：2024/11/4 17:33
 */
@Data
@NoArgsConstructor
@ApiModel("自动生成审计报告参数dto")
public class AuditWordFileParamDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("签发日期")
    private Date signDate;

    @ApiModelProperty("文件类型")
    private Long docType;

}
