package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "项目程序")
public class AuditProjectProcedureSearchResultOnProjectConfigDTO extends
    AuditProjectProcedureSearchResultBaseDTO {

    @ApiModelProperty("审计年份")
    private Integer auditYears;

    @ApiModelProperty("程序库名称")
    private String libName;

    @ApiModelProperty("程序名称")
    private String procedureName;

    @ApiModelProperty("风险点个数")
    private Integer riskCount;

}
