package com.sinitek.bnzg.audit.stage.service;

import com.sinitek.bnzg.audit.stage.dto.StageStepExampleFinishParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;

/**
 * 阶段步骤实例 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-15
 */
public interface IStageStepExampleStatusService {

    void manuallyFinish(StageStepExampleFinishParamDTO param);

    void updateStatus(StageStepExampleStatusChangeParamDTO param);
}
