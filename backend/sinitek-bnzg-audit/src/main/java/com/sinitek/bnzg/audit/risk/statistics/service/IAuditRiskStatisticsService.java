package com.sinitek.bnzg.audit.risk.statistics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartDetailParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.RiskStatisticsChartParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.RiskStatisticsChartReasultDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @Date：2024/9/20 17:21
 */
public interface IAuditRiskStatisticsService {

    IPage<AuditRiskStatisticsReasultDTO> search(AuditRiskStatisticsParamDTO searchDTO);

    List<Integer> findAuditYear();

    List<RiskStatisticsChartReasultDTO> findRiskStatisticsChart(RiskStatisticsChartParamDTO searchDTO);

    List<AuditFindRectifyChartReasultDTO>  findAuditFindRectifyChart(AuditFindRectifyChartParamDTO searchDTO);

    IPage<AuditRiskStatisticsReasultDTO> searchAuditFindRectifyChartDetail(
        AuditFindRectifyChartDetailParamDTO searchDTO);
}
