<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.project.procedure.mapper.AuditProjectProcedureMapper">

  <update id="logicDeletePp">
    update audit_project_procedure
       set remove_flag = 1,
           remover_id = #{operatorId}
       where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
  </update>

  <select id="findLibRefCountInfo" resultType="com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectCountInfoPO">
    select l.id as lib_id,count(distinct pp.project_id) as ref_count
      from audit_project_procedure pp,audit_procedure p,audit_library l
     where pp.procedure_id = p.id
       and pp.thread_latest_flag = 1
       and p.lib_id  = l.id
       and pp.remove_flag = 0
       and p.remove_flag = 0
       and l.remove_flag = 0
       and l.id in
           <foreach close=")" collection="libIds" index="index" item="libId" open="(" separator=",">
             #{libId}
           </foreach>
     group by l.id
  </select>

  <select id="findLibRefInfo" resultType="com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchResultPO">
      select l.id as lib_id,pp.project_id
        from audit_project_procedure pp,
             audit_procedure p,
             audit_library l
       where pp.procedure_id = p.id
         and pp.thread_latest_flag = 1
         and p.lib_id  = l.id
         and pp.remove_flag = 0
         and p.remove_flag = 0
         and l.remove_flag = 0
         and l.id in
             <foreach close=")" collection="libIds" index="index" item="libId" open="(" separator=",">
               #{libId}
             </foreach>
       group by l.id,pp.project_id
       order by pp.project_id
  </select>

  <select id="searchExecutionList" resultType="com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureInfoPO">
    select *
      from audit_project_procedure
      where project_id = #{param.projectId}
        and thread_latest_flag = 1
        and remove_flag = 0
    <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
       order by id asc
    </if>
  </select>

  <select id="searchLibRefInfo" resultType="com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchResultPO">
      select l.id as lib_id,pp.project_id
        from audit_project_procedure pp,
             audit_procedure p,
             audit_library l
       where pp.procedure_id = p.id
         and pp.thread_latest_flag = 1
         and p.lib_id  = l.id
         and pp.remove_flag = 0
         and p.remove_flag = 0
         and l.remove_flag = 0
         and l.id in
             <foreach close=")" collection="param.libIds" index="index" item="libId" open="(" separator=",">
               #{libId}
             </foreach>
       group by l.id,pp.project_id
       <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
          order by pp.project_id
       </if>
  </select>

  <select id="searchOnProjectConfig" resultType="com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchResultPO">
    select pp.*,
           p.name procedure_name,
           l.id lib_id,
           l.name lib_name,
           l.year audit_years
      from audit_project_procedure pp,
      	   audit_procedure p,
      	   audit_library l
     where pp.remove_flag = 0
       and pp.thread_latest_flag = 1
       and p.remove_flag = 0
       and pp.procedure_id  = p.id
       and l.id = p.lib_id
       and pp.project_id = #{param.projectId}
       <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.procedureNames)">
           and
           <foreach collection="param.procedureNames" index="index" item="procedureName" open="(" separator="or" close=")">
               p.name LIKE CONCAT('%', #{procedureName}, '%') ESCAPE '/'
           </foreach>
       </if>
       <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.libNames)">
          and
          <foreach collection="param.libNames" index="index" item="libName" open="(" separator="or" close=")">
              l.name LIKE CONCAT('%', #{libName}, '%') ESCAPE '/'
          </foreach>
       </if>
      <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.auditYears)">
          <!-- 审计年份查询 -->
          and l.year in
          <foreach collection="param.auditYears" index="index" item="auditYear" open="(" separator="," close=")">
              #{auditYear}
          </foreach>
      </if>
       <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
           order by pp.createtimestamp desc,pp.id desc
       </if>
  </select>

  <select id="findByProjectId" resultType="com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure">
  select *
    from audit_project_procedure
   where project_id = #{projectId}
     and thread_latest_flag = 1
  </select>

  <select id="findByProjectIds" resultType="com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure">
  select *
    from audit_project_procedure
   where thread_latest_flag = 1
     and project_id in
     <foreach collection="projectIds" index="index" item="projectId" open="(" separator="," close=")">
         #{projectId}
     </foreach>
  </select>

  <select id="getLatestOne" resultType="com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure">
      select *
        from audit_project_procedure
       where thread_id = #{threadId}
         and remove_flag = 0
         and ((status = 100) or (status = 50))
         and id != #{id}
       order by createtimestamp desc
       limit 1
  </select>
    <select id="countWaitApproveProjectProcedure" resultType="java.lang.Integer">
      select
        count(*) waitApprovePpCount
      from
        (select
          distinct
          app.id,
          app.project_id,
          app.procedure_id,
          null as risk_id,
          null as risk_name,
          app.status,
          app.approve_remark
        from
          wf_exampletask we
          left join audit_pp_approve_result appar on appar.approve_id = we.sourceid and appar.remove_flag = 0
          left join audit_project_procedure app on app.id = appar.pp_id
        where
          we.sourceentity = 'AUDIT_PP_APPROVE'
          and we.status = 1
          and we.dealerid = #{orgId}
          and app.status = 10
        union
        select
          distinct
          app.id,
          app.project_id,
          app.procedure_id,
          ar.id as risk_id,
          ar.name as risk_name,
          ar.status,
          ar.approve_remark
        from
          wf_exampletask we
          left join audit_risk_approve_result arar on arar.approve_id = we.sourceid and arar.remove_flag = 0
          left join audit_risk ar on ar.id = arar.risk_id and ar.thread_latest_flag = 1 and ar.remove_flag = 0
          left join audit_project_procedure app on app.project_id = ar.project_id and app.procedure_id = ar.procedure_id and app.remove_flag = 0 and app.thread_latest_flag  = 1
        where
          we.sourceentity = 'AUDIT_PP_APPROVE'
          and we.status = 1
          and we.dealerid = #{orgId}
          and ar.status = 10 ) t
    </select>
    <select id="countWaitHandleProjectProcedure" resultType="java.lang.Integer">
      select
        count(*) waitHandlePpCount
      from
        (select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.id as procedure_id,
          null as risk_id,
          null as risk_name,
          app.status,
          app.approve_remark
        from
          audit_project_procedure app
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
        where
          (app.status = 0 or app.status = 1)
          and app.thread_latest_flag  = 1
          and app.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and app.auditor_id = #{orgId}
        union
        select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          ar.id as risk_id,
          ar.name as risk_name,
          ar.status,
          ar.approve_remark
        from
          audit_risk ar
          left join audit_project_procedure app on app.project_id = ar.project_id and app.procedure_id = ar.procedure_id and app.remove_flag = 0 and app.thread_latest_flag  = 1
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
        where
          ar.status = 0
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and ar.auditor_id = #{orgId}) t
    </select>
    <select id="countNotPassPpCount" resultType="java.lang.Integer">
      select
        count(*) notPassPpCount
      from
        (select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          null as risk_id,
          null as risk_name,
          app.status,
          app.approve_remark
        from
          audit_project_procedure app
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
        where
          app.status = 50
          and app.thread_latest_flag  = 1
          and app.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and app.auditor_id = #{orgId}
        union
        select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          ar.id as risk_id,
          ar.name as risk_name,
          ar.status,
          ar.approve_remark
        from
          audit_risk ar
          left join audit_project_procedure app on app.project_id = ar.project_id and app.procedure_id = ar.procedure_id and app.remove_flag = 0 and app.thread_latest_flag  = 1
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
        where
          ar.status = 50
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and ar.auditor_id = #{orgId}) t
    </select>
    <select id="searchHomeWaitHandleProcedure"
            resultType="com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO">
      select
        t.*
      from
        (select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          apr.name as procedureName,
          null as risk_id,
          null as risk_name,
          app.status,
          app.approve_remark
        from
          audit_project_procedure app
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
          left join audit_procedure apr on apr.id = app.procedure_id and apr.remove_flag = 0
        where
          (app.status = 0 or app.status = 1)
          and app.thread_latest_flag  = 1
          and app.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and app.auditor_id = #{param.currentOrgId}
        union
        select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          apr.name as procedureName,
          ar.id as risk_id,
          ar.name as risk_name,
          ar.status,
          ar.approve_remark
        from
          audit_risk ar
          left join audit_project_procedure app on app.project_id = ar.project_id and app.procedure_id = ar.procedure_id and app.remove_flag = 0 and app.thread_latest_flag  = 1
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
          left join audit_procedure apr on apr.id = app.procedure_id and apr.remove_flag = 0
        where
          ar.status = 0
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and ar.auditor_id = #{param.currentOrgId}) t
    </select>
    <select id="searchHomeWaitApproveProcedure"
            resultType="com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO">
      select
        t.*
      from
        (select
          distinct
          app.id,
          app.project_id,
          app.procedure_id,
          apr.name as procedureName,
          null as risk_id,
          null as risk_name,
          app.status,
          app.approve_remark
        from
          wf_exampletask we
          left join audit_pp_approve_result appar on appar.approve_id = we.sourceid and appar.remove_flag = 0
          left join audit_project_procedure app on app.id = appar.pp_id
          left join audit_procedure apr on apr.id = app.procedure_id and apr.remove_flag = 0
        where
          we.sourceentity = 'AUDIT_PP_APPROVE'
          and we.status = 1
          and we.dealerid = #{param.currentOrgId}
          and app.status = 10

        union

        select
          distinct
          app.id,
          app.project_id,
          app.procedure_id,
          apr.name as procedureName,
          ar.id as risk_id,
          ar.name as risk_name,
          ar.status,
          ar.approve_remark
        from wf_exampletask we
          left join audit_risk_approve_result arar on arar.approve_id = we.sourceid and arar.remove_flag = 0
          left join audit_risk ar on ar.id = arar.risk_id and ar.thread_latest_flag = 1 and ar.remove_flag = 0
          left join audit_project_procedure app on app.project_id = ar.project_id and app.procedure_id = ar.procedure_id and app.remove_flag = 0 and app.thread_latest_flag  = 1
          left join audit_procedure apr on apr.id = app.procedure_id and apr.remove_flag = 0
        where
          we.sourceentity = 'AUDIT_PP_APPROVE'
          and we.status = 1
          and we.dealerid = #{param.currentOrgId}
          and ar.status = 10 ) t
    </select>

    <select id="searchHomeNotPassData" resultType="com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO">
      select
        t.*
      from
        (select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          apr.name as procedureName,
          null as risk_id,
          null as risk_name,
          app.status,
          app.approve_remark
        from
          audit_project_procedure app
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
          left join audit_procedure apr on apr.id = app.procedure_id and apr.remove_flag = 0
      where
        app.status = 50
        and app.thread_latest_flag  = 1
        and app.remove_flag = 0
        and ap.project_phase not in (5,6,7)
        and app.auditor_id = #{param.currentOrgId}
      union
        select
          app.id,
          ap.plan_id,
          ap.id as project_id,
          ap.name as project_name,
          app.procedure_id,
          apr.name as procedureName,
          ar.id as risk_id,
          ar.name as risk_name,
          ar.status,
          ar.approve_remark
        from
          audit_risk ar
          left join audit_project_procedure app on app.project_id = ar.project_id and app.procedure_id = ar.procedure_id and app.remove_flag = 0 and app.thread_latest_flag  = 1
          left join audit_project ap on ap.id = app.project_id  and ap.remove_flag = 0
          left join audit_procedure apr on apr.id = app.procedure_id and apr.remove_flag = 0
        where
          ar.status = 50
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase not in (5,6,7)
          and ar.auditor_id = #{param.currentOrgId}) t
    </select>
</mapper>
