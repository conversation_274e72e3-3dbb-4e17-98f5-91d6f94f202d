package com.sinitek.bnzg.audit.project.service.impl;

import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_SAVE_PROJECT_MEMBER_BECAUSEOF_CURRENT_PHASE;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_SAVE_PROJECT_MEMBER_NOT_OWNER;
import static com.sinitek.bnzg.audit.project.constant.AuditProjectMessageCodeConstant.CANT_SAVE_PROJECT_MEMBER_NO_OWNER;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.constant.AuditProjectRoleConstant;
import com.sinitek.bnzg.audit.project.dao.AuditProjectDAO;
import com.sinitek.bnzg.audit.project.dao.AuditProjectMemberDAO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectCopyResultDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMember4ImportSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMember4ImportSaveParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberAppendParamWrapperDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberSaveParamDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectMemberSaveParamWrapperDTO;
import com.sinitek.bnzg.audit.project.entity.AuditProject;
import com.sinitek.bnzg.audit.project.entity.AuditProjectMember;
import com.sinitek.bnzg.audit.project.enumation.AuditProjectPhaseEnum;
import com.sinitek.bnzg.audit.project.service.IAuditProjectMemberService;
import com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil;
import com.sinitek.bnzg.audit.project.util.AuditProjectUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:45
 */
@Slf4j
@Service
public class AuditProjectMemberServiceImpl implements IAuditProjectMemberService {

    @Autowired
    private AuditProjectMemberDAO dao;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private AuditProjectDAO auditProjectDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMembersWithoutOwners(AuditProjectMemberSaveParamWrapperDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();
        Boolean checkAuthFlag = param.getCheckAuthFlag();

        AuditProject auditProject = auditProjectDAO.getById(projectId);
        if (ObjectUtils.isNotEmpty(auditProject)) {
            Integer projectPhase = auditProject.getProjectPhase();
            AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(projectPhase);
            if (AuditProjectUtil.isPhaseInvalidForOperation(projectPhase)) {
                log.error("当前项目[{}]的项目进度为[{}],无法设置团队成员", projectId,
                    phaseEnum.getName());
                throw new BussinessException(CANT_SAVE_PROJECT_MEMBER_BECAUSEOF_CURRENT_PHASE,
                    phaseEnum.getName());
            }
        }

        log.info("[{}]保存项目成员[{}]", operatorId, param.getProjectId());

        if (Objects.equals(Boolean.TRUE, checkAuthFlag)) {
            // 检查是否为项目负责人
            AuditProjectCheckUtil.checkProjectOwner(param, "保存项目成员",
                CANT_SAVE_PROJECT_MEMBER_NOT_OWNER, CANT_SAVE_PROJECT_MEMBER_NO_OWNER);
        }

        List<AuditProjectMemberSaveParamDTO> memberList = param.getMemberList();

        List<AuditProjectMember> members = this.dao.findExistsMemberByProjectIds(
            Collections.singleton(projectId));
        // 其他成员
        List<AuditProjectMember> otherMembers = new LinkedList<>();
        if (CollUtil.isNotEmpty(members)) {
            members.forEach(item -> {
                Integer role = item.getRole();
                if (!Objects.equals(role, AuditProjectRoleConstant.OWNER)) {
                    otherMembers.add(item);
                }
            });
        }

        // key: orgId-role 组成的联合键,value: 当前人员拥有的角色
        Map<String, AuditProjectMember> orgIdAndMemberMap;
        if (CollUtil.isNotEmpty(otherMembers)) {
            orgIdAndMemberMap = otherMembers.stream()
                .collect(
                    Collectors.toMap(k -> this.getUniqueKey(k.getOrgId(), k.getRole()), v -> v));
        } else {
            orgIdAndMemberMap = Collections.emptyMap();
        }

        List<AuditProjectMember> needSaveList = new LinkedList<>();
        List<AuditProjectMember> needUpdateList = new LinkedList<>();

        memberList.forEach(item -> {
            String orgId = item.getOrgId();
            Integer role = item.getRole();
            String remark = item.getRemark();

            String uniqueKey = this.getUniqueKey(orgId, role);
            AuditProjectMember member = orgIdAndMemberMap.get(uniqueKey);
            if (Objects.nonNull(member)) {
                member.setOrgId(orgId);
                member.setRole(role);
                member.setRemark(remark);
                member.setProjectId(projectId);
                needUpdateList.add(member);

                orgIdAndMemberMap.remove(uniqueKey);
            } else {
                AuditProjectMember newOne = new AuditProjectMember();
                newOne.setOrgId(orgId);
                newOne.setRole(role);
                newOne.setRemark(remark);
                newOne.setProjectId(projectId);
                needSaveList.add(newOne);
            }
        });

        if (CollUtil.isNotEmpty(needSaveList)) {
            log.info("新保存[{}]条项目成员数据", needSaveList.size());
            this.dao.saveBatch(needSaveList);
        }

        if (CollUtil.isNotEmpty(needUpdateList)) {
            log.info("更新[{}]条项目成员数据", needUpdateList.size());
            this.dao.updateBatchById(needUpdateList);
        }

        Collection<AuditProjectMember> needRemoveCollection = orgIdAndMemberMap.values();
        if (CollUtil.isNotEmpty(needRemoveCollection)) {
            List<Long> ids = needRemoveCollection.stream().map(AuditProjectMember::getId)
                .collect(Collectors.toList());
            log.info("删除[{}]条项目成员数据", ids.size());
            this.dao.removeByIds(ids);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appendMembersWithoutOwners(AuditProjectMemberAppendParamWrapperDTO param) {
        Long projectId = param.getProjectId();
        String operatorId = param.getOperatorId();

        // 检查项目进度
        AuditProject auditProject = auditProjectDAO.getById(projectId);
        if (ObjectUtils.isNotEmpty(auditProject)) {
            Integer projectPhase = auditProject.getProjectPhase();
            AuditProjectPhaseEnum phaseEnum = AuditProjectPhaseEnum.getByValue(projectPhase);
            if (AuditProjectUtil.isPhaseInvalidForOperation(projectPhase)) {
                log.error("当前项目[{}]的项目进度为[{}],无法设置团队成员", projectId,
                    phaseEnum.getName());
                throw new BussinessException(CANT_SAVE_PROJECT_MEMBER_BECAUSEOF_CURRENT_PHASE,
                    phaseEnum.getName());
            }
        }

        log.info("[{}]保存项目成员[{}]", operatorId, param.getProjectId());

        List<AuditProjectMemberSaveParamDTO> appendMemberList = param.getAppendMemberList();

        // 获取所有项目成员
        List<AuditProjectMember> members = this.dao.findExistsMemberByProjectIds(
            Collections.singleton(projectId));
        // 获取非责任人的其他成员
        List<AuditProjectMember> otherMembers = new LinkedList<>();
        if (CollUtil.isNotEmpty(members)) {
            members.forEach(item -> {
                Integer role = item.getRole();
                if (!Objects.equals(role, AuditProjectRoleConstant.OWNER)) {
                    otherMembers.add(item);
                }
            });
        }

        // key: orgId-role 组成的联合键,value: 当前人员拥有的角色
        Map<String, AuditProjectMember> orgIdAndMemberMap;
        if (CollUtil.isNotEmpty(otherMembers)) {
            orgIdAndMemberMap = otherMembers.stream()
                .collect(
                    Collectors.toMap(k -> this.getUniqueKey(k.getOrgId(), k.getRole()), v -> v));
        } else {
            orgIdAndMemberMap = Collections.emptyMap();
        }

        List<AuditProjectMember> needSaveList = new LinkedList<>();

        appendMemberList.forEach(item -> {
            String orgId = item.getOrgId();
            Integer role = item.getRole();
            String remark = item.getRemark();

            String uniqueKey = this.getUniqueKey(orgId, role);
            AuditProjectMember member = orgIdAndMemberMap.get(uniqueKey);

            if (Objects.isNull(member)) {
                AuditProjectMember newOne = new AuditProjectMember();
                newOne.setOrgId(orgId);
                newOne.setRole(role);
                newOne.setRemark(remark);
                newOne.setProjectId(projectId);
                needSaveList.add(newOne);
                log.info("当前项目[{}]添加项目成员[{}]成员角色[{}]", projectId, orgId, role);
            } else {
                log.info("当前项目[{}]项目成员[{}]成员角色[{}]已存在,无需添加", projectId, orgId,
                    role);
            }
        });

        if (CollUtil.isNotEmpty(needSaveList)) {
            log.info("新保存[{}]条项目成员数据", needSaveList.size());
            this.dao.saveBatch(needSaveList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMembers4Import(AuditProjectMember4ImportSaveParamWrapperDTO param) {
        List<AuditProjectMember4ImportSaveParamDTO> list = param.getList();
        String operatorId = param.getOperatorId();

        if (CollUtil.isNotEmpty(list)) {
            List<Long> projectIds = list.stream()
                .map(AuditProjectMember4ImportSaveParamDTO::getProjectId).distinct().collect(
                    Collectors.toList());

            List<AuditProjectMember> members = this.dao.findExistsMemberByProjectIds(projectIds);
            Map<Long, Boolean> projectIdAndExistsFlagMap;
            if (CollUtil.isNotEmpty(members)) {
                projectIdAndExistsFlagMap = members.stream().map(AuditProjectMember::getProjectId)
                    .distinct().collect(Collectors.toMap(v -> v, v -> true));

                log.warn("项目 {} 已存在项目成员,导入时不再更新",
                    projectIdAndExistsFlagMap.keySet());
            } else {
                projectIdAndExistsFlagMap = Collections.emptyMap();
            }

            List<AuditProjectMember> needSaveList = new LinkedList<>();

            list.forEach(item -> {
                Long projectId = item.getProjectId();

                boolean isExists = MapUtils.getBooleanValue(projectIdAndExistsFlagMap, projectId,
                    false);
                if (isExists) {
                    log.debug("项目 {} 已存在项目成员,导入时不再更新", projectId);
                } else {
                    AuditProjectMember newOne = new AuditProjectMember();
                    newOne.setOrgId(item.getOrgId());
                    newOne.setProjectId(projectId);
                    newOne.setRole(item.getRole());
                    needSaveList.add(newOne);
                }
            });

            if (CollUtil.isNotEmpty(needSaveList)) {
                this.dao.saveBatch(needSaveList);
            }
        } else {
            log.info("操作人 [{}] 当前导入数据对应项目成员为空", operatorId);
        }

    }

    private String getUniqueKey(String orgId, Integer role) {
        return String.format("%s-%s", orgId, role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProjectOwners(Collection<String> orgIds, Long projectId) {
        List<AuditProjectMember> owners = this.dao.findExistsMemberByProjectIds(
            Collections.singleton(projectId), AuditProjectRoleConstant.OWNER);
        if (CollUtil.isNotEmpty(owners)) {
            List<Long> ownerDataIds = owners.stream().map(AuditProjectMember::getId)
                .collect(Collectors.toList());
            String ownerOrgIds = owners.stream().map(AuditProjectMember::getOrgId)
                .collect(Collectors.joining(","));
            log.info("删除项目[{}]原有所有者[{}]", projectId, ownerOrgIds);
            this.dao.removeByIds(ownerDataIds);
        } else {
            log.info("当前项目[{}]没有所有者数据", projectId);
        }

        List<AuditProjectMember> needSaveOwners = orgIds.stream().map(orgId -> {
            AuditProjectMember member = new AuditProjectMember();
            member.setProjectId(projectId);
            member.setOrgId(orgId);
            member.setRole(AuditProjectRoleConstant.OWNER);
            return member;
        }).collect(Collectors.toList());
        this.dao.saveBatch(needSaveOwners);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyCascadeByProject(AuditProjectCopyResultDTO param) {
        Map<Long, Long> projectIdMap = param.getProjectIdMap();
        Set<Long> oldProjectIds = projectIdMap.keySet();

        List<AuditProjectMember> members = this.dao.findExistsMemberByProjectIds(
            oldProjectIds);
        if (CollUtil.isNotEmpty(members)) {
            List<AuditProjectMember> needSaveList = members.stream().map(item -> {
                Long oldProjectId = item.getProjectId();
                Long newProjectId = projectIdMap.get(oldProjectId);

                if (Objects.equals(AuditProjectRoleConstant.OWNER, item.getRole())) {
                    // 忽略项目负责人
                    return null;
                } else {
                    AuditProjectMember member = new AuditProjectMember();
                    member.setProjectId(newProjectId);
                    member.setOrgId(item.getOrgId());
                    member.setRole(item.getRole());
                    member.setRemark(null);

                    return member;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(needSaveList)) {
                log.info("复制项目 {} 下项目成员共 [{}] 条数据", oldProjectIds,
                    needSaveList.size());
                this.dao.saveBatch(needSaveList);
            } else {
                log.info("复制项目 {} 下项目成员待保存数据为空,无需复制", oldProjectIds);
            }
        } else {
            log.warn("根据项目id {} 复制项目成员时,不存在成员数据", oldProjectIds);
        }
    }

    @Override
    public List<AuditProjectMemberInfoDTO> findOwnerByProjectIds(Collection<Long> projectIds) {
        if (CollUtil.isNotEmpty(projectIds)) {
            List<AuditProjectMember> members = this.dao.findExistsMemberByProjectIds(projectIds,
                AuditProjectRoleConstant.OWNER);
            if (CollUtil.isNotEmpty(members)) {
                return members.stream().map(item -> {
                    AuditProjectMemberInfoDTO dto = new AuditProjectMemberInfoDTO();
                    dto.setOrgId(item.getOrgId());
                    dto.setRole(item.getRole());
                    dto.setProjectId(item.getProjectId());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditProjectMemberInfoDTO> findMembers(Collection<Long> projectIds,
        Collection<Integer> roles) {
        if (CollUtil.isNotEmpty(projectIds) && CollUtil.isNotEmpty(roles)) {
            List<AuditProjectMember> members = this.dao.findExistsMembers(projectIds,
                roles);
            if (CollUtil.isNotEmpty(members)) {
                return members.stream().map(item -> {
                    AuditProjectMemberInfoDTO dto = new AuditProjectMemberInfoDTO();
                    dto.setOrgId(item.getOrgId());
                    dto.setRole(item.getRole());
                    dto.setProjectId(item.getProjectId());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AuditProjectMemberInfoDTO> findMemberRoles(Collection<Long> projectIds,
        Collection<String> orgIds) {
        if (CollUtil.isNotEmpty(projectIds) && CollUtil.isNotEmpty(orgIds)) {
            List<AuditProjectMember> members = this.dao.findExistsMemberRoles(projectIds,
                orgIds);
            if (CollUtil.isNotEmpty(members)) {
                return members.stream().map(item -> {
                    AuditProjectMemberInfoDTO dto = new AuditProjectMemberInfoDTO();
                    dto.setOrgId(item.getOrgId());
                    dto.setRole(item.getRole());
                    dto.setProjectId(item.getProjectId());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }
}
