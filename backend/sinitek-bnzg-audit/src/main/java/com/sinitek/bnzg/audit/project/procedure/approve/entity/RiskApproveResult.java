package com.sinitek.bnzg.audit.project.procedure.approve.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批结果 Entity
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_risk_approve_result")
@ApiModel(description = "风险点审批结果实体")
public class RiskApproveResult extends BaseAuditEntity {

    /**
     * 风险点审批id
     */
    @ApiModelProperty("风险点审批id")
    private Long approveId;

    @ApiModelProperty("审计项目id")
    private Long projectId;

    @ApiModelProperty("审计程序id")
    private Long procedureId;

    /**
     * 风险点id
     */
    @ApiModelProperty("风险点id")
    private Long riskId;

    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date opTime;

    @ApiModelProperty("顺序")
    private Integer sort;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人
     */
    @ApiModelProperty("删除人")
    private String removerId;

}
