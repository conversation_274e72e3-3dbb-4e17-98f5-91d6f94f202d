package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "项目程序")
public class AuditProjectProcedureSearchResultDTO extends
    AuditProjectProcedureSearchResultBaseDTO {

    @ApiModelProperty("查看人")
    public List<String> viewers;

    @ApiModelProperty("查看人姓名")
    public String viewerNames;

    @ApiModelProperty("编辑人")
    public List<String> editors;

    @ApiModelProperty("编辑人姓名")
    public String editorNames;

    @ApiModelProperty("审批人")
    public List<String> approvers;

    @ApiModelProperty("审批人姓名")
    public String approverNames;

}
