package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目进度
 *
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectPhaseConstant {

    /**
     * 待启动
     */
    public static final int INIT = 0;

    /**
     * 审计准备
     */
    public static final int READY = 1;

    /**
     * 审计实施
     */
    public static final int IMPL = 2;

    /**
     * 征求意见
     */
    // public static final int SUGGEST = 3;

    /**
     * 审计报告
     */
    public static final int REPORT = 4;

    /**
     * 整改跟踪
     */
    public static final int TRACKING = 5;

    /**
     * 已关闭
     */
    public static final int CLOSE = 6;

    /**
     * 已终止
     */
    public static final int STOP = 7;


}
