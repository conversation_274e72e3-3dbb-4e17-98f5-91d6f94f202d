package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 阶段步骤完成检查参数
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "阶段步骤完成检查参数")
public class StageStepFinishCheckParamDTO {

    @NotNull(message = "审计项目不能为空")
    @ApiModelProperty("审计项目id")
    private Long projectId;

    @ApiModelProperty("操作时间")
    private Date opTime;

    @ApiModelProperty("操作人")
    private String operatorId;

}
