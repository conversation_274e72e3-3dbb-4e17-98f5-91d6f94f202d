package com.sinitek.bnzg.audit.stage.log.status.step.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.stage.log.status.step.dao.StageStepExampleStatusLogDAO;
import com.sinitek.bnzg.audit.stage.log.status.step.entity.StageStepExampleStatusLog;
import com.sinitek.bnzg.audit.stage.log.status.step.service.IStageStepExampleStatusLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class StageStepExampleStatusLogServiceImpl extends
    AbstractReecordChangeLogService<StageStepExampleStatusLog, Integer> implements
    IStageStepExampleStatusLogService {

    @Autowired
    private StageStepExampleStatusLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<StageStepExampleStatusLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected StageStepExampleStatusLog generateNewOne() {
        return new StageStepExampleStatusLog();
    }

    @Override
    protected void handlerForeignKey(StageStepExampleStatusLog entity, Long foreignKey) {
        entity.setStageStepExampleId(foreignKey);
    }
}
