package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/02/2024 10:55
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectRoleConstant {

    public static final int VIEW = 1;

    public static final int EDIT = 2;

    /**
     * 审阅
     */
    public static final int APPROVE = 4;

    public static final int APPROVE_AND_EDIT = 6;

    /**
     * 所有者
     */
    public static final int OWNER = 8;

}
