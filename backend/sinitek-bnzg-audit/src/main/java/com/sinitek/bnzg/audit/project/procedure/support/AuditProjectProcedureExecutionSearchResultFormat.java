package com.sinitek.bnzg.audit.project.procedure.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpRiskExecutionListResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureExecutionListResultDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/21/2024 15:35
 */
@Component
public class AuditProjectProcedureExecutionSearchResultFormat implements
    ITableResultFormat<AuditProjectProcedureExecutionListResultDTO> {

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Override
    public List<AuditProjectProcedureExecutionListResultDTO> format(
        List<AuditProjectProcedureExecutionListResultDTO> data) {

        List<String> auditorIds = new LinkedList<>();
        data.forEach(item -> {
            String auditorId = item.getAuditorId();

            List<AuditPpRiskExecutionListResultDTO> children = item.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                children.forEach(chilItem -> {
                    auditorIds.add(chilItem.getAuditorId());
                });
            }

            auditorIds.add(auditorId);
        });

        // key: 类型值字符串
        // value: 名称
        // 风险点状态
        Map<String, String> riskStatusMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_STATUS);

        List<OrgObjectDTO> orgObjects = this.orgService.findOrgObjectsByOrgIds(auditorIds);
        Map<String, String> orgIdAndNameMap;
        if (CollUtil.isNotEmpty(orgObjects)) {
            orgIdAndNameMap = orgObjects.stream().collect(
                Collectors.toMap(OrgObjectDTO::getOrgId,
                    OrgObjectDTO::getOrgName));
        } else {
            orgIdAndNameMap = Collections.emptyMap();
        }

        data.forEach(item -> {
            String auditorId = item.getAuditorId();
            Integer status = item.getStatus();

            item.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId));
            item.setStatusName(MapUtils.getString(riskStatusMap, String.valueOf(status), ""));

            List<AuditPpRiskExecutionListResultDTO> children = item.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                children.forEach(chilItem -> {
                    String auditorId1 = chilItem.getAuditorId();
                    Integer status1 = chilItem.getStatus();

                    chilItem.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId1));
                    chilItem.setStatusName(
                        MapUtils.getString(riskStatusMap, String.valueOf(status1), ""));
                });
            }
        });

        return data;
    }
}
