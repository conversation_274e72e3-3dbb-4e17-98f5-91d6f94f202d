package com.sinitek.bnzg.audit.project.procedure.approve.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.RiskApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpRiskApproveResultSearchResultPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 风险点审批结果 Mapper
 *
 * <AUTHOR>
 * date 2024-08-30
 */
public interface AuditRiskApproveResultMapper extends BaseMapper<RiskApproveResult> {

    List<PpRiskApproveResultSearchResultPO> findPpRiskApproveResultListByApproveId(
        @Param("approveId") Long approveId);

}
