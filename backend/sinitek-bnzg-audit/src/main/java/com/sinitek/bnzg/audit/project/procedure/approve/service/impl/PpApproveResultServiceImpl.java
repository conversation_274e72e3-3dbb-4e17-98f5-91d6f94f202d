package com.sinitek.bnzg.audit.project.procedure.approve.service.impl;

import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_NOT_EXISTS_ON_APPROVE;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_SUBMITED_CANT_APPROVE;
import static com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveMessageConstant.DATA_TERMINATED_CANT_APPROVE;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectAndOpeatorIdBaseDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.constant.AuditPpApproveMessageConstant;
import com.sinitek.bnzg.audit.project.procedure.approve.dao.AuditPpApproveResultDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApprovePageParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSingleApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpRiskApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.util.AuditPpApprvResultChangeEventPublishUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.po.AuditPpApproveResultCreateParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpApproveResultSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.util.PpApproveResultConvertUtil;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.project.util.AuditProjectCheckUtil;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskStatusConstant;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParamDTO;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-11-13 16:33
 */
@Slf4j
@Service
public class PpApproveResultServiceImpl implements IPpApproveResultService {

    @Autowired
    private AuditPpApproveResultDAO dao;

    @Autowired
    private IPpApproveService ppApproveService;

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    @Autowired
    private IAuditProjectProcedureService ppService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IAuditPlanService planService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IEnumService enumService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuditPpApproveResult> save(PpApproveResultCreateParamDTO param) {
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        Long approveId = param.getApproveId();
        Long projectId = param.getProjectId();

        List<Long> ppIds = param.getPpIds();
        Map<Long, Long> ppIdAndProcedureIdMap = param.getPpIdAndProcedureIdMap();
        Map<Long, Integer> ppidAndStatusMap = param.getPpidAndStatusMap();

        if (CollUtil.isNotEmpty(ppIds)) {
            List<AuditPpApproveResult> list = this.dao.create(
                AuditPpApproveResultCreateParamPO.builder()
                    .approveId(approveId)
                    .projectId(projectId)
                    .ppIds(ppIds)
                    .ppIdAndProcedureIdMap(ppIdAndProcedureIdMap)
                    .ppidAndApproveResultMap(this.toApproveResultMap(ppidAndStatusMap))
                    .build());

            List<Long> ids = new LinkedList<>();
            Map<Long, Integer> idAndNewResultMap = new HashMap<>(list.size());

            list.forEach(item -> {
                Long id = item.getId();
                Integer approveResult = item.getApproveResult();
                ids.add(id);
                idAndNewResultMap.put(id, approveResult);
            });

            AuditPpApprvResultChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                    .foreignKeys(ids)
                    .oldValueMap(Collections.emptyMap())
                    .newValueMap(idAndNewResultMap)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("新增")
                    .build());

            return list;
        }

        return Collections.emptyList();
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    private Map<Long, Integer> toApproveResultMap(Map<Long, Integer> ppidAndStatusMap) {
        Map<Long, Integer> result = new HashMap<>(ppidAndStatusMap.size());
        ppidAndStatusMap.forEach((key, value) -> {
            if (Objects.equals(AuditRiskStatusConstant.DRAFT, value)) {
                // 草稿状态待审批
                result.put(key, AuditRiskApproveResultConstant.DRAFT);
            } else {
                // 其余均为无需审批
                result.put(key, AuditRiskApproveResultConstant.NO_NEED);
            }
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approve(PpApproveResultSingleApproveParamDTO param) {
        Long approveResultId = param.getApproveResultId();
        Integer approveResultValue = param.getApproveResult();
        String approveRemark = param.getApproveRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        log.info("操作人[{}]审批风险点,审批数据(approveResultId)[{}],审批结果: {},审批备注: {}",
            operatorId,
            approveResultId, approveResultValue, approveRemark);

        AuditPpApproveResult approveResult = this.dao.getById(approveResultId);
        if (Objects.nonNull(approveResult)) {
            Long approveId = approveResult.getApproveId();

            PpApproveBaseInfoDTO approveDTO = this.ppApproveService.getBaseInfo(approveId);
            Integer status = approveDTO.getStatus();
            if (Objects.equals(status, AuditRiskApproveStatusConstant.SUBMIT)) {
                log.error("当前审批数据approvId {} 已提交,无法再次审批", approveId);
                throw new BussinessException(DATA_SUBMITED_CANT_APPROVE);
            }
            if (Objects.equals(status, AuditRiskApproveStatusConstant.TERMINATE)) {
                log.error("当前审批数据approvId {} 已终止,无法再次审批", approveId);
                throw new BussinessException(DATA_TERMINATED_CANT_APPROVE);
            }

            Long projectId = approveDTO.getProjectId();

            AuditProjectCheckUtil.checkProjectApprover(
                AuditProjectAndOpeatorIdBaseDTO.builder()
                    .projectId(projectId)
                    .operatorId(operatorId)
                    .build(), "提交项目审计程序审批",
                AuditPpApproveMessageConstant.CANT_APPROVE_PP_NOT_APPROVER,
                AuditPpApproveMessageConstant.CANT_APPROVE_PP_NO_APPROVER);

            Long id = approveResult.getId();
            Integer oldApproveResult = approveResult.getApproveResult();
            approveResult.setOperatorId(operatorId);
            approveResult.setApproveResult(approveResultValue);
            approveResult.setApproveRemark(approveRemark);
            approveResult.setOpTime(opTime);

            this.dao.updateById(approveResult);

            AuditPpApprvResultChangeEventPublishUtil.publishEvent(
                RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(id)
                    .oldValue(oldApproveResult)
                    .newValue(approveResultValue)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("审批")
                    .build());
        } else {
            log.error("被审批的数据 {} 在数据库中不存在", approveResultId);
            throw new BussinessException(DATA_NOT_EXISTS_ON_APPROVE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchApprove(PpApproveResultBatchApproveParamDTO param) {
        List<Long> approveResultIds = param.getPpApproveResultIds();
        Integer approveResultValue = param.getApproveResult();
        String approveRemark = param.getApproveRemark();
        String operatorId = param.getOperatorId();
        Date opTime = param.getOpTime();

        if (CollUtil.isEmpty(approveResultIds)) {
            log.warn(
                "操作人[{}]批量审批项目审计程序,审批数据(approveResultIds)为空,不再继续,审批结果: {},审批备注: {}",
                operatorId, approveResultValue, approveRemark);
            return Collections.emptyList();
        }

        log.info(
            "操作人[{}]批量审批项目审计程序,审批数据(approveResultIds)[{}],审批结果: {},审批备注: {}",
            operatorId,
            approveResultIds, approveResultValue, approveRemark);

        List<AuditPpApproveResult> approveResults = this.dao.listByIds(approveResultIds);
        if (CollUtil.isNotEmpty(approveResults)) {
            AuditPpApproveResult firstPpApproveResult = approveResults.get(0);
            Long approveId = firstPpApproveResult.getApproveId();

            PpApproveBaseInfoDTO approveDTO = this.ppApproveService.getBaseInfo(approveId);
            Integer status = approveDTO.getStatus();
            if (Objects.equals(status, AuditRiskApproveStatusConstant.SUBMIT)) {
                log.error("当前审批数据approvId {} 已提交,无法再次审批", approveId);
                throw new BussinessException(DATA_SUBMITED_CANT_APPROVE);
            }
            if (Objects.equals(status, AuditRiskApproveStatusConstant.TERMINATE)) {
                log.error("当前审批数据approvId {} 已终止,无法再次审批", approveId);
                throw new BussinessException(DATA_TERMINATED_CANT_APPROVE);
            }

            Long projectId = approveDTO.getProjectId();

            AuditProjectCheckUtil.checkProjectApprover(
                AuditProjectAndOpeatorIdBaseDTO.builder()
                    .projectId(projectId)
                    .operatorId(operatorId)
                    .build(), "提交项目审计程序批量审批",
                AuditPpApproveMessageConstant.CANT_APPROVE_PP_NOT_APPROVER,
                AuditPpApproveMessageConstant.CANT_APPROVE_PP_NO_APPROVER);

            List<Long> foreignKeys = new LinkedList<>();
            Map<Long, Integer> idAndOldApproveResultMap = new LinkedHashMap<>();

            approveResults.forEach(approveResult -> {
                Long id = approveResult.getId();
                Integer oldApproveResult = approveResult.getApproveResult();
                approveResult.setOperatorId(operatorId);
                approveResult.setApproveResult(approveResultValue);
                approveResult.setApproveRemark(approveRemark);
                approveResult.setOpTime(opTime);

                foreignKeys.add(id);
                idAndOldApproveResultMap.put(id, oldApproveResult);
            });

            this.dao.updateBatchById(approveResults);

            AuditPpApprvResultChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParamDTO.<Integer>builder()
                    .foreignKeys(foreignKeys)
                    .oldValueMap(idAndOldApproveResultMap)
                    .newValue(approveResultValue)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark("批量审批")
                    .build());

            return foreignKeys;
        } else {
            log.error("被审批的项目审计程序数据 {} 在数据库中不存在", approveResultIds);
            throw new BussinessException(DATA_NOT_EXISTS_ON_APPROVE);
        }
    }

    @Override
    public IPage<PpApproveResultSearchResultDTO> search(PpApproveResultSearchParamDTO param) {
        PpApproveResultSearchParamPO paramPO = PpApproveResultConvertUtil.makeSearchParamDTO2PO(
            param);
        IPage<PpApproveResultSearchResultPO> searchResult = this.dao.search(paramPO);

        Long approveId = paramPO.getApproveId();
        List<PpRiskApproveResultSearchResultDTO> risks = this.riskApproveResultService.findPpRiskApproveResultListByApproveId(
            approveId);

        Map<Long, List<PpRiskApproveResultSearchResultDTO>> procedureIdAndRiskMap;
        if (CollUtil.isNotEmpty(risks)) {
            procedureIdAndRiskMap = risks.stream()
                .collect(Collectors.groupingBy(PpRiskApproveResultSearchResultDTO::getProcedureId));
        } else {
            procedureIdAndRiskMap = Collections.emptyMap();
        }

        return searchResult.convert(item -> {
            PpApproveResultSearchResultDTO result = PpApproveResultConvertUtil.makeSearchResultPO2DTO(
                item);
            Long procedureId = result.getProcedureId();
            List<PpRiskApproveResultSearchResultDTO> riskChild = procedureIdAndRiskMap.get(
                procedureId);
            result.setChildren(riskChild);
            return result;
        });
    }

    @Override
    public List<PpApproveResultBaseInfoDTO> findExistByApproveId(Long approveId) {
        List<AuditPpApproveResult> list = this.dao.findExistByApproveId(approveId);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(PpApproveResultConvertUtil::makeEntity2BaseInfoDTO).collect(
                Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<PpApproveResultBaseInfoDTO> findExistByIds(Collection<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            List<AuditPpApproveResult> list = this.dao.listByIds(ids);
            if (CollUtil.isNotEmpty(list)) {
                return list.stream().map(PpApproveResultConvertUtil::makeEntity2BaseInfoDTO)
                    .collect(
                        Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public PpApproveResultDetailDTO loadDetailById(Long id) {
        AuditPpApproveResult ppApproveResult = this.dao.getById(id);
        if (Objects.nonNull(ppApproveResult)) {
            Long ppId = ppApproveResult.getPpId();
            Long projectId = ppApproveResult.getProjectId();
            Long procedureId = ppApproveResult.getProcedureId();

            AuditProjectProcedureInfoDTO pp = this.ppService.getById(ppId);
            AuditProcedureBaseInfoDTO procedureInfo = this.procedureService.getBaseInfoById(
                procedureId);
            AuditProjectInfoDTO projectInfo = this.projectService.getExistsProjectInfoById(
                projectId);

            PpApproveResultDetailDTO result = new PpApproveResultDetailDTO();
            result.setId(ppApproveResult.getId());
            result.setApproveId(ppApproveResult.getApproveId());
            if (Objects.nonNull(projectInfo)) {
                Long planId = projectInfo.getPlanId();
                result.setProjectId(projectId);
                result.setProjectName(projectInfo.getName());

                AuditPlanInfoDTO planInfo = this.planService.getInfoById(planId);

                result.setPlanId(planId);
                if (Objects.nonNull(planInfo)) {
                    result.setPlanName(planInfo.getName());
                } else {
                    log.warn("根据计划id {} 查不到具体计划数据", planId);
                }
            } else {
                log.warn("根据项目id {} 查不到具体项目数据", projectId);
            }

            result.setProcedureId(procedureId);
            if (Objects.nonNull(procedureInfo)) {
                result.setProcedureName(procedureInfo.getName());
            } else {
                log.warn("根据审计程序id {} 查不到具体审计程序数据", procedureId);
            }

            result.setPpId(ppApproveResult.getPpId());
            result.setExecution(pp.getExecution());
            result.setAuditorId(pp.getAuditorId());
            result.setAuditDate(pp.getAuditDate());
            result.setOperatorId(ppApproveResult.getOperatorId());
            result.setOpTime(ppApproveResult.getOpTime());

            String auditorId = result.getAuditorId();
            String operatorId = result.getOperatorId();

            List<String> orgIds = new LinkedList<>();
            orgIds.add(auditorId);
            orgIds.add(operatorId);

            Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
                orgIds);
            result.setAuditorName(MapUtils.getString(orgIdAndNameMap, auditorId));
            result.setOperatorName(MapUtils.getString(orgIdAndNameMap, operatorId));

            Integer approveResult = ppApproveResult.getApproveResult();
            result.setApproveResult(approveResult);

            // key: 类型值字符串
            // value: 名称
            // 审批结果
            Map<String, String> approveResultMap = this.enumService.getSirmEnumByCataLogAndType(
                AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_APPROVE_RESULT);

            result.setApproveResultName(
                MapUtils.getString(approveResultMap, String.valueOf(approveResult)));

            result.setApproveRemark(ppApproveResult.getApproveRemark());

            return result;
        }

        return null;
    }

    @Override
    public PpApprovePageParamDTO getApprovePageParam(Long ppId) {
        List<AuditPpApproveResult> list = this.dao.findApprovingByPpId(ppId);
        if (CollUtil.isNotEmpty(list)) {
            int size = list.size();
            if (size > 1) {
                log.error(
                    "根据ppId[{}]获取审批中数据,应该只有一条数据,但实际获取到[{}]条数据,无法继续审批",
                    ppId, size);
                throw new BussinessException(DATA_NOT_EXISTS_ON_APPROVE);
            }
            AuditPpApproveResult data = list.get(0);
            return PpApprovePageParamDTO.builder()
                .id(data.getId())
                .projectId(data.getProjectId())
                .procedureId(data.getProcedureId()).build();
        } else {
            log.error("根据ppId[{}]获取审批中数据,应该有一条数据,但实际获取到0条数据", ppId);
            throw new BussinessException(DATA_NOT_EXISTS_ON_APPROVE);
        }

    }
}
