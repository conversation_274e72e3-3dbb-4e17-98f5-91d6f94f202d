package com.sinitek.bnzg.audit.project.procedure.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_project_procedure")
@ApiModel(description = "项目程序")
public class AuditProjectProcedure extends BaseAuditEntity {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("审计程序id")
    private Long procedureId;

    @ApiModelProperty("执行情况")
    private String execution;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("审计人")
    private String auditorId;
    
    @ApiModelProperty("审计日期")
    private Date auditDate;

    @ApiModelProperty("审批人")
    private String approverId;

    @ApiModelProperty("审批时间")
    private Date approveTime;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人orgId
     */
    @ApiModelProperty("删除人")
    private String removerId;

    @ApiModelProperty("线索id")
    private Long threadId;

    @ApiModelProperty("最新标识")
    private Integer threadLatestFlag;
}
