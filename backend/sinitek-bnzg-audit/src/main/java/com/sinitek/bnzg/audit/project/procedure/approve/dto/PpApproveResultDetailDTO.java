package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 项目审计程序审批-返回DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(value = "项目审计程序详情DTO")
public class PpApproveResultDetailDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("项目审计程序审批id")
    private Long approveId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "程序id")
    private Long procedureId;

    @ApiModelProperty(value = "程序名称")
    private String procedureName;

    @ApiModelProperty("项目审计程序id")
    private Long ppId;

    @ApiModelProperty(value = "执行情况")
    private String execution;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_TEN)
    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批结果名")
    private String approveResultName;

    @ApiModelProperty("审批反馈")
    private String approveRemark;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    @ApiModelProperty("审批人名称")
    private String operatorName;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("审批时间")
    private Date opTime;

}
