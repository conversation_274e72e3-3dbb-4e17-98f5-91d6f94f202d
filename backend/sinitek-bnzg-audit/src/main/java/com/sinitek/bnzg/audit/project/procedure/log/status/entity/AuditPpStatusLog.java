package com.sinitek.bnzg.audit.project.procedure.log.status.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.bnzg.log.entity.AbstractRecordChangeLogEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计程序库状态日志 Entity
 *
 * <AUTHOR>
 * date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_pp_status_log")
@ApiModel(description = "项目审计程序状态变动日志")
public class AuditPpStatusLog extends AbstractRecordChangeLogEntity<Integer> {

    @ApiModelProperty("项目审计程序id")
    private Long ppId;

}