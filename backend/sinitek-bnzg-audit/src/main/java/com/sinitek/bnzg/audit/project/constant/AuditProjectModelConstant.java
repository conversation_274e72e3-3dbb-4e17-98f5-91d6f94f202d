package com.sinitek.bnzg.audit.project.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditProjectModelConstant {

    public static final String MODEL_CODE = "AUDIT_PROJECT";

    /**
     * 项目进度
     */
    public static final String PROJECT_PHASE_NAME = "projectPhase";

    public static final String PLAN_ID_NAME = "planId";

    public static final String PLAN_NAME = "planName";
}
