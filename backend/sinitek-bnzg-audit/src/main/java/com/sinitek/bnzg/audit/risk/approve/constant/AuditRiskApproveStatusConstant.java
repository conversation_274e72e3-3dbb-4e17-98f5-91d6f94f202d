package com.sinitek.bnzg.audit.risk.approve.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目风险点审批 MessageCode
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskApproveStatusConstant {

    /**
     * 待提交
     */
    public static final int NOT_SUBMIT = 0;

    /**
     * 提交
     */
    public static final int SUBMIT = 1;

    /**
     * 终止
     */
    public static final int TERMINATE = 2;
}
