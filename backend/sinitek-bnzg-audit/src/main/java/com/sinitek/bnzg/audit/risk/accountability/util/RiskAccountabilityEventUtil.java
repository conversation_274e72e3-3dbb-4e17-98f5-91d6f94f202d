package com.sinitek.bnzg.audit.risk.accountability.util;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.audit.risk.accountability.event.RiskAccountabilitySaveOrEditEvent;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 11/15/2024 17:52
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class RiskAccountabilityEventUtil {

    public static <T extends AbstractRecordChangeLogAddParamBaseDTO> void publish(
        T source) {
        LcEventUtil.publishEvent(new RiskAccountabilitySaveOrEditEvent<>(source));
    }
}
