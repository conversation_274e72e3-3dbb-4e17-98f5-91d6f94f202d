package com.sinitek.bnzg.audit.risk.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParamDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.contact.service.IRectifyContactService;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskDAO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.log.rectify.state.util.AuditRiskRectifyStateChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskScheduledTaskService;
import com.sinitek.bnzg.common.properties.BnzgGlobalProperties;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 项目风险点 Service 实现类
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@Slf4j
@Service
public class AuditRiskScheduledTaskServiceImpl implements IAuditRiskScheduledTaskService {

    @Autowired
    private AuditRiskDAO dao;

    @Autowired
    private BnzgGlobalProperties bnzgGlobalProperties;

    @Autowired
    private IRectifyContactService rectifyContactService;

    @Autowired
    private IOrgService orgService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoUpdateRectifyState2Delay() {
        Date currentDate = new Date();
        DateTime checkDate = DateUtil.parse(
            DateUtil.format(currentDate, GlobalConstant.TIME_FORMAT_TEN),
            GlobalConstant.TIME_FORMAT_TEN);

        String checkDateStr = DateUtil.format(checkDate, GlobalConstant.TIME_FORMAT_THIRTEEN);

        List<AuditRisk> list = this.dao.findExistsDelayRisk(checkDate);

        if (CollUtil.isNotEmpty(list)) {
            int size = list.size();

            List<Long> ids = new LinkedList<>();
            Map<Long, Integer> idAndOldStateMap = new HashMap<>(size);
            list.forEach(item -> {
                Long id = item.getId();
                Integer rectifyState = item.getRectifyState();

                ids.add(id);
                idAndOldStateMap.put(id, rectifyState);
            });

            log.info("[{}]查询延期的风险点数量为[{}], id: {}", checkDateStr, size, ids);

            int newState = AuditRiskRectifyStateConstant.OVERDUE_UNRECTIFIED;

            String operatorId = this.bnzgGlobalProperties.getAdministratorOrgId();

            this.dao.updateRectifyState(ids, newState, operatorId);

            AuditRiskRectifyStateChangeEventPublishUtil.publishEvent(
                RecordChangeLogBatchAddParamDTO.<Integer>builder()
                    .foreignKeys(ids)
                    .oldValueMap(idAndOldStateMap)
                    .newValue(newState)
                    .remark("审计实施-定时任务自动更新整改状态至延期")
                    .operatorId(operatorId)
                    .opTime(currentDate)
                    .build());
        } else {
            log.info("查询延期[{}]的风险点数量为空", checkDateStr);
        }
    }

}