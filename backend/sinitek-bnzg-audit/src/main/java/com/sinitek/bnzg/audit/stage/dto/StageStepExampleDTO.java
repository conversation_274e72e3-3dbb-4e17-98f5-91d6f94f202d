package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 阶段步骤实例 Entity
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "阶段步骤实例实体")
public class StageStepExampleDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 对应阶段实例id
     */
    @ApiModelProperty("对应阶段实例id")
    private Long stageExampleId;

    /**
     * 阶段步骤值
     */
    @ApiModelProperty("阶段步骤值")
    private Integer stepValue;

    /**
     * 阶段实例状态
     */
    @ApiModelProperty("阶段实例状态")
    private Integer status;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 开始处理时间
     */
    @ApiModelProperty("开始处理时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;

    /**
     * 终止时间
     */
    @ApiModelProperty("终止时间")
    private Date terminatedTime;

    /**
     * 开始处理人
     */
    @ApiModelProperty("开始处理人")
    private String starterId;

    /**
     * 结束处理人
     */
    @ApiModelProperty("结束处理人")
    private String enderId;

    /**
     * 终止处理人
     */
    @ApiModelProperty("终止处理人")
    private String terminatorId;

}
