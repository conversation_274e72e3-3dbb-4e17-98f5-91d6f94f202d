package com.sinitek.bnzg.audit.risk.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditRiskStatusUpdateParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskChangeOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDeleteOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSaveOrEditOnExecutionParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;

/**
 * 项目风险点 审计实施 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-28
 */
public interface IAuditRiskExecutionService {

    /**
     * 添加或修改项目风险点信息
     */
    Long saveOrEdit(AuditRiskSaveOrEditOnExecutionParamDTO param);

    /**
     * 变更
     */
    Long change(AuditRiskChangeOnExecutionParamDTO param);

    /**
     * 取消变更
     */
    void cancelChange(AuditRiskChangeOnExecutionParamDTO param);

    /**
     * 根据idList批量删除
     */
    void deleteByIdList(AuditRiskDeleteOnExecutionParamDTO param);

    /**
     * 审批后更新
     */
    void updateAfterApprove(AuditRiskStatusUpdateParamDTO param);

    IPage<AuditRiskSearchResultDTO> search(AuditRiskSearchParamDTO searchDTO);
}
