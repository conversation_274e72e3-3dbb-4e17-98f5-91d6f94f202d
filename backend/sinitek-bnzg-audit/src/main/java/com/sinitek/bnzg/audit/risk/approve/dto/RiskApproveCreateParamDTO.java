package com.sinitek.bnzg.audit.risk.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "风险点审批-创建参数DTO")
public class RiskApproveCreateParamDTO {

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotEmpty(message = "风险点id不能为空")
    @ApiModelProperty("风险点id")
    private List<Long> riskIds;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
