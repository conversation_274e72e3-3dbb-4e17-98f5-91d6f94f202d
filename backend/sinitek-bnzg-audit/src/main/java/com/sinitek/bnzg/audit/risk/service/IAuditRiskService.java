package com.sinitek.bnzg.audit.risk.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.FindAllAuditRiskResultDTO;
import com.sinitek.bnzg.audit.risk.dto.AdjustInnerCategoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditProcedureRiskRefBaseDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditProjectRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBatchSaveParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskDetailDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskHistoryResultDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskRefCountDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchParamDTO;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskSearchResultDTO;
import com.sinitek.bnzg.audit.risk.dto.InnerCategoryListParamDTO;
import java.util.Collection;
import java.util.List;

/**
 * 项目风险点 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-28
 */
public interface IAuditRiskService {

    void batchSave(AuditRiskBatchSaveParamDTO param);

    /**
     * 分页列表查询
     */
    IPage<AuditRiskSearchResultDTO> search(AuditRiskSearchParamDTO searchDTO);

    AuditRiskDetailDTO loadDetail(Long id);

    List<AuditRiskBaseInfoDTO> findExistsByIds(Collection<Long> ids);

    List<AuditRiskRefCountDTO> findRiskRefCountByProcedureIds(Collection<Long> procedureIds);

    List<AuditProjectRiskRefCountDTO> findProjectRiskRefCountByProjectIdAndProcedureIds(
        Long projectId,
        Collection<Long> procedureIds);

    List<AuditRiskBaseInfoDTO> findProjectRiskInfoByProjectIdAndProcedureIds(
        Long projectId,
        Collection<Long> procedureIds);

    List<String> findInnerCategory();

    List<String> findInnerCategoryByYear(InnerCategoryListParamDTO param);

    void updateBatchInnerCategory(AdjustInnerCategoryParamDTO param);

    List<AuditProcedureRiskRefBaseDTO> findProcedureRiskRefByProcedureIds(
        Collection<Long> procedureIds);

    List<AuditRiskHistoryResultDTO> findRiskHistory(AuditRiskHistoryParamDTO param);

    List<FindAllAuditRiskResultDTO> findAllauditRisk(FindAllAuditRiskParamDTO param);

    AuditRiskDetailDTO getRiskByIdWithAcCheck(Long id);

    AuditRiskDetailDTO getFormatedData(Long riskId);

    AuditRiskBaseInfoDTO getById(Long id);

    List<AuditRiskBaseInfoDTO> findExistRisksByProjectId(Long projectId);

    List<AuditRiskBaseInfoDTO> findExistRisksByProjectIds(Collection<Long> projectIds);

}
