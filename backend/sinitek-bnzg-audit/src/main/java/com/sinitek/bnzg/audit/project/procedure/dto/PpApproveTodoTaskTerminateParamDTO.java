package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(value = "项目审计程序审批代办任务终止参数")
public class PpApproveTodoTaskTerminateParamDTO {

    @ApiModelProperty("项目审计程序审批id")
    private Long ppApproveId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

    @ApiModelProperty(value = "操作备注")
    private String opRemark;

}
