package com.sinitek.bnzg.audit.risk.statistics.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date：2024/9/25 15:31
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "风险分类统计图-返回DTO")
public class RiskStatisticsChartReasultDTO {

    @ApiModelProperty(value = "内部分类名称")
    private String innerCategory;

    @ApiModelProperty(value = "内部分类数量")
    private Integer categoryCount;

}
