package com.sinitek.bnzg.audit.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:33
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "项目成员保存参数")
public class AuditProjectMemberSaveParamDTO {

    @NotNull(message = "项目成员OrgId不能为空")
    @ApiModelProperty("成员id")
    private String orgId;

    @NotNull(message = "成员角色不能为空")
    @ApiModelProperty("角色")
    private Integer role;

    @ApiModelProperty("备注")
    private String remark;

}
