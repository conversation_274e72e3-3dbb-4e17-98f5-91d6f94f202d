package com.sinitek.bnzg.audit.common.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计程序库 Entity
 *
 * <AUTHOR>
 * date 2024-07-29
 */
@Data
@EqualsAndHashCode
@ApiModel(description = "导入参数")
public class AuditCommonImportParamDTO {

    @NotNull(message = "导入文件不能为空")
    @ApiModelProperty("附件")
    private UploadDTO upload;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
