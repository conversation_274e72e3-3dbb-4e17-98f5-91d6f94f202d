package com.sinitek.bnzg.audit.project.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.dto.AuditProjectSearchResultDTO;
import com.sinitek.bnzg.audit.project.enumation.AuditProjectPhaseEnum;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.service.IOrgService;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date：2024/12/05 14:13
 */
@Component
public class AuditProjectResultFormat extends AuditProjectSearchResultDTO implements
        ITableResultFormat<AuditProjectSearchResultDTO> {

    @Autowired
    private IOrgService orgService;

    @Override
    public List<AuditProjectSearchResultDTO> format(List<AuditProjectSearchResultDTO> data) {
        if (CollUtil.isEmpty(data)) {
            return data;
        }
        List<String> orgIds = new LinkedList<>();
        List<String> ids = data.stream()
                .map(item -> item.getOrgIds())
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());


        orgIds.addAll(ids);

        Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
                orgIds.stream().distinct().collect(Collectors.toList()));

        data.forEach(item -> {

            String respDeptNames = Optional.ofNullable(item.getOrgIds())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                    .collect(Collectors.joining(","));
            item.setOwnerNames(respDeptNames);
            item.setProjectPhaseName(AuditProjectPhaseEnum.getByValue(item.getProjectPhase()).getName());

        });

        return data;
    }
}
