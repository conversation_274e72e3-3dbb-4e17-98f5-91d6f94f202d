package com.sinitek.bnzg.audit.project.procedure.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveSubmitParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditPpApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditRiskApprvResultChangeEvent;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproveService;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.bnzg.audit.project.procedure.approve.support.IApproveResultBase;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveStatusConstant;
import com.sinitek.bnzg.audit.risk.approve.dto.RiskApproveResultBaseInfoDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.log.event.AbstractRecordChangeLogEvent;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditPpOrRiskApproveEventListener {

    @Autowired
    private IPpApproveService ppApproveService;

    @Autowired
    private IPpApproveResultService ppApproveResultService;

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    /**
     * 监听审计程序或风险点审批完成事件,触发审批批次提交事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = {
        AuditPpApprvResultChangeEvent.class,
        AuditRiskApprvResultChangeEvent.class}, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen4TodoTask(
        AbstractRecordChangeLogEvent<T> rowEvent) {
        Tuple3<Long, String, Date> dataDesc = this.getDataDesc(rowEvent);
        Long key = dataDesc.getT1();
        String openratorId = dataDesc.getT2();
        Date opTime = dataDesc.getT3();
        if (rowEvent instanceof AuditPpApprvResultChangeEvent) {
            // 审计程序审批完成
            List<PpApproveResultBaseInfoDTO> ppApproveResults = this.ppApproveResultService.findExistByIds(
                Collections.singletonList(key));
            if (CollUtil.isNotEmpty(ppApproveResults)) {
                PpApproveResultBaseInfoDTO result = ppApproveResults.get(0);
                Long approveId = result.getApproveId();
                this.checkOrSubmitApprove(approveId, openratorId, opTime);
            } else {
                log.error(
                    "监听到审计程序审批结果事件,根据 {} 找不到对应的审计程序审批结果数据,无法提交审批批次",
                    key);
            }
        } else {
            // 风险点完成
            List<RiskApproveResultBaseInfoDTO> riskApproveResults = this.riskApproveResultService.findExistByIds(
                Collections.singletonList(key));
            if (CollUtil.isNotEmpty(riskApproveResults)) {
                RiskApproveResultBaseInfoDTO result = riskApproveResults.get(0);
                Long approveId = result.getApproveId();
                this.checkOrSubmitApprove(approveId, openratorId, opTime);
            } else {
                log.error(
                    "监听到风险点审批结果事件,根据 {} 找不到对应的风险点审批结果数据,无法提交审批批次",
                    key);
            }
        }

    }

    private void checkOrSubmitApprove(Long approveId, String operatorId, Date opTime) {
        List<PpApproveResultBaseInfoDTO> ppApproveResultList = this.ppApproveResultService.findExistByApproveId(
            approveId);
        List<RiskApproveResultBaseInfoDTO> riskApproveResultList = this.riskApproveResultService.findExistByApproveId(
            approveId);

        if (this.isAllApproveFinish(ppApproveResultList, riskApproveResultList)) {
            // 审批批次下所有审计程序，风险点均已审批
            PpApproveBaseInfoDTO baseInfo = this.ppApproveService.getBaseInfo(approveId);
            Integer status = baseInfo.getStatus();
            if (Objects.equals(AuditRiskApproveStatusConstant.NOT_SUBMIT, status)) {
                log.info("审批批次 {} 下所有审计程序，风险点均已审批,提交审批批次", approveId);
                this.ppApproveService.submit(PpApproveSubmitParamDTO.builder()
                    .approveId(approveId)
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .build());
            } else {
                log.info("审批批次 {} 下所有审计程序，风险点均已审批,审批批次已提交", approveId);
            }
        }

    }

    private boolean isAllApproveFinish(List<PpApproveResultBaseInfoDTO> ppApproveResultList,
        List<RiskApproveResultBaseInfoDTO> riskApproveResultList) {
        return this.isAllApproveFinish(ppApproveResultList) && this.isAllApproveFinish(
            riskApproveResultList);
    }

    private boolean isAllApproveFinish(List<? extends IApproveResultBase> list) {
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().allMatch(item -> {
                Integer approveResult = item.getApproveResult();
                return !Objects.equals(AuditRiskApproveResultConstant.DRAFT, approveResult);
            });
        }
        return true;
    }

    private <T extends AbstractRecordChangeLogAddParamBaseDTO> Tuple3<Long, String, Date> getDataDesc(
        AbstractRecordChangeLogEvent<T> rowEvent) {
        T source = rowEvent.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            RecordChangeLogAddParamDTO<T> data = (RecordChangeLogAddParamDTO<T>) source;
            return Tuples.of(data.getForeignKey(), data.getOperatorId(), data.getOpTime());
        } else {
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> data = ((AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source);
            Collection<Long> foreignKeys = data.getForeignKeys();
            Long oneKey = null;
            if (CollUtil.isNotEmpty(foreignKeys)) {
                oneKey = foreignKeys.iterator().next();
            }
            String operatorId = data.getOperatorId(oneKey);
            Date opTime = data.getOpTime(oneKey);
            return Tuples.of(oneKey, operatorId, opTime);
        }
    }

}
