package com.sinitek.bnzg.audit.project.procedure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.project.procedure.entity.AuditProjectProcedure;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureExecutionListResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.PpExecutionSearchParamPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:40
 */
public interface AuditPpExecutionMapper extends BaseMapper<AuditProjectProcedure> {

    /**
     * 查询没有风险点的审计程序和附带审计程序相关数据的风险点数据
     */
    IPage<AuditProjectProcedureExecutionListResultPO> searchExecutionList(
        Page<AuditProjectProcedureInfoPO> page, @Param("param") PpExecutionSearchParamPO param);
}
