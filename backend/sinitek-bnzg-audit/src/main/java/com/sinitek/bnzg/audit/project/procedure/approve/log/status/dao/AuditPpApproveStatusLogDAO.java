package com.sinitek.bnzg.audit.project.procedure.approve.log.status.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.entity.AuditPpApprvStatusLog;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.mapper.AuditPpApprvStatusLogMapper;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 10:44
 */
@Service
public class AuditPpApproveStatusLogDAO extends
    ServiceImpl<AuditPpApprvStatusLogMapper, AuditPpApprvStatusLog> {

    public List<AuditPpApprvStatusLog> findByApproveId(Long approveId) {
        LambdaQueryWrapper<AuditPpApprvStatusLog> queryWrapper = Wrappers.lambdaQuery(
            AuditPpApprvStatusLog.class);
        queryWrapper.eq(AuditPpApprvStatusLog::getApproveId, approveId);
        return this.list(queryWrapper);
    }

}
