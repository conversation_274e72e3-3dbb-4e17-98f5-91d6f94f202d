package com.sinitek.bnzg.audit.project.procedure.approve.log.status.service.impl;

import com.sinitek.bnzg.log.service.impl.AbstractReecordChangeLogService;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.dao.AuditPpApproveStatusLogDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.entity.AuditPpApprvStatusLog;
import com.sinitek.bnzg.audit.project.procedure.approve.log.status.service.IAuditPpApprvStatusChangeLogService;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 07/29/2024 13:01
 */
@Slf4j
@Service
public class AuditPpApprvStatusChangeLogServiceImpl extends
    AbstractReecordChangeLogService<AuditPpApprvStatusLog, Integer> implements
    IAuditPpApprvStatusChangeLogService {

    @Autowired
    private AuditPpApproveStatusLogDAO dao;


    @Override
    protected boolean saveBatch(Collection<AuditPpApprvStatusLog> list) {
        return this.dao.saveBatch(list);
    }

    @Override
    protected AuditPpApprvStatusLog generateNewOne() {
        return new AuditPpApprvStatusLog();
    }

    @Override
    protected void handlerForeignKey(AuditPpApprvStatusLog entity, Long foreignKey) {
        entity.setApproveId(foreignKey);
    }
}
