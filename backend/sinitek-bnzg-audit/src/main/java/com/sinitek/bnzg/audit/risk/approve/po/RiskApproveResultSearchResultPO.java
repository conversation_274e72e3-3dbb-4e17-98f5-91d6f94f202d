package com.sinitek.bnzg.audit.risk.approve.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风险点审批-返回DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "风险点审批-查询结果PO")
public class RiskApproveResultSearchResultPO {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 风险点审批id
     */
    @ApiModelProperty("风险点审批id")
    private Long approveId;

    /**
     * 风险点id
     */
    @ApiModelProperty("风险点id")
    private Long riskId;

    @ApiModelProperty("风险点名称")
    private String riskName;

    @ApiModelProperty(value = "程序id")
    private Long procedureId;

    @ApiModelProperty(value = "程序名称")
    private String procedureName;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "类型名")
    private String typeName;

    @ApiModelProperty(value = "风险等级")
    private Integer level;

    @ApiModelProperty(value = "风险等级名")
    private String levelName;

    @ApiModelProperty(value = "内部分类")
    private String innerCategory;

    @ApiModelProperty(value = "审计人")
    private String auditorId;

    @ApiModelProperty(value = "审计人名称")
    private String auditorName;

    @ApiModelProperty(value = "审计日期")
    private Date auditDate;

    /**
     * 审批结果
     */
    @ApiModelProperty("审批结果")
    private Integer approveResult;

    @ApiModelProperty("审批结果名称")
    private String approveResultName;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String operatorId;

    @ApiModelProperty("审批人名称")
    private String operatorName;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date opTime;

}
