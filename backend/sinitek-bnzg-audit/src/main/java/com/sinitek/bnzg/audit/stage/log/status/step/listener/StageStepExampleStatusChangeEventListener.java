package com.sinitek.bnzg.audit.stage.log.status.step.listener;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.listener.AbstractRecordChangeLogEventListener;
import com.sinitek.bnzg.audit.stage.log.status.step.event.StageStepExampleStatusChangeEvent;
import com.sinitek.bnzg.audit.stage.log.status.step.service.IStageStepExampleStatusLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class StageStepExampleStatusChangeEventListener<E extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEventListener<E, Integer> {

    @Autowired
    private IStageStepExampleStatusLogService logService;

    @Override
    protected String getEventName() {
        return "阶段步骤实例状态改变";
    }

    @Override
    protected IStageStepExampleStatusLogService getLogService() {
        return this.logService;
    }

    /**
     * 监听数据值改变事件
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = StageStepExampleStatusChangeEvent.class, fallbackExecution = true)
    public void listen(
        StageStepExampleStatusChangeEvent<E> event) {
        this.doListen(event);
    }
}
