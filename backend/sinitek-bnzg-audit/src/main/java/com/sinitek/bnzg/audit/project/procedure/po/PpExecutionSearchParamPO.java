package com.sinitek.bnzg.audit.project.procedure.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审计文档 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "审计文档历史分页查询DTO")
public class PpExecutionSearchParamPO extends PageDataParam {

    @ApiModelProperty("项目id")
    private Long projectId;

}
