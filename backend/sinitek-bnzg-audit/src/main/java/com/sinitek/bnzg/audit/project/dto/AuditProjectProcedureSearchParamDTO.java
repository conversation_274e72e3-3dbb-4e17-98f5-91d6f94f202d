package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.sirm.lowcode.model.dto.LcModelSearchOpParamDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/05/2024 17:30
 */
@Data
@ApiModel("审计程序查询")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AuditProjectProcedureSearchParamDTO extends LcModelSearchOpParamDTO {

    @ApiModelProperty("嵌套查询标识")
    private Boolean nestedQueryFlag;

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @NotNull(message = "分组id不能为空")
    @ApiModelProperty("分组id")
    private Long groupId;

}
