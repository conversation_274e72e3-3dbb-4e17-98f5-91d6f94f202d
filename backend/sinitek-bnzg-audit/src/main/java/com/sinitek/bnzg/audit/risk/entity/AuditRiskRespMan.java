package com.sinitek.bnzg.audit.risk.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目风险点责任人 Entity
 *
 * <AUTHOR>
 * date 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audit_risk_resp_man")
@ApiModel(description = "项目风险点责任人")
public class AuditRiskRespMan extends BaseAuditEntity {

    /**
     * 风险点id
     */
    @ApiModelProperty("风险点id")
    private Long riskId;

    /**
     * 责任人
     */
    @ApiModelProperty("责任人")
    private String respManId;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Integer removeFlag;

    /**
     * 删除人
     */
    @ApiModelProperty("删除人")
    private String removerId;

}
