package com.sinitek.bnzg.audit.project.procedure.util;

import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureExecutionListResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchParamOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditProjectProcedureSearchResultOnProjectConfigDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.LibRefProjectInfoSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.PpExecutionSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditPpHomeSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureExecutionListResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureInfoPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchParamOnProjectConfigPO;
import com.sinitek.bnzg.audit.project.procedure.po.AuditProjectProcedureSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchParamPO;
import com.sinitek.bnzg.audit.project.procedure.po.LibRefProjectInfoSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.po.PpExecutionSearchParamPO;
import com.sinitek.bnzg.common.util.CommonStringUtil;
import com.sinitek.sirm.lowcode.common.util.LcConvertUtil;
import java.util.Collection;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-11-13 21:48
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditPpConvertUtil {

    public static AuditProjectProcedureSearchParamOnProjectConfigPO makeSearchParamDTO2PO(
        AuditProjectProcedureSearchParamOnProjectConfigDTO dto) {
        Long projectId = dto.getProjectId();
        List<Integer> auditYears = dto.getAuditYears();

        AuditProjectProcedureSearchParamOnProjectConfigPO po = new AuditProjectProcedureSearchParamOnProjectConfigPO();

        po.setProjectId(projectId);
        po.setProcedureNames(CommonStringUtil.toSearchStrList(dto.getProcedureName()));
        po.setLibNames(CommonStringUtil.toSearchStrList(dto.getLibName()));
        po.setAuditYears(auditYears);
        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());

        return po;
    }

    public static PpExecutionSearchParamPO makeSearchParamDTO2PO(
        PpExecutionSearchParamDTO dto) {
        Long projectId = dto.getProjectId();

        PpExecutionSearchParamPO po = new PpExecutionSearchParamPO();

        po.setProjectId(projectId);

        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());

        return po;
    }

    public static AuditProjectProcedureSearchResultOnProjectConfigDTO makePO2ProjectConfigDTO(
        AuditProjectProcedureSearchResultPO po) {
        return LcConvertUtil.convert(po, AuditProjectProcedureSearchResultOnProjectConfigDTO::new);
    }

    public static AuditProjectProcedureInfoDTO makePO2DTO(AuditProjectProcedureInfoPO po) {
        return LcConvertUtil.convert(po, AuditProjectProcedureInfoDTO::new);
    }

    public static AuditProjectProcedureExecutionListResultDTO makePO2DTO(
        AuditProjectProcedureExecutionListResultPO po) {
        return LcConvertUtil.convert(po, AuditProjectProcedureExecutionListResultDTO::new);
    }

    public static LibRefProjectInfoSearchParamPO makeSearchParamDTO2PO(
        LibRefProjectInfoSearchParamDTO param) {
        Collection<Long> libIds = param.getLibIds();

        LibRefProjectInfoSearchParamPO po = new LibRefProjectInfoSearchParamPO();

        po.setLibIds(libIds);

        po.setOrderName(param.getOrderName());
        po.setOrderType(param.getOrderType());
        po.setPageIndex(param.getPageIndex());
        po.setPageSize(param.getPageSize());

        return po;
    }

    public static LibRefProjectInfoSearchResultDTO makePO2DTO(LibRefProjectInfoSearchResultPO po) {
        return LcConvertUtil.convert(po, LibRefProjectInfoSearchResultDTO::new);
    }

    public static AuditPpHomeSearchParamPO makeSearchParamDTO2PO(
        AuditPpHomeSearchParamDTO dto) {
        return LcConvertUtil.convert(dto, AuditPpHomeSearchParamPO::new);
    }

    public static AuditPpHomeSearchResultDTO makePO2DTO(AuditPpHomeSearchResultPO po) {
        return LcConvertUtil.convert(po, AuditPpHomeSearchResultDTO::new);
    }

}
