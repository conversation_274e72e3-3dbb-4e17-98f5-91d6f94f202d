package com.sinitek.bnzg.audit.risk.accountability.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchResultDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.DeleteAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.dto.SaveOrEditAuditRiskAcParamDTO;
import com.sinitek.bnzg.audit.risk.accountability.service.IRiskAccountabilityService;
import com.sinitek.bnzg.audit.risk.accountability.support.RiskAccountabilityResultFormat;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.lowcode.model.dto.LcIdAndIdListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import javax.validation.Valid;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date：2024/11/15 10:31
 */

@RestController
@RequestMapping("/frontend/api/audit/risk/accountability-condition")
@Api(value = "/frontend/api/audit/risk/accountability-condition", tags = "审计系统-风险点问责情况管理")
public class RiskAccountabilityController {

    @Autowired
    private IRiskAccountabilityService service;

    @Autowired
    private RiskAccountabilityResultFormat formater;

    @ApiOperation(value = "问责情况管理查询统计分页列表")
    @PostMapping("/search")
    public TableResult<AuditRiskAcSearchResultDTO> search(
        @RequestBody @Valid AuditRiskAcSearchParamDTO param) {
        IPage<AuditRiskAcSearchResultDTO> search = this.service.search(param);
        return param.build(search, this.formater);
    }

    @SuppressWarnings("squid:ControllerAnnotitionUrlCheck")
    @ApiOperation(value = "问责情况详情")
    @GetMapping("/detail/get-by-riskId")
    public RequestResult<AuditRiskAcSearchResultDTO> loadDetail(@Param("riskId") Long riskId) {
        AuditRiskAcSearchResultDTO dto = this.service.loadDetailByRiskId(riskId);
        if (Objects.nonNull(dto)) {
            this.formater.format(Collections.singletonList(dto));
        }
        return new RequestResult<>(dto);
    }

    @ApiOperation(value = "录入问责数据")
    @PostMapping("/edit")
    public RequestResult<Void> editAuditRiskAc(
        @RequestBody @Valid SaveOrEditAuditRiskAcParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.service.editAuditRiskAc(param);
        return RequestResult.success();
    }


    @ApiOperation(value = "新增问责数据")
    @PostMapping("/save")
    public RequestResult<Void> saveAuditRiskAc(
        @RequestBody @Valid SaveOrEditAuditRiskAcParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.service.saveAuditRiskAc(param);
        return RequestResult.success();
    }


    @ApiOperation(value = "删除问责数据")
    @PostMapping("/delete")
    public RequestResult<Void> deleteAuditRiskAc(@RequestBody LcIdAndIdListDTO param) {
        DeleteAuditRiskAcParamDTO deleteParam = new DeleteAuditRiskAcParamDTO();
        deleteParam.setOpTime(new Date());
        deleteParam.setOperatorId(CurrentUserFactory.getOrgId());
        deleteParam.setIds(param.mergeParam());
        this.service.deleteByIdList(deleteParam);
        return RequestResult.success();
    }


}
