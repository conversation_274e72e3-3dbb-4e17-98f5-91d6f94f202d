package com.sinitek.bnzg.audit.stage.service;

import com.sinitek.bnzg.audit.stage.dto.GenerateAuditStageExampleParamDTO;
import com.sinitek.bnzg.audit.stage.dto.GenerateStageExample4ImportParamDTO;
import com.sinitek.bnzg.audit.stage.dto.ReStartAuditStageParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleDetailLoadParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageExampleResultDTO;

/**
 * 阶段实例 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-15
 */
public interface IAuditStageExampleService {

    Long generateAuditStageExample(GenerateAuditStageExampleParamDTO param);

    void generateStageExample4Import(GenerateStageExample4ImportParamDTO param);

    void reStartAuditStage(ReStartAuditStageParamDTO param);

    StageExampleResultDTO loadDetail(StageExampleDetailLoadParamDTO param);

}
