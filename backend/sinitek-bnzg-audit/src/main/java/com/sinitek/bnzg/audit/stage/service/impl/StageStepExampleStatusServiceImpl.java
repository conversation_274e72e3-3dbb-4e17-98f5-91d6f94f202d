package com.sinitek.bnzg.audit.stage.service.impl;

import static com.sinitek.bnzg.audit.doc.plan.constant.DocNameConstant.REGULATORY_DOC_TYPE;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant.DESCRIPTION_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant.RECTIFY_RESULT_UPLOAD_TYPE;
import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.CANT_FIND_STAGE_STEP_EXAMPLE_DATA;
import static com.sinitek.bnzg.audit.stage.constant.StageMessageCodeConstant.CANT_MANUALLY_FINISH_BECAUSE_OF_CURRENT_STATUS;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.doc.constant.DocTypeConstant;
import com.sinitek.bnzg.audit.doc.dto.AttachmentDocSyncParamDTO;
import com.sinitek.bnzg.audit.doc.dto.DocumentBaseDTO;
import com.sinitek.bnzg.audit.doc.service.IDocumentService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.dto.AuditProjectProcedureInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditPpUploadConstant;
import com.sinitek.bnzg.audit.project.procedure.constant.AuditProjectProcedureConstant;
import com.sinitek.bnzg.audit.project.procedure.service.IAuditProjectProcedureService;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskConstant;
import com.sinitek.bnzg.audit.risk.dto.AuditRiskBaseInfoDTO;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskService;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepSortConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dao.StageStepExampleDAO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleFinishParamDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.entity.StageStepExample;
import com.sinitek.bnzg.audit.stage.enumation.StageStepStatusEnum;
import com.sinitek.bnzg.audit.stage.log.status.step.util.StageStepExampleStatusChangeEventPublishUtil;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStepStatusUtil;
import com.sinitek.bnzg.log.dto.RecordChangeLogBatchAddParam2DTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 08/19/2024 10:22
 */
@Slf4j
@Service
public class StageStepExampleStatusServiceImpl implements IStageStepExampleStatusService {

    @Autowired
    private StageStepExampleDAO dao;

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IDocumentService documentService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditRiskService auditRiskService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IAuditProjectProcedureService auditProjectProcedureService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manuallyFinish(
        StageStepExampleFinishParamDTO param) {
        Long id = param.getId();

        StageStepExample stageStepExample = this.dao.getById(id);
        if (Objects.isNull(stageStepExample)) {
            log.error("根据阶段步骤实例id[{}]无法获取阶段步骤实例数据", id);
            throw new BussinessException(CANT_FIND_STAGE_STEP_EXAMPLE_DATA);
        }

        Integer currentStatus = stageStepExample.getStatus();
        int newStatus = StageStepStatusConstant.FINISHED;
        boolean canChange = StageStepStatusUtil.checkCanChangeStatus(currentStatus, newStatus);
        if (!canChange) {
            Map<Integer, String> valueAndNameMap = StageStepStatusEnum.getValueAndNameMap();
            String statusName = MapUtils.getString(valueAndNameMap, currentStatus,
                String.valueOf(currentStatus));
            log.error(
                "手动完成[{}]阶段步骤实例失败: 当前阶段步骤实例状态为[{},{}]无法变更为完成阶段", id,
                currentStatus, statusName);
            throw new BussinessException(CANT_MANUALLY_FINISH_BECAUSE_OF_CURRENT_STATUS,
                statusName);
        }
        Long stageExampleId = stageStepExample.getStageExampleId();
        Integer stepValue = stageStepExample.getStepValue();

        if (Objects.equals(stepValue, StageStepConstant.AUDIT_REPORT)) {
            // 审计报告完成,找到审计准备与审计实施步骤
            List<StageStepExampleDTO> stepExampleServiceByStageExampleIds = this.stageStepExampleService.findByStageExampleIds(
                Collections.singleton(stageExampleId));

            String opOrgId = param.getOpOrgId();
            Date opTime = param.getOpTime();
            for (StageStepExampleDTO stepExample : stepExampleServiceByStageExampleIds) {
                Long stepExampleId = stepExample.getId();
                Integer stepExampleStepValue = stepExample.getStepValue();
                if (Objects.equals(stepExample.getStepValue(), StageStepConstant.AUDIT_PREPARING)
                    || Objects.equals(stepExample.getStepValue(),
                    StageStepConstant.AUDIT_EXECUTION)) {
                    log.info("审计报告步骤[{}]完成,提前完成步骤[id:{},stepValue:{}]", id,
                        stepExampleId, stepExampleStepValue);
                    this.doFinishStepExample(StageStepExampleFinishParamDTO.builder()
                        .id(stepExampleId)
                        .opOrgId(opOrgId)
                        .opTime(opTime)
                        .build());
                }
            }
        }

        this.doFinishStepExample(param);
    }

    private void doFinishStepExample(StageStepExampleFinishParamDTO param) {
        Long id = param.getId();
        String opOrgId = param.getOpOrgId();
        Date opTime = param.getOpTime();

        StageStepExample stageStepExample = this.dao.getById(id);

        Integer currentStatus = stageStepExample.getStatus();
        int newStatus = StageStepStatusConstant.FINISHED;
        boolean canChange = StageStepStatusUtil.checkCanChangeStatus(currentStatus, newStatus);
        if (!canChange) {
            Map<Integer, String> valueAndNameMap = StageStepStatusEnum.getValueAndNameMap();
            String statusName = MapUtils.getString(valueAndNameMap, currentStatus,
                String.valueOf(currentStatus));
            log.warn(
                "完成[{}]阶段步骤实例校验不通过: 当前阶段步骤实例状态为[{},{}]无法变更为完成阶段",
                id,
                currentStatus, statusName);
            return;
        }
        Long projectId = stageStepExample.getProjectId();

        // 同步项目上报时间
        this.syncRegulatoryTime(projectId);

        //同步风险点附件到文档表
        this.syncRiskDoc(projectId, opOrgId, stageStepExample, opTime);

        //同步审计程序附件到文档表（审计实施完成）
        if (stageStepExample.getStepValue().equals(StageStepSortConstant.AUDIT_EXECUTION_SORT)) {
            this.syncProcedureDoc(projectId, opOrgId, opTime);
        }

        this.doChangeStageExampleStatus(StageStepExampleStatusChangeParamDTO.builder()
            .ids(Collections.singletonList(id))
            .opOrgId(opOrgId)
            .opTime(opTime)
            .opRemark("手动完成")
            .newStatus(newStatus)
            .publishEventFlag(true)
            .build());
    }

    private void syncProcedureDoc(Long projectId, String opOrgId, Date opTime) {
        List<AuditProjectProcedureInfoDTO> projectProcedures =
            this.auditProjectProcedureService.findByProjectId(projectId);
        if (CollUtil.isNotEmpty(projectProcedures)) {
            for (AuditProjectProcedureInfoDTO projectProcedure : projectProcedures) {
                AttachmentDocSyncParamDTO dto = new AttachmentDocSyncParamDTO();
                dto.setProjectId(projectId);
                dto.setProcedureId(projectProcedure.getProcedureId());
                dto.setSourceName(AuditProjectProcedureConstant.DEFAULT_SOURCE_NAME);
                dto.setSourceId(projectProcedure.getId());
                dto.setUploaderId(opOrgId);
                dto.setOpTime(opTime);
                dto.addAttachmentTypeAndDocType(
                    AuditPpUploadConstant.INVESTIGATION_RESULT_UPLOAD_TYPE,
                    DocTypeConstant.AUDIT_EVIDENCE);
                dto.addAttachmentTypeAndDocType(
                    AuditPpUploadConstant.PUNISH_OPINION_UPLOAD_TYPE,
                    DocTypeConstant.INTERVIEW_RECORD);
                dto.addAttachmentTypeAndDocType(AuditPpUploadConstant.PUNISH_NOTICE_UPLOAD_TYPE,
                    DocTypeConstant.SURVEY_QUESTIONNAIRE);
                dto.addAttachmentTypeAndDocType(
                    AuditPpUploadConstant.RECONSIDERATION_UPLOAD_TYPE,
                    DocTypeConstant.ADVICE_REQUEST);
                this.documentService.syncDoc(dto);
            }
        }
    }

    private void syncRiskDoc(Long projectId, String opOrgId, StageStepExample stageStepExample,
        Date opTime) {
        List<AuditRiskBaseInfoDTO> risks = auditRiskService.findExistRisksByProjectId(projectId);
        if (CollUtil.isNotEmpty(risks)) {
            for (AuditRiskBaseInfoDTO risk : risks) {
                AttachmentDocSyncParamDTO dto = new AttachmentDocSyncParamDTO();
                dto.setProjectId(projectId);
                dto.setProcedureId(risk.getProcedureId());
                dto.setRiskId(risk.getId());
                dto.setSourceName(AuditRiskConstant.DEFAULT_SOURCE_NAME);
                dto.setSourceId(risk.getId());
                dto.setUploaderId(opOrgId);
                dto.setOpTime(opTime);
                if (stageStepExample.getStepValue()
                    .equals(StageStepSortConstant.AUDIT_EXECUTION_SORT)) {
                    //问题描述附件
                    dto.addAttachmentTypeAndDocType(DESCRIPTION_UPLOAD_TYPE,
                        DocTypeConstant.ISSUE_DESC_ATTACHMENT);
                    this.documentService.syncDoc(dto);
                } else if (stageStepExample.getStepValue()
                    .equals(StageStepSortConstant.RECTIFICATION_TRACKING_SORT)) {
                    //整改反馈附件
                    dto.addAttachmentTypeAndDocType(RECTIFY_RESULT_UPLOAD_TYPE,
                        DocTypeConstant.RECTIFY_FEEDBACK_ATTACHMENT);
                    this.documentService.syncDoc(dto);
                }
            }
        }
    }

    /**
     * 审计项目不存在上报时间，同步对应文档上报时间
     */
    private void syncRegulatoryTime(Long projectId) {
        DocumentBaseDTO doc = this.documentService.getThreadLatestDoc(projectId, null, null,
            REGULATORY_DOC_TYPE);
        if (Objects.nonNull(doc)) {
            Date regulatoryTime = doc.getRegulatoryTime();
            AuditProjectInfoDTO auditProject = this.projectService.getExistsProjectInfoById(
                projectId);
            Date projectRegulatoryTime = auditProject.getRegulatoryTime();
            if (Objects.isNull(projectRegulatoryTime)
                && Objects.nonNull(regulatoryTime)) {
                this.projectService.updateRegulatoryTimeById(projectId, regulatoryTime);
                log.info("项目[{}]不存在上报时间,同步对应文档上报时间[{}]", projectId,
                    regulatoryTime);
            } else {
                log.warn("项目[{}]上报时间[{}},对应文档上报时间[{}]不匹配", projectId,
                    projectRegulatoryTime, regulatoryTime);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(StageStepExampleStatusChangeParamDTO param) {
        this.doChangeStageExampleStatus(param);
    }

    private void doChangeStageExampleStatus(StageStepExampleStatusChangeParamDTO param) {
        List<Long> ids = param.getIds();
        Integer newStatus = param.getNewStatus();
        String opOrgId = param.getOpOrgId();
        Date opTime = param.getOpTime();
        String opNote = param.getOpRemark();
        Boolean publishEventFlag = param.getPublishEventFlag();
        Boolean force = param.getForce();

        List<StageStepExample> stageStepExamples = this.dao.listByIds(ids);
        if (CollUtil.isNotEmpty(stageStepExamples)) {

            List<StageStepExample> needUpdateList;
            if (Objects.equals(force, Boolean.TRUE)) {
                needUpdateList = stageStepExamples;
                log.warn("操作人[{}]强制更新步骤实例[{}]状态,参数: {}", opOrgId, ids,
                    JsonUtil.toJsonString(param));
            } else {
                needUpdateList = stageStepExamples.stream().filter(item -> {
                    Integer status = item.getStatus();
                    return StageStepStatusUtil.checkCanChangeStatus(status, newStatus);
                }).collect(Collectors.toList());
            }

            if (CollUtil.isNotEmpty(needUpdateList)) {
                Map<Long, Integer> idAndOldStatusMap = new LinkedHashMap<>();
                Map<Long, Integer> idAndNewStatusMap = new LinkedHashMap<>();
                List<Long> needUpdateIds = new LinkedList<>();

                needUpdateList.forEach(item -> {
                    Long id = item.getId();
                    Integer oldStatus = item.getStatus();
                    Date startTime = item.getStartTime();
                    Date endTime = item.getEndTime();
                    Date terminatedTime = item.getTerminatedTime();

                    item.setStatus(newStatus);
                    if (Objects.equals(StageStepStatusConstant.PROCESSING, newStatus)) {
                        if (Objects.isNull(startTime)) {
                            item.setStarterId(opOrgId);
                            item.setStartTime(opTime);
                        } else {
                            log.info(
                                "操作人[{}]于[{}]开始步骤实例[{}],已存在历史开始时间[{}],不再更新开始时间",
                                opOrgId, opTime, startTime, id);
                        }
                    }
                    if (Objects.equals(StageStepStatusConstant.FINISHED, newStatus)) {
                        if (Objects.isNull(endTime)) {
                            item.setEnderId(opOrgId);
                            item.setEndTime(opTime);
                        } else {
                            log.info(
                                "操作人[{}]于[{}]完成步骤实例[{}],已存在历史完成时间[{}],不再更新完成时间",
                                opOrgId, opTime, endTime, id);
                        }
                    }
                    if (Objects.equals(StageStepStatusConstant.TERMINATE, newStatus)) {
                        if (Objects.isNull(terminatedTime)) {
                            item.setTerminatorId(opOrgId);
                            item.setTerminatedTime(opTime);
                        } else {
                            log.info(
                                "操作人[{}]于[{}]终止步骤实例[{}],已存在历史终止时间[{}],不再更新终止时间",
                                opOrgId, opTime, terminatedTime, id);
                        }
                    }

                    needUpdateIds.add(id);
                    idAndOldStatusMap.put(id, oldStatus);
                    idAndNewStatusMap.put(id, newStatus);
                });

                this.dao.updateBatchById(needUpdateList);

                if (Objects.equals(Boolean.TRUE, publishEventFlag)) {
                    StageStepExampleStatusChangeEventPublishUtil.publishEvent(
                        RecordChangeLogBatchAddParam2DTO.<Integer>builder()
                            .foreignKeys(needUpdateIds)
                            .oldValueMap(idAndOldStatusMap)
                            .newValueMap(idAndNewStatusMap)
                            .operatorId(opOrgId)
                            .opTime(opTime)
                            .remark(opNote)
                            .build());
                } else {
                    log.info("当前无需发布阶段步骤实例[{}]状态变动事件", needUpdateIds);
                }


            } else {
                log.info(
                    "根据阶段步骤实例id[{}]获取到的阶段步骤实例状态不满足变更为[{}]的条件,没有数据更新",
                    ids, newStatus);
            }

        } else {
            log.warn("根据阶段步骤实例id[{}]获取到的阶段步骤实例数据为空,无法继续更新", ids);
        }

    }
}
