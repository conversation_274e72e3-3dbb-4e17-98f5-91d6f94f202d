package com.sinitek.bnzg.audit.risk.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.risk.dao.AuditRiskRespDeptDAO;
import com.sinitek.bnzg.audit.risk.entity.AuditRiskRespDept;
import com.sinitek.bnzg.audit.risk.log.respdept.util.AuditRiskRespDeptChangeEventPublishUtil;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRespDeptService;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 项目风险点责任部门 Service 实现类
 *
 * <AUTHOR>
 * date 2024-11-08
 */
@Slf4j
@Service
public class AuditRiskRespDeptServiceImpl implements IAuditRiskRespDeptService {


    @Autowired
    private AuditRiskRespDeptDAO deptDAO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchRespDept(Long riskId, List<String> respDeptIds) {

        List<AuditRiskRespDept> respDeptList = this.findByRiskId(riskId);
        List<String> existsDeptOrgIds = null;
        if (CollectionUtils.isNotEmpty(respDeptList)) {
            existsDeptOrgIds = respDeptList.stream().map(AuditRiskRespDept::getRespDeptId)
                .collect(Collectors.toList());
            List<Long> idList = respDeptList.stream()
                .map(AuditRiskRespDept::getId)
                .collect(Collectors.toList());
            this.deptDAO.removeByIds(idList);
        }
        List<AuditRiskRespDept> list = respDeptIds.stream()
            .filter(StringUtils::isNotBlank)
            .map(respDeptId -> {
                AuditRiskRespDept respDept = new AuditRiskRespDept();
                respDept.setRiskId(riskId);
                respDept.setRespDeptId(respDeptId);
                respDept.setRemoveFlag(CommonBooleanEnum.FALSE.getValue());
                return respDept;
            })
            .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            log.info("本次操作需保存项目风险点责任部门数据为[{}]条", list.size());
            this.deptDAO.saveBatch(list);
        } else {
            log.warn("本次操作需保存项目风险点责任部门数据为空");
        }

        if (Objects.isNull(existsDeptOrgIds) && CollUtil.isEmpty(list)) {
            log.info("本次操作项目风险点责任部门数据未变动,无需发布项目风险点责任部门变动事件");
            return;
        }

        String respDeptremark = "风险点-更新责任部门";
        if (Objects.isNull(existsDeptOrgIds)) {
            respDeptremark = "风险点-新增责任部门";
        }

        AuditRiskRespDeptChangeEventPublishUtil.publishEvent(
            RecordChangeLogAddParamDTO.<String>builder()
                .foreignKey(riskId)
                .oldValue(JsonUtil.toJsonString(existsDeptOrgIds))
                .newValue(JsonUtil.toJsonString(respDeptIds))
                .operatorId(CurrentUserFactory.getOrgId())
                .opTime(new Date())
                .remark(respDeptremark)
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatch(List<AuditRiskRespDept> auditRiskRespDepts) {
        this.deptDAO.saveOrUpdateBatch(auditRiskRespDepts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Long> ids) {
        this.deptDAO.removeByIds(ids);
    }

    @Override
    public List<AuditRiskRespDept> findByRiskIds(Collection<Long> riskIds) {
        return this.deptDAO.findByRiskIds(riskIds);
    }


    @Override
    public List<AuditRiskRespDept> findByRiskId(Long riskId) {
        return this.deptDAO.findByRiskId(riskId);
    }
}
