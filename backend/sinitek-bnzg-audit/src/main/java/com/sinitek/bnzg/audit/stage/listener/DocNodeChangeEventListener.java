package com.sinitek.bnzg.audit.stage.listener;

import static com.sinitek.bnzg.audit.stage.constant.StageStepDocConstant.STAG_DOC_DEFAULT_SOURCE_NAME;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.doc.event.DocumentEvent;
import com.sinitek.bnzg.audit.doc.event.source.AbstractDocEventBaseSourceDTO;
import com.sinitek.bnzg.audit.doc.event.source.AbstractSingleDocEventSourceDTO;
import com.sinitek.bnzg.audit.doc.event.source.DocNodeDelEventSourceDTO;
import com.sinitek.bnzg.audit.doc.event.source.DocNodeEditEventSourceDTO;
import com.sinitek.bnzg.audit.doc.event.source.DocNodeSaveEventSourceDTO;
import com.sinitek.bnzg.audit.doc.event.source.DocNodeUpdateEventSourceDTO;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleStatusChangeParamDTO;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleStatusService;
import com.sinitek.bnzg.audit.stage.util.StageStepStatusUtil;
import com.sinitek.sirm.common.event.annotation.SiniCubeEventListener;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class DocNodeChangeEventListener {

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IStageStepExampleStatusService stepExampleStatusService;

    /**
     * 监听文档保存或更新事件,将对应阶段步骤实例更新为进行中
     *
     * 该行为为同步行为
     */
    @SiniCubeEventListener
    public <T extends AbstractDocEventBaseSourceDTO> void listen(
        DocumentEvent<T> event) {
        T source = event.getSource();
        if ((source instanceof DocNodeSaveEventSourceDTO)
            || (source instanceof DocNodeUpdateEventSourceDTO)
            || (source instanceof DocNodeEditEventSourceDTO)
            || (source instanceof DocNodeDelEventSourceDTO)) {
            AbstractSingleDocEventSourceDTO singleDocEventSource = (AbstractSingleDocEventSourceDTO) source;
            String sourceName = singleDocEventSource.getSourceName();
            Long sourceId = singleDocEventSource.getSourceId();

            String operatorId = singleDocEventSource.getOperatorId();
            Date opTime = singleDocEventSource.getOpTime();

            if (Objects.equals(STAG_DOC_DEFAULT_SOURCE_NAME, sourceName)) {
                log.info("监听到阶段步骤 {} 文档变动事件,当前步骤应该变为处理中", sourceId);

                List<StageStepExampleDTO> steps = this.stageStepExampleService.findByIds(
                    Collections.singleton(sourceId));
                if (CollUtil.isNotEmpty(steps)) {

                    StageStepExampleDTO stepExample = steps.get(0);
                    Integer status = stepExample.getStatus();

                    if (StageStepStatusUtil.checkCanChangeAsProcessiongStatus(status)) {
                        Long id = stepExample.getId();
                        this.stepExampleStatusService.updateStatus(
                            StageStepExampleStatusChangeParamDTO.builder()
                                .ids(Collections.singletonList(id))
                                .newStatus(StageStepStatusConstant.PROCESSING)
                                .opOrgId(operatorId)
                                .opTime(opTime)
                                .opRemark(this.getRemark(source))
                                .publishEventFlag(true)
                                .build());
                        log.info("文档变动后,阶段步骤实例 {} 状态为 {} 变为进行中", sourceId,
                            status);
                    } else {
                        log.info("文档变动后,阶段步骤实例 {} 状态为 {} 不需要变为进行中", sourceId,
                            status);
                    }
                } else {
                    log.warn("根据阶段步骤实例id {} 无法获取到步骤实例数据", sourceId);
                }

            }
        }
    }

    private <T extends AbstractDocEventBaseSourceDTO> String getRemark(T source) {
        if (source instanceof DocNodeUpdateEventSourceDTO) {
            return "更新阶段步骤文档更新阶段步骤状态为处理中";
        } else if (source instanceof DocNodeEditEventSourceDTO) {
            return "编辑阶段步骤文档更新阶段步骤状态为处理中";
        } else if (source instanceof DocNodeDelEventSourceDTO) {
            return "删除阶段步骤文档更新阶段步骤状态为处理中";
        } else {
            return "上传阶段步骤文档更新阶段步骤状态为处理中";
        }
    }

}
