package com.sinitek.bnzg.audit.risk.statistics.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.statistics.mapper.AuditRiskStatisticsMapper;
import com.sinitek.bnzg.audit.risk.statistics.po.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date：2024/9/20 17:22
 */
@Slf4j
@Service
public class AuditRiskStatisticsDAO extends ServiceImpl<AuditRiskStatisticsMapper, AuditRisk> {
    public IPage<AuditRiskStatisticsReasultPO> search(
            Page<AuditRiskStatisticsReasultPO> page,AuditRiskStatisticsParamPO param) {
        return this.baseMapper.searchRiskStatistics(page, param);
    }

    public List<Integer> findAuditYear() {
        return this.baseMapper.findAuditYear();
    }

    public List<RiskStatisticsChartReasultPO> findRiskStatisticsChart(
            RiskStatisticsChartParamPO param){
        return this.baseMapper.findRiskStatisticsChart(param);
    }

    public  List<AuditFindRectifyChartReasultPO> findAuditFindRectifyChart(
            AuditFindRectifyChartParamPO param){
        return this.baseMapper.findAuditFindRectifyChart(param);
    }

    public IPage<AuditRiskStatisticsReasultPO> searchAuditFindRectifyChartDetail(
            Page<AuditRiskStatisticsReasultPO> page, AuditFindRectifyChartDetailParamPO param){
        return this.baseMapper.searchAuditFindRectifyChartDetail(page, param);
    }
}
