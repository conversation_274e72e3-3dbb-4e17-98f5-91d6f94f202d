package com.sinitek.bnzg.audit.stage.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/15/2024 11:08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStatusConstant {

    public static final int READY = 1;

    public static final int PROCESSING = 2;

    public static final int FINISHED = 3;

    public static final int TERMINATE = 4;

    public static final String READY_NAME = "就绪";

    public static final String PROCESSING_NAME = "处理中";

    public static final String FINISHED_NAME = "已完成";

    public static final String TERMINATE_NAME = "已中止";

}
