package com.sinitek.bnzg.audit.risk.statistics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartDetailParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditFindRectifyChartReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.AuditRiskStatisticsReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.RiskStatisticsChartParamDTO;
import com.sinitek.bnzg.audit.risk.statistics.dto.RiskStatisticsChartReasultDTO;
import com.sinitek.bnzg.audit.risk.statistics.service.IAuditRiskStatisticsService;
import com.sinitek.bnzg.audit.risk.statistics.support.AuditRiskStatisticsResultFormat;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date：2024/9/20 16:56
 */
@RestController
@RequestMapping("/frontend/api/audit/risk/statistics")
@Api(value = "/frontend/api/audit/risk/statistics", tags = "审计系统-风险点查询统计")
public class AuditRiskStatisticsController {

    @Autowired
    private IAuditRiskStatisticsService service;

    @Autowired
    private AuditRiskStatisticsResultFormat formater;

    @ApiOperation(value = "风险点查询统计分页列表")
    @PostMapping("/search")
    public TableResult<AuditRiskStatisticsReasultDTO> search(@RequestBody @Valid AuditRiskStatisticsParamDTO param) {
        IPage<AuditRiskStatisticsReasultDTO> search = service.search(param);
        return param.build(search,this.formater);
    }

    @ApiOperation(value = "查询审计年份")
    @GetMapping("/audit-year/list")
    public RequestResult<List<Integer>> listAuditYear() {
        return new RequestResult<>(this.service.findAuditYear());
    }

    @ApiOperation(value = "风险分类统计图查询")
    @PostMapping("/risk-statistics-chart/search")
    public RequestResult<List<RiskStatisticsChartReasultDTO>> searchRiskStatisticsChart(
            @RequestBody @Valid RiskStatisticsChartParamDTO param) {
        return new RequestResult<>(this.service.findRiskStatisticsChart(param));
    }

    @ApiOperation(value = "审计发现及整改进度表查询")
    @PostMapping("/audit-find-rectify-chart/search")
    public RequestResult<List<AuditFindRectifyChartReasultDTO>> searchAuditFindRectifyChart(
            @RequestBody @Valid AuditFindRectifyChartParamDTO param) {
        return new RequestResult<>(this.service.findAuditFindRectifyChart(param));
    }

    @ApiOperation(value = "审计发现及整改进度表查询明细")
    @PostMapping("/audit-find-rectify-chart/search-detail")
    public TableResult<AuditRiskStatisticsReasultDTO> searchAuditFindRectifyChartDetail(
            @RequestBody @Valid AuditFindRectifyChartDetailParamDTO param) {
        IPage<AuditRiskStatisticsReasultDTO> detail = service.searchAuditFindRectifyChartDetail(param);
        return param.build(detail,this.formater);
    }
}
