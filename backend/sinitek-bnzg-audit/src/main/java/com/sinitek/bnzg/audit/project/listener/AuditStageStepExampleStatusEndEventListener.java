package com.sinitek.bnzg.audit.project.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.dto.UpdateProjectPhaseParamDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.stage.constant.StageStepConstant;
import com.sinitek.bnzg.audit.stage.constant.StageStepStatusConstant;
import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.log.status.step.event.StageStepExampleStatusChangeEvent;
import com.sinitek.bnzg.audit.stage.service.IStageStepExampleService;
import com.sinitek.bnzg.audit.stage.util.StageStepUtil;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 08/09/2024 11:20
 */
@Slf4j
@Component
public class AuditStageStepExampleStatusEndEventListener {

    @Autowired
    private IStageStepExampleService stageStepExampleService;

    @Autowired
    private IAuditProjectService projectService;

    /**
     * 步骤实例状态变化事件
     *
     * 这里只关心完成事件,目前事件都在{@link RecordChangeLogAddParamDTO}中
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = StageStepExampleStatusChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listen(
        StageStepExampleStatusChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            this.dealSingleData((RecordChangeLogAddParamDTO) source);
        } else if (source instanceof AbstractRecordChangeLogBatchAddBaseParamDTO) {
            this.dealBatchData((AbstractRecordChangeLogBatchAddBaseParamDTO) source);
        }
    }

    private void dealBatchData(AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> source) {
        Collection<Long> foreignKeys = source.getForeignKeys();
        List<Long> needDealList = foreignKeys.stream().filter(k -> {
            Integer status = source.getNewValue(k);
            return this.isNeedDealStageStepStatus(status);
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(needDealList)) {
            // 审计准备,审计实施,审计报告 会一起完成
            // 审计报告,整改跟踪完成后会更新项目阶段
            List<StageStepExampleDTO> stageStepExamples = this.stageStepExampleService.findByIds(
                foreignKeys);
            // 这里只关注审计报告完成后开启整改跟踪
            StageStepExampleDTO reportStepExample = stageStepExamples.stream()
                .filter(item -> Objects.equals(StageStepConstant.AUDIT_REPORT, item.getStepValue())
                    || Objects.equals(StageStepConstant.RECTIFICATION_TRACKING,
                    item.getStepValue()))
                .findFirst().orElse(null);

            if (Objects.nonNull(reportStepExample)) {
                Long id = reportStepExample.getId();
                String operatorId = source.getOperatorId(id);
                Date opTime = source.getOpTime(id);

                this.dealSingleData(RecordChangeLogAddParamDTO.<Integer>builder()
                    .foreignKey(id)
                    .newValue(source.getNewValue(id))
                    .oldValue(source.getOldValue(id))
                    .operatorId(operatorId)
                    .opTime(opTime)
                    .remark(source.getRemark(id))
                    .build());
            } else {
                log.info(
                    "监听到审计步骤批量完成/终止事件,但未找到审计报告步骤实例,无法更新项目进度,数据: {}",
                    source);
            }
        }
    }

    private void dealSingleData(RecordChangeLogAddParamDTO<Integer> source) {
        Integer newValue = source.getNewValue();
        if (this.isNeedDealStageStepStatus(newValue)) {
            Long foreignKey = source.getForeignKey();
            Integer oldValue = source.getOldValue();
            String operatorId = source.getOperatorId();
            Date opTime = source.getOpTime();
            String remark = source.getRemark();

            log.info(
                "监听到审计步骤[{}] 完成/终止事件,操作人[{}],操作时间[{}],旧阶段[{}] => 新阶段[{}],备注[{}]",
                foreignKey, operatorId, opTime, oldValue, newValue, remark
            );

            List<StageStepExampleDTO> stageStepExamples = this.stageStepExampleService.findByIds(
                Collections.singleton(foreignKey));
            if (CollUtil.isNotEmpty(stageStepExamples)) {
                StageStepExampleDTO stageStepExample = stageStepExamples.get(0);

                // 更新项目阶段
                this.updateProjectPhase(stageStepExample, operatorId, opTime);
            } else {
                log.warn("根据阶段步骤实例id[{}]无法获取阶段步骤实例数据", foreignKey);
            }

        }
    }

    /**
     * 更新项目阶段
     */
    private void updateProjectPhase(StageStepExampleDTO stageStepExample, String operatorId,
        Date opTime) {

        Long projectId = stageStepExample.getProjectId();

        Integer stepValue = stageStepExample.getStepValue();
        Integer nextProjectPhase = StageStepUtil.getNextProjectPhaseByStageStepValue(
            stepValue);

        if (Objects.nonNull(nextProjectPhase)) {
            log.info("当前项目[{}]步骤值[{}]获取下一产品阶段[{}]", projectId, stepValue,
                nextProjectPhase);
            this.projectService.updateProjectPhase(UpdateProjectPhaseParamDTO.builder()
                .projectId(projectId)
                .newProjectPhase(nextProjectPhase)
                .operatorId(operatorId)
                .opTime(opTime)
                .remark("阶段步骤完成后自动更新产品阶段")
                .build());

        } else {
            log.warn("步骤[{}]完成后获取到下一个产品阶段为null,无法继续更新产品阶段",
                stepValue);
        }
    }

    private boolean isNeedDealStageStepStatus(Integer status) {
        return Objects.equals(StageStepStatusConstant.FINISHED, status);
    }
}
