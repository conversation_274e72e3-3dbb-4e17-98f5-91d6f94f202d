package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 7/11/2023 11:06 AM
 */
@Data
@SuperBuilder
@ApiModel("手动完成阶段步骤实例请求参数")
@NoArgsConstructor
@AllArgsConstructor
public class StageStepExampleFinishParamDTO {

    /**
     * 阶段步骤id
     */
    @NotNull(message = "阶段步骤实例id不能为空")
    @ApiModelProperty("阶段步骤实例id")
    private Long id;

    @ApiModelProperty("操作人(前端省略,后端自动生成)")
    private String opOrgId;

    @ApiModelProperty("操作时间(前端省略,后端自动生成)")
    private Date opTime;


}
