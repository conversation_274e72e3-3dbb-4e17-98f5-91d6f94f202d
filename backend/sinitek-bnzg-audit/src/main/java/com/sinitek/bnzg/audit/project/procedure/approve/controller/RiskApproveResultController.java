package com.sinitek.bnzg.audit.project.procedure.approve.controller;

import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApprovePageParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.RiskApproveResultSingleApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IRiskApproveResultService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风险点审批 Controller
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@RestController
@RequestMapping("/frontend/api/audit/project/risk/approve/result")
@Api(value = "/frontend/api/audit/project/risk/approve/result", tags = "审计系统-项目风险点-审批结果")
public class RiskApproveResultController {

    @Autowired
    private IRiskApproveResultService riskApproveResultService;

    @ApiOperation(value = "审批")
    @PostMapping("/save")
    public RequestResult<Void> approve(
        @RequestBody @Validated RiskApproveResultSingleApproveParamDTO param) {
        param.setOpTime(new Date());
        param.setOperatorId(CurrentUserFactory.getOrgId());
        this.riskApproveResultService.approve(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "审批详情")
    @GetMapping("/detail")
    public RequestResult<RiskApproveResultDetailDTO> loadDetail(
        @RequestParam(value = "id", required = true) Long id) {
        return new RequestResult<>(this.riskApproveResultService.loadDetail(id));
    }

    @ApiOperation(value = "加载页面参数")
    @GetMapping("/load-page-detail")
    public RequestResult<PpApprovePageParamDTO> getApprovePageParam(
        @RequestParam("riskId") Long riskId) {
        return new RequestResult<>(this.riskApproveResultService.getApprovePageParam(riskId));
    }
}
