package com.sinitek.bnzg.audit.stage.util;

import com.sinitek.bnzg.audit.stage.dto.StageStepExampleDTO;
import com.sinitek.bnzg.audit.stage.entity.StageStepExample;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 08/15/2024 16:50
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StageStepExampleConvertUtil {

    public static StageStepExampleDTO makeEntity2DTO(StageStepExample entity) {
        if (Objects.nonNull(entity)) {
            StageStepExampleDTO dto = new StageStepExampleDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }
        return null;
    }

}
