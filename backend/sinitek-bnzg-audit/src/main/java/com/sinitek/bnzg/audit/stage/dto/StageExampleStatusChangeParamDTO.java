package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 阶段实例 Entity
 *
 * <AUTHOR>
 * date 2024-08-15
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "阶段实例状态变动参数")
public class StageExampleStatusChangeParamDTO {

    @ApiModelProperty("主键")
    private List<Long> ids;

    @ApiModelProperty("新阶段实例状态")
    private Integer newStatus;

    @ApiModelProperty("操作时间")
    private Date opTime;

    @ApiModelProperty("操作人")
    private String opOrgId;

    @ApiModelProperty("操作备注")
    private String opRemark;

    @ApiModelProperty("是否抛出事件")
    private Boolean publishEventFlag;

    @ApiModelProperty("强制更新")
    private Boolean force = false;
}
