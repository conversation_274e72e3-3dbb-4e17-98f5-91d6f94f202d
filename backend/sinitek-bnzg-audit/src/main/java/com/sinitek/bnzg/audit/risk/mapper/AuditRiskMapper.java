package com.sinitek.bnzg.audit.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskParamPO;
import com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskResultPO;
import com.sinitek.bnzg.audit.risk.dto.InnerCategoryListParamDTO;
import com.sinitek.bnzg.audit.risk.entity.AuditRisk;
import com.sinitek.bnzg.audit.risk.po.AuditProcedureRiskRefPO;
import com.sinitek.bnzg.audit.risk.po.AuditProjectRiskRefCountPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskDataResultPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryResultPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskRefCountPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchParamPO;
import com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目风险点 Mapper
 *
 * <AUTHOR>
 * date 2024-08-28
 */
public interface AuditRiskMapper extends BaseMapper<AuditRisk> {

    int deleteByIds(@Param("ids") Collection<Long> ids, @Param("operatorId") String operatorId);

    /**
     * 审计实施查询
     */
    IPage<AuditRiskSearchResultPO> searchInExecution(Page<AuditRiskSearchResultPO> page,
        @Param("param") AuditRiskSearchParamPO param);

    /**
     * 整改追踪查询
     */
    IPage<AuditRiskSearchResultPO> searchInTracking(Page<AuditRiskSearchResultPO> page,
        @Param("param") AuditRiskSearchParamPO param);

    List<AuditRiskRefCountPO> findRiskRefCountByProcedureIds(
        @Param("procedureIds") Collection<Long> procedureIds);

    List<AuditProcedureRiskRefPO> findProcedureRiskRefByProcedureIds(
        @Param("procedureIds") Collection<Long> procedureIds);

    List<AuditProjectRiskRefCountPO> findProjectRiskRefCountByProjectIdAndProcedureIds(
        @Param("projectId") Long projectId,
        @Param("procedureIds") Collection<Long> procedureIds);

    List<AuditRisk> findProjectRiskInfoByProjectIdAndProcedureIds(
        @Param("projectId") Long projectId,
        @Param("procedureIds") Collection<Long> procedureIds);

    List<String> findAllInnerCategory();

    List<String> findInnerCategoryByYear(@Param("param") InnerCategoryListParamDTO param);

    List<AuditRisk> findAuditRiskByYear(@Param("auditYear") Integer auditYear);

    List<AuditRiskHistoryResultPO> findRiskHistory(@Param("param") AuditRiskHistoryParamPO param);

    AuditRiskDataResultPO getRiskById(@Param("id") Long id);

    List<FindAllAuditRiskResultPO> findAllauditRisk(@Param("param") FindAllAuditRiskParamPO param);

    /**
     * 获取threadid相同但id不同，根据创建时间排序，获取最新的一个
     */
    AuditRisk getLatestOne(@Param("id") Long id, @Param("threadId") Long threadId);
}
