package com.sinitek.bnzg.audit.project.procedure.approve.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApprovePageParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBaseInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultBatchApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultCreateParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultDetailDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSearchResultDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.dto.PpApproveResultSingleApproveParamDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApproveResult;
import java.util.Collection;
import java.util.List;

/**
 * 风险点审批结果 Service 接口
 *
 * <AUTHOR>
 * date 2024-08-30
 */
public interface IPpApproveResultService {

    List<AuditPpApproveResult> save(PpApproveResultCreateParamDTO param);

    void approve(PpApproveResultSingleApproveParamDTO param);

    List<Long> batchApprove(PpApproveResultBatchApproveParamDTO param);

    IPage<PpApproveResultSearchResultDTO> search(PpApproveResultSearchParamDTO param);

    List<PpApproveResultBaseInfoDTO> findExistByApproveId(Long approveId);

    List<PpApproveResultBaseInfoDTO> findExistByIds(Collection<Long> ids);

    PpApproveResultDetailDTO loadDetailById(Long id);

    PpApprovePageParamDTO getApprovePageParam(Long ppId);
}
