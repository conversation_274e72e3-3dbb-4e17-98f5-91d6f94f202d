package com.sinitek.bnzg.audit.stage.log.status.stage.event;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.event.AbstractRecordChangeLogEvent;

/**
 * <AUTHOR>
 * @date 08/06/2024 16:12
 */
public class StageExampleStatusChangeEvent<T extends AbstractRecordChangeLogAddParamBaseDTO> extends
    AbstractRecordChangeLogEvent<T> {

    public StageExampleStatusChangeEvent(T source) {
        super(source);
    }

}
