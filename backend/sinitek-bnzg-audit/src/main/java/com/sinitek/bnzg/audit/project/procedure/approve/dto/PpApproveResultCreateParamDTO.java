package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024-11-13 16:03
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(value = "项目审计程序审批-创建参数DTO")
public class PpApproveResultCreateParamDTO {

    @ApiModelProperty("审批id")
    private Long approveId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目审计程序id")
    List<Long> ppIds;

    @ApiModelProperty("项目审计程序id和审计程序idMap")
    Map<Long, Long> ppIdAndProcedureIdMap;

    @ApiModelProperty("项目审计程序id和状态Map")
    Map<Long, Integer> ppidAndStatusMap;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}

