package com.sinitek.bnzg.audit.project.procedure.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 项目风险点 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(value = "项目风险点状态更新参数")
public class AuditRiskStatusUpdateParamDTO {

    @ApiModelProperty(value = "风险点ids")
    private List<Long> riskIds;

    @ApiModelProperty(value = "map,key:风险点id,value:新状态")
    private Map<Long, Integer> idAndStatusMap;

    @ApiModelProperty(value = "map,key:风险点id,value:审批人")
    private Map<Long, String> idAndApproverIdMap;

    @ApiModelProperty(value = "map,key:风险点id,value:审批时间")
    private Map<Long, Date> idAndApproveTimeMap;

    @ApiModelProperty(value = "map,key:项目审计程序id,value:审批反馈")
    private Map<Long, String> idAndApproveRemarkMap;

    @ApiModelProperty(value = "操作人,后端自动生成,提交人")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成,提交人")
    private Date opTime;
}
