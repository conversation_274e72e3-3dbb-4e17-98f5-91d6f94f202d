<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.project.mapper.AuditProjectMapper">

    <select id="search" resultType="com.sinitek.bnzg.audit.project.po.AuditProjectSearchResultPO">
        select
            ap.id,
            ap.plan_id,
            apl.name as planName,
            ap.name as projectName,
            apl.year as auditYear,
            ap.start_date ,
            ap.end_date ,
            ap.project_phase ,
            ap.startup_date,
            ap.period_start_date,
            ap.period_end_date
        from
            audit_project ap
            left join audit_plan apl
        on
            apl.id = ap.plan_id
            and apl.remove_flag = 0
        where
            ap.remove_flag = 0
            and ap.project_phase != 0
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.projectNames)">
            <!-- 项目名称查询 -->
            and
            <foreach collection="param.projectNames" index="index" item="projectName" open="(" separator="or" close=")">
                ap.name LIKE CONCAT('%', #{projectName}, '%') ESCAPE '/'
            </foreach>
        </if>

        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.planNames)">
            <!-- 计划名称查询 -->
            and
            <foreach collection="param.planNames" index="index" item="planName" open="(" separator="or" close=")">
                apl.name LIKE CONCAT('%', #{planName}, '%') ESCAPE '/'
            </foreach>
        </if>

        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.projectPhases)">
            <!-- 项目进度查询 -->
            and ap.project_phase in
            <foreach collection="param.projectPhases" index="index" item="projectPhase" open="(" separator="," close=")">
                #{projectPhase}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.auditYears)">
            <!-- 审计年份查询 -->
            and apl.year in
            <foreach collection="param.auditYears" index="index" item="auditYear" open="(" separator="," close=")">
                #{auditYear}
            </foreach>
        </if>
        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
          <if test="param.useAuditYearSortFlag">
            order by apl.year ${param.orderType},ap.startup_date desc
          </if>
          <if test="!param.useAuditYearSortFlag">
            <!-- 确保默认时刚启动的项目在第一行 -->
            order by ap.startup_date desc
          </if>
        </if>
    </select>

    <select id="loadProjectAuditYearById" resultType="java.lang.Integer">

        select apl.year from audit_project  ap
            left join audit_plan apl
               on apl.id = ap.plan_id
                   and apl.remove_flag = 0
        where  ap.remove_flag = 0
          and ap.id = #{projectId}
    </select>

</mapper>
