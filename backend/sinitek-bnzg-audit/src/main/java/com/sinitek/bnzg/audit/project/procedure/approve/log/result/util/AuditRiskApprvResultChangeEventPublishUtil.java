package com.sinitek.bnzg.audit.project.procedure.approve.log.result.util;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.audit.project.procedure.approve.log.result.event.AuditRiskApprvResultChangeEvent;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/09/2024 10:02
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskApprvResultChangeEventPublishUtil {

    public static <T extends AbstractRecordChangeLogAddParamBaseDTO> void publishEvent(
        T event) {
        LcEventUtil.publishEvent(new AuditRiskApprvResultChangeEvent<>(event));
    }

}
