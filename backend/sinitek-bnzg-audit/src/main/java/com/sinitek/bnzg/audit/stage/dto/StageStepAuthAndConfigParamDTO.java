package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-06-16 16:30:15
 */

@Data
@NoArgsConstructor
@ApiModel("查询具体某一步骤权限和页面的参数")
public class StageStepAuthAndConfigParamDTO {

    @NotNull(message = "阶段步骤实例不能为空")
    @ApiModelProperty("阶段步骤实例id")
    private Long stageStepExampleId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "当前是否调试")
    private Boolean debugFlag = false;

}
