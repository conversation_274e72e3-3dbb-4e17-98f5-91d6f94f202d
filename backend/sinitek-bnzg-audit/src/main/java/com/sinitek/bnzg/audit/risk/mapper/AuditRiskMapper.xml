<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.audit.risk.mapper.AuditRiskMapper">

  <delete id="deleteByIds">
    update audit_risk
       set remove_flag = 1,
           remover_id = #{operatorId}
     where id in
      <foreach collection="ids" item="id" open="(" separator="," close=")">
          #{id}
      </foreach>
  </delete>

  <select id="searchInExecution" resultType="com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO">
    select *
      from audit_risk
     where project_id = #{param.projectId}
       and procedure_id = #{param.procedureId}
       and remove_flag = 0
       and thread_latest_flag = 1
  <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
     order by name desc,id asc
  </if>
  </select>

  <select id="searchInTracking" resultType="com.sinitek.bnzg.audit.risk.po.AuditRiskSearchResultPO">
      select *
        from audit_risk
       where project_id = #{param.projectId}
         and remove_flag = 0
         and thread_latest_flag = 1
         and rectify_state is not null
    <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
       order by updatetimestamp desc
    </if>
  </select>

  <select id="findRiskRefCountByProcedureIds" resultType="com.sinitek.bnzg.audit.risk.po.AuditRiskRefCountPO">
      select procedure_id,count(*) as ref_count
        from audit_risk
       where thread_latest_flag = 1
         and remove_flag = 0
         and procedure_id in
              <foreach collection="procedureIds" item="procedureId" open="(" separator="," close=")">
                #{procedureId}
              </foreach>
       group by procedure_id
  </select>

  <select id="findProcedureRiskRefByProcedureIds" resultType="com.sinitek.bnzg.audit.risk.po.AuditProcedureRiskRefPO">
        select id as risk_id,name as risk_name,procedure_id,project_id
          from audit_risk
         where thread_latest_flag = 1
           and remove_flag = 0
           and procedure_id in
                <foreach collection="procedureIds" item="procedureId" open="(" separator="," close=")">
                  #{procedureId}
                </foreach>
  </select>

  <select id="findProjectRiskRefCountByProjectIdAndProcedureIds" resultType="com.sinitek.bnzg.audit.risk.po.AuditProjectRiskRefCountPO">
  select project_id,procedure_id,count(*) as ref_count
    from audit_risk
   where thread_latest_flag = 1
     and remove_flag = 0
     and project_id = #{projectId}
     and procedure_id in
          <foreach collection="procedureIds" item="procedureId" open="(" separator="," close=")">
            #{procedureId}
          </foreach>
   group by procedure_id
  </select>

  <select id="findProjectRiskInfoByProjectIdAndProcedureIds" resultType="com.sinitek.bnzg.audit.risk.entity.AuditRisk">
  select *
    from audit_risk
   where thread_latest_flag = 1
     and remove_flag = 0
     and project_id = #{projectId}
     and procedure_id in
          <foreach collection="procedureIds" item="procedureId" open="(" separator="," close=")">
            #{procedureId}
          </foreach>
   order by name desc,id asc
  </select>

  <select id="findAllInnerCategory" resultType="java.lang.String">
  select  distinct inner_category
    from audit_risk
   where inner_category is not null
  </select>

    <select id="findInnerCategoryByYear" resultType="java.lang.String">
        select
            distinct ar.inner_category
        from
            audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
        where ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and apl.year = #{param.auditYear}
        order by ar.inner_category asc
    </select>

    <select id="findAuditRiskByYear" resultType="com.sinitek.bnzg.audit.risk.entity.AuditRisk">
        select
            *
        from
            audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
        where ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and apl.year = #{auditYear}
    </select>


    <select id="findRiskHistory" resultType="com.sinitek.bnzg.audit.risk.po.AuditRiskHistoryResultPO">
        SELECT
        ar.id AS risk_id,
        ar.name AS risk_name,
        ar.description,
        ar.procedure_id,
        ar.project_id,
        ap.name AS project_name,
        apd.name AS procedure_name,
        apl.name AS plan_name,
        ar.inner_category,
        ar.first_catalog
        FROM
        audit_risk ar
        LEFT JOIN audit_procedure apd
        ON ar.procedure_id = apd.id
        AND apd.remove_flag = 0
        LEFT JOIN audit_project ap
        ON ar.project_id = ap.id
        AND ap.remove_flag = 0
        LEFT JOIN audit_plan apl
        ON ap.plan_id = apl.id
        AND apl.remove_flag = 0
        WHERE
        ar.status = 100
        AND ar.thread_latest_flag = 1
        AND ar.remove_flag = 0
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.procedureIds)">
            <!-- 程序Id查询 -->
            AND ar.procedure_id IN
            <foreach collection="param.procedureIds" index="index" item="procedureId" open="(" separator="," close=")">
                #{procedureId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.firstCatalogs)">
            <!-- 一级分类查询 -->
            AND ar.first_catalog IN
            <foreach collection="param.firstCatalogs" index="index" item="firstCatalog" open="(" separator="," close=")">
                #{firstCatalog}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.innerCategorys)">
            <!-- 二级分类查询 -->
            AND ar.inner_category IN
            <foreach collection="param.innerCategorys" index="index" item="innerCategory" open="(" separator="," close=")">
                #{innerCategory}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.riskNames)">
            <!-- 风险点名称查询 -->
            AND
            <foreach collection="param.riskNames" index="index" item="riskName" open="(" separator="or" close=")">
                ar.name LIKE CONCAT('%', #{riskName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.descriptions)">
            <!-- 问题描述查询 -->
            AND
            <foreach collection="param.descriptions" index="index" item="description" open="(" separator="or" close=")">
                ar.description LIKE CONCAT('%', #{description}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.respDeptIds)">
            <!-- 责任部门Id查询 -->
            AND ar.id IN (select distinct risk_id from audit_risk_resp_dept where resp_dept_id IN
            <foreach collection="param.respDeptIds" index="index" item="respDeptId" open="(" separator="," close=")">
                #{respDeptId}
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.respManIds)">
            <!-- 责任人Id查询 -->
            AND ar.id IN (select distinct risk_id from audit_risk_resp_man where resp_man_id  IN
            <foreach collection="param.respManIds" index="index" item="respManId" open="(" separator="," close=")">
                #{respManId}
            </foreach>
            )
        </if>
        <if test="param.riskId != null">
            AND ar.id != #{param.riskId}
        </if>
    </select>

    <select id="getRiskById" resultType="com.sinitek.bnzg.audit.risk.po.AuditRiskDataResultPO">
        select
            apl.name as planName,
            ap.name as projectName,
            ar.name as riskName,
            apd.name as procedureName,
            ar.id as riskId,
            ar.project_id,
            ap.plan_id,
            ar.procedure_id
        from
         audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
                left join audit_procedure apd
                          on apd.id = ar.procedure_id
                              and apd.remove_flag = 0
        where
            ar.status = 100
          and ar.resp_dept_sug_type = 1
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ar.id = #{id}
    </select>

    <select id="findAllauditRisk" resultType="com.sinitek.bnzg.audit.risk.accountability.po.FindAllAuditRiskResultPO">
        select
        apl.name as planName,
        ap.name as projectName,
        ar.name as riskName,
        apd.name as procedureName,
        ar.id as riskId,
        ar.project_id,
        ap.plan_id,
        ar.procedure_id
        from
            audit_risk ar
                left join audit_project ap
                          on ar.project_id = ap.id
                              and ap.remove_flag = 0
                left join audit_plan apl
                          on apl.id = ap.plan_id
                              and apl.remove_flag = 0
                left join audit_procedure apd
                          on apd.id = ar.procedure_id
                              and apd.remove_flag = 0
        where
            ar.status = 100
          and ar.resp_dept_sug_type = 1
          and ar.thread_latest_flag = 1
          and ar.remove_flag = 0
          and ap.project_phase != 7
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.projectNames)">
            <!-- 项目名称查询 -->
            AND
            <foreach collection="param.projectNames" index="index" item="projectName" open="(" separator="or" close=")">
                ap.name LIKE CONCAT('%', #{projectName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.planIds)">
            <!-- 计划Id查询 -->
            AND apl.id in
            <foreach collection="param.planIds" index="index" item="planId" open="(" separator="," close=")">
                #{planId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(param.procedureNames)">
            <!-- 程序名称查询 -->
            AND
            <foreach collection="param.procedureNames" index="index" item="procedureName" open="(" separator="," close=")">
                apd.name LIKE CONCAT('%', #{procedureName}, '%') ESCAPE '/'
            </foreach>
        </if>
        <if test="param.riskId != null">
            AND ar.id = #{param.riskId}
        </if>
    </select>

    <select id="getLatestOne" resultType="com.sinitek.bnzg.audit.risk.entity.AuditRisk">
    select *
      from audit_risk
     where thread_id = #{threadId}
       and remove_flag = 0
       and ((status = 100) or (status = 50))
       and id != #{id}
     order by createtimestamp desc
     limit 1
    </select>
</mapper>
