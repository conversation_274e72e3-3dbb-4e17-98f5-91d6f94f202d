package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("生成阶段步骤实例参数")
public class GenerateStageStepExampleParamDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("阶段实例id")
    private Long stageExampleId;

    @ApiModelProperty("步骤信息集合")
    private List<StageStepDefInfoDTO> list;

    @ApiModelProperty("操作人")
    private String opOrgId;

    @ApiModelProperty("操作时间")
    private Date opTime;

    @ApiModelProperty("是否抛出事件")
    private Boolean publishEventFlag;

}
