package com.sinitek.bnzg.audit.project.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.lib.dto.AuditProcedureBaseInfoDTO;
import com.sinitek.bnzg.audit.lib.service.IAuditProcedureService;
import com.sinitek.bnzg.audit.plan.dto.AuditPlanInfoDTO;
import com.sinitek.bnzg.audit.plan.service.IAuditPlanService;
import com.sinitek.bnzg.audit.project.dto.AuditProjectInfoDTO;
import com.sinitek.bnzg.audit.project.procedure.dto.AuditPpHomeSearchResultDTO;
import com.sinitek.bnzg.audit.project.service.IAuditProjectService;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskEnumConstant;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.sirmenum.service.IEnumService;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/25
 */
@Component
public class AuditPpHomeSearchResultFormat implements ITableResultFormat<AuditPpHomeSearchResultDTO> {

    @Autowired
    private IAuditPlanService planService;

    @Autowired
    private IAuditProjectService projectService;

    @Autowired
    private IAuditProcedureService procedureService;

    @Autowired
    private IEnumService enumService;

    @Override
    public List<AuditPpHomeSearchResultDTO> format(List<AuditPpHomeSearchResultDTO> data) {
        if (CollUtil.isEmpty(data)) {
            return data;
        }

        List<Long> planIdList = new LinkedList<>();
        List<Long> projectIdList = new LinkedList<>();
        List<Long> procedureIdList = new LinkedList<>();

        // 填充idList
        data.forEach(item -> {
            planIdList.add(item.getPlanId());
            projectIdList.add(item.getProjectId());
            procedureIdList.add(item.getProcedureId());
        });

        // key：状态值
        // value: 状态名称
        Map<String, String> statusAndNameMap = this.enumService.getSirmEnumByCataLogAndType(
            AuditRiskEnumConstant.DEFAULT_CATALOG, AuditRiskEnumConstant.RISK_STATUS);
        // key: 审计计划id
        // value: 审计计划名称
        Map<Long, String> planIdAndNameMap;
        // key: 审计项目id
        // value: 审计项目名称
        Map<Long, String> projectIdAndNameMap;
        // key: 审计程序id
        // value: 审计程序名称
        Map<Long, String> procedureIdAndNameMap;

        List<AuditPlanInfoDTO> auditPlanInfoDTOList = planService.findExistsInfoByIds(planIdList);
        List<AuditProjectInfoDTO> auditProjectInfoDTOList = projectService.findExistsProjctByIds(
            projectIdList);
        List<AuditProcedureBaseInfoDTO> auditProcedureBaseInfoDTOList = procedureService.findExistsBaseInfoIds(
            procedureIdList);

        if (CollUtil.isNotEmpty(auditPlanInfoDTOList)) {
            planIdAndNameMap = auditPlanInfoDTOList.stream()
                .collect(Collectors.toMap(AuditPlanInfoDTO::getId, AuditPlanInfoDTO::getName));
        } else {
            planIdAndNameMap = Collections.emptyMap();
        }

        if (CollUtil.isNotEmpty(auditProjectInfoDTOList)) {
            projectIdAndNameMap = auditProjectInfoDTOList.stream()
                .collect(Collectors.toMap(AuditProjectInfoDTO::getId, AuditProjectInfoDTO::getName));
        } else {
            projectIdAndNameMap = Collections.emptyMap();
        }

        if (CollUtil.isNotEmpty(auditProcedureBaseInfoDTOList)) {
            procedureIdAndNameMap = auditProcedureBaseInfoDTOList.stream()
                .collect(Collectors.toMap(AuditProcedureBaseInfoDTO::getId, AuditProcedureBaseInfoDTO::getName));
        } else {
            procedureIdAndNameMap = Collections.emptyMap();
        }

        return data.stream().map(item -> {
            String planName = MapUtils.getString(planIdAndNameMap, item.getPlanId(), "");
            item.setPlanName(planName);

            String projectName = MapUtils.getString(projectIdAndNameMap, item.getProjectId(), "");
            item.setProjectName(projectName);

            String procedureName = MapUtils.getString(procedureIdAndNameMap, item.getProcedureId(), "");
            item.setProcedureName(procedureName);

            String statusName = MapUtils.getString(statusAndNameMap, String.valueOf(item.getStatus()), "");
            item.setStatusName(statusName);

            return item;
        }).collect(Collectors.toList());
    }
}
