package com.sinitek.bnzg.audit.project.dto;

import com.sinitek.bnzg.audit.project.support.IAuditProjectAndOpeatorId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/02/2024 11:04
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("项目终止参数")
public class AuditStopProjectParamDTO implements IAuditProjectAndOpeatorId {

    @NotNull(message = "项目不能为空")
    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
