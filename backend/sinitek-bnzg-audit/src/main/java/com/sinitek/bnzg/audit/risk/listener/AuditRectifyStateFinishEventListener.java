package com.sinitek.bnzg.audit.risk.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogBatchAddBaseParamDTO;
import com.sinitek.bnzg.log.dto.RecordChangeLogAddParamDTO;
import com.sinitek.bnzg.audit.risk.constant.AuditRiskRectifyStateConstant;
import com.sinitek.bnzg.audit.risk.log.rectify.state.event.AuditRectifyStateChangeEvent;
import com.sinitek.bnzg.audit.risk.service.IAuditRiskRectifyTraceNoticeService;
import com.sinitek.sirm.common.utils.JsonUtil;
import java.util.Collection;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 风险点整改完成事件监听
 *
 * <AUTHOR>
 * @date 08/06/2024 16:25
 */
@Slf4j
@Component
public class AuditRectifyStateFinishEventListener {

    @Autowired
    private IAuditRiskRectifyTraceNoticeService auditRiskRectifyTraceNoticeService;

    /**
     * 监听风险点整改完成
     *
     * 移除所有未完成的消息提醒
     */
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = AuditRectifyStateChangeEvent.class, fallbackExecution = true)
    public <T extends AbstractRecordChangeLogAddParamBaseDTO> void listenRiskRectifyStateFinish(
        AuditRectifyStateChangeEvent<T> event) {
        T source = event.getSource();
        if (source instanceof RecordChangeLogAddParamDTO) {
            RecordChangeLogAddParamDTO<Integer> singleData = (RecordChangeLogAddParamDTO<Integer>) source;
            Integer newValue = singleData.getNewValue();
            if (Objects.equals(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH, newValue)) {
                Long riskId = singleData.getForeignKey();
                String operatorId = singleData.getOperatorId();
                log.info("监听到 风险点[{}]整改完成 事件,取消其对应的所有通知数据",
                    riskId);
                this.deleteAllRelatedNotice(riskId, operatorId);
            }
        } else {
            log.info("监听到 风险点整改完成 批量事件,取消其对应的所有通知数据");
            AbstractRecordChangeLogBatchAddBaseParamDTO<Integer> batchParam = (AbstractRecordChangeLogBatchAddBaseParamDTO<Integer>) source;
            Collection<Long> riskIds = batchParam.getForeignKeys();

            if (CollUtil.isNotEmpty(riskIds)) {
                riskIds.forEach(riskId -> {
                    Integer newValue = batchParam.getNewValue(riskId);
                    if (Objects.equals(AuditRiskRectifyStateConstant.RECTIFICATION_FINISH,
                        newValue)) {
                        String operatorId = batchParam.getOperatorId(riskId);
                        log.info("监听到 风险点[{}]整改完成 事件,取消其对应的所有通知数据",
                            riskId);
                        this.deleteAllRelatedNotice(riskId, operatorId);
                    }
                });
            } else {
                log.warn("监听到 风险点整改完成 事件,数据: {},获取到的风险点id为空",
                    JsonUtil.toJsonString(event));
            }
        }
    }

    private void deleteAllRelatedNotice(Long riskId, String operatorId) {
        // 删除风险点整改提醒
        this.auditRiskRectifyTraceNoticeService.deleteRectifyTraceNotice(riskId,
            operatorId);
        // 删除风险点变动当天提醒
        this.auditRiskRectifyTraceNoticeService.deleteRectifyChangeNotice(riskId,
            operatorId);
    }
}
