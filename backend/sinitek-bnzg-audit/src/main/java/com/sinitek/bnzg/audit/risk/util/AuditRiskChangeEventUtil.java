package com.sinitek.bnzg.audit.risk.util;

import com.sinitek.bnzg.log.dto.AbstractRecordChangeLogAddParamBaseDTO;
import com.sinitek.bnzg.audit.risk.event.AuditRiskSaveOrEditEvent;
import com.sinitek.sirm.lowcode.common.event.util.LcEventUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/28/2024 17:52
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskChangeEventUtil {

    public static <T extends AbstractRecordChangeLogAddParamBaseDTO> void publish(
        T source) {
        LcEventUtil.publishEvent(new AuditRiskSaveOrEditEvent<>(source));
    }
}
