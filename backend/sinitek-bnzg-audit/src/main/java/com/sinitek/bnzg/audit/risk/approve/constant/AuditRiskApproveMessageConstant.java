package com.sinitek.bnzg.audit.risk.approve.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目风险点审批 MessageCode
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditRiskApproveMessageConstant {

    /**
     * 当前项目不存在审批人员,无法提交
     */
    public static final String NO_APPROVER_CANT_SUBMIT = "9902010001";

    /**
     * 待审批风险点数据为空,无法提交
     */
    public static final String EMPTY_DATA_CANT_SUBMIT = "9902010002";

    /**
     * 只允许提交草稿状态的数据
     */
    public static final String HAS_NOT_DRAFT_DATA_CANT_SUBMIT = "9902010003";

    /**
     * 存在未审批的数据无法提交
     */
    public static final String HAS_DRAFT_DATA_CANT_SUBMIT = "9902010004";

    /**
     * 数据不存在无法提交
     */
    public static final String APPROVE_DATA_NOT_EXISTS_CANT_SUBMIT = "9902010005";

    /**
     * 当前数据已提交
     */
    public static final String ALREADY_SUBMITED_CANT_SUBMIT_AGAIN = "9902010006";

    /**
     * 被审批的数据不存在
     */
    public static final String DATA_NOT_EXISTS_ON_APPROVE = "9902010007";

    /**
     * 当前数据已提交,无法再次审批
     */
    public static final String DATA_SUBMITED_CANT_APPROVE = "9902010008";

    /**
     * 只允许提交最新的数据
     */
    public static final String HAS_NOT_THREAD_LATEST_DATA_CANT_SUBMIT = "9902010009";

    /**
     * 被审批数据所属审计项目不存在
     */
    public static final String APPROVE_DATA_BELONG_AUDIT_PROJECT_NOT_EXISTS = "9902010010";

    /**
     * 当前审计项目未配置项目审批人员,无法审批风险点
     */
    public static final String CANT_APPROVE_RISK_NO_APPROVER = "9902010011";

    /**
     * 当前登录人不是当前审计项目的审批人员,无法审批风险点
     */
    public static final String CANT_APPROVE_RISK_NOT_APPROVER = "9902010012";

    /**
     * 当前审计项目未配置项目审批人员,无法提交风险点审批
     */
    public static final String CANT_SUBMIT_APPROVE_RISK_NO_APPROVER = "9902010013";

    /**
     * 当前登录人不是当前审计项目的审批人员,无法提交风险点审批
     */
    public static final String CANT_SUBMIT_APPROVE_RISK_NOT_APPROVER = "9902010014";

    /**
     * 当前项目缺少除【{0}】之外的审批人,无法提交风险点审批
     */
    public static final String CURRENT_PROJECT_LESS_APPROVER = "9902010015";

    /**
     * 9902010019=当前数据已终止,无法再次审批
     */
    public static final String DATA_TERMINATED_CANT_APPROVE = "9902010019";
}
