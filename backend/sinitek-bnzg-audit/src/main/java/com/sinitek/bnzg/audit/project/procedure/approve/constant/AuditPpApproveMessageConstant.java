package com.sinitek.bnzg.audit.project.procedure.approve.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 项目风险点审批 MessageCode
 *
 * <AUTHOR>
 * date 2024-08-28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AuditPpApproveMessageConstant {

    /**
     * 待审批审计程序数据为空,无法提交
     */
    public static final String EMPTY_DATA_CANT_SUBMIT = "9902004001001";

    /**
     * 审计程序存在风险点,无法提交
     */
    public static final String HAS_RISK_CANT_SUBMIT = "9902004001002";

    /**
     * 当前项目缺少除【{0}】之外的审批人,无法提交项目审计程序审批
     */
    public static final String LESS_APPROVER_CANT_SUBMIT = "9902004001003";

    /**
     * 当前审计项目未配置项目审批人员,无法审批
     */
    public static final String CANT_APPROVE_PP_NO_APPROVER = "9902004001004";

    /**
     * 当前登录人不是当前审计项目的审批人员,无法审批
     */
    public static final String CANT_APPROVE_PP_NOT_APPROVER = "9902004001005";

    /**
     * 审计程序[{0}]为未审计数据,无法提交
     */
    public static final String CANT_APPROVE_PP_NO_STATUS = "9902004001006";

    /**
     * 审计程序[{0}]非草稿状态且不存在风险点,无法提交
     */
    public static final String NOT_DRAFT_CAT_SUBMIT = "9902004001007";

    /**
     * 审计程序[{0}]非草稿状态且没有待审批的风险点,无法提交
     */
    public static final String NOT_DRAFT_AND_DONT_HAS_RISK_CAT_SUBMIT = "9902004001008";

    /**
     * 审计程序[{0}]缺少审计人数据,无法提交
     */
    public static final String LESS_AUDIT_DATA_CAT_SUBMIT = "9902004001009";

    /**
     * 审计程序[{0}]为审批中状态,无法提交
     */
    public static final String APPROVING_DATA_CAT_SUBMIT = "9902004001010";

    /**
     * 审计程序[{0}]为审批不通过状态,无法提交
     */
    public static final String NOT_APPROVED_DATA_CAT_SUBMIT = "9902004001011";


    /**
     * 9902010016
     */
    public static final String CANT_SUBMIT_BECAUSE_HAS_RISKS = "9902010016";

    /**
     * 审计程序[{0}]未审批,无法提交
     */
    public static final String CANT_SUBMIT_BECAUSE_PROCEDURE_NOT_AUDIT = "9902010017";

    /**
     * 审计程序[{0}]下风险点[{1}]未审批,无法提交
     */
    public static final String CANT_SUBMIT_BECAUSE_RISK_NOT_AUDIT = "9902010018";

    /**
     * 审计程序不存在,无法创建审批任务
     */
    public static final String CANT_CREATE_TODO_TASK_NO_PP = "9902004001012";

    /**
     * 审计程序[{0}]对应审计项目不存在,无法创建审批任务
     */
    public static final String CANT_CREATE_TODO_TASK_NO_PROJECT = "9902004001013";

    /**
     * 审计程序[{0}]对应审计计划不存在,无法创建审批任务
     */
    public static final String CANT_CREATE_TODO_TASK_NO_PLAN = "9902004001014";


    /**
     * 审批数据为空,保存失败
     */
    public static final String DATA_EMPTY_CANT_SAVE = "9902004001015";
}
