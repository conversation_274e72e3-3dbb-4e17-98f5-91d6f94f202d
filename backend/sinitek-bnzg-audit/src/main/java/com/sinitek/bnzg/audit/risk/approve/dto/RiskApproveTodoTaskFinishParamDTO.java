package com.sinitek.bnzg.audit.risk.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/30/2024 11:27
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(value = "风险点审批代办任务完成参数")
public class RiskApproveTodoTaskFinishParamDTO {

    @ApiModelProperty("风险点审批id")
    private Long riskApproveId;

    @ApiModelProperty(value = "操作人,后端自动生成")
    private String operatorId;

    @ApiModelProperty(value = "操作时间,后端自动生成")
    private Date opTime;

}
