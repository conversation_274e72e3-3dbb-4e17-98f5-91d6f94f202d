package com.sinitek.bnzg.audit.project.procedure.approve.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.RiskApproveResult;
import com.sinitek.bnzg.audit.project.procedure.approve.mapper.AuditRiskApproveResultMapper;
import com.sinitek.bnzg.audit.project.procedure.approve.po.PpRiskApproveResultSearchResultPO;
import com.sinitek.bnzg.audit.project.procedure.approve.po.RiskApproveResultCreateParamPO;
import com.sinitek.bnzg.audit.risk.approve.constant.AuditRiskApproveResultConstant;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 风险点审批结果
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Slf4j
@Component
public class RiskApproveResultDAO extends
    ServiceImpl<AuditRiskApproveResultMapper, RiskApproveResult> {

    public List<RiskApproveResult> create(RiskApproveResultCreateParamPO param) {
        Long approveId = param.getApproveId();
        Long projectId = param.getProjectId();
        List<Long> riskIds = param.getRiskIds();
        Map<Long, Long> riskIdAndProcedureIdMap = param.getRiskIdAndProcedureIdMap();

        List<RiskApproveResult> list = new LinkedList<>();
        for (int i = 0; i < riskIds.size(); i++) {
            Long riskId = riskIds.get(i);

            Long procedureId = MapUtils.getLong(riskIdAndProcedureIdMap, riskId);

            RiskApproveResult entity = new RiskApproveResult();
            entity.setApproveId(approveId);
            entity.setProjectId(projectId);
            entity.setProcedureId(procedureId);
            entity.setRiskId(riskId);
            entity.setApproveResult(AuditRiskApproveResultConstant.DRAFT);
            entity.setSort(i);

            list.add(entity);
        }
        this.saveBatch(list);
        return list;
    }

    public List<RiskApproveResult> findExistByApproveId(Long approveId) {
        LambdaQueryWrapper<RiskApproveResult> query = Wrappers.lambdaQuery(
            RiskApproveResult.class);
        query.eq(RiskApproveResult::getApproveId, approveId);
        return this.list(query);
    }

    public List<PpRiskApproveResultSearchResultPO> findPpRiskApproveResultListByApproveId(
        Long approveId) {
        return this.baseMapper.findPpRiskApproveResultListByApproveId(approveId);
    }

    public RiskApproveResult getByRiskId(Long riskId) {
        LambdaQueryWrapper<RiskApproveResult> queryWrapper = Wrappers.lambdaQuery(
            RiskApproveResult.class);
        queryWrapper.eq(RiskApproveResult::getRiskId, riskId);
        return this.getOne(queryWrapper);
    }
}
