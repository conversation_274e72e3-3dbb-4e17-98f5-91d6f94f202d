package com.sinitek.bnzg.audit.stage.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 08/15/2024 15:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("生成阶段实例参数")
public class GenerateStageExampleParamDTO {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("操作人")
    private String opOrgId;

    @ApiModelProperty("操作时间")
    private Date opTime;

    @ApiModelProperty("是否抛出事件")
    private Boolean publishEventFlag;

}
