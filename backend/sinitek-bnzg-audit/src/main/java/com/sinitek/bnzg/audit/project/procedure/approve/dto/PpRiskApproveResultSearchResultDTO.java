package com.sinitek.bnzg.audit.project.procedure.approve.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 项目审计程序审批-返回DTO
 *
 * <AUTHOR>
 * date 2024-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目审计程序审批-查询结果DTO")
public class PpRiskApproveResultSearchResultDTO extends PpApproveResultSearchResultBaseDTO {

    @ApiModelProperty(value = "风险点id")
    private Long riskId;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "类型名")
    private String typeName;

    @ApiModelProperty(value = "风险等级")
    private Integer level;

    @ApiModelProperty(value = "风险等级名")
    private String levelName;

    @ApiModelProperty(value = "一级分类")
    private Integer firstCatalog;

    @ApiModelProperty(value = "一级分类名")
    private String firstCatalogName;

    @ApiModelProperty(value = "二级分类")
    private String innerCategory;

}
