package com.sinitek.bnzg.audit.stage.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.bnzg.audit.stage.entity.StageStepExample;
import com.sinitek.bnzg.audit.stage.mapper.StageStepExampleMapper;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/15/2024 13:04
 */
@Slf4j
@Component
public class StageStepExampleDAO extends ServiceImpl<StageStepExampleMapper, StageStepExample> {

    public List<StageStepExample> findByStageExampleIds(Collection<Long> stageExampleIds) {
        if (CollUtil.isNotEmpty(stageExampleIds)) {
            LambdaQueryWrapper<StageStepExample> queryWrapper = Wrappers.lambdaQuery(
                StageStepExample.class);
            queryWrapper.in(StageStepExample::getStageExampleId, stageExampleIds);
            return this.list(queryWrapper);
        }
        return Collections.emptyList();
    }

    public StageStepExample getByProjectIdAndStepValue(Long projectId, Integer stepValue) {
        LambdaQueryWrapper<StageStepExample> queryWrapper = Wrappers.lambdaQuery(
            StageStepExample.class);
        queryWrapper.in(StageStepExample::getProjectId, projectId);
        queryWrapper.in(StageStepExample::getStepValue, stepValue);
        List<StageStepExample> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            if (list.size() > 1) {
                log.warn("项目[{}]步骤值[{}]下存在多个步骤实例", projectId, stepValue);
            }
            return list.get(0);
        }
        return null;
    }

}
