package com.sinitek.bnzg.audit.risk.accountability.support;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.risk.accountability.dto.AuditRiskAcSearchResultDTO;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.org.service.IOrgService;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date：2024/11/15 14:13
 */
@Component
public class RiskAccountabilityResultFormat extends AuditRiskAcSearchResultDTO implements
        ITableResultFormat<AuditRiskAcSearchResultDTO> {

    @Autowired
    private IOrgService orgService;

    @Override
    public List<AuditRiskAcSearchResultDTO> format(List<AuditRiskAcSearchResultDTO> data) {
        if (CollUtil.isEmpty(data)) {
            return data;
        }
        List<String> orgIds = new LinkedList<>();
        List<String> respManIds = data.stream()
                .map(item -> item.getRespManIds())
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<String> respDeptIds = data.stream()
                .map(item -> item.getRespDeptIds())
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        orgIds.addAll(respManIds);
        orgIds.addAll(respDeptIds);

        Map<String, String> orgIdAndNameMap = this.orgService.getOrgNameMapByOrgIdList(
                orgIds.stream().distinct().collect(Collectors.toList()));

        data.forEach(item -> {

            String respDeptNames = Optional.ofNullable(item.getRespDeptIds())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                    .collect(Collectors.joining(","));
            item.setRespDeptName(respDeptNames);

            String respManNames = Optional.ofNullable(item.getRespManIds())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(id -> MapUtils.getString(orgIdAndNameMap, id, ""))
                    .collect(Collectors.joining(","));
            item.setRespManName(respManNames);
        });

        return data;
    }
}
