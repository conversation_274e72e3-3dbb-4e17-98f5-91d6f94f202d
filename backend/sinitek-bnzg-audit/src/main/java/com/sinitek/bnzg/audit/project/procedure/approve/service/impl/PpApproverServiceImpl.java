package com.sinitek.bnzg.audit.project.procedure.approve.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.bnzg.audit.project.procedure.approve.dao.AuditPpApproverDAO;
import com.sinitek.bnzg.audit.project.procedure.approve.entity.AuditPpApprover;
import com.sinitek.bnzg.audit.project.procedure.approve.service.IPpApproverService;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-11-13 15:57
 */
@Slf4j
@Service
public class PpApproverServiceImpl implements IPpApproverService {

    @Autowired
    private AuditPpApproverDAO dao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveApprovers(Long approveId, List<String> approverIds) {
        this.dao.saveApprovers(approveId, approverIds);
    }

    @Override
    public List<String> findApproversByApproveId(Long approveId) {
        List<AuditPpApprover> approvers = this.dao.findByApproveId(approveId);
        if (CollUtil.isNotEmpty(approvers)) {
            return approvers.stream().map(AuditPpApprover::getOpOrgid).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
