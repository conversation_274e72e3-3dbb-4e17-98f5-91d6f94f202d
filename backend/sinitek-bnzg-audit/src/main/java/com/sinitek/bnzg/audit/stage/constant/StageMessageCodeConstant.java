package com.sinitek.bnzg.audit.stage.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 08/16/2024 13:33
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class StageMessageCodeConstant {

    /**
     * 当前项目不存在审计阶段
     */
    public static final String CANT_GET_STAGE_EXAMPLE_BY_PROJECT_ID = "9902004001";

    /**
     * 无法获取阶段步骤实例数据
     */
    public static final String CANT_FIND_STAGE_STEP_EXAMPLE_DATA = "9902004002";

    /**
     * 当前阶段步骤状态为[{0}]无法更新为完成状态
     */
    public static final String CANT_MANUALLY_FINISH_BECAUSE_OF_CURRENT_STATUS = "9902004003";

    /**
     * 当前项目已存在阶段实例
     */
    public static final String STAGE_EXAMPLE_ALREADY_EXISTS = "9902004004";

    /**
     * 当前项目不存在阶段实例
     */
    public static final String STAGE_EXAMPLE_NOT_EXISTS = "9902004005";

    /**
     * 产品[{0}]缺少对应的阶段实例
     */
    public static final String PROJECT_STAGE_EXAMPLE_NOT_EXISTS = "9902004006";

    /**
     * 产品[{0}]缺少对应的步骤数据
     */
    public static final String STEP_EXAMPLE_NOT_EXISTS = "9902004007";

}
