<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinitek.bnzg</groupId>
    <artifactId>sinitek-bnzgsjhg</artifactId>
    <version>1.2.2</version>
  </parent>

  <artifactId>sinitek-bnzg-org</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>com.sinitek.bnzg</groupId>
      <artifactId>sinitek-bnzg-common</artifactId>
    </dependency>
    <dependency>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
      <groupId>com.baomidou</groupId>
    </dependency>
  </dependencies>


</project>