package com.sinitek.bnzg.org.sync.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Data
@ApiModel(value = "外部系统部门DTO")
public class ContactDeptDTO {

    @ApiModelProperty(value = "部门id")
    private String deptId;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "父级id")
    private String parentId;

    @ApiModelProperty(value = "是否禁用")
    private String enabled;


}
