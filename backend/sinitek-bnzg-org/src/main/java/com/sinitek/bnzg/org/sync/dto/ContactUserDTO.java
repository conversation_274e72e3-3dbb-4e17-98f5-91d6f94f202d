package com.sinitek.bnzg.org.sync.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 外部系统用户查询DTO
 *
 * <AUTHOR>
 * @date 2024/10/25
 */
@Data
@ApiModel(value = "外部系统用户查询DTO")
public class ContactUserDTO {

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "分机号")
    private String telephone;


    @ApiModelProperty(value = "职位")
    private String title;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "所属部门")
    private String deptIds;

    @ApiModelProperty(value = "是否离职")
    private Integer status;


}
