package com.sinitek.bnzg.org.sync.job;

import com.sinitek.bnzg.org.sync.service.IOrgSyncService;
import com.sinitek.sirm.common.support.BaseScheduleJob;
import com.sinitek.sirm.common.support.ScheduleJobContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Slf4j
public class OrgSyncJob extends BaseScheduleJob {

    @Autowired
    IOrgSyncService orgSyncService;

    @Override
    public void scheduleJob(ScheduleJobContext context) {
        log.info("OrgSyncJob:ready to syncOrg!");
        try {
            ThreadContext.put("ROUTINGKEY", "org-sync");
            orgSyncService.syncOrg();
            log.info("OrgSyncJob:finished syncOrg!");
        } catch (Exception e) {
            log.error("OrgSyncJob:syncOrg error!{}", context, e);
        } finally {
            ThreadContext.clearMap();
        }
    }
}
