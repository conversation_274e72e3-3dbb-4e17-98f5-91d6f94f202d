package com.sinitek.bnzg.org.sync.config;

import com.sinitek.bnzg.org.sync.job.OrgSyncJob;
import com.sinitek.sirm.common.quartz.constant.QuartzConstant;
import org.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/11/5
 */
@Configuration
public class OrgSyncJobConfig {

    public static final String JOB_NAME = "组织结构同步定时任务";

    @Bean
    public JobDetail orgSyncJobDetail() {
        return JobBuilder.newJob(OrgSyncJob.class)
            .withIdentity(JOB_NAME, QuartzConstant.SYSTEM_TYPE)
            .storeDurably()
            .withDescription("组织结构同步")
            .build();
    }

    /**
     * 每天8点执行
     * @param orgSyncJobDetail 组织结构同步定时任务
     * @return
     */
    @Bean
    public Trigger orgSyncJobTrigger(JobDetail orgSyncJobDetail) {
        return TriggerBuilder.newTrigger()
            .forJob(orgSyncJobDetail)
            .withIdentity(JOB_NAME, QuartzConstant.SYSTEM_TYPE)
            .startNow()
            .withSchedule(CronScheduleBuilder.cronSchedule("0 0 8 * * ?"))
            .usingJobData("name", JOB_NAME)
            .withDescription(orgSyncJobDetail.getDescription())
            .build();
    }

}
