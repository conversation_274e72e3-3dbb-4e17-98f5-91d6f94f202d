package com.sinitek.bnzg.org.sync.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class OrgSyncConstant {

    /**
     * 同步数据来源
     */
    public static final String DATE_SRC = "dingding";


    /**
     * 外部系统组织结构根节点id
     */
    public static final String EXTERNAL_SYSTEM_ROOT_ID = "1";


    /**
     * 系统组织结构根节点id
     */
    public static final String SYSTEM_ROOT_ID = "99999";

    /**
     * 移动电话最大输入最大长度
     */
    public static final Integer TELEPHONE_MAX_LENGTH = 30;

    /**
     * 逗号分隔符
     */
    public static final String COMMA_SEPARATOR = ",";


    /**
     * 邮箱标识符
     */
    public static final String EMAIL_IDENTIFIER = "@";

}
