<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.org.sync.mapper.ContactUserMapper">

  <select id="findContactUser" resultType="com.sinitek.bnzg.org.sync.dto.ContactUserDTO">
    select user_id, title, name, status, email, telephone, mobile, dept_ids
    from
    <choose>
      <when test="@org.apache.commons.lang.StringUtils@isBlank(tableSchema)">
        dt_contact_user
      </when>
      <otherwise>
        ${tableSchema}.dt_contact_user
      </otherwise>
    </choose>
    order by tech_updt_time desc
  </select>

</mapper>