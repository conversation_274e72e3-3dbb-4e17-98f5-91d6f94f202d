<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.bnzg.org.sync.mapper.ContactDeptMapper">
  <select id="findContactDept" resultType="com.sinitek.bnzg.org.sync.dto.ContactDeptDTO">
    select parent_id, dept_id, name
    from
    <choose>
      <when test="@org.apache.commons.lang.StringUtils@isBlank(tableSchema)">
        dt_contact_dept
      </when>
      <otherwise>
        ${tableSchema}.dt_contact_dept
      </otherwise>
    </choose>
    order by tech_updt_time desc
  </select>
</mapper>