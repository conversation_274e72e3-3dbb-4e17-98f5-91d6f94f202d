package com.sinitek.bnzg.org.sync.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.sinitek.bnzg.org.sync.config.OrgSyncConfig;
import com.sinitek.bnzg.org.sync.dto.ContactUserDTO;
import com.sinitek.bnzg.org.sync.mapper.ContactUserMapper;
import com.sinitek.bnzg.org.sync.service.IContactUserService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Service
@Slf4j
@DS("oracle")
public class ContactUserServiceImpl implements IContactUserService {

    @Autowired
    private ContactUserMapper contactUserMapper;

    @Autowired
    OrgSyncConfig orgSyncConfig;

    @Override
    public List<ContactUserDTO> findContactUser() {
        return contactUserMapper.findContactUser(orgSyncConfig.getTableSchema());
    }
}
