package com.sinitek.bnzg.org.sync.mapper;

import com.sinitek.bnzg.org.sync.dto.ContactUserDTO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
public interface ContactUserMapper {

    /**
     * 查询用户信息
     * @param tableSchema 外部系统数据库schema
     * @return  用户信息
     */
    List<ContactUserDTO> findContactUser(@Param("tableSchema")String tableSchema);
}
