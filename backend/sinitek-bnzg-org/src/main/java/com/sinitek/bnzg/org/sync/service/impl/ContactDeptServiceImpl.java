package com.sinitek.bnzg.org.sync.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.sinitek.bnzg.org.sync.config.OrgSyncConfig;
import com.sinitek.bnzg.org.sync.dto.ContactDeptDTO;
import com.sinitek.bnzg.org.sync.mapper.ContactDeptMapper;
import com.sinitek.bnzg.org.sync.service.IContactDeptService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Service
@Slf4j
@DS("oracle")
public class ContactDeptServiceImpl  implements IContactDeptService {

    @Autowired
    private ContactDeptMapper contactDeptMapper;

    @Autowired
    private OrgSyncConfig orgSyncConfig;

    @Override
    public List<ContactDeptDTO> findContactDept() {
        return contactDeptMapper.findContactDept(orgSyncConfig.getTableSchema());
    }
}
