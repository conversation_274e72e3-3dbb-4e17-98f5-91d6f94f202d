package com.sinitek.bnzg.org.sync.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.sinitek.bnzg.common.constant.BnzgSettingConstant;
import com.sinitek.bnzg.org.sync.constant.OrgSyncConstant;
import com.sinitek.bnzg.org.sync.dto.ContactDeptDTO;
import com.sinitek.bnzg.org.sync.dto.ContactUserDTO;
import com.sinitek.bnzg.org.sync.service.IContactDeptService;
import com.sinitek.bnzg.org.sync.service.IContactUserService;
import com.sinitek.bnzg.org.sync.service.IOrgSyncService;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.org.dto.SynEmployeeToDeptDTO;
import com.sinitek.sirm.org.dto.SynUnitDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IImportEmpService;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.spirit.org.core.enumerate.UnitTypeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Service
@Slf4j
public class OrgSyncServiceImpl implements IOrgSyncService {

    @Autowired
    private IContactDeptService contactDeptService;

    @Autowired
    private IContactUserService contactUserService;

    @Autowired
    private IImportEmpService importEmpService;

    @Autowired
    private IOrgService orgService;


    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void syncOrg() {

        List<ContactDeptDTO> contactDept = contactDeptService.findContactDept();

        String ignoreSyncEmpUsername = SettingUtils.getStringValue(
            BnzgSettingConstant.DEFAULT_MODULE,
            BnzgSettingConstant.IGNORE_SYNC_EMPLOYEES_USERNAME);

        //组织结构
        List<SynUnitDTO> synUnitDTOS = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(contactDept)) {
            for (ContactDeptDTO contactDeptDTO : contactDept) {
                String deptId = contactDeptDTO.getDeptId();
                String name = contactDeptDTO.getName();
                if (StringUtils.isBlank(deptId) || StringUtils.isBlank(name)) {
                    log.warn("部门id或部门名称为空，忽略导入 contactDeptDTO：【{}】", contactDeptDTO);
                    continue;
                }
                SynUnitDTO synUnitDTO = new SynUnitDTO();
                synUnitDTO.setId(deptId);
                synUnitDTO.setName(name);
                synUnitDTO.setUnitType(UnitTypeEnum.UNIT);
                String parentId = contactDeptDTO.getParentId();

                //当DT_CONTACT_DEPT表中PARENT_ID为1(顶级部门)时,同步时需要把PARENT_ID替换为99999
                if (OrgSyncConstant.EXTERNAL_SYSTEM_ROOT_ID.equals(parentId)) {
                    parentId = OrgSyncConstant.SYSTEM_ROOT_ID;
                }
                synUnitDTO.setParentId(parentId);
                synUnitDTOS.add(synUnitDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(synUnitDTOS)) {
            String jsonStr = JSONUtil.toJsonStr(synUnitDTOS);
            int size = synUnitDTOS.size();
            log.info("同步部门数据总条数：【{}】,同步的部门数据详情：【{}】", size, jsonStr);
            importEmpService.synUnit(synUnitDTOS, OrgSyncConstant.DATE_SRC, false);
        }

        List<String> ignoreSyncEmpUsernameList = new ArrayList<>();
        if (StringUtils.isNotBlank(ignoreSyncEmpUsername)) {
            ignoreSyncEmpUsernameList.addAll(
                Arrays.asList(ignoreSyncEmpUsername.split(OrgSyncConstant.COMMA_SEPARATOR)));
        }

        //查询所有用户
        List<Employee> allEmployees = orgService.findAllEmployees();

        Map<String, String> userNameAndOrigIdMap = allEmployees.stream()
            .filter(item -> StringUtils.isNotBlank(item.getUserName()))
            .collect(Collectors.toMap(Employee::getUserName,
                e -> StringUtils.isNotBlank(e.getOrigId()) ? e.getOrigId() : "",
                (key1, key2) -> key2));

        Map<String, String> userNameAndDataSrcMap = allEmployees.stream()
            .filter(item -> StringUtils.isNotBlank(item.getUserName()))
            .collect(Collectors.toMap(Employee::getUserName,
                e -> StringUtils.isNotBlank(e.getDataSrc()) ? e.getDataSrc() : "",
                (key1, key2) -> key2));

        List<ContactUserDTO> contactUser = contactUserService.findContactUser();

        List<ContactUserDTO> emptyEmailUsers = contactUser.stream()
            .filter(item -> StringUtils.isBlank(item.getEmail())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptyEmailUsers)) {
            log.warn("用户邮箱为空，忽略导入；employees:【{}】", emptyEmailUsers);
        }

        Map<String, List<ContactUserDTO>> userNameAndContactUsersMap = contactUser.stream()
            .filter(item -> StringUtils.isNotBlank(item.getEmail())).collect(
                Collectors.groupingBy(
                    user -> user.getEmail().split(OrgSyncConstant.EMAIL_IDENTIFIER)[0]));

        List<ContactUserDTO> userNameRepeatedUsers = userNameAndContactUsersMap.values().stream()
            .filter(users -> users.size() > 1).flatMap(List::stream)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userNameRepeatedUsers)) {
            log.warn("以用户邮箱前缀作为登录名重复，employees:【{}】", userNameRepeatedUsers);
        }

        List<ContactUserDTO> userNameUniqueUsers = userNameAndContactUsersMap.values().stream()
            .filter(users -> users.size() == 1)
            .flatMap(List::stream)
            .collect(Collectors.toList());

        //用户
        List<SynEmployeeToDeptDTO> employeeDTOS = new ArrayList<>();

        userNameUniqueUsers.forEach(item -> {
            String userId = item.getUserId();
            String name = item.getName();
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(name)) {
                log.info("同步的用户，id和用户名必要参数为空，忽略导入；employee:【{}】", item);
                return;
            }

            String email = item.getEmail();
            String userName = email.split(OrgSyncConstant.EMAIL_IDENTIFIER)[0];

            if (ignoreSyncEmpUsernameList.contains(userName)) {
                log.info("忽略用户导入配置中已包含该用户，忽略导入；employee:【{}】", item);
                return;
            }

            //校验导入的用户和系统中新增的用户登录名是否相同
            if (userNameAndOrigIdMap.containsKey(userName)) {
                String origId = userNameAndOrigIdMap.get(userName);
                if (!userId.equals(origId)) {
                    log.warn(
                        "导入用户数据登录名和系统手动新增用户登录名相同，忽略导入；employee: 【{}】",
                        item);
                    return;
                }
                String dataSrc = userNameAndDataSrcMap.get(userName);
                if (!OrgSyncConstant.DATE_SRC.equals(dataSrc)) {
                    log.warn("该用户在系统中手动修改了数据来源，忽略导入；employee: 【{}】", item);
                    return;
                }
            }

            //组装数据
            SynEmployeeToDeptDTO synEmployeeToDeptDTO = new SynEmployeeToDeptDTO();
            synEmployeeToDeptDTO.setId(userId);
            synEmployeeToDeptDTO.setEmpName(name);
            synEmployeeToDeptDTO.setUserName(userName);
            synEmployeeToDeptDTO.setEmail(email);
            synEmployeeToDeptDTO.setMobilePhone(item.getMobile());

            String deptIds = item.getDeptIds();
            if (StringUtils.isNotBlank(deptIds)) {
                synEmployeeToDeptDTO.setDeptIds(
                    Arrays.asList(deptIds.split(OrgSyncConstant.COMMA_SEPARATOR)));
            }

            String telephone = item.getTelephone();
            if (StringUtils.isNotBlank(telephone)
                && telephone.length() > OrgSyncConstant.TELEPHONE_MAX_LENGTH) {
                log.warn("当前用户【{}】分机号【{}】长度超过最大值，忽略该字段导入", item, telephone);
            } else {
                synEmployeeToDeptDTO.setTel(telephone);
            }
            //  是否在职 0:否,1:是
            Integer status = item.getStatus();
            if (CommonBooleanEnum.TRUE.getValue().equals(status)) {
                synEmployeeToDeptDTO.setInservice(true);
            } else {
                synEmployeeToDeptDTO.setInservice(false);
            }
            employeeDTOS.add(synEmployeeToDeptDTO);

        });

        if (CollectionUtils.isNotEmpty(employeeDTOS)) {
            String jsonStr = JSONUtil.toJsonStr(employeeDTOS);
            int size = employeeDTOS.size();
            log.info("同步的用户信息总条数：【{}】同步的用户信息详情：【{}】", size, jsonStr);
            importEmpService.synEmployeeToDept(employeeDTOS, OrgSyncConstant.DATE_SRC, false);
        }

    }
}
